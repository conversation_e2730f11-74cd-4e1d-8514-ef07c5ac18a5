#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
MySQL连接监控和恢复工具
专门解决"lost connection to MySQL server during query"问题
"""

import mysql.connector
from mysql.connector import Error
import logging
import threading
import time
import signal
import sys
from config import DATABASE_CONFIG

class ConnectionMonitor:
    def __init__(self):
        self.monitoring = False
        self.monitor_thread = None
        self.connection_pool = None
        self.last_check_time = time.time()
        self.connection_failures = 0
        self.max_failures = 5
        
    def init_connection_pool(self):
        """初始化连接池"""
        try:
            pool_config = DATABASE_CONFIG.copy()
            pool_config.update({
                'pool_name': 'monitor_pool',
                'pool_size': 3,
                'pool_reset_session': True,
                'autocommit': True,
                'connection_timeout': 10,
                'charset': 'utf8mb4'
            })
            
            self.connection_pool = mysql.connector.pooling.MySQLConnectionPool(**pool_config)
            logging.info("连接监控池初始化成功")
            return True
            
        except Error as e:
            logging.error(f"初始化连接监控池失败: {e}")
            return False
    
    def test_connection(self):
        """测试数据库连接"""
        try:
            connection = self.connection_pool.get_connection()
            cursor = connection.cursor()
            
            # 执行简单查询测试连接
            cursor.execute("SELECT 1")
            result = cursor.fetchone()
            
            cursor.close()
            connection.close()
            
            if result and result[0] == 1:
                self.connection_failures = 0  # 重置失败计数
                return True
            else:
                return False
                
        except Error as e:
            self.connection_failures += 1
            logging.warning(f"连接测试失败 (第{self.connection_failures}次): {e}")
            
            # 检查是否是连接丢失错误
            if e.errno in (2006, 2013):  # MySQL server has gone away, Lost connection
                logging.error("检测到MySQL连接丢失!")
                return False
            
            return False
        except Exception as e:
            self.connection_failures += 1
            logging.error(f"连接测试异常 (第{self.connection_failures}次): {e}")
            return False
    
    def check_mysql_service(self):
        """检查MySQL服务状态"""
        import subprocess
        try:
            result = subprocess.run(['sc', 'query', 'mysql'], 
                                  capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0 and 'RUNNING' in result.stdout:
                return True
            else:
                logging.warning("MySQL服务未运行")
                return False
                
        except Exception as e:
            logging.error(f"检查MySQL服务失败: {e}")
            return False
    
    def restart_mysql_service(self):
        """重启MySQL服务"""
        import subprocess
        try:
            logging.info("尝试重启MySQL服务...")
            
            # 停止服务
            stop_result = subprocess.run(['net', 'stop', 'mysql'], 
                                       capture_output=True, text=True, timeout=30)
            
            time.sleep(3)  # 等待服务完全停止
            
            # 启动服务
            start_result = subprocess.run(['net', 'start', 'mysql'], 
                                        capture_output=True, text=True, timeout=30)
            
            if start_result.returncode == 0:
                logging.info("MySQL服务重启成功")
                time.sleep(5)  # 等待服务完全启动
                return True
            else:
                logging.error(f"MySQL服务启动失败: {start_result.stderr}")
                return False
                
        except Exception as e:
            logging.error(f"重启MySQL服务异常: {e}")
            return False
    
    def recover_connection(self):
        """恢复数据库连接"""
        logging.info("开始连接恢复流程...")
        
        # 1. 检查MySQL服务
        if not self.check_mysql_service():
            logging.info("MySQL服务异常，尝试重启...")
            if not self.restart_mysql_service():
                logging.error("MySQL服务重启失败")
                return False
        
        # 2. 重新初始化连接池
        try:
            self.connection_pool = None
            time.sleep(2)
            
            if self.init_connection_pool():
                logging.info("连接池重新初始化成功")
                
                # 3. 测试新连接
                if self.test_connection():
                    logging.info("连接恢复成功")
                    return True
                else:
                    logging.error("连接恢复后测试失败")
                    return False
            else:
                logging.error("连接池重新初始化失败")
                return False
                
        except Exception as e:
            logging.error(f"连接恢复异常: {e}")
            return False
    
    def monitor_loop(self):
        """监控循环"""
        logging.info("开始MySQL连接监控...")
        
        while self.monitoring:
            try:
                current_time = time.time()
                
                # 每30秒检查一次连接
                if current_time - self.last_check_time >= 30:
                    self.last_check_time = current_time
                    
                    if not self.test_connection():
                        logging.warning("连接测试失败")
                        
                        # 如果连续失败次数过多，尝试恢复
                        if self.connection_failures >= self.max_failures:
                            logging.error(f"连续{self.connection_failures}次连接失败，开始恢复流程")
                            
                            if self.recover_connection():
                                logging.info("连接恢复成功，继续监控")
                            else:
                                logging.error("连接恢复失败，将在60秒后重试")
                                time.sleep(60)
                    else:
                        # 连接正常
                        if self.connection_failures > 0:
                            logging.info("连接已恢复正常")
                
                time.sleep(10)  # 每10秒检查一次监控状态
                
            except Exception as e:
                logging.error(f"监控循环异常: {e}")
                time.sleep(30)
    
    def start_monitoring(self):
        """开始监控"""
        if self.monitoring:
            logging.info("监控已在运行中")
            return
        
        if not self.init_connection_pool():
            logging.error("无法初始化连接池，监控启动失败")
            return
        
        self.monitoring = True
        self.monitor_thread = threading.Thread(target=self.monitor_loop, daemon=True)
        self.monitor_thread.start()
        logging.info("MySQL连接监控已启动")
    
    def stop_monitoring(self):
        """停止监控"""
        self.monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=5)
        logging.info("MySQL连接监控已停止")
    
    def get_status(self):
        """获取监控状态"""
        return {
            'monitoring': self.monitoring,
            'connection_failures': self.connection_failures,
            'last_check_time': self.last_check_time,
            'pool_available': self.connection_pool is not None
        }

# 全局监控实例
_monitor_instance = None

def get_connection_monitor():
    """获取连接监控实例（单例模式）"""
    global _monitor_instance
    if _monitor_instance is None:
        _monitor_instance = ConnectionMonitor()
    return _monitor_instance

def start_connection_monitoring():
    """启动连接监控"""
    monitor = get_connection_monitor()
    monitor.start_monitoring()

def stop_connection_monitoring():
    """停止连接监控"""
    monitor = get_connection_monitor()
    monitor.stop_monitoring()

def signal_handler(signum, frame):
    """信号处理器"""
    logging.info("收到退出信号，停止监控...")
    stop_connection_monitoring()
    sys.exit(0)

def main():
    """主函数 - 独立运行监控"""
    # 设置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('connection_monitor.log'),
            logging.StreamHandler()
        ]
    )
    
    # 注册信号处理器
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    print("MySQL连接监控工具")
    print("=" * 40)
    print("此工具将持续监控MySQL连接状态")
    print("检测到连接丢失时自动尝试恢复")
    print("按 Ctrl+C 停止监控")
    print("=" * 40)
    
    try:
        monitor = get_connection_monitor()
        monitor.start_monitoring()
        
        # 主循环
        while True:
            time.sleep(60)
            status = monitor.get_status()
            print(f"监控状态: 运行中={status['monitoring']}, "
                  f"失败次数={status['connection_failures']}")
            
    except KeyboardInterrupt:
        print("\n用户中断监控")
    except Exception as e:
        logging.error(f"监控程序异常: {e}")
    finally:
        stop_connection_monitoring()

if __name__ == "__main__":
    main()
