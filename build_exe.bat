@echo off
chcp 65001 >nul
echo ========================================
echo   DataTransmission Windows EXE打包工具
echo ========================================
echo.

REM 检查Python环境
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未检测到Python环境
    echo 请确保Python已安装并添加到PATH环境变量
    pause
    exit /b 1
)

echo 当前Python版本:
python --version

REM 检查是否在虚拟环境中
python -c "import sys; print('虚拟环境:', hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix))"

echo.
echo 开始检查和安装依赖包...

REM 安装PyInstaller
echo 安装PyInstaller...
pip install pyinstaller

REM 检查和修复项目依赖
echo 检查项目依赖...
python fix_package_issues.py

echo 安装项目依赖...
pip install -r requirements.txt

echo.
echo 开始打包EXE文件...

REM 运行打包脚本
python build_windows_exe.py

if errorlevel 1 (
    echo.
    echo 打包失败！尝试使用运行时错误修复...
    python fix_pyinstaller_runtime.py

    if errorlevel 1 (
        echo.
        echo 修复后仍然失败！请检查错误信息
        pause
        exit /b 1
    )
)

echo.
echo ========================================
echo           打包成功完成！
echo ========================================
echo.
echo 生成的文件位置:
echo DataTransmission_Windows_Deploy\
echo.
echo 文件列表:
dir DataTransmission_Windows_Deploy\ /b

echo.
echo 下一步操作:
echo 1. 将 DataTransmission_Windows_Deploy 文件夹复制到Windows服务器
echo 2. 在服务器上安装MySQL数据库
echo 3. 配置 config.py 中的数据库连接信息
echo 4. 运行 start.bat 启动程序
echo 5. 访问 http://localhost:5000/health 测试
echo.
echo 按任意键退出...
pause >nul
