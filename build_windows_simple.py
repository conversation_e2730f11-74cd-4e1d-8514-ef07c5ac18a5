#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
简化的Windows EXE打包脚本
避免复杂的依赖问题，使用基本的PyInstaller命令
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path

def check_basic_dependencies():
    """检查基本依赖"""
    print("检查基本依赖...")
    
    try:
        import PyInstaller
        print(f"✓ PyInstaller版本: {PyInstaller.__version__}")
    except ImportError:
        print("✗ PyInstaller未安装，正在安装...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
        print("✓ PyInstaller安装完成")
    
    return True

def create_simple_spec():
    """创建简单的spec文件"""
    print("创建简单的PyInstaller规格文件...")
    
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('config.py', '.'),
    ],
    hiddenimports=[
        'mysql.connector',
        'cv2',
        'qrcode',
        'pyzbar',
        'PIL',
        'apscheduler',
        'flask',
        'numpy',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        'distutils',
        'setuptools',
        'matplotlib',
        'scipy',
        'pandas',
        'jupyter',
        'test',
        'tests',
        'unittest',
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='DataTransmission',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
'''
    
    with open('DataTransmission_simple.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("✓ 创建简单规格文件")

def build_simple_exe():
    """构建简单的EXE文件"""
    print("开始构建EXE文件...")
    
    # 清理之前的构建
    if os.path.exists('dist'):
        shutil.rmtree('dist')
    if os.path.exists('build'):
        shutil.rmtree('build')
    
    # 使用基本的PyInstaller命令
    cmd = [
        'pyinstaller',
        '--onefile',
        '--console',
        '--name=DataTransmission',
        '--add-data=config.py;.',
        '--hidden-import=mysql.connector',
        '--hidden-import=cv2',
        '--hidden-import=qrcode',
        '--hidden-import=pyzbar',
        '--hidden-import=PIL',
        '--hidden-import=apscheduler',
        '--hidden-import=flask',
        '--hidden-import=numpy',
        '--exclude-module=distutils',
        '--exclude-module=setuptools',
        '--exclude-module=matplotlib',
        '--exclude-module=scipy',
        '--exclude-module=pandas',
        '--exclude-module=test',
        '--exclude-module=tests',
        '--exclude-module=unittest',
        'main.py'
    ]
    
    print("使用基本PyInstaller命令...")
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("✓ EXE文件构建成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ 构建失败: {e}")
        print(f"错误输出: {e.stderr}")
        if e.stdout:
            print(f"标准输出: {e.stdout}")
        return False

def create_windows_config():
    """创建Windows配置文件"""
    config_content = '''# Windows配置文件
# 数据库配置
DATABASE_CONFIG = {
    'host': 'localhost',
    'port': 3306,
    'database': 'DataTransmission',
    'user': 'root',
    'password': '',
    'charset': 'utf8'
}

# Flask配置
FLASK_CONFIG = {
    'host': '0.0.0.0',
    'port': 5000,
    'debug': False
}

# 二维码显示配置
QR_DISPLAY_TIME = 2
QR_DISPLAY_SIZE = 900

# 摄像头配置
CAMERA_INDEX = 0
CAPTURE_INTERVAL = 1
'''
    
    with open('config_windows.py', 'w', encoding='utf-8') as f:
        f.write(config_content)
    
    print("✓ 创建Windows配置文件")

def create_deployment_package():
    """创建部署包"""
    print("创建Windows部署包...")
    
    # 创建部署目录
    deploy_dir = Path('DataTransmission_Windows_Simple')
    if deploy_dir.exists():
        shutil.rmtree(deploy_dir)
    
    deploy_dir.mkdir()
    
    # 复制EXE文件
    exe_path = Path('dist/DataTransmission.exe')
    if exe_path.exists():
        shutil.copy2(exe_path, deploy_dir / 'DataTransmission.exe')
        print("✓ 复制EXE文件")
    else:
        print("✗ 未找到EXE文件")
        return False
    
    # 复制配置文件
    shutil.copy2('config_windows.py', deploy_dir / 'config.py')
    
    # 创建启动脚本
    start_script = '''@echo off
echo 启动DataTransmission服务...
echo 请确保已安装MySQL数据库并配置好连接信息
echo.
DataTransmission.exe
pause
'''
    
    with open(deploy_dir / 'start.bat', 'w', encoding='gbk') as f:
        f.write(start_script)
    
    # 创建README
    readme_content = '''# DataTransmission Windows版本

## 使用说明

1. 安装MySQL数据库
2. 编辑config.py配置数据库连接
3. 双击start.bat启动程序
4. 访问 http://localhost:5000 使用Web界面

## 注意事项

- 确保MySQL服务正在运行
- 确保防火墙允许5000端口
- 如需使用摄像头功能，确保摄像头驱动正常
'''
    
    with open(deploy_dir / 'README.md', 'w', encoding='utf-8') as f:
        f.write(readme_content)
    
    print(f"✓ 部署包创建完成: {deploy_dir}")
    return True

def main():
    """主函数"""
    print("=" * 50)
    print("DataTransmission 简化Windows EXE打包工具")
    print("=" * 50)
    
    # 检查依赖
    if not check_basic_dependencies():
        return False
    
    # 创建Windows配置
    create_windows_config()
    
    # 构建EXE
    if not build_simple_exe():
        return False
    
    # 创建部署包
    if not create_deployment_package():
        return False
    
    print("\n" + "=" * 50)
    print("打包完成！")
    print("=" * 50)
    print("生成的文件:")
    print("- DataTransmission_Windows_Simple/DataTransmission.exe")
    print("- DataTransmission_Windows_Simple/config.py")
    print("- DataTransmission_Windows_Simple/start.bat")
    print("- DataTransmission_Windows_Simple/README.md")
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
