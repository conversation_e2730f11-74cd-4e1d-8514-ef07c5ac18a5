@echo off
chcp 65001 >nul
title 步骤1: MySQL服务测试

echo ========================================
echo    步骤1: MySQL服务状态测试
echo ========================================
echo.

echo 正在检查MySQL服务状态...
echo.

echo 1. 检查MySQL服务是否已安装
echo ----------------------------------------
sc query mysql >nul 2>&1
if %errorlevel% equ 0 (
    echo ✓ MySQL服务已安装
    
    echo.
    echo 2. 检查MySQL服务运行状态
    echo ----------------------------------------
    sc query mysql | find "STATE" | find "RUNNING" >nul
    if %errorlevel% equ 0 (
        echo ✓ MySQL服务正在运行
        
        echo.
        echo 3. 获取MySQL服务详细信息
        echo ----------------------------------------
        sc query mysql
        
        echo.
        echo 4. 检查MySQL进程
        echo ----------------------------------------
        tasklist | findstr /i mysql
        if %errorlevel% equ 0 (
            echo ✓ MySQL进程正在运行
        ) else (
            echo ⚠ 未找到MySQL进程
        )
        
        echo.
        echo 5. 测试MySQL端口连接
        echo ----------------------------------------
        netstat -ano | findstr :3306
        if %errorlevel% equ 0 (
            echo ✓ MySQL端口3306正在监听
        ) else (
            echo ⚠ MySQL端口3306未在监听
        )
        
    ) else (
        echo ✗ MySQL服务未运行
        echo.
        echo 尝试启动MySQL服务...
        net start mysql >nul 2>&1
        if %errorlevel% equ 0 (
            echo ✓ MySQL服务启动成功
        ) else (
            echo ✗ MySQL服务启动失败
            echo.
            echo 可能的原因:
            echo - 需要管理员权限
            echo - MySQL配置文件有问题
            echo - 端口3306被占用
            echo - MySQL安装不完整
        )
    )
    
) else (
    echo ✗ MySQL服务未安装
    echo.
    echo 解决方案:
    echo 1. 下载MySQL 8.0安装包
    echo 2. 运行安装程序
    echo 3. 选择"Server only"或"Developer Default"
    echo 4. 设置root密码
    echo 5. 确保选择"Configure MySQL Server as a Windows Service"
)

echo.
echo ========================================
echo 步骤1测试完成
echo ========================================
echo.

if %errorlevel% equ 0 (
    echo 🎉 MySQL服务测试通过
    echo 可以继续下一步测试
) else (
    echo ❌ MySQL服务测试失败
    echo 请先解决MySQL服务问题
)

echo.
echo 下一步: 运行 test_step2_python_modules.bat
pause
