@echo off
chcp 65001 >nul
echo ========================================
echo   DataTransmission CentOS 7 离线程序构建
echo   基于Conda环境 - 桌面版本
echo ========================================
echo.

REM 检查Python环境
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未检测到Python环境
    pause
    exit /b 1
)

echo 当前Python版本:
python --version

REM 检查conda环境
conda --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未检测到conda环境
    echo 请确保已安装Anaconda或Miniconda
    pause
    exit /b 1
)

echo 当前conda版本:
conda --version

REM 检查是否在虚拟环境中
python -c "import sys; print('虚拟环境:', hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix))"

echo.
echo 目标系统: CentOS 7 桌面版本
echo 打包类型: 离线可执行程序包
echo.
echo 可选构建方式:
echo 1. 完整版本 (基于conda环境，包含完整环境包)
echo 2. 简化版本 (基于当前Python，使用系统pip安装)
echo.

set /p choice="请选择构建方式 (1/2): "

if "%choice%"=="1" (
    echo 选择完整版本构建...
    echo 包含内容: conda环境 + 源代码 + 安装脚本

    set /p confirm="是否开始构建完整版本? (y/N): "
    if /i not "%confirm%"=="y" (
        echo 构建已取消
        pause
        exit /b 0
    )

    echo.
    echo 开始构建完整版本...

    REM 安装必要的包
    echo 检查和安装必要的包...
    pip install conda-pack >nul 2>&1

    REM 运行完整构建脚本
    python build_centos7_offline.py

) else if "%choice%"=="2" (
    echo 选择简化版本构建...
    echo 包含内容: 源代码 + pip依赖列表 + 安装脚本

    set /p confirm="是否开始构建简化版本? (y/N): "
    if /i not "%confirm%"=="y" (
        echo 构建已取消
        pause
        exit /b 0
    )

    echo.
    echo 开始构建简化版本...

    REM 运行简化构建脚本
    python build_centos7_simple.py

) else (
    echo 无效选择，默认使用简化版本...
    python build_centos7_simple.py
)

if errorlevel 1 (
    echo.
    echo 构建失败！请检查错误信息
    pause
    exit /b 1
)

echo.
echo ========================================
echo        CentOS 7离线程序包构建完成！
echo ========================================
echo.
echo 生成的文件:
for /d %%i in (DataTransmission_CentOS7_Offline_*) do (
    echo 部署包目录: %%i
    echo.
    echo 主要文件:
    echo - %%i\src\                     (项目源代码)
    echo - %%i\conda_env\               (conda环境文件)
    echo - %%i\scripts\install_centos7.sh  (安装脚本)
    echo - %%i\scripts\init_database.sql   (数据库脚本)
    echo - %%i\README.md                (使用说明)
)

echo.
echo 部署步骤:
echo 1. 将生成的文件夹传输到CentOS 7机器
echo 2. 在CentOS 7上运行: sudo ./scripts/install_centos7.sh
echo 3. 配置数据库: mysql_secure_installation
echo 4. 初始化数据库: mysql -u root -p ^< scripts/init_database.sql
echo 5. 配置应用: vi /opt/DataTransmission/config.py
echo 6. 启动程序: /opt/DataTransmission/start_datatransmission.sh
echo.
echo 特性说明:
echo - 完全离线安装，无需网络连接
echo - 自动安装conda环境和依赖
echo - 创建桌面快捷方式
echo - 支持图形界面显示二维码和摄像头预览
echo - 包含完整的数据库配置脚本
echo.
echo 按任意键退出...
pause >nul
