NorwegianStemmer=function(){var r=new BaseStemmer;var e=[["a",-1,1],["e",-1,1],["ede",1,1],["ande",1,1],["ende",1,1],["ane",1,1],["ene",1,1],["hetene",6,1],["erte",1,3],["en",-1,1],["heten",9,1],["ar",-1,1],["er",-1,1],["heter",12,1],["s",-1,2],["as",14,1],["es",14,1],["edes",16,1],["endes",16,1],["enes",16,1],["hetenes",19,1],["ens",14,1],["hetens",21,1],["ers",14,1],["ets",14,1],["et",-1,1],["het",25,1],["ert",-1,3],["ast",-1,1]];var i=[["dt",-1,-1],["vt",-1,-1]];var t=[["leg",-1,1],["eleg",0,1],["ig",-1,1],["eig",2,1],["lig",2,1],["elig",4,1],["els",-1,1],["lov",-1,1],["elov",7,1],["slov",7,1],["hetslov",9,1]];var a=[17,65,16,1,0,0,0,0,0,0,0,0,0,0,0,0,48,0,128];var s=[119,125,149,1];var u=0;var c=0;function l(){c=r.limit;var e=r.cursor;{var i=r.cursor+3;if(i>r.limit){return false}r.cursor=i}u=r.cursor;r.cursor=e;r:while(true){var t=r.cursor;e:{if(!r.in_grouping(a,97,248)){break e}r.cursor=t;break r}r.cursor=t;if(r.cursor>=r.limit){return false}r.cursor++}r:while(true){e:{if(!r.out_grouping(a,97,248)){break e}break r}if(r.cursor>=r.limit){return false}r.cursor++}c=r.cursor;r:{if(!(c<u)){break r}c=u}return true}function n(){var i;if(r.cursor<c){return false}var t=r.limit_backward;r.limit_backward=c;r.ket=r.cursor;i=r.find_among_b(e);if(i==0){r.limit_backward=t;return false}r.bra=r.cursor;r.limit_backward=t;switch(i){case 1:if(!r.slice_del()){return false}break;case 2:r:{var u=r.limit-r.cursor;e:{if(!r.in_grouping_b(s,98,122)){break e}break r}r.cursor=r.limit-u;if(!r.eq_s_b("k")){return false}if(!r.out_grouping_b(a,97,248)){return false}}if(!r.slice_del()){return false}break;case 3:if(!r.slice_from("er")){return false}break}return true}function o(){var e=r.limit-r.cursor;if(r.cursor<c){return false}var t=r.limit_backward;r.limit_backward=c;r.ket=r.cursor;if(r.find_among_b(i)==0){r.limit_backward=t;return false}r.bra=r.cursor;r.limit_backward=t;r.cursor=r.limit-e;if(r.cursor<=r.limit_backward){return false}r.cursor--;r.bra=r.cursor;if(!r.slice_del()){return false}return true}function f(){if(r.cursor<c){return false}var e=r.limit_backward;r.limit_backward=c;r.ket=r.cursor;if(r.find_among_b(t)==0){r.limit_backward=e;return false}r.bra=r.cursor;r.limit_backward=e;if(!r.slice_del()){return false}return true}this.stem=function(){var e=r.cursor;l();r.cursor=e;r.limit_backward=r.cursor;r.cursor=r.limit;var i=r.limit-r.cursor;n();r.cursor=r.limit-i;var t=r.limit-r.cursor;o();r.cursor=r.limit-t;var a=r.limit-r.cursor;f();r.cursor=r.limit-a;r.cursor=r.limit_backward;return true};this["stemWord"]=function(e){r.setCurrent(e);this.stem();return r.getCurrent()}};