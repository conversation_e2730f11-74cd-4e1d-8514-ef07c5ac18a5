# 快速部署指南：Windows → CentOS 7

## 🎯 部署概述
从Windows Anaconda开发环境部署到CentOS 7生产环境（带图形界面）

## 📋 准备清单
- ✅ Windows开发环境（已安装Anaconda，项目环境已配置）
- ✅ CentOS 7生产环境（带GNOME桌面）
- ✅ 网络连接或USB传输方式
- ✅ CentOS 7的root或sudo权限

## 🚀 三步快速部署

### 第一步：在Windows上创建部署包

#### 1.1 激活项目环境
```cmd
# 打开Anaconda Prompt
conda activate your_project_env_name
```

#### 1.2 运行部署包创建脚本
```cmd
# 在项目目录下运行
create_centos7_deployment.bat
```

这将创建 `DataTransmission_CentOS7_Deploy` 文件夹，包含：
- 环境配置文件
- 项目源代码
- 自动部署脚本
- 部署说明文档

### 第二步：传输到CentOS 7

#### 方法A：网络传输（推荐）
```bash
# 使用scp传输
scp -r DataTransmission_CentOS7_Deploy/ user@centos7-ip:/tmp/
```

#### 方法B：USB传输（离线环境）
1. 将 `DataTransmission_CentOS7_Deploy` 文件夹复制到USB
2. 在CentOS 7上挂载USB并复制到 `/tmp/`

### 第三步：在CentOS 7上自动部署

```bash
# 进入部署目录
cd /tmp/DataTransmission_CentOS7_Deploy

# 给脚本执行权限
chmod +x deploy_centos7.sh

# 运行自动部署脚本
sudo ./deploy_centos7.sh
```

## ⚙️ 部署后配置

### 配置数据库
```bash
# 1. 安全配置MariaDB
sudo mysql_secure_installation

# 2. 创建数据库
mysql -u root -p
```

在MySQL中执行：
```sql
CREATE DATABASE DataTransmission CHARACTER SET utf8 COLLATE utf8_general_ci;
CREATE USER 'datatrans'@'localhost' IDENTIFIED BY 'JKga#123';
GRANT ALL PRIVILEGES ON DataTransmission.* TO 'datatrans'@'localhost';
FLUSH PRIVILEGES;
EXIT;
```

### 配置应用程序
```bash
# 编辑配置文件
sudo vi /opt/DataTransmission/config.py
```

确认以下配置正确：
```python
DATABASE_CONFIG = {
    'host': 'localhost',
    'port': 3306,
    'database': 'DataTransmission',
    'user': 'datatrans',
    'password': 'JKga#123',
    'charset': 'utf8'
}

# 二维码显示配置（900x900像素，居中显示）
QR_DISPLAY_SIZE = 900
QR_DISPLAY_TIME = 2

# 摄像头配置
CAMERA_INDEX = 0  # 根据实际摄像头调整
```

## 🎮 启动和测试

### 启动服务
```bash
# 启动服务
sudo systemctl start datatransmission

# 设置开机自启
sudo systemctl enable datatransmission

# 检查状态
sudo systemctl status datatransmission
```

### 功能测试
```bash
# 1. 测试HTTP接口
curl http://localhost:5000/health

# 2. 测试数据接收
curl -X POST http://localhost:5000/receiveData \
  -H "Content-Type: application/json" \
  -d '{"id":"'$(date +%s)'","type":1,"data":"测试数据"}'

# 3. 检查摄像头
ls /dev/video*

# 4. 查看日志
sudo journalctl -u datatransmission -f
```

### 验证二维码显示
```bash
# 插入测试数据到数据库
mysql -u datatrans -p DataTransmission -e "
INSERT INTO transmission_data (id, type, data, status) 
VALUES ('$(date +%s)', 1, '测试二维码显示', 0);"

# 观察二维码是否在屏幕中央显示（900x900像素）
```

## 🔧 常见问题快速解决

### Q1: 部署脚本执行失败
```bash
# 查看详细错误信息
sudo ./deploy_centos7.sh 2>&1 | tee deploy.log

# 手动安装Anaconda
cd /tmp
wget https://repo.anaconda.com/archive/Anaconda3-2023.09-0-Linux-x86_64.sh
sudo bash Anaconda3-2023.09-0-Linux-x86_64.sh -b -p /opt/anaconda3
```

### Q2: conda环境创建失败
```bash
# 手动创建环境
source /opt/anaconda3/etc/profile.d/conda.sh
conda create -n datatransmission python=3.10 -y
conda activate datatransmission
pip install -r requirements.txt
```

### Q3: 二维码不显示
```bash
# 检查图形环境
echo $DISPLAY  # 应该显示 :0.0

# 测试图形界面
xclock &

# 安装图形库
sudo yum install -y python3-tkinter mesa-libGL-devel
```

### Q4: 摄像头无法访问
```bash
# 检查摄像头设备
ls -l /dev/video*

# 添加用户到video组
sudo usermod -a -G video $(whoami)
newgrp video
```

### Q5: 服务启动失败
```bash
# 查看详细日志
sudo journalctl -u datatransmission -xe

# 手动启动测试
cd /opt/DataTransmission
sudo -u $(whoami) ./start_centos7.sh
```

## 📊 管理命令速查

### 服务管理
```bash
sudo systemctl start datatransmission     # 启动
sudo systemctl stop datatransmission      # 停止
sudo systemctl restart datatransmission   # 重启
sudo systemctl status datatransmission    # 状态
sudo systemctl enable datatransmission    # 开机自启
```

### 日志查看
```bash
sudo journalctl -u datatransmission -f    # 实时日志
sudo journalctl -u datatransmission -n 50 # 最近50行
tail -f /opt/DataTransmission/data_transmission.log  # 应用日志
```

### 环境管理
```bash
source /opt/anaconda3/etc/profile.d/conda.sh
conda activate datatransmission
conda list                               # 查看已安装包
python -c "import cv2, qrcode; print('OK')"  # 测试关键包
```

## 🎯 部署成功标志

当看到以下输出时，表示部署成功：

1. **服务状态正常**：
   ```bash
   sudo systemctl status datatransmission
   # 显示 Active: active (running)
   ```

2. **HTTP接口响应**：
   ```bash
   curl http://localhost:5000/health
   # 返回 {"status": "healthy", "timestamp": ...}
   ```

3. **二维码正常显示**：
   - 插入测试数据后，二维码在屏幕中央显示
   - 尺寸为900x900像素
   - 显示2秒后自动关闭

4. **摄像头正常工作**：
   - 能够检测到摄像头设备
   - 可以识别二维码并存储数据

## 📞 技术支持

如果遇到问题，请收集以下信息：
- CentOS版本：`cat /etc/redhat-release`
- Python版本：`python --version`
- Conda版本：`conda --version`
- 错误日志：`sudo journalctl -u datatransmission -n 100`
- 系统日志：`dmesg | tail -50`

这个快速部署指南确保您能在最短时间内完成从Windows开发环境到CentOS 7生产环境的部署。
