#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
快速预览功能验证脚本
用于快速验证修复后的摄像头预览功能
"""

import time
import logging
import threading
from database import DatabaseManager
from camera_monitor import CameraMonitor

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def quick_test():
    """快速测试预览功能"""
    print("=" * 60)
    print("摄像头预览功能快速验证")
    print("=" * 60)
    print("修复说明:")
    print("✓ 预览窗口现在在独立线程中运行")
    print("✓ 解决了窗口无响应和阻塞问题")
    print("✓ 添加了线程同步机制")
    print("✓ 优化了窗口创建和销毁流程")
    print("=" * 60)
    
    try:
        # 初始化组件
        print("初始化数据库连接...")
        db = DatabaseManager()
        print("✓ 数据库连接成功")
        
        print("初始化摄像头监控...")
        camera = CameraMonitor(db)
        print("✓ 摄像头监控初始化成功")
        
        # 显示配置信息
        print(f"✓ 预览启用状态: {camera.preview_enabled}")
        if camera.preview_enabled:
            from config import CAMERA_PREVIEW_SIZE, CAMERA_PREVIEW_POSITION
            print(f"✓ 预览窗口尺寸: {CAMERA_PREVIEW_SIZE}")
            print(f"✓ 预览窗口位置: {CAMERA_PREVIEW_POSITION}")
        
        # 启动监控
        print("\n启动摄像头监控...")
        if camera.start_monitoring():
            print("✓ 摄像头监控已启动")
            
            if camera.preview_enabled:
                print("✓ 预览线程已启动")
                print("✓ 预览窗口应该在左上角显示")
                print("\n预览窗口特性:")
                print("- 如果摄像头未准备好，显示等待画面")
                print("- 实时显示摄像头画面")
                print("- 检测到二维码时显示绿色边框")
                print("- 显示状态信息和时间戳")
                print("- 可以手动关闭（点击X按钮）")
            else:
                print("⚠️  预览功能已禁用")
            
            print("\n测试运行中...")
            print("观察要点:")
            print("1. 预览窗口是否正常显示且响应")
            print("2. 窗口内容是否实时更新")
            print("3. 是否可以正常关闭窗口")
            print("4. 主程序是否保持响应")
            print("\n按 Ctrl+C 停止测试...")
            
            # 运行测试
            test_duration = 0
            try:
                while True:
                    time.sleep(1)
                    test_duration += 1
                    
                    # 每5秒显示一次状态
                    if test_duration % 5 == 0:
                        status = "运行中"
                        if camera.preview_closed:
                            status = "预览已关闭"
                        elif not camera.preview_enabled:
                            status = "预览已禁用"
                        
                        print(f"[{test_duration:3d}s] 状态: {status} | 线程: 监控{'✓' if camera.monitor_thread and camera.monitor_thread.is_alive() else '✗'} 预览{'✓' if camera.preview_thread and camera.preview_thread.is_alive() else '✗'}")
                    
                    # 检查预览状态变化
                    if camera.preview_closed:
                        print(f"[{test_duration:3d}s] 检测到预览窗口已被用户关闭")
                        print("摄像头监控继续运行，预览功能已禁用")
                        break
                        
            except KeyboardInterrupt:
                print(f"\n[{test_duration:3d}s] 用户停止测试")
            
            # 停止监控
            print("\n停止摄像头监控...")
            camera.stop_monitoring()
            print("✓ 摄像头监控已停止")
            
        else:
            print("✗ 摄像头监控启动失败")
            print("\n可能的原因:")
            print("- 摄像头未连接或被其他程序占用")
            print("- 摄像头驱动问题")
            print("- config.py中的CAMERA_INDEX设置错误")
            
            print("\n解决方法:")
            print("1. 检查摄像头连接状态")
            print("2. 关闭其他使用摄像头的程序")
            print("3. 尝试修改config.py中的CAMERA_INDEX值")
            print("4. 检查摄像头权限设置")
        
        # 关闭数据库
        db.close()
        print("✓ 数据库连接已关闭")
        
    except Exception as e:
        print(f"✗ 测试过程中出错: {e}")
        logging.error(f"快速测试出错: {e}")
        import traceback
        traceback.print_exc()

def check_threading_status():
    """检查线程状态"""
    print("\n当前线程状态:")
    active_threads = threading.active_count()
    print(f"活动线程数: {active_threads}")
    
    for thread in threading.enumerate():
        print(f"- {thread.name}: {'运行中' if thread.is_alive() else '已停止'}")

def main():
    """主函数"""
    print("DataTransmission 摄像头预览功能快速验证")
    print("验证修复后的预览功能是否正常工作")
    
    # 检查初始线程状态
    check_threading_status()
    
    # 运行快速测试
    quick_test()
    
    # 检查结束后线程状态
    print("\n等待线程清理...")
    time.sleep(2)
    check_threading_status()
    
    print("\n" + "=" * 60)
    print("快速验证完成")
    print("=" * 60)
    print("如果测试成功:")
    print("✓ 预览窗口应该正常显示且响应")
    print("✓ 窗口内容应该实时更新")
    print("✓ 主程序保持响应")
    print("✓ 可以正常关闭预览窗口")
    print("✓ 线程应该正常启动和停止")
    print()
    print("如果仍有问题:")
    print("- 检查错误日志信息")
    print("- 确认摄像头硬件状态")
    print("- 验证OpenCV安装是否正确")

if __name__ == "__main__":
    main()
