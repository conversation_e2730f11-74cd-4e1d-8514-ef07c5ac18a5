#!/bin/bash
# DataTransmission CentOS 7 简化安装脚本

set -e

echo "=== DataTransmission CentOS 7 简化安装 ==="

# 检查系统
if ! grep -q "CentOS Linux release 7" /etc/redhat-release 2>/dev/null; then
    echo "警告: 此脚本专为CentOS 7设计"
    read -p "是否继续? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        exit 1
    fi
fi

# 检查权限
if [ "$EUID" -ne 0 ]; then
    echo "此脚本需要root权限，请使用sudo运行"
    exit 1
fi

# 设置变量
INSTALL_DIR="/opt/DataTransmission"
CURRENT_DIR="$(pwd)"

# 安装系统依赖
echo "安装系统依赖..."
yum update -y
yum install -y epel-release
yum install -y python3 python3-pip python3-devel
yum install -y mariadb-server mariadb mariadb-devel
yum install -y zbar zbar-devel
yum install -y mesa-libGL mesa-libGL-devel
yum install -y python3-tkinter
yum install -y libX11-devel libXext-devel
yum install -y gcc gcc-c++ make
yum install -y wget curl

# 启动数据库
systemctl start mariadb
systemctl enable mariadb

# 安装Python包
echo "安装Python依赖..."
pip3 install --upgrade pip
pip3 install -r requirements/requirements.txt || {
    echo "从requirements.txt安装失败，尝试安装基础包..."
    pip3 install flask mysql-connector-python opencv-python qrcode pyzbar numpy requests APScheduler
}

# 安装程序
echo "安装程序..."
mkdir -p $INSTALL_DIR
cp -r src/* $INSTALL_DIR/
chown -R $(logname):$(logname) $INSTALL_DIR 2>/dev/null || chown -R $SUDO_USER:$SUDO_USER $INSTALL_DIR

# 创建启动脚本
cat > $INSTALL_DIR/start_datatransmission.sh << 'STARTEOF'
#!/bin/bash
# DataTransmission CentOS 7 启动脚本

echo "=== DataTransmission 启动中 ==="

# 设置变量
INSTALL_DIR="/opt/DataTransmission"

# 检查桌面环境
if [ -z "$DISPLAY" ]; then
    echo "错误: 未检测到图形桌面环境"
    exit 1
fi

# 设置环境变量
export DISPLAY=${DISPLAY:-:0.0}
export LD_LIBRARY_PATH=/usr/lib64:/usr/local/lib:$LD_LIBRARY_PATH

# 切换到项目目录
cd $INSTALL_DIR || {
    echo "错误: 无法进入项目目录"
    exit 1
}

echo "✓ 工作目录: $(pwd)"
echo "✓ 显示环境: $DISPLAY"
echo "✓ Python版本: $(python3 --version)"

# 检查主程序文件
if [ ! -f "main.py" ]; then
    echo "错误: 未找到主程序文件 main.py"
    exit 1
fi

echo "启动DataTransmission程序..."
echo "注意: 程序将在桌面环境中显示二维码和摄像头预览"
echo "按Ctrl+C停止程序"
echo ""

# 启动程序
python3 main.py
STARTEOF

chmod +x $INSTALL_DIR/start_datatransmission.sh

# 创建桌面快捷方式
REAL_USER=$(logname 2>/dev/null || echo $SUDO_USER)
if [ -n "$REAL_USER" ] && [ -d "/home/<USER>/Desktop" ]; then
    cat > /home/<USER>/Desktop/DataTransmission.desktop << 'DESKTOPEOF'
[Desktop Entry]
Version=1.0
Type=Application
Name=DataTransmission
Comment=数据传输客户端
Exec=/opt/DataTransmission/start_datatransmission.sh
Terminal=true
StartupNotify=true
Categories=Application;Network;
DESKTOPEOF
    
    chmod +x /home/<USER>/Desktop/DataTransmission.desktop
    chown $REAL_USER:$REAL_USER /home/<USER>/Desktop/DataTransmission.desktop
    echo "✓ 桌面快捷方式已创建"
fi

echo ""
echo "=== 安装完成 ==="
echo "下一步操作："
echo "1. 配置数据库: mysql_secure_installation"
echo "2. 创建数据库和用户"
echo "3. 配置应用: vi $INSTALL_DIR/config.py"
echo "4. 启动程序: $INSTALL_DIR/start_datatransmission.sh"
echo "5. 或双击桌面快捷方式启动"
