@echo off
chcp 65001 >nul
echo ========================================
echo   DataTransmission Windows完整部署包
echo ========================================
echo.

REM 检查Python环境
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未检测到Python环境
    pause
    exit /b 1
)

echo 当前Python版本:
python --version
echo.

REM 创建部署目录
set DEPLOY_DIR=DataTransmission_Windows_Complete
if exist %DEPLOY_DIR% (
    echo 删除现有部署目录...
    rmdir /s /q %DEPLOY_DIR%
)

echo 创建部署目录: %DEPLOY_DIR%
mkdir %DEPLOY_DIR%
mkdir %DEPLOY_DIR%\logs

REM 安装必要的包
echo 安装打包依赖...
pip install pyinstaller pywin32

REM 检查项目依赖
echo 检查项目依赖...
pip install -r requirements.txt

REM 运行EXE打包脚本
echo 运行EXE打包...
python build_windows_exe.py

if errorlevel 1 (
    echo EXE打包失败
    pause
    exit /b 1
)

REM 复制EXE文件到完整部署目录
if exist DataTransmission_Windows_Deploy\DataTransmission.exe (
    copy DataTransmission_Windows_Deploy\DataTransmission.exe %DEPLOY_DIR%\
    copy DataTransmission_Windows_Deploy\config.py %DEPLOY_DIR%\
    copy DataTransmission_Windows_Deploy\INSTALL_GUIDE.md %DEPLOY_DIR%\
    echo ✓ 复制EXE文件
) else (
    echo ✗ 未找到EXE文件
    pause
    exit /b 1
)

REM 复制Windows服务文件
copy windows_service_wrapper.py %DEPLOY_DIR%\
copy main.py %DEPLOY_DIR%\
copy database.py %DEPLOY_DIR%\
copy web_server.py %DEPLOY_DIR%\
copy qr_generator.py %DEPLOY_DIR%\
copy camera_monitor.py %DEPLOY_DIR%\

REM 创建服务管理脚本
echo 创建服务管理脚本...
(
echo @echo off
echo chcp 65001 ^>nul
echo echo ========================================
echo echo   DataTransmission Windows服务管理
echo echo ========================================
echo echo.
echo echo 请选择操作:
echo echo 1. 安装Windows服务
echo echo 2. 启动服务
echo echo 3. 停止服务
echo echo 4. 卸载服务
echo echo 5. 查看服务状态
echo echo 6. 直接运行程序（非服务模式）
echo echo 0. 退出
echo echo.
echo set /p choice="请输入选择 (0-6): "
echo.
echo if "%%choice%%"=="1" goto install
echo if "%%choice%%"=="2" goto start
echo if "%%choice%%"=="3" goto stop
echo if "%%choice%%"=="4" goto uninstall
echo if "%%choice%%"=="5" goto status
echo if "%%choice%%"=="6" goto direct
echo if "%%choice%%"=="0" goto exit
echo echo 无效选择
echo pause
echo goto menu
echo.
echo :install
echo echo 安装Windows服务...
echo python windows_service_wrapper.py install
echo if errorlevel 1 (
echo     echo 安装失败，请以管理员身份运行
echo ^) else (
echo     echo 服务安装成功
echo ^)
echo pause
echo goto menu
echo.
echo :start
echo echo 启动服务...
echo python windows_service_wrapper.py start
echo pause
echo goto menu
echo.
echo :stop
echo echo 停止服务...
echo python windows_service_wrapper.py stop
echo pause
echo goto menu
echo.
echo :uninstall
echo echo 卸载服务...
echo python windows_service_wrapper.py uninstall
echo pause
echo goto menu
echo.
echo :status
echo echo 查看服务状态...
echo sc query DataTransmissionService
echo pause
echo goto menu
echo.
echo :direct
echo echo 直接运行程序...
echo echo 按Ctrl+C停止程序
echo DataTransmission.exe
echo pause
echo goto menu
echo.
echo :menu
echo cls
echo echo ========================================
echo echo   DataTransmission Windows服务管理
echo echo ========================================
echo echo.
echo echo 请选择操作:
echo echo 1. 安装Windows服务
echo echo 2. 启动服务
echo echo 3. 停止服务
echo echo 4. 卸载服务
echo echo 5. 查看服务状态
echo echo 6. 直接运行程序（非服务模式）
echo echo 0. 退出
echo echo.
echo set /p choice="请输入选择 (0-6): "
echo goto process
echo.
echo :process
echo if "%%choice%%"=="1" goto install
echo if "%%choice%%"=="2" goto start
echo if "%%choice%%"=="3" goto stop
echo if "%%choice%%"=="4" goto uninstall
echo if "%%choice%%"=="5" goto status
echo if "%%choice%%"=="6" goto direct
echo if "%%choice%%"=="0" goto exit
echo echo 无效选择
echo pause
echo goto menu
echo.
echo :exit
echo echo 退出程序
) > %DEPLOY_DIR%\service_manager.bat

REM 创建快速启动脚本
(
echo @echo off
echo echo 启动DataTransmission服务...
echo echo 请确保已配置好数据库连接信息
echo echo.
echo DataTransmission.exe
echo pause
) > %DEPLOY_DIR%\start.bat

REM 创建配置向导脚本
(
echo @echo off
echo chcp 65001 ^>nul
echo echo ========================================
echo echo   DataTransmission 配置向导
echo echo ========================================
echo echo.
echo echo 此向导将帮助您配置数据库连接信息
echo echo.
echo set /p db_host="数据库主机地址 [localhost]: "
echo if "%%db_host%%"=="" set db_host=localhost
echo.
echo set /p db_port="数据库端口 [3306]: "
echo if "%%db_port%%"=="" set db_port=3306
echo.
echo set /p db_name="数据库名称 [DataTransmission]: "
echo if "%%db_name%%"=="" set db_name=DataTransmission
echo.
echo set /p db_user="数据库用户名 [root]: "
echo if "%%db_user%%"=="" set db_user=root
echo.
echo set /p db_pass="数据库密码: "
echo.
echo set /p web_port="Web服务端口 [5000]: "
echo if "%%web_port%%"=="" set web_port=5000
echo.
echo set /p camera_index="摄像头索引 [0]: "
echo if "%%camera_index%%"=="" set camera_index=0
echo.
echo echo 生成配置文件...
echo (
echo # 数据库配置
echo DATABASE_CONFIG = {
echo     'host': '%%db_host%%',
echo     'port': %%db_port%%,
echo     'database': '%%db_name%%',
echo     'user': '%%db_user%%',
echo     'password': '%%db_pass%%',
echo     'charset': 'utf8'
echo }
echo.
echo # Flask配置
echo FLASK_CONFIG = {
echo     'host': '0.0.0.0',
echo     'port': %%web_port%%,
echo     'debug': False
echo }
echo.
echo # 二维码显示配置
echo QR_DISPLAY_TIME = 2
echo QR_DISPLAY_SIZE = 900
echo.
echo # 摄像头配置
echo CAMERA_INDEX = %%camera_index%%
echo CAPTURE_INTERVAL = 1
echo ^) ^> config.py
echo.
echo echo 配置文件已生成: config.py
echo echo.
echo echo 下一步:
echo echo 1. 确保MySQL数据库已安装并运行
echo echo 2. 创建数据库: %%db_name%%
echo echo 3. 运行 start.bat 启动程序
echo echo.
echo pause
) > %DEPLOY_DIR%\config_wizard.bat

REM 创建数据库初始化脚本
(
echo -- DataTransmission 数据库初始化脚本
echo -- 请在MySQL中执行此脚本
echo.
echo CREATE DATABASE IF NOT EXISTS DataTransmission CHARACTER SET utf8 COLLATE utf8_general_ci;
echo.
echo USE DataTransmission;
echo.
echo -- 创建用户（可选）
echo -- CREATE USER 'datatrans'@'localhost' IDENTIFIED BY 'your_password';
echo -- GRANT ALL PRIVILEGES ON DataTransmission.* TO 'datatrans'@'localhost';
echo -- FLUSH PRIVILEGES;
echo.
echo -- 表结构将由程序自动创建
echo.
echo SELECT 'DataTransmission数据库初始化完成' AS message;
) > %DEPLOY_DIR%\init_database.sql

REM 创建完整的部署说明
(
echo # DataTransmission Windows完整部署指南
echo.
echo ## 文件说明
echo - DataTransmission.exe: 主程序可执行文件
echo - config.py: 配置文件
echo - service_manager.bat: Windows服务管理工具
echo - start.bat: 快速启动脚本
echo - config_wizard.bat: 配置向导
echo - init_database.sql: 数据库初始化脚本
echo - windows_service_wrapper.py: Windows服务包装器
echo - logs/: 日志目录
echo.
echo ## 快速部署步骤
echo.
echo ### 1. 安装MySQL数据库
echo 下载并安装MySQL 8.0或MariaDB 10.x
echo.
echo ### 2. 初始化数据库
echo 在MySQL中执行 init_database.sql 脚本
echo.
echo ### 3. 配置应用程序
echo 运行 config_wizard.bat 配置向导，或手动编辑 config.py
echo.
echo ### 4. 选择运行方式
echo.
echo #### 方式A: 直接运行（推荐测试）
echo 双击 start.bat
echo.
echo #### 方式B: Windows服务（推荐生产）
echo 1. 以管理员身份运行 service_manager.bat
echo 2. 选择"1"安装服务
echo 3. 选择"2"启动服务
echo.
echo ### 5. 验证部署
echo 打开浏览器访问: http://localhost:5000/health
echo.
echo ## 功能说明
echo - HTTP API接口: http://localhost:5000/receiveData
echo - 二维码生成: 自动从数据库读取数据生成二维码显示
echo - 摄像头监控: 自动识别二维码并存储数据
echo - 日志记录: 查看 logs/ 目录下的日志文件
echo.
echo ## 故障排除
echo 1. 程序无法启动: 检查 config.py 中的数据库配置
echo 2. 摄像头无法使用: 检查摄像头驱动和权限
echo 3. 二维码无法显示: 确保系统支持图形界面
echo 4. 服务安装失败: 以管理员身份运行
echo 5. 查看详细错误: 检查 logs/ 目录下的日志文件
echo.
echo ## 技术支持
echo 如遇问题，请提供以下信息:
echo - Windows版本
echo - 错误日志内容
echo - 数据库版本和配置
echo - 程序运行环境描述
) > %DEPLOY_DIR%\DEPLOYMENT_GUIDE.md

echo.
echo ========================================
echo        Windows完整部署包创建完成！
echo ========================================
echo.
echo 生成的文件位置: %DEPLOY_DIR%\
echo.
echo 主要文件:
echo - DataTransmission.exe        (主程序)
echo - service_manager.bat         (服务管理工具)
echo - config_wizard.bat           (配置向导)
echo - start.bat                   (快速启动)
echo - DEPLOYMENT_GUIDE.md         (部署指南)
echo.
echo 部署步骤:
echo 1. 将 %DEPLOY_DIR% 文件夹复制到Windows服务器
echo 2. 安装MySQL数据库
echo 3. 运行 config_wizard.bat 配置数据库连接
echo 4. 运行 service_manager.bat 安装和管理Windows服务
echo.
echo 按任意键退出...
pause >nul
