#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
PyInstaller运行时错误修复脚本
专门解决打包后EXE运行时的模块缺失问题
"""

import os
import sys
import subprocess

def check_runtime_modules():
    """检查运行时必需的模块"""
    print("=" * 60)
    print("PyInstaller运行时模块检查")
    print("=" * 60)
    
    # 运行时必需的模块
    runtime_modules = [
        'reprlib',
        'collections',
        'collections.abc',
        'pkgutil',
        'importlib',
        'importlib.util',
        'importlib.machinery',
        'sys',
        'os',
        'types',
        'weakref',
        'gc',
        'builtins',
        'warnings',
        'traceback',
        'linecache',
        'tokenize',
        'keyword',
        'operator',
        'functools',
        'itertools',
        'copy',
        'copyreg',
        'pickle',
        'struct',
        'array',
        'io',
        'codecs',
        'locale',
        'encodings',
        'encodings.utf_8',
        'encodings.latin_1',
        'encodings.cp1252',
        'encodings.ascii',
    ]
    
    missing_modules = []
    
    for module in runtime_modules:
        try:
            __import__(module)
            print(f"✓ {module}")
        except ImportError as e:
            missing_modules.append(module)
            print(f"✗ {module} - {e}")
    
    if missing_modules:
        print(f"\n⚠️  缺少运行时模块: {missing_modules}")
        return False
    else:
        print("\n✓ 所有运行时模块检查通过")
        return True

def create_runtime_fix_spec():
    """创建修复运行时错误的PyInstaller规格文件"""
    print("\n创建运行时错误修复规格文件...")
    
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-
# PyInstaller规格文件 - 修复运行时错误版本

block_cipher = None

# 数据文件
datas = [
    ('config.py', '.'),
    ('README.md', '.'),
]

# 二进制文件
binaries = []

# 隐藏导入 - 专门修复运行时错误
hiddenimports = [
    # 修复 "No module named 'reprlib'" 错误
    'reprlib',
    'collections',
    'collections.abc',
    'pkgutil',
    
    # 修复 importlib 相关错误
    'importlib',
    'importlib.util',
    'importlib.machinery',
    'importlib.abc',
    'importlib._bootstrap',
    'importlib._bootstrap_external',
    
    # 修复编码相关错误
    'encodings',
    'encodings.utf_8',
    'encodings.latin_1',
    'encodings.cp1252',
    'encodings.ascii',
    'encodings.idna',
    'encodings.mbcs',
    'encodings.aliases',
    
    # 修复基础模块错误
    'sys',
    'os',
    'os.path',
    'pathlib',
    'types',
    'weakref',
    'gc',
    'builtins',
    'warnings',
    'traceback',
    'linecache',
    'tokenize',
    'keyword',
    'token',
    'operator',
    'functools',
    'itertools',
    'copy',
    'copyreg',
    'pickle',
    'struct',
    'array',
    'io',
    'codecs',
    'locale',
    
    # 应用程序依赖
    'mysql.connector',
    'mysql.connector.locales.eng',
    'mysql.connector.locales.eng.client_error',
    'mysql.connector.connection',
    'mysql.connector.cursor',
    'mysql.connector.errors',
    
    'PIL',
    'PIL.Image',
    'PIL.ImageDraw',
    'PIL.ImageFont',
    'PIL.ImageTk',
    
    'cv2',
    'cv2.data',
    
    'qrcode',
    'qrcode.image.pil',
    
    'pyzbar',
    'pyzbar.pyzbar',
    'pyzbar.wrapper',
    
    'flask',
    'flask.json.tag',
    'flask.helpers',
    'flask.wrappers',
    
    'apscheduler',
    'apscheduler.schedulers.background',
    'apscheduler.triggers.interval',
    'apscheduler.executors.pool',
    'apscheduler.jobstores.memory',
    
    'numpy',
    'numpy.core',
    'numpy.core._multiarray_umath',
    
    'tkinter',
    'tkinter.ttk',
    'tkinter.messagebox',
    
    'requests',
    'requests.adapters',
    'requests.auth',
    'requests.cookies',
    'requests.exceptions',
    'requests.models',
    'requests.sessions',
    
    'json',
    'logging',
    'logging.handlers',
    'threading',
    'time',
    'datetime',
]

# 排除的模块 - 只排除确实不需要的
excludes = [
    'matplotlib',
    'scipy',
    'pandas',
    'jupyter',
    'notebook',
    'IPython',
    'pytest',
    'test',
    'tests',
    'unittest',
    'doctest',
    'pydoc',
    'xmlrpc',
    'email',
    'html',
    'xml',
    'multiprocessing',
    'concurrent',
    'asyncio',
    'queue',
    'dbm',
    'shelve',
    'marshal',
    'xdrlib',
    'turtle',
    'turtledemo',
]

a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=binaries,
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=excludes,
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='DataTransmission',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='app_icon.ico' if os.path.exists('app_icon.ico') else None,
)
'''
    
    with open('DataTransmission_runtime_fix.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("✓ 运行时错误修复规格文件已创建: DataTransmission_runtime_fix.spec")
    return True

def rebuild_with_fix():
    """使用修复规格文件重新构建"""
    print("\n使用修复规格文件重新构建...")
    
    # 清理之前的构建
    if os.path.exists('dist'):
        import shutil
        shutil.rmtree('dist')
        print("✓ 清理旧的构建文件")
    
    if os.path.exists('build'):
        import shutil
        shutil.rmtree('build')
        print("✓ 清理构建缓存")
    
    # 使用修复规格文件构建
    cmd = [
        'pyinstaller',
        '--clean',
        '--noconfirm',
        'DataTransmission_runtime_fix.spec'
    ]
    
    try:
        print("开始重新构建...")
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("✓ 重新构建成功")
        if result.stdout:
            print("构建输出:", result.stdout[-500:])
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ 重新构建失败: {e}")
        print(f"错误输出: {e.stderr}")
        return False

def test_exe():
    """测试生成的EXE文件"""
    exe_path = os.path.join('dist', 'DataTransmission.exe')
    
    if not os.path.exists(exe_path):
        print(f"✗ EXE文件不存在: {exe_path}")
        return False
    
    print(f"\n测试EXE文件: {exe_path}")
    print("注意: 这将启动程序，请手动检查是否有运行时错误")
    
    try:
        response = input("是否测试运行EXE文件? (y/N): ")
        if response.lower() == 'y':
            print("启动EXE文件...")
            subprocess.Popen([exe_path])
            print("✓ EXE文件已启动，请检查是否有错误")
            return True
        else:
            print("跳过EXE测试")
            return True
    except KeyboardInterrupt:
        print("\n测试已取消")
        return False

def main():
    """主修复函数"""
    print("PyInstaller运行时错误修复工具")
    print("专门解决 'No module named reprlib' 等运行时错误")
    
    # 检查运行时模块
    if not check_runtime_modules():
        print("运行时模块检查失败，但继续修复...")
    
    # 创建修复规格文件
    if not create_runtime_fix_spec():
        print("创建修复规格文件失败")
        return
    
    # 询问是否重新构建
    try:
        response = input("\n是否使用修复规格文件重新构建? (y/N): ")
        if response.lower() != 'y':
            print("修复规格文件已创建，可以手动运行:")
            print("pyinstaller --clean --noconfirm DataTransmission_runtime_fix.spec")
            return
    except KeyboardInterrupt:
        print("\n操作已取消")
        return
    
    # 重新构建
    if rebuild_with_fix():
        print("\n" + "=" * 60)
        print("修复完成")
        print("=" * 60)
        print("生成的文件:")
        print("- DataTransmission_runtime_fix.spec (修复规格文件)")
        print("- dist/DataTransmission.exe (修复后的EXE)")
        
        # 测试EXE
        test_exe()
    else:
        print("重新构建失败，请检查错误信息")

if __name__ == "__main__":
    main()
