#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
最小化测试脚本
用于测试基本功能是否正常
"""

import sys
import os
import logging
import traceback

# 配置日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('test_minimal.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)

def test_step_by_step():
    """逐步测试每个组件"""
    
    print("开始逐步测试...")
    logging.info("开始逐步测试...")
    
    try:
        # 步骤1: 测试配置文件导入
        print("步骤1: 测试配置文件导入...")
        logging.info("步骤1: 测试配置文件导入...")
        
        try:
            import config
            print("✓ 配置文件导入成功")
            logging.info("配置文件导入成功")
            
            if hasattr(config, 'DATABASE_CONFIG'):
                db_config = config.DATABASE_CONFIG
                print(f"数据库配置: {db_config}")
                logging.info(f"数据库配置: {db_config}")
            else:
                print("✗ 配置文件中缺少DATABASE_CONFIG")
                logging.error("配置文件中缺少DATABASE_CONFIG")
                return False
                
        except Exception as e:
            print(f"✗ 配置文件导入失败: {e}")
            logging.error(f"配置文件导入失败: {e}")
            traceback.print_exc()
            return False
        
        # 步骤2: 测试MySQL连接器
        print("\n步骤2: 测试MySQL连接器...")
        logging.info("步骤2: 测试MySQL连接器...")
        
        try:
            import mysql.connector
            from mysql.connector import Error
            print("✓ MySQL连接器导入成功")
            logging.info("MySQL连接器导入成功")
        except Exception as e:
            print(f"✗ MySQL连接器导入失败: {e}")
            logging.error(f"MySQL连接器导入失败: {e}")
            traceback.print_exc()
            return False
        
        # 步骤3: 测试数据库连接
        print("\n步骤3: 测试数据库连接...")
        logging.info("步骤3: 测试数据库连接...")
        
        try:
            connection = mysql.connector.connect(**config.DATABASE_CONFIG)
            if connection.is_connected():
                print("✓ 数据库连接成功")
                logging.info("数据库连接成功")
                
                # 获取数据库信息
                cursor = connection.cursor()
                cursor.execute("SELECT VERSION()")
                version = cursor.fetchone()
                print(f"MySQL版本: {version[0]}")
                logging.info(f"MySQL版本: {version[0]}")
                
                cursor.close()
                connection.close()
            else:
                print("✗ 数据库连接失败")
                logging.error("数据库连接失败")
                return False
                
        except Error as e:
            print(f"✗ 数据库连接错误: {e}")
            logging.error(f"数据库连接错误: {e}")
            return False
        except Exception as e:
            print(f"✗ 数据库连接异常: {e}")
            logging.error(f"数据库连接异常: {e}")
            traceback.print_exc()
            return False
        
        # 步骤4: 测试数据库管理器
        print("\n步骤4: 测试数据库管理器...")
        logging.info("步骤4: 测试数据库管理器...")
        
        try:
            from database import DatabaseManager
            db_manager = DatabaseManager()
            print("✓ 数据库管理器初始化成功")
            logging.info("数据库管理器初始化成功")
        except Exception as e:
            print(f"✗ 数据库管理器初始化失败: {e}")
            logging.error(f"数据库管理器初始化失败: {e}")
            traceback.print_exc()
            return False
        
        # 步骤5: 测试其他组件
        print("\n步骤5: 测试其他组件...")
        logging.info("步骤5: 测试其他组件...")
        
        try:
            from web_server import WebServer
            web_server = WebServer(db_manager)
            print("✓ Web服务器初始化成功")
            logging.info("Web服务器初始化成功")
        except Exception as e:
            print(f"✗ Web服务器初始化失败: {e}")
            logging.error(f"Web服务器初始化失败: {e}")
            traceback.print_exc()
            return False
        
        try:
            from qr_generator import QRGenerator
            qr_generator = QRGenerator(db_manager)
            print("✓ 二维码生成器初始化成功")
            logging.info("二维码生成器初始化成功")
        except Exception as e:
            print(f"✗ 二维码生成器初始化失败: {e}")
            logging.error(f"二维码生成器初始化失败: {e}")
            traceback.print_exc()
            return False
        
        try:
            from camera_monitor import CameraMonitor
            camera_monitor = CameraMonitor(db_manager)
            print("✓ 摄像头监控初始化成功")
            logging.info("摄像头监控初始化成功")
        except Exception as e:
            print(f"✗ 摄像头监控初始化失败: {e}")
            logging.error(f"摄像头监控初始化失败: {e}")
            traceback.print_exc()
            return False
        
        print("\n✓ 所有组件测试通过！")
        logging.info("所有组件测试通过！")
        return True
        
    except Exception as e:
        print(f"✗ 测试过程中出现未预期的错误: {e}")
        logging.error(f"测试过程中出现未预期的错误: {e}")
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("DataTransmission 最小化测试工具")
    print("=" * 50)
    
    success = test_step_by_step()
    
    print("\n" + "=" * 50)
    if success:
        print("✓ 测试完成，所有组件正常")
        print("程序应该能够正常运行")
    else:
        print("✗ 测试失败，发现问题")
        print("请检查上述错误信息")
    
    print(f"\n详细日志已保存到: test_minimal.log")
    input("按回车键退出...")

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        print(f"程序运行时出错: {e}")
        logging.error(f"程序运行时出错: {e}")
        traceback.print_exc()
        input("按回车键退出...")
