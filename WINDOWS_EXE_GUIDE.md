# DataTransmission Windows EXE部署完整指南

## 🎯 概述
将DataTransmission Python项目打包成Windows可执行文件，支持直接运行和Windows服务两种模式。

## 📦 打包方式选择

### 方式一：完整部署包（推荐）
包含EXE文件、Windows服务、配置向导等完整功能
```cmd
create_windows_package.bat
```

### 方式二：简单EXE打包
仅生成基本的EXE文件
```cmd
build_exe.bat
```

### 方式三：手动打包
使用Python脚本进行自定义打包
```cmd
python build_windows_exe.py
```

## 🚀 推荐部署流程

### 第一步：在开发环境中打包

#### 1.1 准备环境
```cmd
# 确保在项目的conda环境中
conda activate your_project_env

# 检查依赖
pip list
```

#### 1.2 运行完整打包脚本
```cmd
# 在项目根目录运行
create_windows_package.bat
```

这将生成 `DataTransmission_Windows_Complete` 文件夹，包含：
- `DataTransmission.exe` - 主程序
- `service_manager.bat` - 服务管理工具
- `config_wizard.bat` - 配置向导
- `start.bat` - 快速启动
- `DEPLOYMENT_GUIDE.md` - 部署指南
- 其他支持文件

### 第二步：传输到Windows服务器

将整个 `DataTransmission_Windows_Complete` 文件夹复制到Windows服务器的任意位置（如 `C:\DataTransmission\`）

### 第三步：在Windows服务器上部署

#### 3.1 安装MySQL数据库
下载并安装MySQL 8.0或MariaDB 10.x

#### 3.2 初始化数据库
```sql
-- 在MySQL中执行
CREATE DATABASE DataTransmission CHARACTER SET utf8 COLLATE utf8_general_ci;
CREATE USER 'datatrans'@'localhost' IDENTIFIED BY 'your_password';
GRANT ALL PRIVILEGES ON DataTransmission.* TO 'datatrans'@'localhost';
FLUSH PRIVILEGES;
```

#### 3.3 配置应用程序
运行 `config_wizard.bat` 配置向导，按提示输入：
- 数据库主机地址
- 数据库端口
- 数据库名称
- 用户名和密码
- Web服务端口
- 摄像头索引

#### 3.4 选择运行方式

##### 方式A：直接运行（测试推荐）
```cmd
# 双击运行
start.bat

# 或直接运行
DataTransmission.exe
```

##### 方式B：Windows服务（生产推荐）
```cmd
# 以管理员身份运行
service_manager.bat

# 选择操作：
# 1 - 安装服务
# 2 - 启动服务
# 5 - 查看状态
```

### 第四步：验证部署

#### 4.1 测试HTTP接口
```cmd
# 在浏览器中访问
http://localhost:5000/health

# 或使用curl
curl http://localhost:5000/health
```

#### 4.2 测试数据接收接口
```cmd
curl -X POST http://localhost:5000/receiveData ^
  -H "Content-Type: application/json" ^
  -d "{\"id\":\"%date:~0,4%%date:~5,2%%date:~8,2%%time:~0,2%%time:~3,2%%time:~6,2%\",\"type\":1,\"data\":\"测试数据\"}"
```

#### 4.3 测试二维码功能
- 通过API插入数据后，观察是否在屏幕中央显示900x900像素的二维码
- 二维码应显示2秒后自动关闭

#### 4.4 测试摄像头功能
- 确保摄像头设备正常连接
- 观察左上角是否显示320x240像素的预览窗口
- 预览窗口应显示实时摄像头画面和状态信息
- 使用二维码扫描软件生成测试二维码
- 观察预览窗口中是否显示绿色检测框
- 观察程序是否能识别并存储数据
- 测试手动关闭预览窗口功能

## 🔧 高级配置

### Windows服务配置
服务安装后可通过以下方式管理：

```cmd
# 命令行管理
sc start DataTransmissionService    # 启动服务
sc stop DataTransmissionService     # 停止服务
sc query DataTransmissionService    # 查看状态

# 或使用Python脚本
python windows_service_wrapper.py start
python windows_service_wrapper.py stop
python windows_service_wrapper.py restart
```

### 配置文件详解
`config.py` 主要配置项：

```python
# 数据库配置
DATABASE_CONFIG = {
    'host': 'localhost',        # 数据库主机
    'port': 3306,              # 数据库端口
    'database': 'DataTransmission',  # 数据库名
    'user': 'datatrans',       # 用户名
    'password': 'your_password', # 密码
    'charset': 'utf8'          # 字符集
}

# Web服务配置
FLASK_CONFIG = {
    'host': '0.0.0.0',         # 监听地址（0.0.0.0表示所有接口）
    'port': 5000,              # 监听端口
    'debug': False             # 调试模式
}

# 二维码显示配置
QR_DISPLAY_TIME = 2            # 显示时间（秒）
QR_DISPLAY_SIZE = 900          # 显示尺寸（像素）

# 摄像头配置
CAMERA_INDEX = 0               # 摄像头索引（通常为0）
CAPTURE_INTERVAL = 1           # 截图间隔（秒）

# 摄像头预览配置
CAMERA_PREVIEW_ENABLED = True        # 是否启用预览窗口
CAMERA_PREVIEW_SIZE = (320, 240)     # 预览窗口尺寸
CAMERA_PREVIEW_POSITION = (10, 10)   # 预览窗口位置（左上角）
```

### 日志配置
程序运行日志保存在 `logs/` 目录：
- `service.log` - 服务日志
- `data_transmission.log` - 应用日志

## 🛠️ 故障排除

### 常见问题及解决方案

#### Q1: EXE文件无法启动
```cmd
# 检查依赖
# 确保目标机器安装了Visual C++ Redistributable
# 下载地址: https://aka.ms/vs/17/release/vc_redist.x64.exe

# 检查配置文件
# 确保config.py格式正确，数据库连接信息准确
```

#### Q2: 数据库连接失败
```cmd
# 检查MySQL服务状态
net start mysql

# 测试数据库连接
mysql -u datatrans -p -h localhost DataTransmission

# 检查防火墙设置
# 确保3306端口未被阻止
```

#### Q3: Windows服务安装失败
```cmd
# 以管理员身份运行命令提示符
# 右键"命令提示符" -> "以管理员身份运行"

# 安装pywin32（如果缺失）
pip install pywin32

# 手动安装服务
python windows_service_wrapper.py install
```

#### Q4: 摄像头无法访问
```cmd
# 检查摄像头设备
# 设备管理器 -> 图像设备

# 检查摄像头权限
# Windows设置 -> 隐私 -> 摄像头

# 修改摄像头索引
# 编辑config.py中的CAMERA_INDEX值
```

#### Q5: 二维码无法显示
```cmd
# 确保系统支持图形界面
# 不能在Windows Server Core版本上运行

# 检查显示设置
# 确保有活动的桌面会话

# 测试图形功能
python -c "import tkinter; tkinter.Tk().mainloop()"
```

#### Q7: 摄像头预览窗口不显示
```cmd
# 检查预览配置
# 编辑config.py确保 CAMERA_PREVIEW_ENABLED = True

# 检查摄像头权限
# Windows设置 -> 隐私 -> 摄像头 -> 允许应用访问摄像头

# 测试摄像头预览
python test_camera_preview.py

# 如果预览被意外关闭，重启服务
# 预览窗口关闭后需要重启程序才能重新显示
```

#### Q6: 端口被占用
```cmd
# 查看端口占用
netstat -ano | findstr :5000

# 修改端口配置
# 编辑config.py中的FLASK_CONFIG['port']值

# 配置防火墙
# Windows防火墙 -> 高级设置 -> 入站规则 -> 新建规则
```

## 📊 性能优化

### 系统要求
- **操作系统**: Windows 10/11 或 Windows Server 2016+
- **内存**: 至少2GB RAM
- **磁盘**: 至少1GB可用空间
- **网络**: 支持TCP/IP
- **摄像头**: USB摄像头或内置摄像头（可选）

### 性能调优
```python
# 在config.py中添加性能配置
PERFORMANCE_CONFIG = {
    'max_workers': 4,           # 最大工作线程数
    'db_pool_size': 10,         # 数据库连接池大小
    'qr_process_interval': 3,   # 二维码处理间隔
    'camera_buffer_size': 1,    # 摄像头缓冲区大小
}
```

## 🔒 安全建议

1. **数据库安全**
   - 使用强密码
   - 限制数据库用户权限
   - 定期备份数据

2. **网络安全**
   - 配置防火墙规则
   - 使用HTTPS（生产环境）
   - 限制API访问来源

3. **系统安全**
   - 定期更新Windows系统
   - 使用专用服务账户运行服务
   - 监控系统日志

## 📞 技术支持

如遇到问题，请提供以下信息：
- Windows版本和架构
- Python版本
- 错误日志内容
- 配置文件内容
- 问题复现步骤

这个完整的Windows EXE部署方案确保您能够轻松地将DataTransmission项目部署到任何Windows服务器上，支持多种运行模式和完整的管理功能。
