# Translations template for Sphinx.
# Copyright (C) 2024 ORGANIZATION
# This file is distributed under the same license as the Sphinx project.
# 
# Translators:
# BouRock, 2020
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2013-2016
# <AUTHOR> <EMAIL>, 2011
# <AUTHOR> <EMAIL>, 2020
msgid ""
msgstr ""
"Project-Id-Version: Sphinx\n"
"Report-Msgid-Bugs-To: EMAIL@ADDRESS\n"
"POT-Creation-Date: 2024-04-14 23:27+0000\n"
"PO-Revision-Date: 2013-04-02 08:44+0000\n"
"Last-Translator: BouRock, 2020\n"
"Language-Team: Turkish (http://app.transifex.com/sphinx-doc/sphinx-1/language/tr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.14.0\n"
"Language: tr\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#: sphinx/application.py:157
#, python-format
msgid "Cannot find source directory (%s)"
msgstr "Kaynak dizin bulunamıyor (%s)"

#: sphinx/application.py:161
#, python-format
msgid "Output directory (%s) is not a directory"
msgstr ""

#: sphinx/application.py:165
msgid "Source directory and destination directory cannot be identical"
msgstr "Kaynak dizin ve hedef dizin aynı olamaz"

#: sphinx/application.py:197
#, python-format
msgid "Running Sphinx v%s"
msgstr "Sphinx s%s çalışıyor"

#: sphinx/application.py:219
#, python-format
msgid ""
"This project needs at least Sphinx v%s and therefore cannot be built with "
"this version."
msgstr "Bu proje en az Sphinx s%s gerektirir ve bu nedenle bu sürüm ile oluşturulamaz."

#: sphinx/application.py:235
msgid "making output directory"
msgstr "çıktı dizini yapılıyor"

#: sphinx/application.py:240 sphinx/registry.py:450
#, python-format
msgid "while setting up extension %s:"
msgstr "%s uzantısı ayarlanırken:"

#: sphinx/application.py:246
msgid ""
"'setup' as currently defined in conf.py isn't a Python callable. Please "
"modify its definition to make it a callable function. This is needed for "
"conf.py to behave as a Sphinx extension."
msgstr "Şu anda conf.py dosyasında tanımlanan 'kurulum' çağrılabilir bir Python değil. Lütfen tanımını çağrılabilir bir işlev yapmak için değiştirin. Bunun, Sphinx uzantısı gibi davranması için conf.py dosyasına ihtiyacı vardır."

#: sphinx/application.py:277
#, python-format
msgid "loading translations [%s]... "
msgstr "çeviriler yükleniyor [%s]... "

#: sphinx/application.py:294 sphinx/util/display.py:85
msgid "done"
msgstr "bitti"

#: sphinx/application.py:296
msgid "not available for built-in messages"
msgstr "yerleşik iletiler için kullanılamaz"

#: sphinx/application.py:310
msgid "loading pickled environment"
msgstr "derin temizlenen ortam yükleniyor"

#: sphinx/application.py:318
#, python-format
msgid "failed: %s"
msgstr "başarısız olan: %s"

#: sphinx/application.py:332
msgid "No builder selected, using default: html"
msgstr "Seçilen oluşturucu yok, varsayılan kullanılıyor: html"

#: sphinx/application.py:365
msgid "succeeded"
msgstr "başarılı oldu"

#: sphinx/application.py:366
msgid "finished with problems"
msgstr "sorunlarla tamamlandı"

#: sphinx/application.py:370
#, python-format
msgid "build %s, %s warning (with warnings treated as errors)."
msgstr "yapım %s, %s uyarı (hata olarak kabul edilen uyarılarla)."

#: sphinx/application.py:372
#, python-format
msgid "build %s, %s warnings (with warnings treated as errors)."
msgstr "yapım %s, %s uyarı (hatalar olarak kabul edilen uyarılarla)."

#: sphinx/application.py:375
#, python-format
msgid "build %s, %s warning."
msgstr "yapım %s, %s uyarı."

#: sphinx/application.py:377
#, python-format
msgid "build %s, %s warnings."
msgstr "yapım %s, %s uyarı."

#: sphinx/application.py:381
#, python-format
msgid "build %s."
msgstr "yapım %s."

#: sphinx/application.py:610
#, python-format
msgid "node class %r is already registered, its visitors will be overridden"
msgstr "düğüm sınıfı %r zaten kayıtlı, ziyaretçileri geçersiz kılınacaktır"

#: sphinx/application.py:689
#, python-format
msgid "directive %r is already registered, it will be overridden"
msgstr "yönerge %r zaten kayıtlı, geçersiz kılınacaktır"

#: sphinx/application.py:711 sphinx/application.py:733
#, python-format
msgid "role %r is already registered, it will be overridden"
msgstr "rol %r zaten kayıtlı, geçersiz kılınacaktır"

#: sphinx/application.py:1282
#, python-format
msgid ""
"the %s extension does not declare if it is safe for parallel reading, "
"assuming it isn't - please ask the extension author to check and make it "
"explicit"
msgstr "%s uzantısı paralel okuma için güvenli olup olmadığını bildirmez, olmadığını varsayarak - lütfen uzantıyı hazırlayandan gözden geçirmesini ve açık hale getirmesini isteyin"

#: sphinx/application.py:1286
#, python-format
msgid "the %s extension is not safe for parallel reading"
msgstr "%s uzantısı paralel okuma için güvenli değil"

#: sphinx/application.py:1289
#, python-format
msgid ""
"the %s extension does not declare if it is safe for parallel writing, "
"assuming it isn't - please ask the extension author to check and make it "
"explicit"
msgstr "%s uzantısı paralel yazma için güvenli olup olmadığını bildirmez, olmadığını varsayarak - lütfen uzantıyı hazırlayandan gözden geçirmesini ve açık hale getirmesini isteyin"

#: sphinx/application.py:1293
#, python-format
msgid "the %s extension is not safe for parallel writing"
msgstr "%s uzantısı paralel yazma için güvenli değil"

#: sphinx/application.py:1301 sphinx/application.py:1305
#, python-format
msgid "doing serial %s"
msgstr "%s seri nosu yapılıyor"

#: sphinx/config.py:309
#, python-format
msgid "config directory doesn't contain a conf.py file (%s)"
msgstr "config dizini bir conf.py dosyası içermiyor (%s)"

#: sphinx/config.py:318
msgid ""
"Invalid configuration value found: 'language = None'. Update your "
"configuration to a valid language code. Falling back to 'en' (English)."
msgstr ""

#: sphinx/config.py:341
#, python-format
msgid ""
"cannot override dictionary config setting %r, ignoring (use %r to set "
"individual elements)"
msgstr "sözlük yapılandırma ayarı %r geçersiz kılınamaz, yoksayılıyor (tek tek öğeleri ayarlamak için %r kullanın)"

#: sphinx/config.py:350
#, python-format
msgid "invalid number %r for config value %r, ignoring"
msgstr "geçersiz sayı %r; yapılandırma değeri %r için; yoksayılıyor"

#: sphinx/config.py:355
#, python-format
msgid "cannot override config setting %r with unsupported type, ignoring"
msgstr "desteklenmeyen tür ile yapılandırma ayarı %r geçersiz kılınamaz, yoksayılıyor"

#: sphinx/config.py:378
#, python-format
msgid "unknown config value %r in override, ignoring"
msgstr "geçersiz kılmada bilinmeyen yapılandırma değeri %r, yoksayılıyor"

#: sphinx/config.py:418
#, python-format
msgid "No such config value: %r"
msgstr ""

#: sphinx/config.py:440
#, python-format
msgid "Config value %r already present"
msgstr "Yapılandırma değeri %r zaten mevcut"

#: sphinx/config.py:473
#, python-format
msgid "cannot cache unpickable configuration value: %r"
msgstr ""

#: sphinx/config.py:509
#, python-format
msgid "There is a syntax error in your configuration file: %s\n"
msgstr "Yapılandırma dosyanızda bir sözdizimi hatası var: %s\n"

#: sphinx/config.py:512
msgid ""
"The configuration file (or one of the modules it imports) called sys.exit()"
msgstr "Yapılandırma dosyası (veya içe aktarılan modüllerden biri) sys.exit() olarak adlandırılır"

#: sphinx/config.py:519
#, python-format
msgid ""
"There is a programmable error in your configuration file:\n"
"\n"
"%s"
msgstr "Yapılandırma dosyanızda programlanabilir bir hata var:\n\n%s"

#: sphinx/config.py:540
#, python-format
msgid "Failed to convert %r to a set or tuple"
msgstr ""

#: sphinx/config.py:565
#, python-format
msgid ""
"The config value `source_suffix' expects a string, list of strings, or "
"dictionary. But `%r' is given."
msgstr "'source_suffix' yapılandırma değeri bir dizgi, dizgiler listesi ya da sözlük bekler. Ama '%r' verilir."

#: sphinx/config.py:585
#, python-format
msgid "Section %s"
msgstr "Bölüm %s"

#: sphinx/config.py:586
#, python-format
msgid "Fig. %s"
msgstr "Şekil %s"

#: sphinx/config.py:587
#, python-format
msgid "Table %s"
msgstr "Tablo %s"

#: sphinx/config.py:588
#, python-format
msgid "Listing %s"
msgstr "Listeleme %s"

#: sphinx/config.py:663
msgid ""
"The config value `{name}` has to be a one of {candidates}, but `{current}` "
"is given."
msgstr "`{name}` yapılandırma değeri, {candidates} geğerlrinden biri olmak zorundadır, ancak `{current}` değeridir."

#: sphinx/config.py:687
msgid ""
"The config value `{name}' has type `{current.__name__}'; expected "
"{permitted}."
msgstr "`{name}' yapılandırma değeri `{current.__name__}' türüne sahip; beklenen {permitted}."

#: sphinx/config.py:700
msgid ""
"The config value `{name}' has type `{current.__name__}', defaults to "
"`{default.__name__}'."
msgstr "`{name}' yapılandırma değeri `{current.__name__}' türüne sahip, vassayılanları `{default.__name__}'."

#: sphinx/config.py:711
#, python-format
msgid "primary_domain %r not found, ignored."
msgstr "primary_domain %r bulunamadı, yoksayıldı."

#: sphinx/config.py:723
msgid ""
"Since v2.0, Sphinx uses \"index\" as root_doc by default. Please add "
"\"root_doc = 'contents'\" to your conf.py."
msgstr ""

#: sphinx/events.py:64
#, python-format
msgid "Event %r already present"
msgstr "Olay %r zaten mevcut"

#: sphinx/events.py:70
#, python-format
msgid "Unknown event name: %s"
msgstr "Bilinmeyen olay adı: %s"

#: sphinx/events.py:109
#, python-format
msgid "Handler %r for event %r threw an exception"
msgstr ""

#: sphinx/extension.py:55
#, python-format
msgid ""
"The %s extension is required by needs_extensions settings, but it is not "
"loaded."
msgstr "%s uzantısı needs_extensions ayarları tarafından gereklidir, ancak yüklü değildir."

#: sphinx/extension.py:76
#, python-format
msgid ""
"This project needs the extension %s at least in version %s and therefore "
"cannot be built with the loaded version (%s)."
msgstr "Bu projenin %s uzantısına ve en az %s sürümüne ihtiyacı vardır ve bu nedenle yüklenen sürümle oluşturulamaz (%s)."

#: sphinx/highlighting.py:155
#, python-format
msgid "Pygments lexer name %r is not known"
msgstr "Pygments lexer adı %r bilinmiyor"

#: sphinx/highlighting.py:189
#, python-format
msgid ""
"Lexing literal_block %r as \"%s\" resulted in an error at token: %r. "
"Retrying in relaxed mode."
msgstr ""

#: sphinx/project.py:66
#, python-format
msgid ""
"multiple files found for the document \"%s\": %r\n"
"Use %r for the build."
msgstr ""

#: sphinx/project.py:81
#, python-format
msgid "Ignored unreadable document %r."
msgstr ""

#: sphinx/registry.py:142
#, python-format
msgid "Builder class %s has no \"name\" attribute"
msgstr "Oluşturucu sınıfı %s \"ad\" özniteliğine sahip değil"

#: sphinx/registry.py:144
#, python-format
msgid "Builder %r already exists (in module %s)"
msgstr "Oluşturucu %r zaten mevcut (%s modülünde)"

#: sphinx/registry.py:157
#, python-format
msgid "Builder name %s not registered or available through entry point"
msgstr "Oluşturucu adı %s kayıtlı veya giriş noktası aracılığıyla kullanılabilir değil"

#: sphinx/registry.py:164
#, python-format
msgid "Builder name %s not registered"
msgstr "Oluşturucu adı %s kayıtlı değil"

#: sphinx/registry.py:171
#, python-format
msgid "domain %s already registered"
msgstr "etki alanı %s zaten kayıtlı"

#: sphinx/registry.py:194 sphinx/registry.py:207 sphinx/registry.py:218
#, python-format
msgid "domain %s not yet registered"
msgstr "etki alanı %s henüz kayıtlı değil"

#: sphinx/registry.py:198
#, python-format
msgid "The %r directive is already registered to domain %s"
msgstr "%r yönergesi zaten %s etki alanına kayıtlı"

#: sphinx/registry.py:210
#, python-format
msgid "The %r role is already registered to domain %s"
msgstr "%r rolü zaten %s etki alanına kayıtlı"

#: sphinx/registry.py:221
#, python-format
msgid "The %r index is already registered to domain %s"
msgstr "%r dizini zaten %s etki alanına kayıtlı"

#: sphinx/registry.py:252
#, python-format
msgid "The %r object_type is already registered"
msgstr "%r object_type zaten kayıtlı"

#: sphinx/registry.py:278
#, python-format
msgid "The %r crossref_type is already registered"
msgstr "%r crossref_type zaten kayıtlı"

#: sphinx/registry.py:285
#, python-format
msgid "source_suffix %r is already registered"
msgstr "source_suffix %r zaten kayıtlı"

#: sphinx/registry.py:294
#, python-format
msgid "source_parser for %r is already registered"
msgstr "%r için source_parser zaten kayıtlı"

#: sphinx/registry.py:302
#, python-format
msgid "Source parser for %s not registered"
msgstr "%s için kaynak ayrıştırıcı kayıtlı değil"

#: sphinx/registry.py:318
#, python-format
msgid "Translator for %r already exists"
msgstr "%r için çevirmen zaten mevcut"

#: sphinx/registry.py:334
#, python-format
msgid "kwargs for add_node() must be a (visit, depart) function tuple: %r=%r"
msgstr "add_node() için kwargs bir (visit, depart) tanımlama grubu işlevi olmak zorundadır: %r=%r"

#: sphinx/registry.py:417
#, python-format
msgid "enumerable_node %r already registered"
msgstr "enumerable_node %r zaten kayıtlı"

#: sphinx/registry.py:429
#, python-format
msgid "math renderer %s is already registered"
msgstr ""

#: sphinx/registry.py:444
#, python-format
msgid ""
"the extension %r was already merged with Sphinx since version %s; this "
"extension is ignored."
msgstr "%r uzantısı zaten %s sürümünden bu yana Sphinx ile birleştirildi; bu uzantı yoksayıldı."

#: sphinx/registry.py:455
msgid "Original exception:\n"
msgstr "Özgün özel durumu:\n"

#: sphinx/registry.py:456
#, python-format
msgid "Could not import extension %s"
msgstr " %s uzantısı içe aktarılamadı"

#: sphinx/registry.py:461
#, python-format
msgid ""
"extension %r has no setup() function; is it really a Sphinx extension "
"module?"
msgstr "%r uzantısı setup() işlevine sahip değil; bu gerçekten bir Sphinx uzantısı modülü mü?"

#: sphinx/registry.py:470
#, python-format
msgid ""
"The %s extension used by this project needs at least Sphinx v%s; it "
"therefore cannot be built with this version."
msgstr "Bu proje tarafından kullanılan %s uzantısının en az Sphinx s%s sürümüne ihtiyacı var; bu nedenle bu sürümle oluşturulamaz."

#: sphinx/registry.py:478
#, python-format
msgid ""
"extension %r returned an unsupported object from its setup() function; it "
"should return None or a metadata dictionary"
msgstr "%r uzantısı, setup() işlevinden desteklenmeyen bir nesne döndürdü; Hiçbir şey veya üstveri sözlüğü döndürmeli"

#: sphinx/roles.py:201
#, python-format
msgid "Python Enhancement Proposals; PEP %s"
msgstr "Python Geliştirme Önerileri; PEP %s"

#: sphinx/roles.py:222
#, python-format
msgid "invalid PEP number %s"
msgstr ""

#: sphinx/roles.py:257
#, python-format
msgid "invalid RFC number %s"
msgstr ""

#: sphinx/theming.py:125
#, python-format
msgid "setting %s.%s occurs in none of the searched theme configs"
msgstr "%s.%s ayarı, aranan tema yapılandırmalarının hiçbirinde meydana gelmiyor"

#: sphinx/theming.py:140
#, python-format
msgid "unsupported theme option %r given"
msgstr "desteklenmeyen tema seçeneği %r verildi"

#: sphinx/theming.py:206
#, python-format
msgid "file %r on theme path is not a valid zipfile or contains no theme"
msgstr "tema yolundaki %r dosyası geçerli bir zip dosyası değil ya da hiç tema içermiyor"

#: sphinx/theming.py:226
#, python-format
msgid "no theme named %r found (missing theme.toml?)"
msgstr ""

#: sphinx/theming.py:259
#, python-format
msgid "The %r theme has circular inheritance"
msgstr ""

#: sphinx/theming.py:262
#, python-format
msgid ""
"The %r theme inherits from %r, which is not a loaded theme. Loaded themes "
"are: %s"
msgstr ""

#: sphinx/theming.py:269
#, python-format
msgid "The %r theme has too many ancestors"
msgstr ""

#: sphinx/theming.py:295
#, python-format
msgid "no theme configuration file found in %r"
msgstr ""

#: sphinx/theming.py:323 sphinx/theming.py:374
#, python-format
msgid "theme %r doesn't have the \"theme\" table"
msgstr ""

#: sphinx/theming.py:327
#, python-format
msgid "The %r theme \"[theme]\" table is not a table"
msgstr ""

#: sphinx/theming.py:331 sphinx/theming.py:377
#, python-format
msgid "The %r theme must define the \"theme.inherit\" setting"
msgstr ""

#: sphinx/theming.py:335
#, python-format
msgid "The %r theme \"[options]\" table is not a table"
msgstr ""

#: sphinx/theming.py:353
#, python-format
msgid "The \"theme.pygments_style\" setting must be a table. Hint: \"%s\""
msgstr ""

#: sphinx/builders/__init__.py:183
#, python-format
msgid "a suitable image for %s builder not found: %s (%s)"
msgstr "%s oluşturucu için uygun bir resim bulunamadı: %s (%s)"

#: sphinx/builders/__init__.py:187
#, python-format
msgid "a suitable image for %s builder not found: %s"
msgstr "%s oluşturucu için uygun bir resim bulunamadı: %s"

#: sphinx/builders/__init__.py:207
msgid "building [mo]: "
msgstr "[mo] oluşturuluyor: "

#: sphinx/builders/__init__.py:208 sphinx/builders/__init__.py:574
#: sphinx/builders/__init__.py:601
msgid "writing output... "
msgstr "çıktı yazılıyor..."

#: sphinx/builders/__init__.py:217
#, python-format
msgid "all of %d po files"
msgstr "%d po dosyasının tümü"

#: sphinx/builders/__init__.py:235
#, python-format
msgid "targets for %d po files that are specified"
msgstr "belirtilen %d po dosyası için hedefler"

#: sphinx/builders/__init__.py:243
#, python-format
msgid "targets for %d po files that are out of date"
msgstr "güncel olmayan %d po dosyası için hedefler"

#: sphinx/builders/__init__.py:252
msgid "all source files"
msgstr "tüm kaynak dosyaları"

#: sphinx/builders/__init__.py:262
#, python-format
msgid "file %r given on command line does not exist, "
msgstr ""

#: sphinx/builders/__init__.py:267
#, python-format
msgid ""
"file %r given on command line is not under the source directory, ignoring"
msgstr "komut satırında verilen %r dosyası kaynak dizinin altında değil, yoksayılıyor"

#: sphinx/builders/__init__.py:273
#, python-format
msgid "file %r given on command line is not a valid document, ignoring"
msgstr ""

#: sphinx/builders/__init__.py:282
#, python-format
msgid "%d source files given on command line"
msgstr "komut satırında verilen %d kaynak dosyası"

#: sphinx/builders/__init__.py:294
#, python-format
msgid "targets for %d source files that are out of date"
msgstr "güncel olmayan %d kaynak dosyası için hedefler"

#: sphinx/builders/__init__.py:309 sphinx/builders/gettext.py:243
#, python-format
msgid "building [%s]: "
msgstr "[%s] oluşturuluyor:"

#: sphinx/builders/__init__.py:316
msgid "looking for now-outdated files... "
msgstr "şimdi güncel olmayan dosyalar aranıyor..."

#: sphinx/builders/__init__.py:320
#, python-format
msgid "%d found"
msgstr "%d tane bulundu"

#: sphinx/builders/__init__.py:322
msgid "none found"
msgstr "hiç bulunamadı"

#: sphinx/builders/__init__.py:327
msgid "pickling environment"
msgstr "ortam derin temizleniyor"

#: sphinx/builders/__init__.py:333
msgid "checking consistency"
msgstr "tutarlılık denetleniyor"

#: sphinx/builders/__init__.py:337
msgid "no targets are out of date."
msgstr "hiçbir hedef güncel değil."

#: sphinx/builders/__init__.py:376
msgid "updating environment: "
msgstr "güncellenen ortam:"

#: sphinx/builders/__init__.py:397
#, python-format
msgid "%s added, %s changed, %s removed"
msgstr "%s eklendi, %s değiştirildi, %s kaldırıldı"

#: sphinx/builders/__init__.py:435 sphinx/builders/__init__.py:447
msgid "reading sources... "
msgstr "kaynaklar okunuyor..."

#: sphinx/builders/__init__.py:549
#, python-format
msgid "docnames to write: %s"
msgstr "yazmak için belge adları: %s"

#: sphinx/builders/__init__.py:558 sphinx/builders/singlehtml.py:157
msgid "preparing documents"
msgstr "belgeler hazırlanıyor"

#: sphinx/builders/__init__.py:561
msgid "copying assets"
msgstr ""

#: sphinx/builders/_epub_base.py:215
#, python-format
msgid "duplicated ToC entry found: %s"
msgstr "kopyalanmış ToC girişi bulundu: %s"

#: sphinx/builders/_epub_base.py:404 sphinx/builders/html/__init__.py:758
#: sphinx/builders/latex/__init__.py:432 sphinx/builders/texinfo.py:187
msgid "copying images... "
msgstr "resimler kopyalanıyor..."

#: sphinx/builders/_epub_base.py:411
#, python-format
msgid "cannot read image file %r: copying it instead"
msgstr "resim dosyası %r okunamıyor: bunun yerine kopyalanıyor"

#: sphinx/builders/_epub_base.py:417 sphinx/builders/html/__init__.py:766
#: sphinx/builders/latex/__init__.py:440 sphinx/builders/texinfo.py:197
#, python-format
msgid "cannot copy image file %r: %s"
msgstr "resim dosyası %r kopyalanamıyor: %s"

#: sphinx/builders/_epub_base.py:434
#, python-format
msgid "cannot write image file %r: %s"
msgstr "resim dosyası %r yazılamıyor: %s"

#: sphinx/builders/_epub_base.py:444
msgid "Pillow not found - copying image files"
msgstr "Yastık bulunamadı - resim dosyaları kopyalanıyor"

#: sphinx/builders/_epub_base.py:470
msgid "writing mimetype file..."
msgstr ""

#: sphinx/builders/_epub_base.py:475
msgid "writing META-INF/container.xml file..."
msgstr ""

#: sphinx/builders/_epub_base.py:508
msgid "writing content.opf file..."
msgstr ""

#: sphinx/builders/_epub_base.py:539
#, python-format
msgid "unknown mimetype for %s, ignoring"
msgstr "%s için bilinmeyen mime türü, yoksayılıyor"

#: sphinx/builders/_epub_base.py:686
msgid "writing toc.ncx file..."
msgstr ""

#: sphinx/builders/_epub_base.py:711
#, python-format
msgid "writing %s file..."
msgstr "%s dosyası yazılıyor..."

#: sphinx/builders/changes.py:32
#, python-format
msgid "The overview file is in %(outdir)s."
msgstr "Genel bakış dosyası %(outdir)s içinde."

#: sphinx/builders/changes.py:59
#, python-format
msgid "no changes in version %s."
msgstr "%s sürümünde değişiklik yok."

#: sphinx/builders/changes.py:61
msgid "writing summary file..."
msgstr "özet dosyası yazılıyor..."

#: sphinx/builders/changes.py:76
msgid "Builtins"
msgstr "Yerleşikler"

#: sphinx/builders/changes.py:78
msgid "Module level"
msgstr "Modül seviyesi"

#: sphinx/builders/changes.py:123
msgid "copying source files..."
msgstr "kaynak dosyalar kopyalanıyor..."

#: sphinx/builders/changes.py:130
#, python-format
msgid "could not read %r for changelog creation"
msgstr "değişiklikler günlüğü oluşturma için %r okunamadı"

#: sphinx/builders/dummy.py:19
msgid "The dummy builder generates no files."
msgstr "Taklit oluşturucu hiçbir dosya oluşturmaz."

#: sphinx/builders/epub3.py:81
#, python-format
msgid "The ePub file is in %(outdir)s."
msgstr "ePub dosyası %(outdir)s içinde."

#: sphinx/builders/epub3.py:185
msgid "writing nav.xhtml file..."
msgstr ""

#: sphinx/builders/epub3.py:211
msgid "conf value \"epub_language\" (or \"language\") should not be empty for EPUB3"
msgstr "yapılandırma değeri \"epub_language\" (veya \"language\"), EPUB3 için boş olmamalıdır"

#: sphinx/builders/epub3.py:215
msgid "conf value \"epub_uid\" should be XML NAME for EPUB3"
msgstr "yapılandırma değeri \"epub_uid\", EPUB3 için XML NAME olmalıdır"

#: sphinx/builders/epub3.py:218
msgid "conf value \"epub_title\" (or \"html_title\") should not be empty for EPUB3"
msgstr "yapılandırma değeri \"epub_title\" (veya \"html_title\"), EPUB3 için boş olmamalıdır"

#: sphinx/builders/epub3.py:222
msgid "conf value \"epub_author\" should not be empty for EPUB3"
msgstr "yapılandırma değeri \"epub_author\", EPUB3 için boş olmamalıdır"

#: sphinx/builders/epub3.py:225
msgid "conf value \"epub_contributor\" should not be empty for EPUB3"
msgstr "yapılandırma değeri \"epub_contributor\", EPUB3 için boş olmamalıdır"

#: sphinx/builders/epub3.py:228
msgid "conf value \"epub_description\" should not be empty for EPUB3"
msgstr "yapılandırma değeri \"epub_description\", EPUB3 için boş olmamalıdır"

#: sphinx/builders/epub3.py:231
msgid "conf value \"epub_publisher\" should not be empty for EPUB3"
msgstr "yapılandırma değeri \"epub_publisher\", EPUB3 için boş olmamalıdır"

#: sphinx/builders/epub3.py:234
msgid "conf value \"epub_copyright\" (or \"copyright\")should not be empty for EPUB3"
msgstr "yapılandırma değeri \"epub_copyright\" (veya \"copyright\"), EPUB3 için boş olmamalıdır"

#: sphinx/builders/epub3.py:238
msgid "conf value \"epub_identifier\" should not be empty for EPUB3"
msgstr "yapılandırma değeri \"epub_identifier\", EPUB3 için boş olmamalıdır"

#: sphinx/builders/epub3.py:241
msgid "conf value \"version\" should not be empty for EPUB3"
msgstr "yapılandırma değeri \"version\", EPUB3 için boş olmamalıdır"

#: sphinx/builders/epub3.py:255 sphinx/builders/html/__init__.py:1187
#, python-format
msgid "invalid css_file: %r, ignored"
msgstr "geçersiz css_file: %r, yoksayıldı"

#: sphinx/builders/gettext.py:222
#, python-format
msgid "The message catalogs are in %(outdir)s."
msgstr "İleti katalogları %(outdir)s içinde."

#: sphinx/builders/gettext.py:244
#, python-format
msgid "targets for %d template files"
msgstr "%d şablon dosyası için hedefler"

#: sphinx/builders/gettext.py:248
msgid "reading templates... "
msgstr "şablonlar okunuyor..."

#: sphinx/builders/gettext.py:282
msgid "writing message catalogs... "
msgstr "ileti katalogları yazılıyor..."

#: sphinx/builders/linkcheck.py:59
#, python-format
msgid "Look for any errors in the above output or in %(outdir)s/output.txt"
msgstr "Yukarıdaki çıktıda veya %(outdir)s/output.txt içinde herhangi bir hata arayın"

#: sphinx/builders/linkcheck.py:137
#, python-format
msgid "broken link: %s (%s)"
msgstr "bozuk bağlantı: %s (%s)"

#: sphinx/builders/linkcheck.py:660
#, python-format
msgid "Failed to compile regex in linkcheck_allowed_redirects: %r %s"
msgstr ""

#: sphinx/builders/manpage.py:37
#, python-format
msgid "The manual pages are in %(outdir)s."
msgstr "Rehber sayfaları %(outdir)s içinde."

#: sphinx/builders/manpage.py:44
msgid "no \"man_pages\" config value found; no manual pages will be written"
msgstr "hiçbir \"man_pages\" yapılandırma değeri bulunamadı; hiçbir rehber sayfası yazılmayacaktır"

#: sphinx/builders/latex/__init__.py:314 sphinx/builders/manpage.py:53
#: sphinx/builders/singlehtml.py:165 sphinx/builders/texinfo.py:112
msgid "writing"
msgstr "yazılıyor"

#: sphinx/builders/manpage.py:68
#, python-format
msgid "\"man_pages\" config value references unknown document %s"
msgstr "\"man_pages\" yapılandırma değeri bilinmeyen %s belgesine başvurur"

#: sphinx/builders/singlehtml.py:34
#, python-format
msgid "The HTML page is in %(outdir)s."
msgstr "HTML sayfası %(outdir)s içinde."

#: sphinx/builders/singlehtml.py:160
msgid "assembling single document"
msgstr "tek bir belgede toplanıyor"

#: sphinx/builders/singlehtml.py:178
msgid "writing additional files"
msgstr "ilave dosyalar yazılıyor"

#: sphinx/builders/texinfo.py:48
#, python-format
msgid "The Texinfo files are in %(outdir)s."
msgstr "Texinfo dosyaları %(outdir)s içinde."

#: sphinx/builders/texinfo.py:50
msgid ""
"\n"
"Run 'make' in that directory to run these through makeinfo\n"
"(use 'make info' here to do that automatically)."
msgstr "\nMakeinfo aracılığıyla bunları çalıştırmak için bu dizinde 'make' komutunu çalıştırın\n(bunu otomatik olarak yapmak için burada 'make info' komutunu kullanın)."

#: sphinx/builders/texinfo.py:77
msgid "no \"texinfo_documents\" config value found; no documents will be written"
msgstr "hiçbir \"texinfo_documents\" yapılandırma değeri bulunamadı; hiçbir belge yazılmayacaktır"

#: sphinx/builders/texinfo.py:85
#, python-format
msgid "\"texinfo_documents\" config value references unknown document %s"
msgstr "\"texinfo_documents\" yapılandırma değeri bilinmeyen %s belgesine başvurur"

#: sphinx/builders/latex/__init__.py:296 sphinx/builders/texinfo.py:108
#, python-format
msgid "processing %s"
msgstr "%s işleniyor"

#: sphinx/builders/latex/__init__.py:369 sphinx/builders/texinfo.py:161
msgid "resolving references..."
msgstr "başvurular çözümleniyor..."

#: sphinx/builders/latex/__init__.py:380 sphinx/builders/texinfo.py:171
msgid " (in "
msgstr " (şurada:  "

#: sphinx/builders/texinfo.py:202
msgid "copying Texinfo support files"
msgstr "Texinfo destek dosyaları kopyalanıyor..."

#: sphinx/builders/texinfo.py:206
#, python-format
msgid "error writing file Makefile: %s"
msgstr "Makefile dosyası yazılırken hata oldu: %s"

#: sphinx/builders/text.py:30
#, python-format
msgid "The text files are in %(outdir)s."
msgstr "Metin dosyaları %(outdir)s içinde."

#: sphinx/builders/html/__init__.py:1138 sphinx/builders/text.py:77
#: sphinx/builders/xml.py:96
#, python-format
msgid "error writing file %s: %s"
msgstr "%s dosyası yazılırken hata oldu: %s"

#: sphinx/builders/xml.py:36
#, python-format
msgid "The XML files are in %(outdir)s."
msgstr "XML dosyaları %(outdir)s içinde."

#: sphinx/builders/xml.py:109
#, python-format
msgid "The pseudo-XML files are in %(outdir)s."
msgstr "Pseudo-XML dosyaları %(outdir)s içinde."

#: sphinx/builders/html/__init__.py:130
#, python-format
msgid "build info file is broken: %r"
msgstr "oluşturma bilgisi dosyası bozuldu: %r"

#: sphinx/builders/html/__init__.py:168
#, python-format
msgid "The HTML pages are in %(outdir)s."
msgstr "HTML sayfaları %(outdir)s içinde."

#: sphinx/builders/html/__init__.py:394
#, python-format
msgid "Failed to read build info file: %r"
msgstr "oluşturma bilgisi dosyasını okuma başarısız: %r"

#: sphinx/builders/html/__init__.py:487 sphinx/builders/latex/__init__.py:189
#: sphinx/transforms/__init__.py:119 sphinx/writers/manpage.py:101
#: sphinx/writers/texinfo.py:227
#, python-format
msgid "%b %d, %Y"
msgstr "%d %b %Y"

#: sphinx/builders/html/__init__.py:506 sphinx/themes/basic/defindex.html:30
msgid "General Index"
msgstr "Genel Dizin"

#: sphinx/builders/html/__init__.py:506
msgid "index"
msgstr "dizin"

#: sphinx/builders/html/__init__.py:579
msgid "next"
msgstr "sonraki"

#: sphinx/builders/html/__init__.py:588
msgid "previous"
msgstr "önceki"

#: sphinx/builders/html/__init__.py:684
msgid "generating indices"
msgstr "dizinler oluşturuluyor"

#: sphinx/builders/html/__init__.py:699
msgid "writing additional pages"
msgstr "ilave sayfalar yazılıyor"

#: sphinx/builders/html/__init__.py:776
msgid "copying downloadable files... "
msgstr "indirilebilir dosyalar kopyalanıyor..."

#: sphinx/builders/html/__init__.py:784
#, python-format
msgid "cannot copy downloadable file %r: %s"
msgstr "indirilebilir dosya %r kopyalanamıyor: %s"

#: sphinx/builders/html/__init__.py:817 sphinx/builders/html/__init__.py:829
#, python-format
msgid "Failed to copy a file in html_static_file: %s: %r"
msgstr ""

#: sphinx/builders/html/__init__.py:850
msgid "copying static files"
msgstr ""

#: sphinx/builders/html/__init__.py:866
#, python-format
msgid "cannot copy static file %r"
msgstr "sabit dosya %r kopyalanamıyor"

#: sphinx/builders/html/__init__.py:871
msgid "copying extra files"
msgstr "fazladan dosyalar kopyalanıyor"

#: sphinx/builders/html/__init__.py:877
#, python-format
msgid "cannot copy extra file %r"
msgstr "fazladan dosya %r kopyalanamıyor..."

#: sphinx/builders/html/__init__.py:884
#, python-format
msgid "Failed to write build info file: %r"
msgstr "oluşturma bilgisi dosyasını yazma başarısız: %r"

#: sphinx/builders/html/__init__.py:933
msgid ""
"search index couldn't be loaded, but not all documents will be built: the "
"index will be incomplete."
msgstr "arama dizini yüklenemedi, ancak tüm belgeler oluşturulmayacaktır: dizin tamamlanmamış olacaktır."

#: sphinx/builders/html/__init__.py:978
#, python-format
msgid "page %s matches two patterns in html_sidebars: %r and %r"
msgstr "sayfa %s html_sidebars içinde iki şekille eşleşiyor: %r ve %r"

#: sphinx/builders/html/__init__.py:1121
#, python-format
msgid ""
"a Unicode error occurred when rendering the page %s. Please make sure all "
"config values that contain non-ASCII content are Unicode strings."
msgstr "%s sayfasını işlerken bir Evrensel kod hatası meydana geldi. Lütfen ASCII olmayan içerik içeren tüm yapılandırma değerlerinin Evrensel kod dizgiler olduğundan emin olun."

#: sphinx/builders/html/__init__.py:1126
#, python-format
msgid ""
"An error happened in rendering the page %s.\n"
"Reason: %r"
msgstr "%s sayfasını işlerken bir hata oldu.\nSebep: %r"

#: sphinx/builders/html/__init__.py:1154
msgid "dumping object inventory"
msgstr "nesne envanteri dökümleniyor"

#: sphinx/builders/html/__init__.py:1162
#, python-format
msgid "dumping search index in %s"
msgstr "%s içinde arama dizini dökümleniyor"

#: sphinx/builders/html/__init__.py:1210
#, python-format
msgid "invalid js_file: %r, ignored"
msgstr "geçersiz js_file: %r, yoksayıldı"

#: sphinx/builders/html/__init__.py:1238
msgid "Many math_renderers are registered. But no math_renderer is selected."
msgstr "Birçok math_renderers kayıtlı. Ama hiç math_renderer seçilmedi."

#: sphinx/builders/html/__init__.py:1241
#, python-format
msgid "Unknown math_renderer %r is given."
msgstr "Bilinmeyen math_renderer %r verildi."

#: sphinx/builders/html/__init__.py:1249
#, python-format
msgid "html_extra_path entry %r does not exist"
msgstr "html_extra_path girişi %r mevcut değil"

#: sphinx/builders/html/__init__.py:1253
#, python-format
msgid "html_extra_path entry %r is placed inside outdir"
msgstr "html_extra_path girişi %r, çıktı dizini içine yerleştirildi"

#: sphinx/builders/html/__init__.py:1262
#, python-format
msgid "html_static_path entry %r does not exist"
msgstr "html_static_path girişi %r mevcut değil"

#: sphinx/builders/html/__init__.py:1266
#, python-format
msgid "html_static_path entry %r is placed inside outdir"
msgstr "html_static_path girişi %r, çıktı dizini içine yerleştirildi"

#: sphinx/builders/html/__init__.py:1275 sphinx/builders/latex/__init__.py:444
#, python-format
msgid "logo file %r does not exist"
msgstr "logo dosyası %r mevcut değil"

#: sphinx/builders/html/__init__.py:1284
#, python-format
msgid "favicon file %r does not exist"
msgstr "favicon dosyası %r mevcut değil"

#: sphinx/builders/html/__init__.py:1291
msgid ""
"HTML 4 is no longer supported by Sphinx. (\"html4_writer=True\" detected in "
"configuration options)"
msgstr ""

#: sphinx/builders/html/__init__.py:1306
#, python-format
msgid "%s %s documentation"
msgstr "%s %s belgelendirmesi"

#: sphinx/builders/latex/__init__.py:115
#, python-format
msgid "The LaTeX files are in %(outdir)s."
msgstr "LaTeX dosyaları %(outdir)s içinde."

#: sphinx/builders/latex/__init__.py:117
msgid ""
"\n"
"Run 'make' in that directory to run these through (pdf)latex\n"
"(use `make latexpdf' here to do that automatically)."
msgstr "\n(pdf)latex aracılığıyla bunları çalıştırmak için bu dizinde 'make' komutunu çalıştırın\n(bunu otomatik olarak yapmak için burada 'make latexpdf' komutunu kullanın)."

#: sphinx/builders/latex/__init__.py:152
msgid "no \"latex_documents\" config value found; no documents will be written"
msgstr "hiçbir \"latex_documents\" yapılandırma değeri bulunamadı; hiçbir belge yazılmayacaktır"

#: sphinx/builders/latex/__init__.py:160
#, python-format
msgid "\"latex_documents\" config value references unknown document %s"
msgstr "\"latex_documents\" yapılandırma değeri bilinmeyen %s belgesine başvurur"

#: sphinx/builders/latex/__init__.py:196 sphinx/domains/std/__init__.py:559
#: sphinx/domains/std/__init__.py:571 sphinx/templates/latex/latex.tex_t:106
#: sphinx/themes/basic/genindex-single.html:30
#: sphinx/themes/basic/genindex-single.html:55
#: sphinx/themes/basic/genindex-split.html:11
#: sphinx/themes/basic/genindex-split.html:14
#: sphinx/themes/basic/genindex.html:11 sphinx/themes/basic/genindex.html:34
#: sphinx/themes/basic/genindex.html:67 sphinx/themes/basic/layout.html:138
#: sphinx/writers/texinfo.py:497
msgid "Index"
msgstr "Dizin"

#: sphinx/builders/latex/__init__.py:199 sphinx/templates/latex/latex.tex_t:91
msgid "Release"
msgstr "Yayım"

#: sphinx/builders/latex/__init__.py:213 sphinx/writers/latex.py:370
#, python-format
msgid "no Babel option known for language %r"
msgstr "%r dili için bilinen hiç Babel seçeneği yok"

#: sphinx/builders/latex/__init__.py:394
msgid "copying TeX support files"
msgstr "TeX destek dosyaları kopyalanıyor"

#: sphinx/builders/latex/__init__.py:410
msgid "copying TeX support files..."
msgstr "TeX destek dosyaları kopyalanıyor..."

#: sphinx/builders/latex/__init__.py:423
msgid "copying additional files"
msgstr "ilave dosyalar kopyalanıyor"

#: sphinx/builders/latex/__init__.py:466
#, python-format
msgid "Unknown configure key: latex_elements[%r], ignored."
msgstr "Bilinmeyen yapılandırma anahtarı: latex_elements[%r], yoksayıldı."

#: sphinx/builders/latex/__init__.py:474
#, python-format
msgid "Unknown theme option: latex_theme_options[%r], ignored."
msgstr ""

#: sphinx/builders/latex/theming.py:87
#, python-format
msgid "%r doesn't have \"theme\" setting"
msgstr "%r, \"theme\" ayarına sahip değil"

#: sphinx/builders/latex/theming.py:90
#, python-format
msgid "%r doesn't have \"%s\" setting"
msgstr "%r, \"%s\" ayarına sahip değil"

#: sphinx/builders/latex/transforms.py:120
msgid "Failed to get a docname!"
msgstr ""

#: sphinx/builders/latex/transforms.py:121
msgid "Failed to get a docname for source {source!r}!"
msgstr ""

#: sphinx/builders/latex/transforms.py:482
#, python-format
msgid "No footnote was found for given reference node %r"
msgstr ""

#: sphinx/cmd/build.py:46
msgid "Exception occurred while building, starting debugger:"
msgstr "Oluşturulurken meydana gelen özel durum, hata ayıklayıcı başlatılıyor:"

#: sphinx/cmd/build.py:61
msgid "Interrupted!"
msgstr "Yarıda kesildi!"

#: sphinx/cmd/build.py:63
msgid "reST markup error:"
msgstr "reST biçimlendirme hatası:"

#: sphinx/cmd/build.py:69
msgid "Encoding error:"
msgstr "Kodlama hatası:"

#: sphinx/cmd/build.py:72 sphinx/cmd/build.py:87
#, python-format
msgid ""
"The full traceback has been saved in %s, if you want to report the issue to "
"the developers."
msgstr "Sorunu geliştiricilere bildirmek istiyorsanız, tam geri izleme %s içine kaydedildi."

#: sphinx/cmd/build.py:76
msgid "Recursion error:"
msgstr "Tekrarlama hatası:"

#: sphinx/cmd/build.py:79
msgid ""
"This can happen with very large or deeply nested source files. You can "
"carefully increase the default Python recursion limit of 1000 in conf.py "
"with e.g.:"
msgstr ""

#: sphinx/cmd/build.py:84
msgid "Exception occurred:"
msgstr "Özel durum meydana geldi:"

#: sphinx/cmd/build.py:90
msgid ""
"Please also report this if it was a user error, so that a better error "
"message can be provided next time."
msgstr "Bir dahaki sefere daha iyi bir hata iletisi sağlanabilmesi için lütfen bunu bir kullanıcı hatasıysa da bildirin."

#: sphinx/cmd/build.py:93
msgid ""
"A bug report can be filed in the tracker at <https://github.com/sphinx-"
"doc/sphinx/issues>. Thanks!"
msgstr "Bir hata raporu <https://github.com/sphinx-doc/sphinx/issues> adresindeki izleyicide dosyalanabilir. Teşekkürler!"

#: sphinx/cmd/build.py:109
msgid "job number should be a positive number"
msgstr "iş numarası pozitif bir sayı olmalıdır"

#: sphinx/cmd/build.py:117 sphinx/cmd/quickstart.py:474
#: sphinx/ext/apidoc.py:317 sphinx/ext/autosummary/generate.py:689
msgid "For more information, visit <https://www.sphinx-doc.org/>."
msgstr ""

#: sphinx/cmd/build.py:118
msgid ""
"\n"
"Generate documentation from source files.\n"
"\n"
"sphinx-build generates documentation from the files in SOURCEDIR and places it\n"
"in OUTPUTDIR. It looks for 'conf.py' in SOURCEDIR for the configuration\n"
"settings. The 'sphinx-quickstart' tool may be used to generate template files,\n"
"including 'conf.py'\n"
"\n"
"sphinx-build can create documentation in different formats. A format is\n"
"selected by specifying the builder name on the command line; it defaults to\n"
"HTML. Builders can also perform other tasks related to documentation\n"
"processing.\n"
"\n"
"By default, everything that is outdated is built. Output only for selected\n"
"files can be built by specifying individual filenames.\n"
msgstr ""

#: sphinx/cmd/build.py:139
msgid "path to documentation source files"
msgstr "belgelendirme kaynak dosyaları için yol"

#: sphinx/cmd/build.py:141
msgid "path to output directory"
msgstr "çıktı dizini için yol"

#: sphinx/cmd/build.py:143
msgid ""
"(optional) a list of specific files to rebuild. Ignored if --write-all is "
"specified"
msgstr ""

#: sphinx/cmd/build.py:146
msgid "general options"
msgstr "genel seçenekler"

#: sphinx/cmd/build.py:149
msgid "builder to use (default: 'html')"
msgstr ""

#: sphinx/cmd/build.py:152
msgid ""
"run in parallel with N processes, when possible. 'auto' uses the number of "
"CPU cores"
msgstr ""

#: sphinx/cmd/build.py:155
msgid "write all files (default: only write new and changed files)"
msgstr "tüm dosyaları yaz (varsayılan: sadece yeni ve değiştirilmiş dosyaları yaz)"

#: sphinx/cmd/build.py:158
msgid "don't use a saved environment, always read all files"
msgstr "kaydedilmiş bir ortam kullanma, her zaman tüm dosyaları oku"

#: sphinx/cmd/build.py:161
msgid "path options"
msgstr ""

#: sphinx/cmd/build.py:163
msgid ""
"directory for doctree and environment files (default: OUTPUT_DIR/.doctrees)"
msgstr ""

#: sphinx/cmd/build.py:166
msgid "directory for the configuration file (conf.py) (default: SOURCE_DIR)"
msgstr ""

#: sphinx/cmd/build.py:171
msgid "use no configuration file, only use settings from -D options"
msgstr ""

#: sphinx/cmd/build.py:174
msgid "override a setting in configuration file"
msgstr "yapılandırma dosyasındaki bir ayarı geçersiz kıl"

#: sphinx/cmd/build.py:177
msgid "pass a value into HTML templates"
msgstr "HTML şablonlarına bir değer geçir"

#: sphinx/cmd/build.py:180
msgid "define tag: include \"only\" blocks with TAG"
msgstr "etiketi tanımla: \"sadece\" TAG'li blokları dahil et"

#: sphinx/cmd/build.py:182
msgid "nit-picky mode: warn about all missing references"
msgstr ""

#: sphinx/cmd/build.py:184
msgid "console output options"
msgstr "konsol çıktısı seçenekleri"

#: sphinx/cmd/build.py:187
msgid "increase verbosity (can be repeated)"
msgstr "ayrıntı seviyesini artır (tekrarlanabilir)"

#: sphinx/cmd/build.py:189 sphinx/ext/apidoc.py:340
msgid "no output on stdout, just warnings on stderr"
msgstr "stdout üzerinde çıktı yok, stderr üzerinde sadece uyarılar"

#: sphinx/cmd/build.py:191
msgid "no output at all, not even warnings"
msgstr "hiç çıktı yok, hatta uyarılarda bile"

#: sphinx/cmd/build.py:194
msgid "do emit colored output (default: auto-detect)"
msgstr "renkli çıktı yayımı yap (varsayılan: otomatik algıla)"

#: sphinx/cmd/build.py:197
msgid "do not emit colored output (default: auto-detect)"
msgstr "renkli çıktı yayımı yapma (varsayılan: otomatik algıla)"

#: sphinx/cmd/build.py:199
msgid "warning control options"
msgstr ""

#: sphinx/cmd/build.py:201
msgid "write warnings (and errors) to given file"
msgstr "verilen dosyaya uyarıları (ve hataları) yaz"

#: sphinx/cmd/build.py:203
msgid "turn warnings into errors"
msgstr "uyarıları hatalara dönüştür"

#: sphinx/cmd/build.py:205
msgid "with --fail-on-warning, keep going when getting warnings"
msgstr ""

#: sphinx/cmd/build.py:207
msgid "show full traceback on exception"
msgstr "özel durumda tam geri izleme göster"

#: sphinx/cmd/build.py:209
msgid "run Pdb on exception"
msgstr "özel durumda Pdb çalıştır"

#: sphinx/cmd/build.py:244
msgid "cannot combine -a option and filenames"
msgstr "-a seçeneği ve dosya adları birleştirilemiyor"

#: sphinx/cmd/build.py:276
#, python-format
msgid "cannot open warning file %r: %s"
msgstr "uyarı dosyası %r açılamıyor: %s"

#: sphinx/cmd/build.py:296
msgid "-D option argument must be in the form name=value"
msgstr "-D seçeneği bağımsız değişkeni ad=değer biçiminde olmak zorundadır"

#: sphinx/cmd/build.py:303
msgid "-A option argument must be in the form name=value"
msgstr "-A seçeneği bağımsız değişkeni ad=değer biçiminde olmak zorundadır"

#: sphinx/cmd/quickstart.py:42
msgid "automatically insert docstrings from modules"
msgstr "modüllerden otomatik olarak docstrings ekle"

#: sphinx/cmd/quickstart.py:43
msgid "automatically test code snippets in doctest blocks"
msgstr "doctest bloklarında kod parçacıklarını otomatik olarak dene"

#: sphinx/cmd/quickstart.py:44
msgid "link between Sphinx documentation of different projects"
msgstr "farklı projelerin Sphinx begelendirmeleri arasında bağlantıla"

#: sphinx/cmd/quickstart.py:45
msgid "write \"todo\" entries that can be shown or hidden on build"
msgstr "oluşturmada gösterilebilen veya gizlenebilen \"yapılacaklar\" girişlerini yaz"

#: sphinx/cmd/quickstart.py:46
msgid "checks for documentation coverage"
msgstr "belgelendirme kapsamı için denetlemeler"

#: sphinx/cmd/quickstart.py:47
msgid "include math, rendered as PNG or SVG images"
msgstr "PNG veya SVG resimleri olarak işleneni, matematiği dahil et"

#: sphinx/cmd/quickstart.py:48
msgid "include math, rendered in the browser by MathJax"
msgstr "MathJax ile tarayıcıda işleneni, matematiği dahil et"

#: sphinx/cmd/quickstart.py:49
msgid "conditional inclusion of content based on config values"
msgstr "yapılandırma değerlerine dayalı içeriğin koşullu olarak eklenmesi"

#: sphinx/cmd/quickstart.py:50
msgid "include links to the source code of documented Python objects"
msgstr "belgelenmiş Python nesnelerinin kaynak koduna bağlantıları dahil et"

#: sphinx/cmd/quickstart.py:51
msgid "create .nojekyll file to publish the document on GitHub pages"
msgstr "belgeyi GitHub sayfalarında yayımlamak için .nojekyll dosyası oluştur"

#: sphinx/cmd/quickstart.py:93
msgid "Please enter a valid path name."
msgstr "Lütfen geçerli bir yol adı girin."

#: sphinx/cmd/quickstart.py:109
msgid "Please enter some text."
msgstr "Lütfen biraz metin girin."

#: sphinx/cmd/quickstart.py:116
#, python-format
msgid "Please enter one of %s."
msgstr "Lütfen %s seçeneklerinden birini girin."

#: sphinx/cmd/quickstart.py:123
msgid "Please enter either 'y' or 'n'."
msgstr "Lütfen ya 'y' ya da 'n' girin."

#: sphinx/cmd/quickstart.py:129
msgid "Please enter a file suffix, e.g. '.rst' or '.txt'."
msgstr "Lütfen bir dosya soneki girin, örn. '.rst' veya '.txt'."

#: sphinx/cmd/quickstart.py:208
#, python-format
msgid "Welcome to the Sphinx %s quickstart utility."
msgstr "Sphinx %s hızlı başlangıç yardımcı uygulamasına hoş geldiniz."

#: sphinx/cmd/quickstart.py:210
msgid ""
"Please enter values for the following settings (just press Enter to\n"
"accept a default value, if one is given in brackets)."
msgstr "Lütfen aşağıdaki ayarlar için değerleri girin (parantez içinde verilirse\nvarsayılan değeri kabul etmek için yalnızca Enter tuşuna basın)."

#: sphinx/cmd/quickstart.py:215
#, python-format
msgid "Selected root path: %s"
msgstr "Seçilen kök dizin yolu: %s"

#: sphinx/cmd/quickstart.py:218
msgid "Enter the root path for documentation."
msgstr "Belgelendirme için kök dizin yolunu girin."

#: sphinx/cmd/quickstart.py:219
msgid "Root path for the documentation"
msgstr "Belgelendirme için kök dizin yolu"

#: sphinx/cmd/quickstart.py:224
msgid "Error: an existing conf.py has been found in the selected root path."
msgstr "Hata: seçilen kök dizin yolunda varolan bir conf.py bulundu."

#: sphinx/cmd/quickstart.py:226
msgid "sphinx-quickstart will not overwrite existing Sphinx projects."
msgstr "sfenks-quickstart varolan Sphinx projelerinin üzerine yazmayacak."

#: sphinx/cmd/quickstart.py:228
msgid "Please enter a new root path (or just Enter to exit)"
msgstr "Lütfen yeni bir kök dizin yolu girin (ya da çıkmak için yalnızca Enter'a basın)"

#: sphinx/cmd/quickstart.py:235
msgid ""
"You have two options for placing the build directory for Sphinx output.\n"
"Either, you use a directory \"_build\" within the root path, or you separate\n"
"\"source\" and \"build\" directories within the root path."
msgstr "Sphinx çıktısının oluşturma dizinini yerleştirmek için iki seçeneğiniz var.\nYa, kök dizin yolu içinde bir \"_build\" dizini kullanın, ya da kök dizin yolu\niçinde \"source\" ve \"build\" dizinlerini ayırın."

#: sphinx/cmd/quickstart.py:238
msgid "Separate source and build directories (y/n)"
msgstr "Source ve build dizinlerini ayır (y/n)"

#: sphinx/cmd/quickstart.py:242
msgid ""
"Inside the root directory, two more directories will be created; \"_templates\"\n"
"for custom HTML templates and \"_static\" for custom stylesheets and other static\n"
"files. You can enter another prefix (such as \".\") to replace the underscore."
msgstr "Kök dizinin içinde, iki dizin daha oluşturulacaktır; özel HTML şablonları için \"_templates\"\nve özel stil sayfaları ve diğer sabit dosyalar için \"_static\".\nAlt çizgi yerine başka bir önek (\".\" gibi) girebilirsiniz."

#: sphinx/cmd/quickstart.py:245
msgid "Name prefix for templates and static dir"
msgstr "Templates ve static dizinleri için ad öneki"

#: sphinx/cmd/quickstart.py:249
msgid ""
"The project name will occur in several places in the built documentation."
msgstr "Proje adı, oluşturulan belgelendirmedeki çeşitli yerlerde oluşacak."

#: sphinx/cmd/quickstart.py:250
msgid "Project name"
msgstr "Proje adı"

#: sphinx/cmd/quickstart.py:252
msgid "Author name(s)"
msgstr "Hazırlayan ad(ları)ı"

#: sphinx/cmd/quickstart.py:256
msgid ""
"Sphinx has the notion of a \"version\" and a \"release\" for the\n"
"software. Each version can have multiple releases. For example, for\n"
"Python the version is something like 2.5 or 3.0, while the release is\n"
"something like 2.5.1 or 3.0a1. If you don't need this dual structure,\n"
"just set both to the same value."
msgstr ""

#: sphinx/cmd/quickstart.py:261
msgid "Project version"
msgstr "Proje sürümü"

#: sphinx/cmd/quickstart.py:263
msgid "Project release"
msgstr "Proje yayımı"

#: sphinx/cmd/quickstart.py:267
msgid ""
"If the documents are to be written in a language other than English,\n"
"you can select a language here by its language code. Sphinx will then\n"
"translate text that it generates into that language.\n"
"\n"
"For a list of supported codes, see\n"
"https://www.sphinx-doc.org/en/master/usage/configuration.html#confval-language."
msgstr "Eğer belgeler İngilizce dışında bir dilde yazıldıysa, bunun dil koduna\ngöre burada bir dil seçebilirsiniz. Sphinx daha sonra, ürettiği metni o\ndile çevirecektir.\n\nDesteklenen kodların listesi için\nhttps://www.sphinx-doc.org/en/master/usage/configuration.html#confval-language adresine bakın."

#: sphinx/cmd/quickstart.py:275
msgid "Project language"
msgstr "Proje dili"

#: sphinx/cmd/quickstart.py:281
msgid ""
"The file name suffix for source files. Commonly, this is either \".txt\"\n"
"or \".rst\". Only files with this suffix are considered documents."
msgstr ""

#: sphinx/cmd/quickstart.py:283
msgid "Source file suffix"
msgstr "Kaynak dosya soneki"

#: sphinx/cmd/quickstart.py:287
msgid ""
"One document is special in that it is considered the top node of the\n"
"\"contents tree\", that is, it is the root of the hierarchical structure\n"
"of the documents. Normally, this is \"index\", but if your \"index\"\n"
"document is a custom template, you can also set this to another filename."
msgstr "Bir belge, \"içindekiler ağacı\"nın üst düğümü olarak kabul edilmesi, yani\nbelgelerin hiyerarşik yapısının kökü olması açısından özeldir.\nNormalde bu \"dizin\"dir, ancak \"dizin\" belgeniz özel bir şablonsa,\nbunu başka bir dosya adına da ayarlayabilirsiniz."

#: sphinx/cmd/quickstart.py:291
msgid "Name of your master document (without suffix)"
msgstr "Asıl belgenizin adı (sonek olmadan)"

#: sphinx/cmd/quickstart.py:296
#, python-format
msgid ""
"Error: the master file %s has already been found in the selected root path."
msgstr "Hata: %s asıl dosyası zaten seçilen kök dizin yolunda bulundu."

#: sphinx/cmd/quickstart.py:298
msgid "sphinx-quickstart will not overwrite the existing file."
msgstr "sfenks-quickstart varolan dosyanın üzerine yazmayacak."

#: sphinx/cmd/quickstart.py:300
msgid ""
"Please enter a new file name, or rename the existing file and press Enter"
msgstr "Lütfen yeni bir dosya adı girin ya da varolan dosyayı yeniden adlandırın ve Enter tuşuna basın"

#: sphinx/cmd/quickstart.py:304
msgid "Indicate which of the following Sphinx extensions should be enabled:"
msgstr "Aşağıdaki Sphinx uzantılarından hangisinin etkinleştirilmesi gerektiğini gösterir:"

#: sphinx/cmd/quickstart.py:312
msgid ""
"Note: imgmath and mathjax cannot be enabled at the same time. imgmath has "
"been deselected."
msgstr "Not: imgmath ve mathjax aynı anda etkinleştirilemez. imgmath seçimi kaldırıldı."

#: sphinx/cmd/quickstart.py:318
msgid ""
"A Makefile and a Windows command file can be generated for you so that you\n"
"only have to run e.g. `make html' instead of invoking sphinx-build\n"
"directly."
msgstr "Makefile ve Windows komut dosyası sizin için oluşturulabilir,\nböylece doğrudan örn. sphinx-build çağırmak yerine sadece\n`make html' komutu çalıştırılmak zorundadır."

#: sphinx/cmd/quickstart.py:321
msgid "Create Makefile? (y/n)"
msgstr "Makefile oluşturulsun mu? (y/n)"

#: sphinx/cmd/quickstart.py:324
msgid "Create Windows command file? (y/n)"
msgstr "Windows komut satırı oluşturulsun mu? (y/n)"

#: sphinx/cmd/quickstart.py:368 sphinx/ext/apidoc.py:93
#, python-format
msgid "Creating file %s."
msgstr "%s dosyası oluşturuluyor."

#: sphinx/cmd/quickstart.py:373 sphinx/ext/apidoc.py:90
#, python-format
msgid "File %s already exists, skipping."
msgstr "%s dosyası zaten var, atlanıyor."

#: sphinx/cmd/quickstart.py:418
msgid "Finished: An initial directory structure has been created."
msgstr "Tamamlandı: İlk dizin yapısı oluşturuldu."

#: sphinx/cmd/quickstart.py:420
#, python-format
msgid ""
"You should now populate your master file %s and create other documentation\n"
"source files. "
msgstr "Artık %s asıl dosyanızı doldurmalı ve diğer belgelendirme kaynak dosyalarını\noluşturmalısınız."

#: sphinx/cmd/quickstart.py:423
msgid ""
"Use the Makefile to build the docs, like so:\n"
"   make builder"
msgstr "Belgeleri oluşturmak için Makefile'ı kullanın, aşağıdaki gibi:\n   make builder"

#: sphinx/cmd/quickstart.py:426
#, python-format
msgid ""
"Use the sphinx-build command to build the docs, like so:\n"
"   sphinx-build -b builder %s %s"
msgstr "Belgeleri oluşturmak için sphinx-build komutunu kullanın, aşağıdaki gibi:\n   sphinx-build -b builder %s %s"

#: sphinx/cmd/quickstart.py:428
msgid ""
"where \"builder\" is one of the supported builders, e.g. html, latex or "
"linkcheck."
msgstr "\"builder\" burada desteklenen oluşturuculardan biridir, örn. html, latex veya linkcheck."

#: sphinx/cmd/quickstart.py:464
msgid ""
"\n"
"Generate required files for a Sphinx project.\n"
"\n"
"sphinx-quickstart is an interactive tool that asks some questions about your\n"
"project and then generates a complete documentation directory and sample\n"
"Makefile to be used with sphinx-build.\n"
msgstr "\nBir Sphinx projesi için gerekli dosyaları oluşturun.\n\nsphinx-quickstart, projeniz hakkında bazı sorular soran ve ardından tam bir\nbelgelendirme dizini ve örnek oluşturan etkileşimli bir araçtır\nMakefile, sphinx-build ile kullanılır.\n"

#: sphinx/cmd/quickstart.py:479
msgid "quiet mode"
msgstr "sessiz kipi"

#: sphinx/cmd/quickstart.py:484
msgid "project root"
msgstr "proje kök dizini"

#: sphinx/cmd/quickstart.py:486
msgid "Structure options"
msgstr "Yapı seçenekleri"

#: sphinx/cmd/quickstart.py:488
msgid "if specified, separate source and build dirs"
msgstr "eğer belirtildiyse, kaynak ve oluşturma dizinlerini ayırın"

#: sphinx/cmd/quickstart.py:490
msgid "if specified, create build dir under source dir"
msgstr ""

#: sphinx/cmd/quickstart.py:492
msgid "replacement for dot in _templates etc."
msgstr "_templates vb. içinde nokta için değiştirme"

#: sphinx/cmd/quickstart.py:494
msgid "Project basic options"
msgstr "Proje temel seçenekleri"

#: sphinx/cmd/quickstart.py:496
msgid "project name"
msgstr "proje adı"

#: sphinx/cmd/quickstart.py:498
msgid "author names"
msgstr "hazırlayan adları"

#: sphinx/cmd/quickstart.py:500
msgid "version of project"
msgstr "projenin sürümü"

#: sphinx/cmd/quickstart.py:502
msgid "release of project"
msgstr "projenin yayımı"

#: sphinx/cmd/quickstart.py:504
msgid "document language"
msgstr "belge dili"

#: sphinx/cmd/quickstart.py:506
msgid "source file suffix"
msgstr "kaynak dosya soneki"

#: sphinx/cmd/quickstart.py:508
msgid "master document name"
msgstr "asıl belge adı"

#: sphinx/cmd/quickstart.py:510
msgid "use epub"
msgstr "epub kullan"

#: sphinx/cmd/quickstart.py:512
msgid "Extension options"
msgstr "Uzantı seçenekleri"

#: sphinx/cmd/quickstart.py:516 sphinx/ext/apidoc.py:400
#, python-format
msgid "enable %s extension"
msgstr "%s uzantısını etkinleştir"

#: sphinx/cmd/quickstart.py:518 sphinx/ext/apidoc.py:396
msgid "enable arbitrary extensions"
msgstr "rasgele uzantıları etkinleştir"

#: sphinx/cmd/quickstart.py:520
msgid "Makefile and Batchfile creation"
msgstr "Makefile ve Batchfile oluşturma"

#: sphinx/cmd/quickstart.py:522
msgid "create makefile"
msgstr "makefile oluştur"

#: sphinx/cmd/quickstart.py:524
msgid "do not create makefile"
msgstr "makefile oluşturma"

#: sphinx/cmd/quickstart.py:526
msgid "create batchfile"
msgstr "batchfile oluştur"

#: sphinx/cmd/quickstart.py:529
msgid "do not create batchfile"
msgstr "batchfile oluşturma"

#: sphinx/cmd/quickstart.py:532
msgid "use make-mode for Makefile/make.bat"
msgstr "Makefile/make.bat için make-mode kullan"

#: sphinx/cmd/quickstart.py:535
msgid "do not use make-mode for Makefile/make.bat"
msgstr "Makefile/make.bat için make-mode kullanma"

#: sphinx/cmd/quickstart.py:537 sphinx/ext/apidoc.py:402
msgid "Project templating"
msgstr "Proje şablonlama"

#: sphinx/cmd/quickstart.py:540 sphinx/ext/apidoc.py:405
msgid "template directory for template files"
msgstr "şablon dosyaları için şablon dizini"

#: sphinx/cmd/quickstart.py:543
msgid "define a template variable"
msgstr "bir şablon değişkeni tanımla"

#: sphinx/cmd/quickstart.py:576
msgid "\"quiet\" is specified, but any of \"project\" or \"author\" is not specified."
msgstr "\"sessiz\" belirtilmiş, ancak herhangi bir \"proje\" veya \"hazırlayan\" belirtilmemiş."

#: sphinx/cmd/quickstart.py:590
msgid ""
"Error: specified path is not a directory, or sphinx files already exist."
msgstr "Hata: belirtilen yol bir dizin değil ya da sphinx dosyaları zaten var."

#: sphinx/cmd/quickstart.py:592
msgid ""
"sphinx-quickstart only generate into a empty directory. Please specify a new"
" root path."
msgstr "sfenks-quickstart sadece boş bir dizin içine oluşturur. Lütfen yeni bir kök dizin yolu belirtin."

#: sphinx/cmd/quickstart.py:607
#, python-format
msgid "Invalid template variable: %s"
msgstr "Geçersiz şablon değişkeni: %s"

#: sphinx/directives/code.py:61
msgid "non-whitespace stripped by dedent"
msgstr ""

#: sphinx/directives/code.py:82
#, python-format
msgid "Invalid caption: %s"
msgstr "Geçersiz resim yazısı: %s"

#: sphinx/directives/code.py:127 sphinx/directives/code.py:277
#: sphinx/directives/code.py:453
#, python-format
msgid "line number spec is out of range(1-%d): %r"
msgstr "satır numarası özellikleri aralık dışında (1-%d): %r"

#: sphinx/directives/code.py:206
#, python-format
msgid "Cannot use both \"%s\" and \"%s\" options"
msgstr "Hem \"%s\" hem de \"%s\" seçeneği kullanılamıyor"

#: sphinx/directives/code.py:220
#, python-format
msgid "Include file %r not found or reading it failed"
msgstr "Dahil edilen %r dosyası bulunamadı ya da dosyayı okuma başarısız oldu"

#: sphinx/directives/code.py:223
#, python-format
msgid ""
"Encoding %r used for reading included file %r seems to be wrong, try giving "
"an :encoding: option"
msgstr "%r kodlamasının kullanıldığı, dahil edilen %r dosyasını okuma yanlış gibi görünüyor, bir :encoding: seçeneği vermeyi deneyin"

#: sphinx/directives/code.py:260
#, python-format
msgid "Object named %r not found in include file %r"
msgstr "%r adlı nesne, dahil edilen %r dosyasında bulunamadı"

#: sphinx/directives/code.py:286
msgid "Cannot use \"lineno-match\" with a disjoint set of \"lines\""
msgstr "Ayrık bir \"satır\" kümesiyle \"lineno-match\" kullanılamıyor"

#: sphinx/directives/code.py:291
#, python-format
msgid "Line spec %r: no lines pulled from include file %r"
msgstr "Satır özelliği %r: dahil edilen %r dosyasından çekilen hiç satır yok"

#: sphinx/directives/other.py:120
#, python-format
msgid "toctree glob pattern %r didn't match any documents"
msgstr ""

#: sphinx/directives/other.py:146 sphinx/environment/adapters/toctree.py:324
#, python-format
msgid "toctree contains reference to excluded document %r"
msgstr "toctree, hariç tutulan %r belgesine başvuru içeriyor"

#: sphinx/directives/other.py:149 sphinx/environment/adapters/toctree.py:328
#, python-format
msgid "toctree contains reference to nonexisting document %r"
msgstr "toctree, varolmayan %r belgesine başvuru içeriyor"

#: sphinx/directives/other.py:160
#, python-format
msgid "duplicated entry found in toctree: %s"
msgstr ""

#: sphinx/directives/other.py:193
msgid "Section author: "
msgstr "Bölümü hazırlayan: "

#: sphinx/directives/other.py:195
msgid "Module author: "
msgstr "Modülü hazırlayan: "

#: sphinx/directives/other.py:197
msgid "Code author: "
msgstr "Kodu hazırlayan: "

#: sphinx/directives/other.py:199
msgid "Author: "
msgstr "Hazırlayan: "

#: sphinx/directives/other.py:275
msgid ".. acks content is not a list"
msgstr ""

#: sphinx/directives/other.py:301
msgid ".. hlist content is not a list"
msgstr ""

#: sphinx/directives/patches.py:66
msgid ""
"\":file:\" option for csv-table directive now recognizes an absolute path as"
" a relative path from source directory. Please update your document."
msgstr ""

#: sphinx/domains/__init__.py:397
#, python-format
msgid "%s %s"
msgstr "%s %s"

#: sphinx/domains/changeset.py:23
#, python-format
msgid "Added in version %s"
msgstr ""

#: sphinx/domains/changeset.py:24
#, python-format
msgid "Changed in version %s"
msgstr "%s sürümünde değişti"

#: sphinx/domains/changeset.py:25
#, python-format
msgid "Deprecated since version %s"
msgstr "%s sürümünden beri kullanım dışı"

#: sphinx/domains/changeset.py:26
#, python-format
msgid "Removed in version %s"
msgstr ""

#: sphinx/domains/citation.py:71
#, python-format
msgid "duplicate citation %s, other instance in %s"
msgstr "%s kopya alıntısı, %s içindeki diğer örnek"

#: sphinx/domains/citation.py:82
#, python-format
msgid "Citation [%s] is not referenced."
msgstr "Alıntı [%s] kaynak gösterilmedi."

#: sphinx/domains/javascript.py:165
#, python-format
msgid "%s() (built-in function)"
msgstr "%s() (yerleşik işlev)"

#: sphinx/domains/javascript.py:166 sphinx/domains/python/__init__.py:240
#, python-format
msgid "%s() (%s method)"
msgstr "%s() (%s yöntemi)"

#: sphinx/domains/javascript.py:168
#, python-format
msgid "%s() (class)"
msgstr "%s() (sınıf)"

#: sphinx/domains/javascript.py:170
#, python-format
msgid "%s (global variable or constant)"
msgstr "%s (genel değişken veya sabit)"

#: sphinx/domains/javascript.py:172 sphinx/domains/python/__init__.py:325
#, python-format
msgid "%s (%s attribute)"
msgstr "%s (%s özniteliği)"

#: sphinx/domains/javascript.py:255
msgid "Arguments"
msgstr "Bağımsız Değişkenler"

#: sphinx/domains/cpp/__init__.py:350 sphinx/domains/javascript.py:258
msgid "Throws"
msgstr "Şunu verir: "

#: sphinx/domains/c/__init__.py:251 sphinx/domains/cpp/__init__.py:361
#: sphinx/domains/javascript.py:261 sphinx/domains/python/_object.py:175
msgid "Returns"
msgstr "Dönüşler"

#: sphinx/domains/c/__init__.py:253 sphinx/domains/javascript.py:263
#: sphinx/domains/python/_object.py:177
msgid "Return type"
msgstr "Dönüş türü"

#: sphinx/domains/javascript.py:331
#, python-format
msgid "%s (module)"
msgstr "%s (modül)"

#: sphinx/domains/c/__init__.py:622 sphinx/domains/cpp/__init__.py:764
#: sphinx/domains/javascript.py:368 sphinx/domains/python/__init__.py:574
msgid "function"
msgstr "işlevi"

#: sphinx/domains/javascript.py:369 sphinx/domains/python/__init__.py:578
msgid "method"
msgstr "yöntemi"

#: sphinx/domains/cpp/__init__.py:762 sphinx/domains/javascript.py:370
#: sphinx/domains/python/__init__.py:576
msgid "class"
msgstr "sınıfı"

#: sphinx/domains/javascript.py:371 sphinx/domains/python/__init__.py:575
msgid "data"
msgstr "verisi"

#: sphinx/domains/javascript.py:372 sphinx/domains/python/__init__.py:581
msgid "attribute"
msgstr "özniteliği"

#: sphinx/domains/javascript.py:373 sphinx/domains/python/__init__.py:583
msgid "module"
msgstr "modülü"

#: sphinx/domains/javascript.py:404
#, python-format
msgid "duplicate %s description of %s, other %s in %s"
msgstr "kopya %s açıklamasına ait %s, diğer %s, %s içinde"

#: sphinx/domains/math.py:63
#, python-format
msgid "duplicate label of equation %s, other instance in %s"
msgstr "%s denkleminin kopya etiketi, %s içindeki diğer örnek"

#: sphinx/domains/math.py:118 sphinx/writers/latex.py:2252
#, python-format
msgid "Invalid math_eqref_format: %r"
msgstr "Geçersiz math_eqref_format: %r"

#: sphinx/domains/rst.py:127 sphinx/domains/rst.py:184
#, python-format
msgid "%s (directive)"
msgstr "%s (yönerge)"

#: sphinx/domains/rst.py:185 sphinx/domains/rst.py:189
#, python-format
msgid ":%s: (directive option)"
msgstr ":%s: (yönerge seçeneği)"

#: sphinx/domains/rst.py:213
#, python-format
msgid "%s (role)"
msgstr "%s (rol)"

#: sphinx/domains/rst.py:223
msgid "directive"
msgstr "yönergesi"

#: sphinx/domains/rst.py:224
msgid "directive-option"
msgstr "yönerge seçeneği"

#: sphinx/domains/rst.py:225
msgid "role"
msgstr "rolü"

#: sphinx/domains/rst.py:247
#, python-format
msgid "duplicate description of %s %s, other instance in %s"
msgstr "%s %s kopya açıklaması, %s içindeki diğer örnek"

#: sphinx/domains/c/__init__.py:146
#, python-format
msgid "%s (C %s)"
msgstr ""

#: sphinx/domains/c/__init__.py:207 sphinx/domains/c/_symbol.py:552
#, python-format
msgid ""
"Duplicate C declaration, also defined at %s:%s.\n"
"Declaration is '.. c:%s:: %s'."
msgstr ""

#: sphinx/domains/c/__init__.py:245 sphinx/domains/cpp/__init__.py:344
#: sphinx/domains/python/_object.py:163 sphinx/ext/napoleon/docstring.py:762
msgid "Parameters"
msgstr "Parametreler"

#: sphinx/domains/c/__init__.py:248 sphinx/domains/cpp/__init__.py:357
msgid "Return values"
msgstr ""

#: sphinx/domains/c/__init__.py:620 sphinx/domains/cpp/__init__.py:765
msgid "member"
msgstr "üyesi"

#: sphinx/domains/c/__init__.py:621
msgid "variable"
msgstr "değişkeni"

#: sphinx/domains/c/__init__.py:623
msgid "macro"
msgstr "makrosu"

#: sphinx/domains/c/__init__.py:624
msgid "struct"
msgstr ""

#: sphinx/domains/c/__init__.py:625 sphinx/domains/cpp/__init__.py:763
msgid "union"
msgstr "birliği"

#: sphinx/domains/c/__init__.py:626 sphinx/domains/cpp/__init__.py:768
msgid "enum"
msgstr "enum"

#: sphinx/domains/c/__init__.py:627 sphinx/domains/cpp/__init__.py:769
msgid "enumerator"
msgstr "numaralandırıcı"

#: sphinx/domains/c/__init__.py:628 sphinx/domains/cpp/__init__.py:766
msgid "type"
msgstr "türü"

#: sphinx/domains/c/__init__.py:630 sphinx/domains/cpp/__init__.py:771
msgid "function parameter"
msgstr ""

#: sphinx/domains/cpp/__init__.py:63
msgid "Template Parameters"
msgstr "Şablon Parametreleri"

#: sphinx/domains/cpp/__init__.py:185
#, python-format
msgid "%s (C++ %s)"
msgstr "%s (C++ %s)"

#: sphinx/domains/cpp/__init__.py:268 sphinx/domains/cpp/_symbol.py:790
#, python-format
msgid ""
"Duplicate C++ declaration, also defined at %s:%s.\n"
"Declaration is '.. cpp:%s:: %s'."
msgstr ""

#: sphinx/domains/cpp/__init__.py:767
msgid "concept"
msgstr "kavramı"

#: sphinx/domains/cpp/__init__.py:772
msgid "template parameter"
msgstr ""

#: sphinx/domains/python/__init__.py:94 sphinx/domains/python/__init__.py:231
#, python-format
msgid "%s() (in module %s)"
msgstr "%s() (%s modülü içinde)"

#: sphinx/domains/python/__init__.py:154 sphinx/domains/python/__init__.py:321
#: sphinx/domains/python/__init__.py:372
#, python-format
msgid "%s (in module %s)"
msgstr "%s (%s modülü içinde)"

#: sphinx/domains/python/__init__.py:156
#, python-format
msgid "%s (built-in variable)"
msgstr "%s (yerleşik değişken)"

#: sphinx/domains/python/__init__.py:181
#, python-format
msgid "%s (built-in class)"
msgstr "%s (yerleşik sınıf)"

#: sphinx/domains/python/__init__.py:182
#, python-format
msgid "%s (class in %s)"
msgstr "%s (%s içindeki sınıf)"

#: sphinx/domains/python/__init__.py:236
#, python-format
msgid "%s() (%s class method)"
msgstr "%s() (%s sınıf yöntemi)"

#: sphinx/domains/python/__init__.py:238
#, python-format
msgid "%s() (%s static method)"
msgstr "%s() (%s sabit yöntemi)"

#: sphinx/domains/python/__init__.py:376
#, python-format
msgid "%s (%s property)"
msgstr ""

#: sphinx/domains/python/__init__.py:502
msgid "Python Module Index"
msgstr "Python Modül Dizini"

#: sphinx/domains/python/__init__.py:503
msgid "modules"
msgstr "modülleri"

#: sphinx/domains/python/__init__.py:552
msgid "Deprecated"
msgstr "Kullanım dışı"

#: sphinx/domains/python/__init__.py:577
msgid "exception"
msgstr "özel durum"

#: sphinx/domains/python/__init__.py:579
msgid "class method"
msgstr "sınıf yöntemi"

#: sphinx/domains/python/__init__.py:580
msgid "static method"
msgstr "sabit yöntemi"

#: sphinx/domains/python/__init__.py:582
msgid "property"
msgstr ""

#: sphinx/domains/python/__init__.py:640
#, python-format
msgid ""
"duplicate object description of %s, other instance in %s, use :no-index: for"
" one of them"
msgstr ""

#: sphinx/domains/python/__init__.py:760
#, python-format
msgid "more than one target found for cross-reference %r: %s"
msgstr "çapraz referans %r için birden fazla hedef bulundu: %s"

#: sphinx/domains/python/__init__.py:821
msgid " (deprecated)"
msgstr " (kullanım dışı)"

#: sphinx/domains/python/_object.py:168
msgid "Variables"
msgstr "Değişkenler"

#: sphinx/domains/python/_object.py:172
msgid "Raises"
msgstr "Harekete geçirir"

#: sphinx/domains/std/__init__.py:80 sphinx/domains/std/__init__.py:97
#, python-format
msgid "environment variable; %s"
msgstr "ortam değişkeni; %s"

#: sphinx/domains/std/__init__.py:157
#, python-format
msgid ""
"Malformed option description %r, should look like \"opt\", \"-opt args\", \""
"--opt args\", \"/opt args\" or \"+opt args\""
msgstr "Hatalı biçimlendirilmiş seçenek açıklaması %r, \"opt\", \"-opt args\", \"--opt args\", \"/opt args\" veya \"+opt args\" şeklinde görünmelidir"

#: sphinx/domains/std/__init__.py:228
#, python-format
msgid "%s command line option"
msgstr "%s komut satırı seçeneği"

#: sphinx/domains/std/__init__.py:230
msgid "command line option"
msgstr "komut satırı seçeneği"

#: sphinx/domains/std/__init__.py:348
msgid "glossary term must be preceded by empty line"
msgstr "sözlük teriminden önce boş satır gelmek zorundadır"

#: sphinx/domains/std/__init__.py:356
msgid "glossary terms must not be separated by empty lines"
msgstr "sözlük terimleri boş satırlarla ayrılmamak zorundadır"

#: sphinx/domains/std/__init__.py:362 sphinx/domains/std/__init__.py:375
msgid "glossary seems to be misformatted, check indentation"
msgstr "sözlük yanlış biçimlendirilmiş gibi görünüyor, girintiyi gözden geçirin"

#: sphinx/domains/std/__init__.py:518
msgid "glossary term"
msgstr "sözlük terimi"

#: sphinx/domains/std/__init__.py:519
msgid "grammar token"
msgstr "dilbilgisi belirteci"

#: sphinx/domains/std/__init__.py:520
msgid "reference label"
msgstr "başvuru etiketi"

#: sphinx/domains/std/__init__.py:522
msgid "environment variable"
msgstr "ortam değişkeni"

#: sphinx/domains/std/__init__.py:523
msgid "program option"
msgstr "program seçeneği"

#: sphinx/domains/std/__init__.py:524
msgid "document"
msgstr "belge"

#: sphinx/domains/std/__init__.py:560 sphinx/domains/std/__init__.py:572
msgid "Module Index"
msgstr "Modül Dizini"

#: sphinx/domains/std/__init__.py:561 sphinx/domains/std/__init__.py:573
#: sphinx/themes/basic/defindex.html:25
msgid "Search Page"
msgstr "Arama Sayfası"

#: sphinx/domains/std/__init__.py:616 sphinx/domains/std/__init__.py:722
#: sphinx/ext/autosectionlabel.py:53
#, python-format
msgid "duplicate label %s, other instance in %s"
msgstr "%s kopya etiketi, %s içindeki diğer örnek"

#: sphinx/domains/std/__init__.py:635
#, python-format
msgid "duplicate %s description of %s, other instance in %s"
msgstr "kopya %s açıklamasına ait %s, %s içindeki diğer örnek "

#: sphinx/domains/std/__init__.py:841
msgid "numfig is disabled. :numref: is ignored."
msgstr "numfig etkisizleştirildi. :numref: yoksayıldı."

#: sphinx/domains/std/__init__.py:849
#, python-format
msgid "Failed to create a cross reference. Any number is not assigned: %s"
msgstr ""

#: sphinx/domains/std/__init__.py:861
#, python-format
msgid "the link has no caption: %s"
msgstr "bağlantının resim yazısı yok: %s"

#: sphinx/domains/std/__init__.py:875
#, python-format
msgid "invalid numfig_format: %s (%r)"
msgstr "geçersiz numfig_format: %s (%r)"

#: sphinx/domains/std/__init__.py:878
#, python-format
msgid "invalid numfig_format: %s"
msgstr "geçersiz numfig_format: %s"

#: sphinx/domains/std/__init__.py:1109
#, python-format
msgid "undefined label: %r"
msgstr ""

#: sphinx/domains/std/__init__.py:1111
#, python-format
msgid "Failed to create a cross reference. A title or caption not found: %r"
msgstr ""

#: sphinx/environment/__init__.py:71
msgid "new config"
msgstr "yeni yapılandırma"

#: sphinx/environment/__init__.py:72
msgid "config changed"
msgstr "yapılandırma değişti"

#: sphinx/environment/__init__.py:73
msgid "extensions changed"
msgstr "uzantılar değişti"

#: sphinx/environment/__init__.py:279
msgid "build environment version not current"
msgstr "yapım ortamı sürümü şu anki değil"

#: sphinx/environment/__init__.py:281
msgid "source directory has changed"
msgstr "kaynak dizin değişti"

#: sphinx/environment/__init__.py:360
msgid ""
"This environment is incompatible with the selected builder, please choose "
"another doctree directory."
msgstr "Bu ortam seçilen oluşturucuyla uyumsuzdur, lütfen başka bir belge ağacı dizini seçin."

#: sphinx/environment/__init__.py:459
#, python-format
msgid "Failed to scan documents in %s: %r"
msgstr "%s içinde belgeleri tarama başarısız oldu: %r"

#: sphinx/environment/__init__.py:596
#, python-format
msgid "Domain %r is not registered"
msgstr "Etki alanı %r kayıtlı değil"

#: sphinx/environment/__init__.py:730
msgid "document isn't included in any toctree"
msgstr "belge herhangi bir toctree içine dahil değil"

#: sphinx/environment/__init__.py:766
msgid "self referenced toctree found. Ignored."
msgstr "kendinden kaynaklı toctree bulundu. Yoksayıldı."

#: sphinx/environment/adapters/indexentries.py:69
#, python-format
msgid "see %s"
msgstr "bakınız %s"

#: sphinx/environment/adapters/indexentries.py:73
#, python-format
msgid "see also %s"
msgstr "ayrıca bakınız %s"

#: sphinx/environment/adapters/indexentries.py:76
#, python-format
msgid "unknown index entry type %r"
msgstr "bilinmeyen dizin girişi türü %r"

#: sphinx/environment/adapters/indexentries.py:187
#: sphinx/templates/latex/sphinxmessages.sty_t:11
msgid "Symbols"
msgstr "Semboller"

#: sphinx/environment/adapters/toctree.py:297
#, python-format
msgid "circular toctree references detected, ignoring: %s <- %s"
msgstr "dairesel toctree kaynakları algılandı, yoksayılan: %s <- %s"

#: sphinx/environment/adapters/toctree.py:317
#, python-format
msgid ""
"toctree contains reference to document %r that doesn't have a title: no link"
" will be generated"
msgstr "toctree, başlığı olmayan %r belgesine başvuru içeriyor: hiç bağlantı oluşturulmayacaktır"

#: sphinx/environment/adapters/toctree.py:326
#, python-format
msgid "toctree contains reference to non-included document %r"
msgstr ""

#: sphinx/environment/collectors/asset.py:89
#, python-format
msgid "image file not readable: %s"
msgstr "resim dosyası okunabilir değil: %s"

#: sphinx/environment/collectors/asset.py:108
#, python-format
msgid "image file %s not readable: %s"
msgstr "%s resim dosyası okunabilir değil: %s"

#: sphinx/environment/collectors/asset.py:134
#, python-format
msgid "download file not readable: %s"
msgstr "indirme dosyası okunabilir değil: %s"

#: sphinx/environment/collectors/toctree.py:225
#, python-format
msgid "%s is already assigned section numbers (nested numbered toctree?)"
msgstr "%s zaten atanmış bölüm numaralarıdır (iç içe numaralı toctree mi?)"

#: sphinx/ext/apidoc.py:86
#, python-format
msgid "Would create file %s."
msgstr "%s dosyası oluşturur."

#: sphinx/ext/apidoc.py:318
msgid ""
"\n"
"Look recursively in <MODULE_PATH> for Python modules and packages and create\n"
"one reST file with automodule directives per package in the <OUTPUT_PATH>.\n"
"\n"
"The <EXCLUDE_PATTERN>s can be file and/or directory patterns that will be\n"
"excluded from generation.\n"
"\n"
"Note: By default this script will not overwrite already created files."
msgstr "\nPython modülleri ve paketleri için <MODULE_PATH> içine art arda bakın ve <OUTPUT_PATH> içindeki\npaket başına otomodül talimatlarını içeren bir reST dosyası oluşturun.\n\n<EXCLUDE_PATTERN>'ler, nesilden hariç tutulacak olan dosya ve/veya dizin şekilleri olabilir.\n\nNot: Varsayılan olarak bu betik zaten oluşturulmuş dosyaların üzerine yazmayacak."

#: sphinx/ext/apidoc.py:331
msgid "path to module to document"
msgstr "belge için modüle giden yol"

#: sphinx/ext/apidoc.py:333
msgid ""
"fnmatch-style file and/or directory patterns to exclude from generation"
msgstr "nesilden hariç tutmak için fnmatch-style dosyası ve/veya dizin şekilleri"

#: sphinx/ext/apidoc.py:338
msgid "directory to place all output"
msgstr "tüm çıktıların yerleştirileceği dizin"

#: sphinx/ext/apidoc.py:343
msgid "maximum depth of submodules to show in the TOC (default: 4)"
msgstr "TOC'da gösterilecek alt modüllerin en fazla derinliği (varsayılan: 4)"

#: sphinx/ext/apidoc.py:346
msgid "overwrite existing files"
msgstr "varolan dosyaların üzerine yaz"

#: sphinx/ext/apidoc.py:349
msgid ""
"follow symbolic links. Powerful when combined with "
"collective.recipe.omelette."
msgstr "sembolik bağlantıları takip edin. Collective.recipe.omelette ile birleştirildiğinde güçlü."

#: sphinx/ext/apidoc.py:352
msgid "run the script without creating files"
msgstr "dosyaları oluşturmadan betiği çalıştır"

#: sphinx/ext/apidoc.py:355
msgid "put documentation for each module on its own page"
msgstr "her modül için belgelendirmeyi kendi sayfasına koy"

#: sphinx/ext/apidoc.py:358
msgid "include \"_private\" modules"
msgstr "\"_private\" modülleri dahil"

#: sphinx/ext/apidoc.py:360
msgid "filename of table of contents (default: modules)"
msgstr "içindekiler dosyası adı (varsayılan: modüller)"

#: sphinx/ext/apidoc.py:362
msgid "don't create a table of contents file"
msgstr "içindekiler tablosu oluşturma"

#: sphinx/ext/apidoc.py:365
msgid ""
"don't create headings for the module/package packages (e.g. when the "
"docstrings already contain them)"
msgstr "modül/paket paketleri için başlıklar oluşturma (örn. docstrings zaten bunları içerdiğinde)"

#: sphinx/ext/apidoc.py:370
msgid "put module documentation before submodule documentation"
msgstr "modül belgelerini alt modül belgelerinin önüne koyun"

#: sphinx/ext/apidoc.py:374
msgid ""
"interpret module paths according to PEP-0420 implicit namespaces "
"specification"
msgstr "modül yollarını PEP-0420 kapalı ad alanları özelliklerine göre yorumla"

#: sphinx/ext/apidoc.py:378
msgid "file suffix (default: rst)"
msgstr "dosya soneki (varsayılan: rst)"

#: sphinx/ext/apidoc.py:380
msgid "generate a full project with sphinx-quickstart"
msgstr "sphinx-quickstart ile tam bir proje oluştur"

#: sphinx/ext/apidoc.py:383
msgid "append module_path to sys.path, used when --full is given"
msgstr "--full yazıldığında, append module_path to sys.path, kullanılır"

#: sphinx/ext/apidoc.py:385
msgid "project name (default: root module name)"
msgstr "proje adı (varsayılan: kök modül adı)"

#: sphinx/ext/apidoc.py:387
msgid "project author(s), used when --full is given"
msgstr "--full yazıldığında, proje hazırlayan(lar)ı kullanılır"

#: sphinx/ext/apidoc.py:389
msgid "project version, used when --full is given"
msgstr "--full yazıldığında, proje sürümü kullanılır"

#: sphinx/ext/apidoc.py:391
msgid "project release, used when --full is given, defaults to --doc-version"
msgstr "--full yazıldığında, varsayılanı to --doc-version, proje yayımı kullanılır"

#: sphinx/ext/apidoc.py:394
msgid "extension options"
msgstr "uzantı seçenekleri"

#: sphinx/ext/apidoc.py:427
#, python-format
msgid "%s is not a directory."
msgstr "%s bir dizin değil."

#: sphinx/ext/autosectionlabel.py:49
#, python-format
msgid "section \"%s\" gets labeled as \"%s\""
msgstr ""

#: sphinx/ext/coverage.py:46
#, python-format
msgid "invalid regex %r in %s"
msgstr ""

#: sphinx/ext/coverage.py:75
#, python-format
msgid ""
"Testing of coverage in the sources finished, look at the results in "
"%(outdir)spython.txt."
msgstr ""

#: sphinx/ext/coverage.py:89
#, python-format
msgid "invalid regex %r in coverage_c_regexes"
msgstr ""

#: sphinx/ext/coverage.py:157
#, python-format
msgid "undocumented c api: %s [%s] in file %s"
msgstr ""

#: sphinx/ext/coverage.py:189
#, python-format
msgid "module %s could not be imported: %s"
msgstr ""

#: sphinx/ext/coverage.py:340
#, python-format
msgid "undocumented python function: %s :: %s"
msgstr ""

#: sphinx/ext/coverage.py:356
#, python-format
msgid "undocumented python class: %s :: %s"
msgstr ""

#: sphinx/ext/coverage.py:369
#, python-format
msgid "undocumented python method: %s :: %s :: %s"
msgstr ""

#: sphinx/ext/doctest.py:115
#, python-format
msgid "missing '+' or '-' in '%s' option."
msgstr ""

#: sphinx/ext/doctest.py:120
#, python-format
msgid "'%s' is not a valid option."
msgstr ""

#: sphinx/ext/doctest.py:134
#, python-format
msgid "'%s' is not a valid pyversion option"
msgstr ""

#: sphinx/ext/doctest.py:220
msgid "invalid TestCode type"
msgstr ""

#: sphinx/ext/doctest.py:281
#, python-format
msgid ""
"Testing of doctests in the sources finished, look at the results in "
"%(outdir)s/output.txt."
msgstr ""

#: sphinx/ext/doctest.py:438
#, python-format
msgid "no code/output in %s block at %s:%s"
msgstr ""

#: sphinx/ext/doctest.py:526
#, python-format
msgid "ignoring invalid doctest code: %r"
msgstr ""

#: sphinx/ext/duration.py:77
msgid ""
"====================== slowest reading durations ======================="
msgstr ""

#: sphinx/ext/extlinks.py:82
#, python-format
msgid ""
"hardcoded link %r could be replaced by an extlink (try using %r instead)"
msgstr ""

#: sphinx/ext/graphviz.py:135
msgid "Graphviz directive cannot have both content and a filename argument"
msgstr ""

#: sphinx/ext/graphviz.py:145
#, python-format
msgid "External Graphviz file %r not found or reading it failed"
msgstr ""

#: sphinx/ext/graphviz.py:152
msgid "Ignoring \"graphviz\" directive without content."
msgstr ""

#: sphinx/ext/graphviz.py:268
#, python-format
msgid "graphviz_dot executable path must be set! %r"
msgstr ""

#: sphinx/ext/graphviz.py:303
#, python-format
msgid ""
"dot command %r cannot be run (needed for graphviz output), check the "
"graphviz_dot setting"
msgstr ""

#: sphinx/ext/graphviz.py:310
#, python-format
msgid ""
"dot exited with error:\n"
"[stderr]\n"
"%r\n"
"[stdout]\n"
"%r"
msgstr ""

#: sphinx/ext/graphviz.py:313
#, python-format
msgid ""
"dot did not produce an output file:\n"
"[stderr]\n"
"%r\n"
"[stdout]\n"
"%r"
msgstr ""

#: sphinx/ext/graphviz.py:329
#, python-format
msgid "graphviz_output_format must be one of 'png', 'svg', but is %r"
msgstr ""

#: sphinx/ext/graphviz.py:333 sphinx/ext/graphviz.py:386
#: sphinx/ext/graphviz.py:423
#, python-format
msgid "dot code %r: %s"
msgstr "nokta kodu %r: %s"

#: sphinx/ext/graphviz.py:436 sphinx/ext/graphviz.py:444
#, python-format
msgid "[graph: %s]"
msgstr "[grafik: %s]"

#: sphinx/ext/graphviz.py:438 sphinx/ext/graphviz.py:446
msgid "[graph]"
msgstr "[grafik]"

#: sphinx/ext/imgconverter.py:39
#, python-format
msgid ""
"Unable to run the image conversion command %r. 'sphinx.ext.imgconverter' requires ImageMagick by default. Ensure it is installed, or set the 'image_converter' option to a custom conversion command.\n"
"\n"
"Traceback: %s"
msgstr ""

#: sphinx/ext/imgconverter.py:48 sphinx/ext/imgconverter.py:72
#, python-format
msgid ""
"convert exited with error:\n"
"[stderr]\n"
"%r\n"
"[stdout]\n"
"%r"
msgstr "dönüştürmeden hata ile çıkıldı:\n[stderr]\n%r\n[stdout]\n%r"

#: sphinx/ext/imgconverter.py:67
#, python-format
msgid "convert command %r cannot be run, check the image_converter setting"
msgstr "dönüştürme komutu %r çalıştırılamaz, image_converter ayarını gözden geçirin"

#: sphinx/ext/imgmath.py:158
#, python-format
msgid ""
"LaTeX command %r cannot be run (needed for math display), check the "
"imgmath_latex setting"
msgstr "LaTeX komutu %r çalıştırılamaz (matematik görüntüleme için gerekli), imgmath_latex ayarını gözden geçirin"

#: sphinx/ext/imgmath.py:173
#, python-format
msgid ""
"%s command %r cannot be run (needed for math display), check the imgmath_%s "
"setting"
msgstr "%s komutu %r çalıştırılamaz (matematik görüntüleme için gerekli), imgmath_%s ayarını gözden geçirin"

#: sphinx/ext/imgmath.py:327
#, python-format
msgid "display latex %r: %s"
msgstr "görüntü latex %r: %s"

#: sphinx/ext/imgmath.py:361
#, python-format
msgid "inline latex %r: %s"
msgstr "satır içi latex %r: %s"

#: sphinx/ext/imgmath.py:368 sphinx/ext/mathjax.py:53
msgid "Link to this equation"
msgstr ""

#: sphinx/ext/intersphinx.py:195
#, python-format
msgid "intersphinx inventory has moved: %s -> %s"
msgstr "intersphinx envanteri taşındı: %s -> %s"

#: sphinx/ext/intersphinx.py:230
#, python-format
msgid "loading intersphinx inventory from %s..."
msgstr "%s konumundan intersphinx envanteri yükleniyor..."

#: sphinx/ext/intersphinx.py:244
msgid ""
"encountered some issues with some of the inventories, but they had working "
"alternatives:"
msgstr ""

#: sphinx/ext/intersphinx.py:250
msgid "failed to reach any of the inventories with the following issues:"
msgstr "aşağıdaki sorunlardan dolayı envanterlerden herhangi birine ulaşılamadı:"

#: sphinx/ext/intersphinx.py:303
#, python-format
msgid "(in %s v%s)"
msgstr "(%s v%s içinde)"

#: sphinx/ext/intersphinx.py:305
#, python-format
msgid "(in %s)"
msgstr "(%s içinde)"

#: sphinx/ext/intersphinx.py:538
#, python-format
msgid "inventory for external cross-reference not found: %r"
msgstr ""

#: sphinx/ext/intersphinx.py:546
#, python-format
msgid "invalid external cross-reference suffix: %r"
msgstr ""

#: sphinx/ext/intersphinx.py:557
#, python-format
msgid "domain for external cross-reference not found: %r"
msgstr ""

#: sphinx/ext/intersphinx.py:750
#, python-format
msgid "external %s:%s reference target not found: %s"
msgstr ""

#: sphinx/ext/intersphinx.py:775
#, python-format
msgid "intersphinx identifier %r is not string. Ignored"
msgstr ""

#: sphinx/ext/intersphinx.py:797
#, python-format
msgid "Failed to read intersphinx_mapping[%s], ignored: %r"
msgstr ""

#: sphinx/ext/linkcode.py:69 sphinx/ext/viewcode.py:199
msgid "[source]"
msgstr "[kaynak]"

#: sphinx/ext/todo.py:69
msgid "Todo"
msgstr "Yapılacaklar"

#: sphinx/ext/todo.py:102
#, python-format
msgid "TODO entry found: %s"
msgstr ""

#: sphinx/ext/todo.py:161
msgid "<<original entry>>"
msgstr "<<original entry>>"

#: sphinx/ext/todo.py:163
#, python-format
msgid "(The <<original entry>> is located in %s, line %d.)"
msgstr "(<<original entry>>, %s içinde, %d. satırda bulunur.)"

#: sphinx/ext/todo.py:173
msgid "original entry"
msgstr "özgün giriş"

#: sphinx/ext/viewcode.py:256
msgid "highlighting module code... "
msgstr ""

#: sphinx/ext/viewcode.py:284
msgid "[docs]"
msgstr "[belgeler]"

#: sphinx/ext/viewcode.py:304
msgid "Module code"
msgstr "Modül kodu"

#: sphinx/ext/viewcode.py:310
#, python-format
msgid "<h1>Source code for %s</h1>"
msgstr "<h1>%s öğesinin kaynak kodu</h1>"

#: sphinx/ext/viewcode.py:336
msgid "Overview: module code"
msgstr "Genel bakış: modül kodu"

#: sphinx/ext/viewcode.py:337
msgid "<h1>All modules for which code is available</h1>"
msgstr "<h1>Kodları mevcut bütün modüller</h1>"

#: sphinx/ext/autodoc/__init__.py:135
#, python-format
msgid "invalid value for member-order option: %s"
msgstr ""

#: sphinx/ext/autodoc/__init__.py:143
#, python-format
msgid "invalid value for class-doc-from option: %s"
msgstr ""

#: sphinx/ext/autodoc/__init__.py:399
#, python-format
msgid "invalid signature for auto%s (%r)"
msgstr ""

#: sphinx/ext/autodoc/__init__.py:515
#, python-format
msgid "error while formatting arguments for %s: %s"
msgstr ""

#: sphinx/ext/autodoc/__init__.py:798
#, python-format
msgid ""
"autodoc: failed to determine %s.%s (%r) to be documented, the following exception was raised:\n"
"%s"
msgstr ""

#: sphinx/ext/autodoc/__init__.py:893
#, python-format
msgid ""
"don't know which module to import for autodocumenting %r (try placing a "
"\"module\" or \"currentmodule\" directive in the document, or giving an "
"explicit module name)"
msgstr ""

#: sphinx/ext/autodoc/__init__.py:937
#, python-format
msgid "A mocked object is detected: %r"
msgstr ""

#: sphinx/ext/autodoc/__init__.py:956
#, python-format
msgid "error while formatting signature for %s: %s"
msgstr ""

#: sphinx/ext/autodoc/__init__.py:1019
msgid "\"::\" in automodule name doesn't make sense"
msgstr ""

#: sphinx/ext/autodoc/__init__.py:1026
#, python-format
msgid "signature arguments or return annotation given for automodule %s"
msgstr ""

#: sphinx/ext/autodoc/__init__.py:1039
#, python-format
msgid ""
"__all__ should be a list of strings, not %r (in module %s) -- ignoring "
"__all__"
msgstr ""

#: sphinx/ext/autodoc/__init__.py:1105
#, python-format
msgid ""
"missing attribute mentioned in :members: option: module %s, attribute %s"
msgstr ""

#: sphinx/ext/autodoc/__init__.py:1327 sphinx/ext/autodoc/__init__.py:1404
#: sphinx/ext/autodoc/__init__.py:2824
#, python-format
msgid "Failed to get a function signature for %s: %s"
msgstr ""

#: sphinx/ext/autodoc/__init__.py:1618
#, python-format
msgid "Failed to get a constructor signature for %s: %s"
msgstr ""

#: sphinx/ext/autodoc/__init__.py:1745
#, python-format
msgid "Bases: %s"
msgstr ""

#: sphinx/ext/autodoc/__init__.py:1759
#, python-format
msgid "missing attribute %s in object %s"
msgstr ""

#: sphinx/ext/autodoc/__init__.py:1858 sphinx/ext/autodoc/__init__.py:1895
#: sphinx/ext/autodoc/__init__.py:1990
#, python-format
msgid "alias of %s"
msgstr ""

#: sphinx/ext/autodoc/__init__.py:1878
#, python-format
msgid "alias of TypeVar(%s)"
msgstr ""

#: sphinx/ext/autodoc/__init__.py:2216 sphinx/ext/autodoc/__init__.py:2316
#, python-format
msgid "Failed to get a method signature for %s: %s"
msgstr ""

#: sphinx/ext/autodoc/__init__.py:2447
#, python-format
msgid "Invalid __slots__ found on %s. Ignored."
msgstr ""

#: sphinx/ext/autodoc/preserve_defaults.py:190
#, python-format
msgid "Failed to parse a default argument value for %r: %s"
msgstr ""

#: sphinx/ext/autodoc/type_comment.py:132
#, python-format
msgid "Failed to update signature for %r: parameter not found: %s"
msgstr ""

#: sphinx/ext/autodoc/type_comment.py:135
#, python-format
msgid "Failed to parse type_comment for %r: %s"
msgstr ""

#: sphinx/ext/autosummary/__init__.py:251
#, python-format
msgid "autosummary references excluded document %r. Ignored."
msgstr ""

#: sphinx/ext/autosummary/__init__.py:253
#, python-format
msgid ""
"autosummary: stub file not found %r. Check your autosummary_generate "
"setting."
msgstr ""

#: sphinx/ext/autosummary/__init__.py:272
msgid "A captioned autosummary requires :toctree: option. ignored."
msgstr ""

#: sphinx/ext/autosummary/__init__.py:325
#, python-format
msgid ""
"autosummary: failed to import %s.\n"
"Possible hints:\n"
"%s"
msgstr ""

#: sphinx/ext/autosummary/__init__.py:339
#, python-format
msgid "failed to parse name %s"
msgstr ""

#: sphinx/ext/autosummary/__init__.py:344
#, python-format
msgid "failed to import object %s"
msgstr ""

#: sphinx/ext/autosummary/__init__.py:802
#, python-format
msgid "autosummary_generate: file not found: %s"
msgstr ""

#: sphinx/ext/autosummary/__init__.py:810
msgid ""
"autosummary generates .rst files internally. But your source_suffix does not"
" contain .rst. Skipped."
msgstr ""

#: sphinx/ext/autosummary/generate.py:200
#: sphinx/ext/autosummary/generate.py:358
#, python-format
msgid ""
"autosummary: failed to determine %r to be documented, the following exception was raised:\n"
"%s"
msgstr ""

#: sphinx/ext/autosummary/generate.py:470
#, python-format
msgid "[autosummary] generating autosummary for: %s"
msgstr ""

#: sphinx/ext/autosummary/generate.py:474
#, python-format
msgid "[autosummary] writing to %s"
msgstr ""

#: sphinx/ext/autosummary/generate.py:517
#, python-format
msgid ""
"[autosummary] failed to import %s.\n"
"Possible hints:\n"
"%s"
msgstr ""

#: sphinx/ext/autosummary/generate.py:690
msgid ""
"\n"
"Generate ReStructuredText using autosummary directives.\n"
"\n"
"sphinx-autogen is a frontend to sphinx.ext.autosummary.generate. It generates\n"
"the reStructuredText files from the autosummary directives contained in the\n"
"given input files.\n"
"\n"
"The format of the autosummary directive is documented in the\n"
"``sphinx.ext.autosummary`` Python module and can be read using::\n"
"\n"
"  pydoc sphinx.ext.autosummary\n"
msgstr ""

#: sphinx/ext/autosummary/generate.py:707
msgid "source files to generate rST files for"
msgstr ""

#: sphinx/ext/autosummary/generate.py:711
msgid "directory to place all output in"
msgstr ""

#: sphinx/ext/autosummary/generate.py:714
#, python-format
msgid "default suffix for files (default: %(default)s)"
msgstr ""

#: sphinx/ext/autosummary/generate.py:718
#, python-format
msgid "custom template directory (default: %(default)s)"
msgstr ""

#: sphinx/ext/autosummary/generate.py:722
#, python-format
msgid "document imported members (default: %(default)s)"
msgstr ""

#: sphinx/ext/autosummary/generate.py:726
#, python-format
msgid ""
"document exactly the members in module __all__ attribute. (default: "
"%(default)s)"
msgstr ""

#: sphinx/ext/napoleon/__init__.py:341 sphinx/ext/napoleon/docstring.py:728
msgid "Keyword Arguments"
msgstr ""

#: sphinx/ext/napoleon/docstring.py:682
msgid "Example"
msgstr "Örnek"

#: sphinx/ext/napoleon/docstring.py:683
msgid "Examples"
msgstr "Örnekler"

#: sphinx/ext/napoleon/docstring.py:744
msgid "Notes"
msgstr "Notlar"

#: sphinx/ext/napoleon/docstring.py:753
msgid "Other Parameters"
msgstr "Diğer Parametreler"

#: sphinx/ext/napoleon/docstring.py:789
msgid "Receives"
msgstr ""

#: sphinx/ext/napoleon/docstring.py:793
msgid "References"
msgstr "Kaynaklar"

#: sphinx/ext/napoleon/docstring.py:825
msgid "Warns"
msgstr "Uyarılar"

#: sphinx/ext/napoleon/docstring.py:829
msgid "Yields"
msgstr "Getiriler"

#: sphinx/ext/napoleon/docstring.py:987
#, python-format
msgid "invalid value set (missing closing brace): %s"
msgstr ""

#: sphinx/ext/napoleon/docstring.py:994
#, python-format
msgid "invalid value set (missing opening brace): %s"
msgstr ""

#: sphinx/ext/napoleon/docstring.py:1001
#, python-format
msgid "malformed string literal (missing closing quote): %s"
msgstr ""

#: sphinx/ext/napoleon/docstring.py:1008
#, python-format
msgid "malformed string literal (missing opening quote): %s"
msgstr ""

#: sphinx/locale/__init__.py:228
msgid "Attention"
msgstr "Dikkat"

#: sphinx/locale/__init__.py:229
msgid "Caution"
msgstr "Uyarı"

#: sphinx/locale/__init__.py:230
msgid "Danger"
msgstr "Tehlike"

#: sphinx/locale/__init__.py:231
msgid "Error"
msgstr "Hata"

#: sphinx/locale/__init__.py:232
msgid "Hint"
msgstr "İpucu"

#: sphinx/locale/__init__.py:233
msgid "Important"
msgstr "Önemli"

#: sphinx/locale/__init__.py:234
msgid "Note"
msgstr "Not"

#: sphinx/locale/__init__.py:235
msgid "See also"
msgstr "Ayrıca bakınız"

#: sphinx/locale/__init__.py:236
msgid "Tip"
msgstr "Tüyo"

#: sphinx/locale/__init__.py:237
msgid "Warning"
msgstr "Uyarı"

#: sphinx/templates/latex/longtable.tex_t:52
#: sphinx/templates/latex/sphinxmessages.sty_t:8
msgid "continued from previous page"
msgstr "önceki sayfadan devam"

#: sphinx/templates/latex/longtable.tex_t:63
#: sphinx/templates/latex/sphinxmessages.sty_t:9
msgid "continues on next page"
msgstr "sonraki sayfaya devam"

#: sphinx/templates/latex/sphinxmessages.sty_t:10
msgid "Non-alphabetical"
msgstr "Alfabetik olmayan"

#: sphinx/templates/latex/sphinxmessages.sty_t:12
msgid "Numbers"
msgstr "Numaralar"

#: sphinx/templates/latex/sphinxmessages.sty_t:13
msgid "page"
msgstr "sayfa"

#: sphinx/themes/agogo/layout.html:38 sphinx/themes/basic/globaltoc.html:10
#: sphinx/themes/basic/localtoc.html:12 sphinx/themes/scrolls/layout.html:41
msgid "Table of Contents"
msgstr "İçindekiler"

#: sphinx/themes/agogo/layout.html:43 sphinx/themes/basic/layout.html:141
#: sphinx/themes/basic/search.html:11 sphinx/themes/basic/search.html:23
msgid "Search"
msgstr "Ara"

#: sphinx/themes/agogo/layout.html:46 sphinx/themes/basic/searchbox.html:16
#: sphinx/themes/basic/searchfield.html:18
msgid "Go"
msgstr "Git"

#: sphinx/themes/agogo/layout.html:90 sphinx/themes/basic/sourcelink.html:15
msgid "Show Source"
msgstr "Kaynağı Göster"

#: sphinx/themes/basic/defindex.html:11
msgid "Overview"
msgstr "Genel Bakış"

#: sphinx/themes/basic/defindex.html:15
msgid "Welcome! This is"
msgstr "Hoş Geldiniz! Karşınızda"

#: sphinx/themes/basic/defindex.html:16
msgid "the documentation for"
msgstr "belgelendirme konusu: "

#: sphinx/themes/basic/defindex.html:17
msgid "last updated"
msgstr "son güncelleme"

#: sphinx/themes/basic/defindex.html:20
msgid "Indices and tables:"
msgstr "Dizinler ve tablolar:"

#: sphinx/themes/basic/defindex.html:23
msgid "Complete Table of Contents"
msgstr "Tam İçindekiler"

#: sphinx/themes/basic/defindex.html:24
msgid "lists all sections and subsections"
msgstr "tüm bölümleri ve alt bölümleri listeler"

#: sphinx/themes/basic/defindex.html:26
msgid "search this documentation"
msgstr "bu belgelendirmeyi ara"

#: sphinx/themes/basic/defindex.html:28
msgid "Global Module Index"
msgstr "Genel Modül Dizini"

#: sphinx/themes/basic/defindex.html:29
msgid "quick access to all modules"
msgstr "tüm modüllere hızlı erişim"

#: sphinx/themes/basic/defindex.html:31
msgid "all functions, classes, terms"
msgstr "tüm işlevler, sınıflar, terimler"

#: sphinx/themes/basic/genindex-single.html:33
#, python-format
msgid "Index &ndash; %(key)s"
msgstr "Dizin &ndash; %(key)s"

#: sphinx/themes/basic/genindex-single.html:61
#: sphinx/themes/basic/genindex-split.html:24
#: sphinx/themes/basic/genindex-split.html:38
#: sphinx/themes/basic/genindex.html:73
msgid "Full index on one page"
msgstr "Tek sayfada tam dizin"

#: sphinx/themes/basic/genindex-split.html:16
msgid "Index pages by letter"
msgstr "Harfe göre dizin sayfaları"

#: sphinx/themes/basic/genindex-split.html:25
msgid "can be huge"
msgstr "çok büyük olabilir"

#: sphinx/themes/basic/layout.html:26
msgid "Navigation"
msgstr "Gezinti"

#: sphinx/themes/basic/layout.html:126
#, python-format
msgid "Search within %(docstitle)s"
msgstr "%(docstitle)s içinde ara"

#: sphinx/themes/basic/layout.html:135
msgid "About these documents"
msgstr "Bu belgeler hakkında"

#: sphinx/themes/basic/layout.html:144 sphinx/themes/basic/layout.html:188
#: sphinx/themes/basic/layout.html:190
msgid "Copyright"
msgstr "Telif hakkı"

#: sphinx/themes/basic/layout.html:194 sphinx/themes/basic/layout.html:200
#, python-format
msgid "&#169; %(copyright_prefix)s %(copyright)s."
msgstr ""

#: sphinx/themes/basic/layout.html:212
#, python-format
msgid "Last updated on %(last_updated)s."
msgstr "Son güncelleme: %(last_updated)s."

#: sphinx/themes/basic/layout.html:215
#, python-format
msgid ""
"Created using <a href=\"https://www.sphinx-doc.org/\">Sphinx</a> "
"%(sphinx_version)s."
msgstr ""

#: sphinx/themes/basic/opensearch.xml:4
#, python-format
msgid "Search %(docstitle)s"
msgstr "%(docstitle)s ara"

#: sphinx/themes/basic/relations.html:12
msgid "Previous topic"
msgstr "Önceki konu"

#: sphinx/themes/basic/relations.html:14
msgid "previous chapter"
msgstr "önceki bölüm"

#: sphinx/themes/basic/relations.html:19
msgid "Next topic"
msgstr "Sonraki konu"

#: sphinx/themes/basic/relations.html:21
msgid "next chapter"
msgstr "sonraki bölüm"

#: sphinx/themes/basic/search.html:28
msgid ""
"Please activate JavaScript to enable the search\n"
"    functionality."
msgstr "Arama işlevini kullanabilmek için lütfen JavaScript'i\n    etkinleştirin."

#: sphinx/themes/basic/search.html:36
msgid ""
"Searching for multiple words only shows matches that contain\n"
"    all words."
msgstr ""

#: sphinx/themes/basic/search.html:43
msgid "search"
msgstr "ara"

#: sphinx/themes/basic/searchbox.html:12
msgid "Quick search"
msgstr "Hızlı Arama"

#: sphinx/themes/basic/sourcelink.html:12
msgid "This Page"
msgstr "Bu Sayfa"

#: sphinx/themes/basic/changes/frameset.html:5
#: sphinx/themes/basic/changes/versionchanges.html:12
#, python-format
msgid "Changes in Version %(version)s &#8212; %(docstitle)s"
msgstr "Sürüm %(version)s &#8212; %(docstitle)s içindeki Değişiklikler"

#: sphinx/themes/basic/changes/rstsource.html:5
#, python-format
msgid "%(filename)s &#8212; %(docstitle)s"
msgstr "%(filename)s &#8212; %(docstitle)s"

#: sphinx/themes/basic/changes/versionchanges.html:17
#, python-format
msgid "Automatically generated list of changes in version %(version)s"
msgstr "%(version)s sürümündeki değişikliklerin otomatik olarak üretilmiş listesi"

#: sphinx/themes/basic/changes/versionchanges.html:18
msgid "Library changes"
msgstr "Kütüphane değişiklikleri"

#: sphinx/themes/basic/changes/versionchanges.html:23
msgid "C API changes"
msgstr "C API'sindeki değişiklikler"

#: sphinx/themes/basic/changes/versionchanges.html:25
msgid "Other changes"
msgstr "Diğer değişiklikler"

#: sphinx/themes/basic/static/searchtools.js:112
msgid "Search Results"
msgstr "Arama Sonuçları"

#: sphinx/themes/basic/static/searchtools.js:114
msgid ""
"Your search did not match any documents. Please make sure that all words are"
" spelled correctly and that you've selected enough categories."
msgstr "Arama sonucunda herhangi bir belge bulunamadı. Bütün kelimeleri doğru yazdığınızdan ve gerekli bütün kategorileri seçtiğinizden emin olun."

#: sphinx/themes/basic/static/searchtools.js:118
msgid ""
"Search finished, found ${resultCount} page(s) matching the search query."
msgstr ""

#: sphinx/themes/basic/static/searchtools.js:246
msgid "Searching"
msgstr "Aranıyor"

#: sphinx/themes/basic/static/searchtools.js:262
msgid "Preparing search..."
msgstr "Aramaya hazırlanıyor..."

#: sphinx/themes/basic/static/searchtools.js:463
msgid ", in "
msgstr ", şunun içinde:"

#: sphinx/themes/basic/static/sphinx_highlight.js:112
msgid "Hide Search Matches"
msgstr "Arama Eşleşmelerini Gizle"

#: sphinx/themes/classic/layout.html:20
#: sphinx/themes/classic/static/sidebar.js_t:57
msgid "Collapse sidebar"
msgstr "Yan çubuğu daralt"

#: sphinx/themes/classic/static/sidebar.js_t:48
msgid "Expand sidebar"
msgstr "Yan çubuğu genişlet"

#: sphinx/themes/haiku/layout.html:24
msgid "Contents"
msgstr "İçindekiler"

#: sphinx/transforms/__init__.py:128
msgid "could not calculate translation progress!"
msgstr ""

#: sphinx/transforms/__init__.py:133
msgid "no translated elements!"
msgstr ""

#: sphinx/transforms/__init__.py:250
#, python-format
msgid ""
"4 column based index found. It might be a bug of extensions you use: %r"
msgstr ""

#: sphinx/transforms/__init__.py:291
#, python-format
msgid "Footnote [%s] is not referenced."
msgstr ""

#: sphinx/transforms/__init__.py:297
msgid "Footnote [#] is not referenced."
msgstr ""

#: sphinx/transforms/i18n.py:205 sphinx/transforms/i18n.py:270
msgid ""
"inconsistent footnote references in translated message. original: {0}, "
"translated: {1}"
msgstr ""

#: sphinx/transforms/i18n.py:245
msgid ""
"inconsistent references in translated message. original: {0}, translated: "
"{1}"
msgstr ""

#: sphinx/transforms/i18n.py:285
msgid ""
"inconsistent citation references in translated message. original: {0}, "
"translated: {1}"
msgstr ""

#: sphinx/transforms/i18n.py:302
msgid ""
"inconsistent term references in translated message. original: {0}, "
"translated: {1}"
msgstr ""

#: sphinx/transforms/post_transforms/__init__.py:116
msgid ""
"Could not determine the fallback text for the cross-reference. Might be a "
"bug."
msgstr ""

#: sphinx/transforms/post_transforms/__init__.py:158
#, python-format
msgid "more than one target found for 'any' cross-reference %r: could be %s"
msgstr ""

#: sphinx/transforms/post_transforms/__init__.py:209
#, python-format
msgid "%s:%s reference target not found: %s"
msgstr ""

#: sphinx/transforms/post_transforms/__init__.py:212
#, python-format
msgid "%r reference target not found: %s"
msgstr ""

#: sphinx/transforms/post_transforms/images.py:89
#, python-format
msgid "Could not fetch remote image: %s [%d]"
msgstr ""

#: sphinx/transforms/post_transforms/images.py:117
#, python-format
msgid "Could not fetch remote image: %s [%s]"
msgstr ""

#: sphinx/transforms/post_transforms/images.py:135
#, python-format
msgid "Unknown image format: %s..."
msgstr "Bilinmeyen resim biçimi: %s..."

#: sphinx/util/__init__.py:168
#, python-format
msgid "undecodable source characters, replacing with \"?\": %r"
msgstr ""

#: sphinx/util/display.py:78
msgid "skipped"
msgstr "atlandı"

#: sphinx/util/display.py:83
msgid "failed"
msgstr "başarısız oldu"

#: sphinx/util/docfields.py:87
#, python-format
msgid ""
"Problem in %s domain: field is supposed to use role '%s', but that role is "
"not in the domain."
msgstr ""

#: sphinx/util/docutils.py:295
#, python-format
msgid "unknown directive or role name: %s:%s"
msgstr ""

#: sphinx/util/docutils.py:591
#, python-format
msgid "unknown node type: %r"
msgstr ""

#: sphinx/util/i18n.py:94
#, python-format
msgid "reading error: %s, %s"
msgstr "okuma hatası: %s, %s"

#: sphinx/util/i18n.py:101
#, python-format
msgid "writing error: %s, %s"
msgstr "yazma hatası: %s, %s"

#: sphinx/util/i18n.py:125
#, python-format
msgid "locale_dir %s does not exist"
msgstr ""

#: sphinx/util/i18n.py:215
#, python-format
msgid ""
"Invalid date format. Quote the string by single quote if you want to output "
"it directly: %s"
msgstr ""

#: sphinx/util/nodes.py:386
#, python-format
msgid ""
"%r is deprecated for index entries (from entry %r). Use 'pair: %s' instead."
msgstr ""

#: sphinx/util/nodes.py:439
#, python-format
msgid "toctree contains ref to nonexisting file %r"
msgstr ""

#: sphinx/util/nodes.py:637
#, python-format
msgid "exception while evaluating only directive expression: %s"
msgstr ""

#: sphinx/util/rst.py:71
#, python-format
msgid "default role %s not found"
msgstr "varsayılan rol %s bulunamadı"

#: sphinx/writers/html5.py:100 sphinx/writers/html5.py:109
msgid "Link to this definition"
msgstr ""

#: sphinx/writers/html5.py:397
#, python-format
msgid "numfig_format is not defined for %s"
msgstr ""

#: sphinx/writers/html5.py:407
#, python-format
msgid "Any IDs not assigned for %s node"
msgstr ""

#: sphinx/writers/html5.py:462
msgid "Link to this term"
msgstr ""

#: sphinx/writers/html5.py:496 sphinx/writers/html5.py:501
msgid "Link to this heading"
msgstr ""

#: sphinx/writers/html5.py:505
msgid "Link to this table"
msgstr ""

#: sphinx/writers/html5.py:548
msgid "Link to this code"
msgstr ""

#: sphinx/writers/html5.py:550
msgid "Link to this image"
msgstr ""

#: sphinx/writers/html5.py:552
msgid "Link to this toctree"
msgstr ""

#: sphinx/writers/html5.py:688
msgid "Could not obtain image size. :scale: option is ignored."
msgstr ""

#: sphinx/writers/latex.py:335
#, python-format
msgid "unknown %r toplevel_sectioning for class %r"
msgstr ""

#: sphinx/writers/latex.py:386
msgid "too large :maxdepth:, ignored."
msgstr ""

#: sphinx/writers/latex.py:625
msgid "document title is not a single Text node"
msgstr ""

#: sphinx/writers/latex.py:656 sphinx/writers/texinfo.py:626
msgid ""
"encountered title node not in section, topic, table, admonition or sidebar"
msgstr ""

#: sphinx/writers/latex.py:959 sphinx/writers/manpage.py:259
#: sphinx/writers/texinfo.py:641
msgid "Footnotes"
msgstr "Dipnotlar"

#: sphinx/writers/latex.py:1028
msgid ""
"both tabularcolumns and :widths: option are given. :widths: is ignored."
msgstr ""

#: sphinx/writers/latex.py:1388
#, python-format
msgid "dimension unit %s is invalid. Ignored."
msgstr ""

#: sphinx/writers/latex.py:1722
#, python-format
msgid "unknown index entry type %s found"
msgstr ""

#: sphinx/writers/manpage.py:305 sphinx/writers/text.py:907
#, python-format
msgid "[image: %s]"
msgstr "[resim: %s]"

#: sphinx/writers/manpage.py:306 sphinx/writers/text.py:908
msgid "[image]"
msgstr "[resim]"

#: sphinx/writers/texinfo.py:1197
msgid "caption not inside a figure."
msgstr ""

#: sphinx/writers/texinfo.py:1284
#, python-format
msgid "unimplemented node type: %r"
msgstr ""
