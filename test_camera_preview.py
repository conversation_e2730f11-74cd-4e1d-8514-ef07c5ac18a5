#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
摄像头预览功能测试脚本
测试新增的实时摄像头预览窗口功能
"""

import time
import logging
from database import DatabaseManager
from camera_monitor import CameraMonitor

# 配置日志
logging.basicConfig(level=logging.INFO)

def test_camera_preview():
    """测试摄像头预览功能"""
    print("=" * 50)
    print("摄像头预览功能测试 (修复版)")
    print("=" * 50)
    print("功能说明:")
    print("- 在左上角显示实时摄像头预览窗口")
    print("- 窗口尺寸: 320x240 像素")
    print("- 独立线程运行，不会阻塞主程序")
    print("- 显示二维码检测框和状态信息")
    print("- 可以手动关闭预览窗口")
    print("- 关闭后需要重启程序才能重新开启")
    print("=" * 50)
    
    try:
        # 初始化数据库管理器
        db = DatabaseManager()
        print("✓ 数据库连接成功")
        
        # 初始化摄像头监控
        camera = CameraMonitor(db)
        print("✓ 摄像头监控初始化成功")
        
        # 启动摄像头监控
        if camera.start_monitoring():
            print("✓ 摄像头监控已启动")
            print("✓ 预览窗口应该已在左上角显示")
            print()
            print("测试说明:")
            print("1. 预览窗口在独立线程中运行，不会阻塞主程序")
            print("2. 如果摄像头未准备好，会显示'Waiting for camera...'")
            print("3. 预览窗口显示实时摄像头画面")
            print("4. 当检测到二维码时，会显示绿色边框")
            print("5. 窗口顶部显示摄像头状态和检测到的二维码数量")
            print("6. 窗口底部显示当前时间")
            print("7. 可以点击窗口的X按钮关闭预览")
            print("8. 关闭预览后，程序继续运行但不再显示预览")
            print()
            print("修复内容:")
            print("- 预览窗口现在在独立线程中运行")
            print("- 解决了窗口无响应的问题")
            print("- 添加了等待画面显示")
            print("- 优化了线程同步机制")
            print()
            print("按 Ctrl+C 停止测试...")
            
            # 运行测试
            try:
                while True:
                    time.sleep(1)
                    
                    # 检查预览窗口状态
                    if camera.preview_closed:
                        print("检测到预览窗口已被关闭")
                        print("摄像头监控继续运行，但预览已禁用")
                        break
                        
            except KeyboardInterrupt:
                print("\n用户中断测试")
            
            # 停止监控
            camera.stop_monitoring()
            print("✓ 摄像头监控已停止")
            
        else:
            print("✗ 摄像头监控启动失败")
            print("请检查:")
            print("- 摄像头是否正确连接")
            print("- 摄像头是否被其他程序占用")
            print("- config.py中的CAMERA_INDEX是否正确")
        
        db.close()
        print("✓ 数据库连接已关闭")
        
    except Exception as e:
        print(f"✗ 测试过程中出错: {e}")
        logging.error(f"测试摄像头预览时出错: {e}")

def test_preview_config():
    """测试预览配置"""
    print("\n" + "=" * 50)
    print("预览配置测试")
    print("=" * 50)
    
    from config import (CAMERA_PREVIEW_ENABLED, CAMERA_PREVIEW_SIZE, 
                       CAMERA_PREVIEW_POSITION, CAMERA_INDEX)
    
    print(f"预览启用状态: {CAMERA_PREVIEW_ENABLED}")
    print(f"预览窗口尺寸: {CAMERA_PREVIEW_SIZE}")
    print(f"预览窗口位置: {CAMERA_PREVIEW_POSITION}")
    print(f"摄像头索引: {CAMERA_INDEX}")
    
    if not CAMERA_PREVIEW_ENABLED:
        print("注意: 预览功能已在配置中禁用")
        print("要启用预览，请在config.py中设置 CAMERA_PREVIEW_ENABLED = True")

def main():
    """主测试函数"""
    print("DataTransmission 摄像头预览功能测试")
    print("测试新增的实时预览窗口功能")
    print()
    
    # 测试配置
    test_preview_config()
    
    # 询问是否继续测试
    try:
        response = input("\n是否开始摄像头预览测试? (y/N): ")
        if response.lower() != 'y':
            print("测试已取消")
            return
    except KeyboardInterrupt:
        print("\n测试已取消")
        return
    
    # 运行预览测试
    test_camera_preview()
    
    print("\n" + "=" * 50)
    print("测试完成")
    print("=" * 50)
    print("如果预览功能正常:")
    print("- 应该看到左上角的小预览窗口")
    print("- 窗口显示实时摄像头画面")
    print("- 检测到二维码时显示绿色边框")
    print("- 可以手动关闭预览窗口")
    print()
    print("如果遇到问题:")
    print("- 检查摄像头连接和驱动")
    print("- 确认config.py中的摄像头配置")
    print("- 查看控制台的错误信息")

if __name__ == "__main__":
    main()
