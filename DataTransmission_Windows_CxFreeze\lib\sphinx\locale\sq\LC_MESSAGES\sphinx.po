# Translations template for Sphinx.
# Copyright (C) 2024 ORGANIZATION
# This file is distributed under the same license as the Sphinx project.
# 
# Translators:
# <AUTHOR> <EMAIL>, 2021-2024
msgid ""
msgstr ""
"Project-Id-Version: Sphinx\n"
"Report-Msgid-Bugs-To: EMAIL@ADDRESS\n"
"POT-Creation-Date: 2024-04-14 23:27+0000\n"
"PO-Revision-Date: 2013-04-02 08:44+0000\n"
"Last-Translator: Besnik Bleta <<EMAIL>>, 2021-2024\n"
"Language-Team: Albanian (http://app.transifex.com/sphinx-doc/sphinx-1/language/sq/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.14.0\n"
"Language: sq\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#: sphinx/application.py:157
#, python-format
msgid "Cannot find source directory (%s)"
msgstr "S’gjendet dot drejtori burim (%s)"

#: sphinx/application.py:161
#, python-format
msgid "Output directory (%s) is not a directory"
msgstr "Drejtoria e përfundimeve (%s) s’është drejtori"

#: sphinx/application.py:165
msgid "Source directory and destination directory cannot be identical"
msgstr "Drejtoria burim dhe drejtoria vendmbërritje s’mund të jenë identike"

#: sphinx/application.py:197
#, python-format
msgid "Running Sphinx v%s"
msgstr "Po xhirohet Sphinx v%s"

#: sphinx/application.py:219
#, python-format
msgid ""
"This project needs at least Sphinx v%s and therefore cannot be built with "
"this version."
msgstr "Ky projekt lyp të paktën Sphinx v%s, ndaj s’mund të montohet me këtë version."

#: sphinx/application.py:235
msgid "making output directory"
msgstr "po krijohet drejtori përfundimesh"

#: sphinx/application.py:240 sphinx/registry.py:450
#, python-format
msgid "while setting up extension %s:"
msgstr "teksa ujdiset zgjerimi %s:"

#: sphinx/application.py:246
msgid ""
"'setup' as currently defined in conf.py isn't a Python callable. Please "
"modify its definition to make it a callable function. This is needed for "
"conf.py to behave as a Sphinx extension."
msgstr "'setup' siç është përcaktuar aktualisht te conf.py s’është funksion Python që mund të thirret. Ju lutemi, ndryshojeni përcaktimin e tij që ta bëni një funksion që mund të thirret. Kjo është e nevojshme që conf.py të sillet si një zgjerim Sphinx."

#: sphinx/application.py:277
#, python-format
msgid "loading translations [%s]... "
msgstr "po ngarkohen përkthime [%s]… "

#: sphinx/application.py:294 sphinx/util/display.py:85
msgid "done"
msgstr "u bë"

#: sphinx/application.py:296
msgid "not available for built-in messages"
msgstr "s’është i passhëm për mesazhe të brendshëm"

#: sphinx/application.py:310
msgid "loading pickled environment"
msgstr ""

#: sphinx/application.py:318
#, python-format
msgid "failed: %s"
msgstr "dështoi: %s"

#: sphinx/application.py:332
msgid "No builder selected, using default: html"
msgstr "S’u përzgjodh montues, po përdoret parazgjedhja: html"

#: sphinx/application.py:365
msgid "succeeded"
msgstr "doli me sukses"

#: sphinx/application.py:366
msgid "finished with problems"
msgstr "u përfundua me probleme"

#: sphinx/application.py:370
#, python-format
msgid "build %s, %s warning (with warnings treated as errors)."
msgstr "montimi %s, % sinjalizim (me sinjalizime të trajtuara si gabime)."

#: sphinx/application.py:372
#, python-format
msgid "build %s, %s warnings (with warnings treated as errors)."
msgstr "montimi %s, %s sinjalizime (me sinjalizime të trajtuara si gabime)."

#: sphinx/application.py:375
#, python-format
msgid "build %s, %s warning."
msgstr "build %s, %s warning."

#: sphinx/application.py:377
#, python-format
msgid "build %s, %s warnings."
msgstr "montimi %s, %s sinjalizime."

#: sphinx/application.py:381
#, python-format
msgid "build %s."
msgstr "montimi %s."

#: sphinx/application.py:610
#, python-format
msgid "node class %r is already registered, its visitors will be overridden"
msgstr "klasa %r e nyjeve është e regjistruar tashmë, vizitorët e saj do të anashkalohen"

#: sphinx/application.py:689
#, python-format
msgid "directive %r is already registered, it will be overridden"
msgstr "direktiva %r është e regjistruar tashmë, do të anashkalohet"

#: sphinx/application.py:711 sphinx/application.py:733
#, python-format
msgid "role %r is already registered, it will be overridden"
msgstr "roli %r është e regjistruar tashmë, do të anashkalohet"

#: sphinx/application.py:1282
#, python-format
msgid ""
"the %s extension does not declare if it is safe for parallel reading, "
"assuming it isn't - please ask the extension author to check and make it "
"explicit"
msgstr "zgjerimi %s nuk deklaron nëse është i parrezik për lexim paralel, po merret se s’është - ju lutemi, kërkojini autorin të zgjerimit ta kontrollojë dhe ta bëjë këtë shprehimisht"

#: sphinx/application.py:1286
#, python-format
msgid "the %s extension is not safe for parallel reading"
msgstr "zgjerimi %s s’është i sigurt për lexim paralel"

#: sphinx/application.py:1289
#, python-format
msgid ""
"the %s extension does not declare if it is safe for parallel writing, "
"assuming it isn't - please ask the extension author to check and make it "
"explicit"
msgstr "zgjerimi %s nuk deklaron nëse është i parrezik për shkrim paralel, po merret se s’është - ju lutemi, kërkojini autorin të zgjerimit ta kontrollojë dhe ta bëjë këtë shprehimisht"

#: sphinx/application.py:1293
#, python-format
msgid "the %s extension is not safe for parallel writing"
msgstr "zgjerimi %s s’është i sigurt për shkrim paralel"

#: sphinx/application.py:1301 sphinx/application.py:1305
#, python-format
msgid "doing serial %s"
msgstr ""

#: sphinx/config.py:309
#, python-format
msgid "config directory doesn't contain a conf.py file (%s)"
msgstr "drejtoria e formësimeve nuk përmban një kartelë conf.py (%s)"

#: sphinx/config.py:318
msgid ""
"Invalid configuration value found: 'language = None'. Update your "
"configuration to a valid language code. Falling back to 'en' (English)."
msgstr "U gjet vlerë e pavlefshme formësimi: 'language = Asnjë'. Përditësojeni formësimin tuaj me një kod të vlefshëm gjuhe. Përkohësisht po përdoret 'en' (anglisht)."

#: sphinx/config.py:341
#, python-format
msgid ""
"cannot override dictionary config setting %r, ignoring (use %r to set "
"individual elements)"
msgstr "s’mund të anashkalohet rregullim formësimi fjalorthi %r, po shpërfillet (për të ujdisur elemente individuale, përdorni %r)"

#: sphinx/config.py:350
#, python-format
msgid "invalid number %r for config value %r, ignoring"
msgstr "numër %r i pavlefshëm për vlerë formësimi %r, po shpërfillet"

#: sphinx/config.py:355
#, python-format
msgid "cannot override config setting %r with unsupported type, ignoring"
msgstr "s’mund të anashkalohet rregullim formësimi %r me një lloj të pambuluar, po shpërfillet"

#: sphinx/config.py:378
#, python-format
msgid "unknown config value %r in override, ignoring"
msgstr "vlerë e panjohur formësimi %r te anashkalimi, po shpërfillet"

#: sphinx/config.py:418
#, python-format
msgid "No such config value: %r"
msgstr "S’ka vlerë të tillë formësimi: %r"

#: sphinx/config.py:440
#, python-format
msgid "Config value %r already present"
msgstr "Vlerë formësimi %r e pranishme tashmë"

#: sphinx/config.py:473
#, python-format
msgid "cannot cache unpickable configuration value: %r"
msgstr ""

#: sphinx/config.py:509
#, python-format
msgid "There is a syntax error in your configuration file: %s\n"
msgstr "Ka një gabim sintakse te kartela juaj e formësimit: %s\n"

#: sphinx/config.py:512
msgid ""
"The configuration file (or one of the modules it imports) called sys.exit()"
msgstr "Kartela e formësimit (ose një nga modulet që ajo importon) thirri sys.exit()"

#: sphinx/config.py:519
#, python-format
msgid ""
"There is a programmable error in your configuration file:\n"
"\n"
"%s"
msgstr "Ka një gabim të programueshëm te kartela juaj e formësimit:\n\n%s"

#: sphinx/config.py:540
#, python-format
msgid "Failed to convert %r to a set or tuple"
msgstr ""

#: sphinx/config.py:565
#, python-format
msgid ""
"The config value `source_suffix' expects a string, list of strings, or "
"dictionary. But `%r' is given."
msgstr "Vlera e formësimit `source_suffix' pret një varg, një listë vargjesh, ose një fjalor. Por është dhënë `%r'."

#: sphinx/config.py:585
#, python-format
msgid "Section %s"
msgstr "Ndarja %s"

#: sphinx/config.py:586
#, python-format
msgid "Fig. %s"
msgstr "Figura %s"

#: sphinx/config.py:587
#, python-format
msgid "Table %s"
msgstr "Tabela %s"

#: sphinx/config.py:588
#, python-format
msgid "Listing %s"
msgstr ""

#: sphinx/config.py:663
msgid ""
"The config value `{name}` has to be a one of {candidates}, but `{current}` "
"is given."
msgstr "Vlera e formësimit `{name}` duhet të jetë një nga {candidates}, por është dhënë `{current}`."

#: sphinx/config.py:687
msgid ""
"The config value `{name}' has type `{current.__name__}'; expected "
"{permitted}."
msgstr "Vlera e formësimit `{name}' është e llojit `{current.__name__}'; pritej {permitted}."

#: sphinx/config.py:700
msgid ""
"The config value `{name}' has type `{current.__name__}', defaults to "
"`{default.__name__}'."
msgstr "Vlera e formësimit `{name}' është e llojit `{current.__name__}', si parazgjedhje merr `{default.__name__}'."

#: sphinx/config.py:711
#, python-format
msgid "primary_domain %r not found, ignored."
msgstr "s’u gjet primary_domain %r, po shpërfillet."

#: sphinx/config.py:723
msgid ""
"Since v2.0, Sphinx uses \"index\" as root_doc by default. Please add "
"\"root_doc = 'contents'\" to your conf.py."
msgstr "Që prej v2.0, Sphinx përdor \"index\" për root_doc, si parazgjedhje. Ju lutemi, shtoni \"root_doc = 'contents'\" te conf.py juaj."

#: sphinx/events.py:64
#, python-format
msgid "Event %r already present"
msgstr "Vlerë formësimi %r e pranishme tashmë"

#: sphinx/events.py:70
#, python-format
msgid "Unknown event name: %s"
msgstr "Emër i panjohur akti: %s"

#: sphinx/events.py:109
#, python-format
msgid "Handler %r for event %r threw an exception"
msgstr "Trajtuesi %r për aktin %r u përgjigj me një përjashtim"

#: sphinx/extension.py:55
#, python-format
msgid ""
"The %s extension is required by needs_extensions settings, but it is not "
"loaded."
msgstr "Zgjerimi %s është i domosdoshëm për needs_extensions settings, por s’është ngarkuar."

#: sphinx/extension.py:76
#, python-format
msgid ""
"This project needs the extension %s at least in version %s and therefore "
"cannot be built with the loaded version (%s)."
msgstr "Ky projekt lyp zgjerimin %s të paktën nën versionin %s dhe prandaj s’mund të montohet me versionin e ngarkuar (%s)."

#: sphinx/highlighting.py:155
#, python-format
msgid "Pygments lexer name %r is not known"
msgstr ""

#: sphinx/highlighting.py:189
#, python-format
msgid ""
"Lexing literal_block %r as \"%s\" resulted in an error at token: %r. "
"Retrying in relaxed mode."
msgstr ""

#: sphinx/project.py:66
#, python-format
msgid ""
"multiple files found for the document \"%s\": %r\n"
"Use %r for the build."
msgstr "u gjetën shumë kartela për dokumentin \"%s\": %r\nPërdorni %r për montimin."

#: sphinx/project.py:81
#, python-format
msgid "Ignored unreadable document %r."
msgstr "U shpërfill dokument i palexueshëm %r."

#: sphinx/registry.py:142
#, python-format
msgid "Builder class %s has no \"name\" attribute"
msgstr "Klasa %s e montuesit nuk ka atribut \"name\""

#: sphinx/registry.py:144
#, python-format
msgid "Builder %r already exists (in module %s)"
msgstr "Montuesi %r ekziston tashmë (te moduli %s)"

#: sphinx/registry.py:157
#, python-format
msgid "Builder name %s not registered or available through entry point"
msgstr "Emër %s montuesi jo i regjistruar ose i passhëm përmes pike hyrjeje"

#: sphinx/registry.py:164
#, python-format
msgid "Builder name %s not registered"
msgstr "Emër montuesi %s jo i regjistruar"

#: sphinx/registry.py:171
#, python-format
msgid "domain %s already registered"
msgstr "përkatësi %s e regjistruar tashmë"

#: sphinx/registry.py:194 sphinx/registry.py:207 sphinx/registry.py:218
#, python-format
msgid "domain %s not yet registered"
msgstr "përkatësi %s ende e paregjistruar"

#: sphinx/registry.py:198
#, python-format
msgid "The %r directive is already registered to domain %s"
msgstr "Direktiva %r është e regjistruar tashmë te përkatësia %s"

#: sphinx/registry.py:210
#, python-format
msgid "The %r role is already registered to domain %s"
msgstr "Roli %r është i regjistruar tashmë te përkatësia %s"

#: sphinx/registry.py:221
#, python-format
msgid "The %r index is already registered to domain %s"
msgstr "Treguesi %r është i regjistruar tashmë te përkatësia %s"

#: sphinx/registry.py:252
#, python-format
msgid "The %r object_type is already registered"
msgstr "object_type %r është i regjistruar tashmë"

#: sphinx/registry.py:278
#, python-format
msgid "The %r crossref_type is already registered"
msgstr "crossref_type %r është i regjistruar tashmë"

#: sphinx/registry.py:285
#, python-format
msgid "source_suffix %r is already registered"
msgstr "source_suffix %r është i regjistruar tashmë"

#: sphinx/registry.py:294
#, python-format
msgid "source_parser for %r is already registered"
msgstr "source_parser për %r është i regjistruar tashmë"

#: sphinx/registry.py:302
#, python-format
msgid "Source parser for %s not registered"
msgstr "Përtypës burimesh për %s jo i regjistruar"

#: sphinx/registry.py:318
#, python-format
msgid "Translator for %r already exists"
msgstr "Përkthyesi për %r ekziston tashmë"

#: sphinx/registry.py:334
#, python-format
msgid "kwargs for add_node() must be a (visit, depart) function tuple: %r=%r"
msgstr "kwargs për add_node() duhet të jetë një çift funksioni (visit, depart): %r=%r"

#: sphinx/registry.py:417
#, python-format
msgid "enumerable_node %r already registered"
msgstr "enumerable_node %r tashmë i regjistruar"

#: sphinx/registry.py:429
#, python-format
msgid "math renderer %s is already registered"
msgstr "vizatuesi i formulave matematikore %s është i regjistruar tashmë"

#: sphinx/registry.py:444
#, python-format
msgid ""
"the extension %r was already merged with Sphinx since version %s; this "
"extension is ignored."
msgstr "zgjerimi %r qe shkrirë me Sphinx-in që me versionin %s; ky zgjerim është shpërfillur."

#: sphinx/registry.py:455
msgid "Original exception:\n"
msgstr "Përjashtimi origjinal:\n"

#: sphinx/registry.py:456
#, python-format
msgid "Could not import extension %s"
msgstr "S’u importua dot zgjerimi %s"

#: sphinx/registry.py:461
#, python-format
msgid ""
"extension %r has no setup() function; is it really a Sphinx extension "
"module?"
msgstr "zgjerimi %r s’ka funksion setup(); a është vërtet një modul zgjerimi Sphinx-i?"

#: sphinx/registry.py:470
#, python-format
msgid ""
"The %s extension used by this project needs at least Sphinx v%s; it "
"therefore cannot be built with this version."
msgstr "Zgjerimi %s i përdorur nga ky projekt lyp të paktën Sphinx v%s; prandaj s’mund të montohet me këtë version."

#: sphinx/registry.py:478
#, python-format
msgid ""
"extension %r returned an unsupported object from its setup() function; it "
"should return None or a metadata dictionary"
msgstr "zgjerimi %r u përgjigj me një objekt të pambuluar prej funksionit të vet setup(); duhet të përgjigjet me Asnjë ose një fjalorth tejtëdhënash"

#: sphinx/roles.py:201
#, python-format
msgid "Python Enhancement Proposals; PEP %s"
msgstr "Python Enhancement Proposals; PEP %s"

#: sphinx/roles.py:222
#, python-format
msgid "invalid PEP number %s"
msgstr "numër PEP i pavlefshëm %s"

#: sphinx/roles.py:257
#, python-format
msgid "invalid RFC number %s"
msgstr "numër RFC i pavlefshëm %s"

#: sphinx/theming.py:125
#, python-format
msgid "setting %s.%s occurs in none of the searched theme configs"
msgstr "rregullimi %s.%s nuk haset në asnjë prej formësimeve temash ku u kërkua"

#: sphinx/theming.py:140
#, python-format
msgid "unsupported theme option %r given"
msgstr "është dhënë mundësi teme %r e pambuluar"

#: sphinx/theming.py:206
#, python-format
msgid "file %r on theme path is not a valid zipfile or contains no theme"
msgstr "kartela %r te shteg teme s’është kartelë zip e vlefshme ose nuk përmban temë"

#: sphinx/theming.py:226
#, python-format
msgid "no theme named %r found (missing theme.toml?)"
msgstr "s’u gjet temë e emërtuar %r (mungon theme.toml?)"

#: sphinx/theming.py:259
#, python-format
msgid "The %r theme has circular inheritance"
msgstr "Tema %r ka trashëgimi rrethore"

#: sphinx/theming.py:262
#, python-format
msgid ""
"The %r theme inherits from %r, which is not a loaded theme. Loaded themes "
"are: %s"
msgstr "Tema %r trashëgon nga %r, e cila s’është temë e ngarkuar. Temat e ngarkuara janë: %s"

#: sphinx/theming.py:269
#, python-format
msgid "The %r theme has too many ancestors"
msgstr "Tema %r ka shumë paraardhës"

#: sphinx/theming.py:295
#, python-format
msgid "no theme configuration file found in %r"
msgstr "s’u gjet kartelë formësimi teme në %r"

#: sphinx/theming.py:323 sphinx/theming.py:374
#, python-format
msgid "theme %r doesn't have the \"theme\" table"
msgstr "tema %r s’ka tabelën “theme”"

#: sphinx/theming.py:327
#, python-format
msgid "The %r theme \"[theme]\" table is not a table"
msgstr "Tabela “[theme]” e temës %r s’është tabelë"

#: sphinx/theming.py:331 sphinx/theming.py:377
#, python-format
msgid "The %r theme must define the \"theme.inherit\" setting"
msgstr "Tema %r duhet të përcaktojë rregullimin “theme.inherit”"

#: sphinx/theming.py:335
#, python-format
msgid "The %r theme \"[options]\" table is not a table"
msgstr "Tablea “[options]” e temës %r s’është tabelë"

#: sphinx/theming.py:353
#, python-format
msgid "The \"theme.pygments_style\" setting must be a table. Hint: \"%s\""
msgstr "Rregullimi “theme.pygments_style” duhet të jetë një tabelë. Ndihmëz: “%s”"

#: sphinx/builders/__init__.py:183
#, python-format
msgid "a suitable image for %s builder not found: %s (%s)"
msgstr "s’u gjet figurë e përshtatshme për montuesin %s: %s (%s)"

#: sphinx/builders/__init__.py:187
#, python-format
msgid "a suitable image for %s builder not found: %s"
msgstr "s’u gjet figurë e përshtatshme për montuesin %s: %s"

#: sphinx/builders/__init__.py:207
msgid "building [mo]: "
msgstr "po montohet [mo]: "

#: sphinx/builders/__init__.py:208 sphinx/builders/__init__.py:574
#: sphinx/builders/__init__.py:601
msgid "writing output... "
msgstr "po shkruhet përfundim… "

#: sphinx/builders/__init__.py:217
#, python-format
msgid "all of %d po files"
msgstr "krejt kartelat po %d"

#: sphinx/builders/__init__.py:235
#, python-format
msgid "targets for %d po files that are specified"
msgstr "objektiva për kartela po %d që janë specifikuar"

#: sphinx/builders/__init__.py:243
#, python-format
msgid "targets for %d po files that are out of date"
msgstr "objektiva për kartela po %d që janë të papërditësuara"

#: sphinx/builders/__init__.py:252
msgid "all source files"
msgstr "krejt kartelat burim"

#: sphinx/builders/__init__.py:262
#, python-format
msgid "file %r given on command line does not exist, "
msgstr "kartela %r, dhënë te rreshti i urdhrave, nuk ekziston, "

#: sphinx/builders/__init__.py:267
#, python-format
msgid ""
"file %r given on command line is not under the source directory, ignoring"
msgstr "kartela %r e dhënë te rresht urdhrash s’gjendet te drejtori burim, po shpërfillet"

#: sphinx/builders/__init__.py:273
#, python-format
msgid "file %r given on command line is not a valid document, ignoring"
msgstr "kartela %r, dhënë te rreshti i urdhrave, s’është dokument i vlefshëm, po shpërfillet"

#: sphinx/builders/__init__.py:282
#, python-format
msgid "%d source files given on command line"
msgstr "kartela burim %d dhënë te rresht urdhrash"

#: sphinx/builders/__init__.py:294
#, python-format
msgid "targets for %d source files that are out of date"
msgstr "objektiva për kartela burim %d që janë të papërditësuara"

#: sphinx/builders/__init__.py:309 sphinx/builders/gettext.py:243
#, python-format
msgid "building [%s]: "
msgstr "po montohet [%s]: "

#: sphinx/builders/__init__.py:316
msgid "looking for now-outdated files... "
msgstr "po shihet për kartela të sapovjetruara… "

#: sphinx/builders/__init__.py:320
#, python-format
msgid "%d found"
msgstr "U gjet %d"

#: sphinx/builders/__init__.py:322
msgid "none found"
msgstr "s’u gjet gjë"

#: sphinx/builders/__init__.py:327
msgid "pickling environment"
msgstr ""

#: sphinx/builders/__init__.py:333
msgid "checking consistency"
msgstr "po kontrollohet njëtrajtshmëria"

#: sphinx/builders/__init__.py:337
msgid "no targets are out of date."
msgstr "s’ka objektiva të vjetruar."

#: sphinx/builders/__init__.py:376
msgid "updating environment: "
msgstr "po përditësohet mjedisi: "

#: sphinx/builders/__init__.py:397
#, python-format
msgid "%s added, %s changed, %s removed"
msgstr "%s të shtuar, %s të ndryshuar, %s të hequr"

#: sphinx/builders/__init__.py:435 sphinx/builders/__init__.py:447
msgid "reading sources... "
msgstr "po lexohen burime… "

#: sphinx/builders/__init__.py:549
#, python-format
msgid "docnames to write: %s"
msgstr "emra dokumentesh për shkrim: %s"

#: sphinx/builders/__init__.py:558 sphinx/builders/singlehtml.py:157
msgid "preparing documents"
msgstr "po përgatiten dokumente"

#: sphinx/builders/__init__.py:561
msgid "copying assets"
msgstr ""

#: sphinx/builders/_epub_base.py:215
#, python-format
msgid "duplicated ToC entry found: %s"
msgstr "u gjet zë TeL i përsëdytur: %s"

#: sphinx/builders/_epub_base.py:404 sphinx/builders/html/__init__.py:758
#: sphinx/builders/latex/__init__.py:432 sphinx/builders/texinfo.py:187
msgid "copying images... "
msgstr "po kopjohen figura… "

#: sphinx/builders/_epub_base.py:411
#, python-format
msgid "cannot read image file %r: copying it instead"
msgstr "s’lexohet dot kartelë figure %r: në vend të tij, po kopjohet"

#: sphinx/builders/_epub_base.py:417 sphinx/builders/html/__init__.py:766
#: sphinx/builders/latex/__init__.py:440 sphinx/builders/texinfo.py:197
#, python-format
msgid "cannot copy image file %r: %s"
msgstr "s’kopjohet dot kartelë figure %r: %s"

#: sphinx/builders/_epub_base.py:434
#, python-format
msgid "cannot write image file %r: %s"
msgstr "s’shkruhet dot kartelë figure %r: %s"

#: sphinx/builders/_epub_base.py:444
msgid "Pillow not found - copying image files"
msgstr "S’u gjet Pillow - po kopjohen kartela figurë"

#: sphinx/builders/_epub_base.py:470
msgid "writing mimetype file..."
msgstr "po shkruhet kartelë llojesh MIME…"

#: sphinx/builders/_epub_base.py:475
msgid "writing META-INF/container.xml file..."
msgstr "po shkruhet kartelë META-INF/container.xml…"

#: sphinx/builders/_epub_base.py:508
msgid "writing content.opf file..."
msgstr "po shkruhet kartelë content.opf…"

#: sphinx/builders/_epub_base.py:539
#, python-format
msgid "unknown mimetype for %s, ignoring"
msgstr "lloj MIME i panjohur për %s, po shpërfillet"

#: sphinx/builders/_epub_base.py:686
msgid "writing toc.ncx file..."
msgstr "po shkruhet kartelë toc.ncx…"

#: sphinx/builders/_epub_base.py:711
#, python-format
msgid "writing %s file..."
msgstr "po shkruhet kartelë %s…"

#: sphinx/builders/changes.py:32
#, python-format
msgid "The overview file is in %(outdir)s."
msgstr "Kartela përmbledhje gjendet te %(outdir)s."

#: sphinx/builders/changes.py:59
#, python-format
msgid "no changes in version %s."
msgstr "s’ka ndryshime në version %s."

#: sphinx/builders/changes.py:61
msgid "writing summary file..."
msgstr "po shkruhet kartelë përmbledhje…"

#: sphinx/builders/changes.py:76
msgid "Builtins"
msgstr "Të brendshme"

#: sphinx/builders/changes.py:78
msgid "Module level"
msgstr "Shkallë moduli"

#: sphinx/builders/changes.py:123
msgid "copying source files..."
msgstr "po kopjohen kartela burim…"

#: sphinx/builders/changes.py:130
#, python-format
msgid "could not read %r for changelog creation"
msgstr "s’u lexua dot %r për krijim regjistrimi ndryshimesh"

#: sphinx/builders/dummy.py:19
msgid "The dummy builder generates no files."
msgstr "Montuesi provë nuk prodhon kartela."

#: sphinx/builders/epub3.py:81
#, python-format
msgid "The ePub file is in %(outdir)s."
msgstr "Kartela ePub gjendet te %(outdir)s."

#: sphinx/builders/epub3.py:185
msgid "writing nav.xhtml file..."
msgstr "po shkruhet kartelë nav.xhtml…"

#: sphinx/builders/epub3.py:211
msgid "conf value \"epub_language\" (or \"language\") should not be empty for EPUB3"
msgstr "vlera e formësimit \"epub_language\" (ose \"language\") s’duhet të jetë e zbrazët për EPUB3"

#: sphinx/builders/epub3.py:215
msgid "conf value \"epub_uid\" should be XML NAME for EPUB3"
msgstr "vlera e formësimit \"epub_uid\" duhet të jetë XML NAME për EPUB3"

#: sphinx/builders/epub3.py:218
msgid "conf value \"epub_title\" (or \"html_title\") should not be empty for EPUB3"
msgstr "vlera e formësimit \"epub_title\" (ose \"html_title\") s’duhet të jetë e zbrazët për EPUB3"

#: sphinx/builders/epub3.py:222
msgid "conf value \"epub_author\" should not be empty for EPUB3"
msgstr "vlera e formësimit \"epub_author\" s’duhet të jetë e zbrazët për EPUB3"

#: sphinx/builders/epub3.py:225
msgid "conf value \"epub_contributor\" should not be empty for EPUB3"
msgstr "vlera e formësimit \"epub_contributor\" s’duhet të jetë e zbrazët për EPUB3"

#: sphinx/builders/epub3.py:228
msgid "conf value \"epub_description\" should not be empty for EPUB3"
msgstr "vlera e formësimit \"epub_description\" s’duhet të jetë e zbrazët për EPUB3"

#: sphinx/builders/epub3.py:231
msgid "conf value \"epub_publisher\" should not be empty for EPUB3"
msgstr "vlera e formësimit \"epub_publisher\" s’duhet të jetë e zbrazët për EPUB3"

#: sphinx/builders/epub3.py:234
msgid "conf value \"epub_copyright\" (or \"copyright\")should not be empty for EPUB3"
msgstr "vlera e formësimit \"epub_copyright\" (ose \"copyright\") s’duhet të jetë e zbrazët për EPUB3"

#: sphinx/builders/epub3.py:238
msgid "conf value \"epub_identifier\" should not be empty for EPUB3"
msgstr "vlera e formësimit \"epub_identifier\" s’duhet të jetë e zbrazët për EPUB3"

#: sphinx/builders/epub3.py:241
msgid "conf value \"version\" should not be empty for EPUB3"
msgstr "vlera e formësimit \"version\" s’duhet të jetë e zbrazët për EPUB3"

#: sphinx/builders/epub3.py:255 sphinx/builders/html/__init__.py:1187
#, python-format
msgid "invalid css_file: %r, ignored"
msgstr "css_file e pavlefshme: %r, u shpërfill"

#: sphinx/builders/gettext.py:222
#, python-format
msgid "The message catalogs are in %(outdir)s."
msgstr "Katalogët e mesazheve gjenden te %(outdir)s."

#: sphinx/builders/gettext.py:244
#, python-format
msgid "targets for %d template files"
msgstr "objektiva për kartela gjedhe %d"

#: sphinx/builders/gettext.py:248
msgid "reading templates... "
msgstr "po lexohen gjedhe… "

#: sphinx/builders/gettext.py:282
msgid "writing message catalogs... "
msgstr "po shkruhen katalogë mesazhesh… "

#: sphinx/builders/linkcheck.py:59
#, python-format
msgid "Look for any errors in the above output or in %(outdir)s/output.txt"
msgstr "Shihni për çfarëdo gabimesh te përfundimi më sipër ose te %(outdir)s/output.txt"

#: sphinx/builders/linkcheck.py:137
#, python-format
msgid "broken link: %s (%s)"
msgstr "lidhje e dëmtuar: %s (%s)"

#: sphinx/builders/linkcheck.py:660
#, python-format
msgid "Failed to compile regex in linkcheck_allowed_redirects: %r %s"
msgstr "S’u arrit të përpilohet shprehje e rregullt te linkcheck_allowed_redirects: %r %s"

#: sphinx/builders/manpage.py:37
#, python-format
msgid "The manual pages are in %(outdir)s."
msgstr "Faqet e doracakut gjenden në %(outdir)s."

#: sphinx/builders/manpage.py:44
msgid "no \"man_pages\" config value found; no manual pages will be written"
msgstr "s’u gjet vlerë formësimi \"man_pages\"; s’do të shkruhet ndonjë faqe doracaku"

#: sphinx/builders/latex/__init__.py:314 sphinx/builders/manpage.py:53
#: sphinx/builders/singlehtml.py:165 sphinx/builders/texinfo.py:112
msgid "writing"
msgstr "po shkruhet"

#: sphinx/builders/manpage.py:68
#, python-format
msgid "\"man_pages\" config value references unknown document %s"
msgstr "vlera e formësimit \"man_pages\" i referohet një dokumenti të panjohur %s"

#: sphinx/builders/singlehtml.py:34
#, python-format
msgid "The HTML page is in %(outdir)s."
msgstr "Faqja HTML gjenden në %(outdir)s."

#: sphinx/builders/singlehtml.py:160
msgid "assembling single document"
msgstr "po montohet dokument njësh"

#: sphinx/builders/singlehtml.py:178
msgid "writing additional files"
msgstr "po shkruhen kartela shtesë"

#: sphinx/builders/texinfo.py:48
#, python-format
msgid "The Texinfo files are in %(outdir)s."
msgstr "Kartelat Texinfo gjenden në %(outdir)s."

#: sphinx/builders/texinfo.py:50
msgid ""
"\n"
"Run 'make' in that directory to run these through makeinfo\n"
"(use 'make info' here to do that automatically)."
msgstr "\nXhironi 'make' te ajo drejtori, që të xhirohen këto përmes makeinfo-s\n(përdorni këtu 'make info' që kjo të kryhet automatikisht)."

#: sphinx/builders/texinfo.py:77
msgid "no \"texinfo_documents\" config value found; no documents will be written"
msgstr "s’u gjet vlerë formësimi \"texinfo_documents\"; s’do të shkruhet ndonjë dokument"

#: sphinx/builders/texinfo.py:85
#, python-format
msgid "\"texinfo_documents\" config value references unknown document %s"
msgstr "vlera e formësimit \"texinfo_documents\" i referohet një dokumenti të panjohur %s"

#: sphinx/builders/latex/__init__.py:296 sphinx/builders/texinfo.py:108
#, python-format
msgid "processing %s"
msgstr "po përpunohet %s"

#: sphinx/builders/latex/__init__.py:369 sphinx/builders/texinfo.py:161
msgid "resolving references..."
msgstr "po shquhen referencat…"

#: sphinx/builders/latex/__init__.py:380 sphinx/builders/texinfo.py:171
msgid " (in "
msgstr " (në "

#: sphinx/builders/texinfo.py:202
msgid "copying Texinfo support files"
msgstr "po kopjohen kartela mbulimi Texinfo"

#: sphinx/builders/texinfo.py:206
#, python-format
msgid "error writing file Makefile: %s"
msgstr "gabim në shkrim kartele Makefile: %s"

#: sphinx/builders/text.py:30
#, python-format
msgid "The text files are in %(outdir)s."
msgstr "Kartelat tekst gjenden në %(outdir)s."

#: sphinx/builders/html/__init__.py:1138 sphinx/builders/text.py:77
#: sphinx/builders/xml.py:96
#, python-format
msgid "error writing file %s: %s"
msgstr "gabim në shkrim kartele %s: %s"

#: sphinx/builders/xml.py:36
#, python-format
msgid "The XML files are in %(outdir)s."
msgstr "Kartelat XML gjenden në %(outdir)s."

#: sphinx/builders/xml.py:109
#, python-format
msgid "The pseudo-XML files are in %(outdir)s."
msgstr "Kartelat pseudo-XML gjenden në %(outdir)s."

#: sphinx/builders/html/__init__.py:130
#, python-format
msgid "build info file is broken: %r"
msgstr "kartela e të dhënave të montimit është e dëmtuar: %r"

#: sphinx/builders/html/__init__.py:168
#, python-format
msgid "The HTML pages are in %(outdir)s."
msgstr "Faqet HTML gjenden në %(outdir)s."

#: sphinx/builders/html/__init__.py:394
#, python-format
msgid "Failed to read build info file: %r"
msgstr "S’u arrit të lexohet kartelë të dhënash montimi: %r"

#: sphinx/builders/html/__init__.py:487 sphinx/builders/latex/__init__.py:189
#: sphinx/transforms/__init__.py:119 sphinx/writers/manpage.py:101
#: sphinx/writers/texinfo.py:227
#, python-format
msgid "%b %d, %Y"
msgstr "%d %b, %Y"

#: sphinx/builders/html/__init__.py:506 sphinx/themes/basic/defindex.html:30
msgid "General Index"
msgstr "Tregues i Përgjithshëm"

#: sphinx/builders/html/__init__.py:506
msgid "index"
msgstr "tregues"

#: sphinx/builders/html/__init__.py:579
msgid "next"
msgstr "pasuesi"

#: sphinx/builders/html/__init__.py:588
msgid "previous"
msgstr "i mëparshmi"

#: sphinx/builders/html/__init__.py:684
msgid "generating indices"
msgstr "po prodhohen tregues"

#: sphinx/builders/html/__init__.py:699
msgid "writing additional pages"
msgstr "po shkruhen faqe shtesë"

#: sphinx/builders/html/__init__.py:776
msgid "copying downloadable files... "
msgstr "po kopjohen kartela të shkarkueshme… "

#: sphinx/builders/html/__init__.py:784
#, python-format
msgid "cannot copy downloadable file %r: %s"
msgstr "s’kopjohet dot kartelë e shkarkueshme %r: %s"

#: sphinx/builders/html/__init__.py:817 sphinx/builders/html/__init__.py:829
#, python-format
msgid "Failed to copy a file in html_static_file: %s: %r"
msgstr "S’u arrit të kopjohet një kartelë te html_static_file: %s: %r"

#: sphinx/builders/html/__init__.py:850
msgid "copying static files"
msgstr "po kopjohen kartela statike"

#: sphinx/builders/html/__init__.py:866
#, python-format
msgid "cannot copy static file %r"
msgstr "s’kopjohet dot kartelë statike %r"

#: sphinx/builders/html/__init__.py:871
msgid "copying extra files"
msgstr "po kopjohen kartela ekstra"

#: sphinx/builders/html/__init__.py:877
#, python-format
msgid "cannot copy extra file %r"
msgstr "s’kopjohet dot kartelë ekstra %r"

#: sphinx/builders/html/__init__.py:884
#, python-format
msgid "Failed to write build info file: %r"
msgstr "S’u arrit të shkruhet kartelë të dhënash montimi: %r"

#: sphinx/builders/html/__init__.py:933
msgid ""
"search index couldn't be loaded, but not all documents will be built: the "
"index will be incomplete."
msgstr "treguesi i kërkimi s’u ngarkua dot, por jo krejt dokumentet do të montohen: treguesi do të jetë i paplotë."

#: sphinx/builders/html/__init__.py:978
#, python-format
msgid "page %s matches two patterns in html_sidebars: %r and %r"
msgstr "faqja %s ka përputhje me dy rregullsi te html_sidebars: %r dhe %r"

#: sphinx/builders/html/__init__.py:1121
#, python-format
msgid ""
"a Unicode error occurred when rendering the page %s. Please make sure all "
"config values that contain non-ASCII content are Unicode strings."
msgstr "ndodhi një gabim Unikod, kur vizatohej faqja %s. Ju lutemi, siguroni që krejt vlerat e formësimit që përmbajnë lëndë jo-ASCII të jenë vargje Unikod."

#: sphinx/builders/html/__init__.py:1126
#, python-format
msgid ""
"An error happened in rendering the page %s.\n"
"Reason: %r"
msgstr "Ndodhi një gabim gjatë vizatimit të faqes %s.\nArsye: %r"

#: sphinx/builders/html/__init__.py:1154
msgid "dumping object inventory"
msgstr ""

#: sphinx/builders/html/__init__.py:1162
#, python-format
msgid "dumping search index in %s"
msgstr "po shkruhet lënda e treguesit të kërkimeve në %s"

#: sphinx/builders/html/__init__.py:1210
#, python-format
msgid "invalid js_file: %r, ignored"
msgstr "js_file e pavlefshme: %r, u shpërfill"

#: sphinx/builders/html/__init__.py:1238
msgid "Many math_renderers are registered. But no math_renderer is selected."
msgstr "Janë të regjistruar plot math_renderers. Por s’u përzgjodh math_renderer."

#: sphinx/builders/html/__init__.py:1241
#, python-format
msgid "Unknown math_renderer %r is given."
msgstr "U dha math_renderer %r i panjohur."

#: sphinx/builders/html/__init__.py:1249
#, python-format
msgid "html_extra_path entry %r does not exist"
msgstr "zëri html_extra_path %r s’ekziston"

#: sphinx/builders/html/__init__.py:1253
#, python-format
msgid "html_extra_path entry %r is placed inside outdir"
msgstr "zëri %r i html_extra_path entry është vendosur jashtë outdir-it"

#: sphinx/builders/html/__init__.py:1262
#, python-format
msgid "html_static_path entry %r does not exist"
msgstr "zëri html_static_path %r s’ekziston"

#: sphinx/builders/html/__init__.py:1266
#, python-format
msgid "html_static_path entry %r is placed inside outdir"
msgstr "zëri %r i html_extra_path entry është vendosur brenda outdir-it"

#: sphinx/builders/html/__init__.py:1275 sphinx/builders/latex/__init__.py:444
#, python-format
msgid "logo file %r does not exist"
msgstr "kartela stemë %r s’ekziston"

#: sphinx/builders/html/__init__.py:1284
#, python-format
msgid "favicon file %r does not exist"
msgstr "kartela favikonë %r s’ekziston"

#: sphinx/builders/html/__init__.py:1291
msgid ""
"HTML 4 is no longer supported by Sphinx. (\"html4_writer=True\" detected in "
"configuration options)"
msgstr "HTML 4 s’mbulohet më nga Sphinx-i. (U pikas “html4_writer=True” te mundësi formësimi)"

#: sphinx/builders/html/__init__.py:1306
#, python-format
msgid "%s %s documentation"
msgstr "Dokumentim i %s %s"

#: sphinx/builders/latex/__init__.py:115
#, python-format
msgid "The LaTeX files are in %(outdir)s."
msgstr "Kartelat LaTeX gjenden në %(outdir)s."

#: sphinx/builders/latex/__init__.py:117
msgid ""
"\n"
"Run 'make' in that directory to run these through (pdf)latex\n"
"(use `make latexpdf' here to do that automatically)."
msgstr "\nXhironi 'make' te ajo drejtori që të xhirohen këto përmes (pdf)latex\n(që të bëhet kjo automatikisht, përdorni `make latexpdf' këtu)."

#: sphinx/builders/latex/__init__.py:152
msgid "no \"latex_documents\" config value found; no documents will be written"
msgstr "s’u gjet vlerë formësimi \"texinfo_documents\"; s’do të shkruhet ndonjë dokument"

#: sphinx/builders/latex/__init__.py:160
#, python-format
msgid "\"latex_documents\" config value references unknown document %s"
msgstr "vlera e formësimit \"texinfo_documents\" i referohet një dokumenti të panjohur %s"

#: sphinx/builders/latex/__init__.py:196 sphinx/domains/std/__init__.py:559
#: sphinx/domains/std/__init__.py:571 sphinx/templates/latex/latex.tex_t:106
#: sphinx/themes/basic/genindex-single.html:30
#: sphinx/themes/basic/genindex-single.html:55
#: sphinx/themes/basic/genindex-split.html:11
#: sphinx/themes/basic/genindex-split.html:14
#: sphinx/themes/basic/genindex.html:11 sphinx/themes/basic/genindex.html:34
#: sphinx/themes/basic/genindex.html:67 sphinx/themes/basic/layout.html:138
#: sphinx/writers/texinfo.py:497
msgid "Index"
msgstr "Tregues"

#: sphinx/builders/latex/__init__.py:199 sphinx/templates/latex/latex.tex_t:91
msgid "Release"
msgstr "Hedhje Në Qarkullim"

#: sphinx/builders/latex/__init__.py:213 sphinx/writers/latex.py:370
#, python-format
msgid "no Babel option known for language %r"
msgstr "s’ka mundësi Babel të njohur për gjuhën %r"

#: sphinx/builders/latex/__init__.py:394
msgid "copying TeX support files"
msgstr "po kopjohen kartela mbulimi TeX"

#: sphinx/builders/latex/__init__.py:410
msgid "copying TeX support files..."
msgstr "po kopjohen kartela mbulimi TeX…"

#: sphinx/builders/latex/__init__.py:423
msgid "copying additional files"
msgstr "po kopjohen kartela shtesë"

#: sphinx/builders/latex/__init__.py:466
#, python-format
msgid "Unknown configure key: latex_elements[%r], ignored."
msgstr "Kyç i panjohur formësimi: latex_elements[%r], u shpërfill."

#: sphinx/builders/latex/__init__.py:474
#, python-format
msgid "Unknown theme option: latex_theme_options[%r], ignored."
msgstr "Mundësi e panjohur teme: latex_theme_options[%r], u shpërfill."

#: sphinx/builders/latex/theming.py:87
#, python-format
msgid "%r doesn't have \"theme\" setting"
msgstr "%r s’ka rregullimin \"theme\""

#: sphinx/builders/latex/theming.py:90
#, python-format
msgid "%r doesn't have \"%s\" setting"
msgstr "%r s’ka rregullimin \"%s\""

#: sphinx/builders/latex/transforms.py:120
msgid "Failed to get a docname!"
msgstr "S’u arrit të merrej një “docname”!"

#: sphinx/builders/latex/transforms.py:121
msgid "Failed to get a docname for source {source!r}!"
msgstr "S’u arrit të merrej një “docname” për burimin {source!r}!"

#: sphinx/builders/latex/transforms.py:482
#, python-format
msgid "No footnote was found for given reference node %r"
msgstr "S’u gjet poshtëshënim për nyjë reference të dhënë %r"

#: sphinx/cmd/build.py:46
msgid "Exception occurred while building, starting debugger:"
msgstr "Ndodhi një përjashtim gjatë montimit, po niset diagnostikuesi:"

#: sphinx/cmd/build.py:61
msgid "Interrupted!"
msgstr "U ndërpre!"

#: sphinx/cmd/build.py:63
msgid "reST markup error:"
msgstr "Gabim markup-i reST:"

#: sphinx/cmd/build.py:69
msgid "Encoding error:"
msgstr "Gabim kodimi:"

#: sphinx/cmd/build.py:72 sphinx/cmd/build.py:87
#, python-format
msgid ""
"The full traceback has been saved in %s, if you want to report the issue to "
"the developers."
msgstr "Traceback-u i plotë është ruajtur te %s, nëse doni t’ua raportoni problemin zhvilluesve."

#: sphinx/cmd/build.py:76
msgid "Recursion error:"
msgstr "Gabim përsëritje:"

#: sphinx/cmd/build.py:79
msgid ""
"This can happen with very large or deeply nested source files. You can "
"carefully increase the default Python recursion limit of 1000 in conf.py "
"with e.g.:"
msgstr "Kjo mund të ndodhë me kartela burim shumë të mëdha ose të futura thellë brenda njëra-tjetrës. Mund të rrisni me kujdes kufirin parazgjedhje për ripërsëritje Python prej 1000, te conf.py, me p.sh.:"

#: sphinx/cmd/build.py:84
msgid "Exception occurred:"
msgstr "Ndodhi një përjashtim:"

#: sphinx/cmd/build.py:90
msgid ""
"Please also report this if it was a user error, so that a better error "
"message can be provided next time."
msgstr "Ju lutemi, njoftojeni nëse qe një gabim përdoruesi, që kështu herës tjetër të mund të furnizohet një mesazh më i mirë gabimi."

#: sphinx/cmd/build.py:93
msgid ""
"A bug report can be filed in the tracker at <https://github.com/sphinx-"
"doc/sphinx/issues>. Thanks!"
msgstr "Një njoftim të mete mund të depozitohet te gjurmuesi në <https://github.com/sphinx-doc/sphinx/issues>. Faleminderit!"

#: sphinx/cmd/build.py:109
msgid "job number should be a positive number"
msgstr "numri i aktit duhet të jetë një numër pozitiv"

#: sphinx/cmd/build.py:117 sphinx/cmd/quickstart.py:474
#: sphinx/ext/apidoc.py:317 sphinx/ext/autosummary/generate.py:689
msgid "For more information, visit <https://www.sphinx-doc.org/>."
msgstr "Për më tepër hollësi, vizitoni <https://www.sphinx-doc.org/>."

#: sphinx/cmd/build.py:118
msgid ""
"\n"
"Generate documentation from source files.\n"
"\n"
"sphinx-build generates documentation from the files in SOURCEDIR and places it\n"
"in OUTPUTDIR. It looks for 'conf.py' in SOURCEDIR for the configuration\n"
"settings. The 'sphinx-quickstart' tool may be used to generate template files,\n"
"including 'conf.py'\n"
"\n"
"sphinx-build can create documentation in different formats. A format is\n"
"selected by specifying the builder name on the command line; it defaults to\n"
"HTML. Builders can also perform other tasks related to documentation\n"
"processing.\n"
"\n"
"By default, everything that is outdated is built. Output only for selected\n"
"files can be built by specifying individual filenames.\n"
msgstr "\nProdhoni dokumentim nga kartela burim.\n\nsphinx-build prodhon dokumentim prej kartelash te SOURCEDIR dhe e vendos\nte OUTPUTDIR. Kërkon për 'conf.py' te SOURCEDIR për rregullime formësimi.\nMjeti 'sphinx-quickstart' mund të përdoret për të prodhuar kartela gjedhe,\npërfshi 'conf.py'\n\nsphinx-build mund të krijojë dokumentim në formate të ndryshëm. Një format\npërzgjidhet duke specifikuar te rreshti i urdhrave emrin e montuesit; HTML-ja,\nsi parazgjedhje. Montuesit mund të kryejnë gjithashtu veprime të tjera të lidhura\nme përpunim dokumentimi.\n\nSi parazgjedhje, gjithçka që është e papërditësuar, montohet. Nëse doni\nmontim vetëm për kartela të përzgjedhura, kjo mund të bëhet duke\nspecifikuar emra kartelash individuale.\n"

#: sphinx/cmd/build.py:139
msgid "path to documentation source files"
msgstr "shteg për te kartela burimi dokumentimi"

#: sphinx/cmd/build.py:141
msgid "path to output directory"
msgstr "shteg për te drejtori përfundimesh"

#: sphinx/cmd/build.py:143
msgid ""
"(optional) a list of specific files to rebuild. Ignored if --write-all is "
"specified"
msgstr "(opsionale) një listë kartelash të caktuara për t’u rimontuar. E shpërfillur, nëse është dhënë --write-all"

#: sphinx/cmd/build.py:146
msgid "general options"
msgstr "mundësi të përgjithshme"

#: sphinx/cmd/build.py:149
msgid "builder to use (default: 'html')"
msgstr "montues për t’u përdorur (parazgjedhje: 'html')"

#: sphinx/cmd/build.py:152
msgid ""
"run in parallel with N processes, when possible. 'auto' uses the number of "
"CPU cores"
msgstr ""

#: sphinx/cmd/build.py:155
msgid "write all files (default: only write new and changed files)"
msgstr "shkruaj krejt kartelat (parazgjedhje: shkruaj vetëm kartela të reja dhe ato të ndryshuara)"

#: sphinx/cmd/build.py:158
msgid "don't use a saved environment, always read all files"
msgstr "mos përdor një mjedis të ruajtur, lexo përherë krejt kartelat"

#: sphinx/cmd/build.py:161
msgid "path options"
msgstr "mundësi shtegu"

#: sphinx/cmd/build.py:163
msgid ""
"directory for doctree and environment files (default: OUTPUT_DIR/.doctrees)"
msgstr "drejtori për kartela doctree dhe mjedisi (parazgjedhje: OUTPUT_DIR/.doctrees)"

#: sphinx/cmd/build.py:166
msgid "directory for the configuration file (conf.py) (default: SOURCE_DIR)"
msgstr "drejtori për kartelën e formësimit (conf.py) (parazgjedhje: SOURCE_DIR)"

#: sphinx/cmd/build.py:171
msgid "use no configuration file, only use settings from -D options"
msgstr "mos përdor kartelë formësimi, përdor vetëm rregullime nga mundësitë -D"

#: sphinx/cmd/build.py:174
msgid "override a setting in configuration file"
msgstr "anashkalo një rregullim te kartelë formësimi"

#: sphinx/cmd/build.py:177
msgid "pass a value into HTML templates"
msgstr "jep një vlerë te gjedhe HTML"

#: sphinx/cmd/build.py:180
msgid "define tag: include \"only\" blocks with TAG"
msgstr "përcaktoni etiketë: përfshi blloqe “only” me TAG"

#: sphinx/cmd/build.py:182
msgid "nit-picky mode: warn about all missing references"
msgstr "mënyra “nit-picky”: sinjalizo për krejt referencat që mungonjnë"

#: sphinx/cmd/build.py:184
msgid "console output options"
msgstr "mundësi për ç’prodhon konsola"

#: sphinx/cmd/build.py:187
msgid "increase verbosity (can be repeated)"
msgstr ""

#: sphinx/cmd/build.py:189 sphinx/ext/apidoc.py:340
msgid "no output on stdout, just warnings on stderr"
msgstr "pa output në stdout, thjesht sinjalizime në stderr"

#: sphinx/cmd/build.py:191
msgid "no output at all, not even warnings"
msgstr "pa output fare, madje as sinjalizime"

#: sphinx/cmd/build.py:194
msgid "do emit colored output (default: auto-detect)"
msgstr ""

#: sphinx/cmd/build.py:197
msgid "do not emit colored output (default: auto-detect)"
msgstr ""

#: sphinx/cmd/build.py:199
msgid "warning control options"
msgstr "mundësi kontrolli sinjalizimesh"

#: sphinx/cmd/build.py:201
msgid "write warnings (and errors) to given file"
msgstr "shkruaj sinjalizime (dhe gabime) te kartela e dhënë"

#: sphinx/cmd/build.py:203
msgid "turn warnings into errors"
msgstr "shndërroji sinjalizimet në gabime"

#: sphinx/cmd/build.py:205
msgid "with --fail-on-warning, keep going when getting warnings"
msgstr "me --fail-on-warning, vazhdo, kur merren sinjalizime"

#: sphinx/cmd/build.py:207
msgid "show full traceback on exception"
msgstr ""

#: sphinx/cmd/build.py:209
msgid "run Pdb on exception"
msgstr "xhiro Pdb, në rast përjashtimesh"

#: sphinx/cmd/build.py:244
msgid "cannot combine -a option and filenames"
msgstr "s’mund të ndërthuret një mundësi -a dhe emra kartelash"

#: sphinx/cmd/build.py:276
#, python-format
msgid "cannot open warning file %r: %s"
msgstr "s’hapet dot kartelë sinjalizimesh %r: %s"

#: sphinx/cmd/build.py:296
msgid "-D option argument must be in the form name=value"
msgstr "argumenti i mundësisë -D duhet të jetë në formën emër=vlerë"

#: sphinx/cmd/build.py:303
msgid "-A option argument must be in the form name=value"
msgstr "argumenti i mundësisë -A duhet të jetë në formën emër=vlerë"

#: sphinx/cmd/quickstart.py:42
msgid "automatically insert docstrings from modules"
msgstr "fut automatikisht docstrings prej modulesh"

#: sphinx/cmd/quickstart.py:43
msgid "automatically test code snippets in doctest blocks"
msgstr "testo automatikisht copëza kodi te blloqe doctest"

#: sphinx/cmd/quickstart.py:44
msgid "link between Sphinx documentation of different projects"
msgstr "lidhje mes dokumentimi Sphinx projektesh të ndryshëm"

#: sphinx/cmd/quickstart.py:45
msgid "write \"todo\" entries that can be shown or hidden on build"
msgstr "shkruaj zëra \"todo\" që mund të shfaqen ose fshihen te montimi"

#: sphinx/cmd/quickstart.py:46
msgid "checks for documentation coverage"
msgstr "kontrolle për mbulim dokumentimi"

#: sphinx/cmd/quickstart.py:47
msgid "include math, rendered as PNG or SVG images"
msgstr "përfshi formula matematikore, të vizatuara si figura PNG ose SVG"

#: sphinx/cmd/quickstart.py:48
msgid "include math, rendered in the browser by MathJax"
msgstr "përfshi formula matematikore, të vizatuara te shfletuesi nga MathJax"

#: sphinx/cmd/quickstart.py:49
msgid "conditional inclusion of content based on config values"
msgstr "përfshirje e kushtëzuar lënde, bazuar në vlera formësimi"

#: sphinx/cmd/quickstart.py:50
msgid "include links to the source code of documented Python objects"
msgstr "përfshi lidhje te kodi burim i objekteve Python të dokumentuara"

#: sphinx/cmd/quickstart.py:51
msgid "create .nojekyll file to publish the document on GitHub pages"
msgstr "krijo kartelë .nojekyll për të botuar dokumentin në faqe GitHub"

#: sphinx/cmd/quickstart.py:93
msgid "Please enter a valid path name."
msgstr "Ju lutemi, jepni një emër shtegu të vlefshëm."

#: sphinx/cmd/quickstart.py:109
msgid "Please enter some text."
msgstr "Ju lutemi, jepni ca tekst."

#: sphinx/cmd/quickstart.py:116
#, python-format
msgid "Please enter one of %s."
msgstr "Ju lutemi, jepni një nga %s."

#: sphinx/cmd/quickstart.py:123
msgid "Please enter either 'y' or 'n'."
msgstr "Ju lutemi, jepni 'y' ose 'n'."

#: sphinx/cmd/quickstart.py:129
msgid "Please enter a file suffix, e.g. '.rst' or '.txt'."
msgstr "Ju lutemi, jepni një prapashtesë kartele, për shembull, '.rst' ose '.txt'."

#: sphinx/cmd/quickstart.py:208
#, python-format
msgid "Welcome to the Sphinx %s quickstart utility."
msgstr "Mirë se vini te mjeti për fillim të shpejtë me Sphinx %s."

#: sphinx/cmd/quickstart.py:210
msgid ""
"Please enter values for the following settings (just press Enter to\n"
"accept a default value, if one is given in brackets)."
msgstr "Ju lutemi, jepni vlera për rregullimet vijuese (thjesht shtypni tastin\nEnter që të pranohet një vlerë parazgjedhje, nëse është një e tillë\nbrenda kllapave)."

#: sphinx/cmd/quickstart.py:215
#, python-format
msgid "Selected root path: %s"
msgstr "Shteg rrënjë i përzgjedhur: %s"

#: sphinx/cmd/quickstart.py:218
msgid "Enter the root path for documentation."
msgstr "Jepni shtegun rrënjë për te dokumenti."

#: sphinx/cmd/quickstart.py:219
msgid "Root path for the documentation"
msgstr "Shteg rrënje për te dokumenti"

#: sphinx/cmd/quickstart.py:224
msgid "Error: an existing conf.py has been found in the selected root path."
msgstr "Gabim: te shtegu rrënjë i përzgjedhur u gjet një conf.py ekzistues."

#: sphinx/cmd/quickstart.py:226
msgid "sphinx-quickstart will not overwrite existing Sphinx projects."
msgstr "sphinx-quickstart s’do të mbishkruajë projekte ekzistuese Sphinx."

#: sphinx/cmd/quickstart.py:228
msgid "Please enter a new root path (or just Enter to exit)"
msgstr "Ju lutemi, jepni një shteg të ri rrënjë (ose thjesht shtypni tastin Enter, që të dilet)"

#: sphinx/cmd/quickstart.py:235
msgid ""
"You have two options for placing the build directory for Sphinx output.\n"
"Either, you use a directory \"_build\" within the root path, or you separate\n"
"\"source\" and \"build\" directories within the root path."
msgstr "Keni dy mundësi për vendosjen e drejtorisë së montimeve për çka prodhon Sphinx-i.\nPërdorni një drejtori \"_build\" brenda shtegut rrënjë, ose i ndani\ndrejtoritë \"burim\" dhe \"montim\" brenda shtegut rrënjë."

#: sphinx/cmd/quickstart.py:238
msgid "Separate source and build directories (y/n)"
msgstr "Nda veçmas drejtoritë burim dhe montim (y/n)"

#: sphinx/cmd/quickstart.py:242
msgid ""
"Inside the root directory, two more directories will be created; \"_templates\"\n"
"for custom HTML templates and \"_static\" for custom stylesheets and other static\n"
"files. You can enter another prefix (such as \".\") to replace the underscore."
msgstr "Brenda drejtorisë rrënjë do të krijohen dy drejtori të tjera; \"_templates\" për\ngjedhe vetjake HTML, dhe \"_static\" për fletëstile vetjakë dhe kartela të tjera statike.\nMund të krijoni një tjetër parashtesë (bie fjala, \".\") në vend të nënvijës."

#: sphinx/cmd/quickstart.py:245
msgid "Name prefix for templates and static dir"
msgstr "Parashtesë emrash për drejtori gjedhesh dhe statikesh"

#: sphinx/cmd/quickstart.py:249
msgid ""
"The project name will occur in several places in the built documentation."
msgstr "Emri i projektit do të shfaqet në disa vende te dokumentimi i montuar."

#: sphinx/cmd/quickstart.py:250
msgid "Project name"
msgstr "Emër projekti"

#: sphinx/cmd/quickstart.py:252
msgid "Author name(s)"
msgstr "Emër(a) autori(ësh)"

#: sphinx/cmd/quickstart.py:256
msgid ""
"Sphinx has the notion of a \"version\" and a \"release\" for the\n"
"software. Each version can have multiple releases. For example, for\n"
"Python the version is something like 2.5 or 3.0, while the release is\n"
"something like 2.5.1 or 3.0a1. If you don't need this dual structure,\n"
"just set both to the same value."
msgstr "Sphinx-i përdor nocionet e “versionit” dhe “hedhjes në qarkullim” për\nsoftware-in. Çdo version mund të ketë hedhje të shumta në qarkullim.\nBie fjala, për Python-in versionet ngjajnë me 2.5 ose 3.0, teksa hedhja\nnë qarkullim ngjan me 2.5.1 ose 3.0a1.  Nëse kjo strukturë duale s’ju\nhyn në punë, thjesht vëruni të dyjave të njëjtën vlerë."

#: sphinx/cmd/quickstart.py:261
msgid "Project version"
msgstr "Version projekti"

#: sphinx/cmd/quickstart.py:263
msgid "Project release"
msgstr "Hedhje në qarkullim e projektit"

#: sphinx/cmd/quickstart.py:267
msgid ""
"If the documents are to be written in a language other than English,\n"
"you can select a language here by its language code. Sphinx will then\n"
"translate text that it generates into that language.\n"
"\n"
"For a list of supported codes, see\n"
"https://www.sphinx-doc.org/en/master/usage/configuration.html#confval-language."
msgstr "Nëse dokumentet janë shkruan në gjuhë tjetër nga anglishtja,\nmund të përzgjidhni një gjuhë këtu, përmes kodit të asaj gjuhe. Sphinx-i mandej\ndo të përkthejë në atë gjuhë tekstin që prodhon.\n\nPër një listë kodesh të mbuluar, shihni\nhttps://www.sphinx-doc.org/en/master/usage/configuration.html#confval-language."

#: sphinx/cmd/quickstart.py:275
msgid "Project language"
msgstr "Gjuhë projekti"

#: sphinx/cmd/quickstart.py:281
msgid ""
"The file name suffix for source files. Commonly, this is either \".txt\"\n"
"or \".rst\". Only files with this suffix are considered documents."
msgstr "Prapashtesa e emrave të kartelave për kartela burim. Zakonisht, kjo\nështë ose \".txt\", ose \".rst\".  Vetëm kartelat me këtë prapashtesë\nmerren si dokumente."

#: sphinx/cmd/quickstart.py:283
msgid "Source file suffix"
msgstr "Prapashtesë kartele burim"

#: sphinx/cmd/quickstart.py:287
msgid ""
"One document is special in that it is considered the top node of the\n"
"\"contents tree\", that is, it is the root of the hierarchical structure\n"
"of the documents. Normally, this is \"index\", but if your \"index\"\n"
"document is a custom template, you can also set this to another filename."
msgstr "Një dokument është i veçantë, për faktin se konsiderohet si nyja e epërme\ne  \"pemës së lëndës\", domethënë, është rrënja e strukturës hierarkike\ntë dokumenteve. Zakonisht, ky është \"index\", por nëse dokumenti juaj \"index\"\nështë një gjedhe vetjake, si të tillë mund të caktoni një tjetër emër kartele."

#: sphinx/cmd/quickstart.py:291
msgid "Name of your master document (without suffix)"
msgstr "Emër i dokumentit tuaj kryesor (pa prapashtesë)"

#: sphinx/cmd/quickstart.py:296
#, python-format
msgid ""
"Error: the master file %s has already been found in the selected root path."
msgstr "Gabim: kartela master %s është gjetur tashmë një herë në shtegun rrënjë të përzgjedhur."

#: sphinx/cmd/quickstart.py:298
msgid "sphinx-quickstart will not overwrite the existing file."
msgstr "sphinx-quickstart s’do të mbishkruajë kartelën ekzistuese."

#: sphinx/cmd/quickstart.py:300
msgid ""
"Please enter a new file name, or rename the existing file and press Enter"
msgstr "Ju lutemi, jepni një emër të ri kartele, ose riemërtojeni kartelën ekzistuese dhe shtypni tastin Enter"

#: sphinx/cmd/quickstart.py:304
msgid "Indicate which of the following Sphinx extensions should be enabled:"
msgstr "Përcaktoni se cilët nga zgjerimet vijuese Sphinx duhen aktivizuar:"

#: sphinx/cmd/quickstart.py:312
msgid ""
"Note: imgmath and mathjax cannot be enabled at the same time. imgmath has "
"been deselected."
msgstr "Shënim: imgmath dhe mathjax s’mund të aktivizohen në të njëjtën kohë. U hoqë përzgjedhja e imgmath-it."

#: sphinx/cmd/quickstart.py:318
msgid ""
"A Makefile and a Windows command file can be generated for you so that you\n"
"only have to run e.g. `make html' instead of invoking sphinx-build\n"
"directly."
msgstr "Për ju mund të prodhohen një kartelë makefile dhe një urdhrash Windows, që\nkështu t’ju duhet vetëm të xhironi, për shembull, `make html', në vend se\ntë thirret drejtpërsëdrejti sphinx-build."

#: sphinx/cmd/quickstart.py:321
msgid "Create Makefile? (y/n)"
msgstr "Të krijohet Makefile? (y/n)"

#: sphinx/cmd/quickstart.py:324
msgid "Create Windows command file? (y/n)"
msgstr "Të krijohet kartelë urdhrash Windows? (y/n)"

#: sphinx/cmd/quickstart.py:368 sphinx/ext/apidoc.py:93
#, python-format
msgid "Creating file %s."
msgstr "Po krijohet kartela %s."

#: sphinx/cmd/quickstart.py:373 sphinx/ext/apidoc.py:90
#, python-format
msgid "File %s already exists, skipping."
msgstr "Ka tashmë një kartelë %s, po anashkalohet."

#: sphinx/cmd/quickstart.py:418
msgid "Finished: An initial directory structure has been created."
msgstr "Përfundoi: U krijua një strukturë fillestare drejtorish."

#: sphinx/cmd/quickstart.py:420
#, python-format
msgid ""
"You should now populate your master file %s and create other documentation\n"
"source files. "
msgstr "Tani duhet të populloni kartelën tuaj master file %s dhe të krijoni kartela të tjera\nburim të dokumentimit. "

#: sphinx/cmd/quickstart.py:423
msgid ""
"Use the Makefile to build the docs, like so:\n"
"   make builder"
msgstr "Përdorni Makefile-in që të montohen dokumentet, kështu:\n   make builder"

#: sphinx/cmd/quickstart.py:426
#, python-format
msgid ""
"Use the sphinx-build command to build the docs, like so:\n"
"   sphinx-build -b builder %s %s"
msgstr "Përodrni urdhrin sphinx-build që të montohen dokumentet, kështu:\n   sphinx-build -b montues %s %s"

#: sphinx/cmd/quickstart.py:428
msgid ""
"where \"builder\" is one of the supported builders, e.g. html, latex or "
"linkcheck."
msgstr "ku montues është një nga montuesin e mbuluar, p.sh., html, latex ose linkcheck."

#: sphinx/cmd/quickstart.py:464
msgid ""
"\n"
"Generate required files for a Sphinx project.\n"
"\n"
"sphinx-quickstart is an interactive tool that asks some questions about your\n"
"project and then generates a complete documentation directory and sample\n"
"Makefile to be used with sphinx-build.\n"
msgstr "\nProdho kartelat e domosdoshme për një projekt Sphinx.\n\nsphinx-quickstart është një mjet ndërveprues që bën disa pyetje rreth projektit\ntuaj dhe mandej prodhon një drejtori të plotë dokumentimi dhe një shembull\nMakefile për t’u përdorur me sphinx-build.\n"

#: sphinx/cmd/quickstart.py:479
msgid "quiet mode"
msgstr "mënyra pa zhurmë"

#: sphinx/cmd/quickstart.py:484
msgid "project root"
msgstr "rrënjë e projektit"

#: sphinx/cmd/quickstart.py:486
msgid "Structure options"
msgstr "Mundësi strukture"

#: sphinx/cmd/quickstart.py:488
msgid "if specified, separate source and build dirs"
msgstr "nëse është kërkuar, nda veçmas drejtoritë burim dhe montim"

#: sphinx/cmd/quickstart.py:490
msgid "if specified, create build dir under source dir"
msgstr "në u përcaktoftë, krijo drejtori montimi nën drejtorinë burim"

#: sphinx/cmd/quickstart.py:492
msgid "replacement for dot in _templates etc."
msgstr "zëvendësim për pikën te _templates, etj."

#: sphinx/cmd/quickstart.py:494
msgid "Project basic options"
msgstr "Mundësi bazë të projektit"

#: sphinx/cmd/quickstart.py:496
msgid "project name"
msgstr "emër projekti"

#: sphinx/cmd/quickstart.py:498
msgid "author names"
msgstr "emra autorësh"

#: sphinx/cmd/quickstart.py:500
msgid "version of project"
msgstr "version i projektit"

#: sphinx/cmd/quickstart.py:502
msgid "release of project"
msgstr "hedhje në qarkullim e projektit"

#: sphinx/cmd/quickstart.py:504
msgid "document language"
msgstr "gjuhë dokumenti"

#: sphinx/cmd/quickstart.py:506
msgid "source file suffix"
msgstr "prapashtesë kartele burim"

#: sphinx/cmd/quickstart.py:508
msgid "master document name"
msgstr "emër dokumenti bazë"

#: sphinx/cmd/quickstart.py:510
msgid "use epub"
msgstr "përdor epub"

#: sphinx/cmd/quickstart.py:512
msgid "Extension options"
msgstr "Mundësi zgjerimi"

#: sphinx/cmd/quickstart.py:516 sphinx/ext/apidoc.py:400
#, python-format
msgid "enable %s extension"
msgstr "aktivizo zgjerimin %s"

#: sphinx/cmd/quickstart.py:518 sphinx/ext/apidoc.py:396
msgid "enable arbitrary extensions"
msgstr "aktivizo zgjerime arbitrare"

#: sphinx/cmd/quickstart.py:520
msgid "Makefile and Batchfile creation"
msgstr "Krijim makefile-i dhe batchfile-i"

#: sphinx/cmd/quickstart.py:522
msgid "create makefile"
msgstr "krijo makefile"

#: sphinx/cmd/quickstart.py:524
msgid "do not create makefile"
msgstr "mos krijo makefile"

#: sphinx/cmd/quickstart.py:526
msgid "create batchfile"
msgstr "krijo batchfile"

#: sphinx/cmd/quickstart.py:529
msgid "do not create batchfile"
msgstr "mos krijo batchfile"

#: sphinx/cmd/quickstart.py:532
msgid "use make-mode for Makefile/make.bat"
msgstr "përdor make-mode për Makefile/make.bat"

#: sphinx/cmd/quickstart.py:535
msgid "do not use make-mode for Makefile/make.bat"
msgstr "mos përdor make-mode për Makefile/make.bat"

#: sphinx/cmd/quickstart.py:537 sphinx/ext/apidoc.py:402
msgid "Project templating"
msgstr "Gjedhe projekti"

#: sphinx/cmd/quickstart.py:540 sphinx/ext/apidoc.py:405
msgid "template directory for template files"
msgstr "drejtori gjedhesh për kartela gjedhe"

#: sphinx/cmd/quickstart.py:543
msgid "define a template variable"
msgstr "përkufizoni një ndryshore gjedheje"

#: sphinx/cmd/quickstart.py:576
msgid "\"quiet\" is specified, but any of \"project\" or \"author\" is not specified."
msgstr "\"quiet\" është specifikuar, por s’është specifikuar ndonjë \"projekt\" ose \"autor\"."

#: sphinx/cmd/quickstart.py:590
msgid ""
"Error: specified path is not a directory, or sphinx files already exist."
msgstr "Gabim:shtegu i dhënë s’është drejtori, ose kartelat sphinx ekzistojnë tashmë."

#: sphinx/cmd/quickstart.py:592
msgid ""
"sphinx-quickstart only generate into a empty directory. Please specify a new"
" root path."
msgstr "sphinx-quickstart prodhon vetëm te një drejtori e zbrazët. Ju lutemi, specifikoni një shteg rrënjë të ri."

#: sphinx/cmd/quickstart.py:607
#, python-format
msgid "Invalid template variable: %s"
msgstr "Ndryshore e pavlefshme gjedheje: %s"

#: sphinx/directives/code.py:61
msgid "non-whitespace stripped by dedent"
msgstr ""

#: sphinx/directives/code.py:82
#, python-format
msgid "Invalid caption: %s"
msgstr "Titull i pavlefshëm: %s"

#: sphinx/directives/code.py:127 sphinx/directives/code.py:277
#: sphinx/directives/code.py:453
#, python-format
msgid "line number spec is out of range(1-%d): %r"
msgstr "numri i specifikuar për rreshtin është jashtë intervali (1-%d): %r"

#: sphinx/directives/code.py:206
#, python-format
msgid "Cannot use both \"%s\" and \"%s\" options"
msgstr "S’mund të përdoren në të njëjtën kohë të dyja mundësitë \"%s\" \"%s\""

#: sphinx/directives/code.py:220
#, python-format
msgid "Include file %r not found or reading it failed"
msgstr "S’u gjet kartelë “include” %r, ose leximi i saj dështoi"

#: sphinx/directives/code.py:223
#, python-format
msgid ""
"Encoding %r used for reading included file %r seems to be wrong, try giving "
"an :encoding: option"
msgstr "Kodimi %r i përdorur për lexim të kartelës “include” %r duket të jetë i gabuar, provoni të jepni një mundësi :encoding:"

#: sphinx/directives/code.py:260
#, python-format
msgid "Object named %r not found in include file %r"
msgstr "Objekti i emërtuar %r s’u gjet te kartelë include %r"

#: sphinx/directives/code.py:286
msgid "Cannot use \"lineno-match\" with a disjoint set of \"lines\""
msgstr ""

#: sphinx/directives/code.py:291
#, python-format
msgid "Line spec %r: no lines pulled from include file %r"
msgstr ""

#: sphinx/directives/other.py:120
#, python-format
msgid "toctree glob pattern %r didn't match any documents"
msgstr ""

#: sphinx/directives/other.py:146 sphinx/environment/adapters/toctree.py:324
#, python-format
msgid "toctree contains reference to excluded document %r"
msgstr "toctree përmban referencë ndaj dokumenti %r të përjashtuar"

#: sphinx/directives/other.py:149 sphinx/environment/adapters/toctree.py:328
#, python-format
msgid "toctree contains reference to nonexisting document %r"
msgstr "toctree përmban referencë ndaj dokumenti %r që s’ekziston"

#: sphinx/directives/other.py:160
#, python-format
msgid "duplicated entry found in toctree: %s"
msgstr "zë i përsëdytur, gjetur te toctree: %s"

#: sphinx/directives/other.py:193
msgid "Section author: "
msgstr "Autor ndarjeje: "

#: sphinx/directives/other.py:195
msgid "Module author: "
msgstr "Autor moduli: "

#: sphinx/directives/other.py:197
msgid "Code author: "
msgstr "Autor kodi: "

#: sphinx/directives/other.py:199
msgid "Author: "
msgstr "Autor: "

#: sphinx/directives/other.py:275
msgid ".. acks content is not a list"
msgstr ""

#: sphinx/directives/other.py:301
msgid ".. hlist content is not a list"
msgstr ""

#: sphinx/directives/patches.py:66
msgid ""
"\":file:\" option for csv-table directive now recognizes an absolute path as"
" a relative path from source directory. Please update your document."
msgstr "Mundësia \":file:\" për direktivë csv-table tani njeh një shteg absolut si shteg relativ prej drejtorisë burim. Ju lutemi, përditësoni dokumentin tuaj."

#: sphinx/domains/__init__.py:397
#, python-format
msgid "%s %s"
msgstr "%s %s"

#: sphinx/domains/changeset.py:23
#, python-format
msgid "Added in version %s"
msgstr "Shtuar në versionin %s"

#: sphinx/domains/changeset.py:24
#, python-format
msgid "Changed in version %s"
msgstr "Ndryshuar në versionin %s"

#: sphinx/domains/changeset.py:25
#, python-format
msgid "Deprecated since version %s"
msgstr "Nxjerrë nga përdorimi që me versionin %s"

#: sphinx/domains/changeset.py:26
#, python-format
msgid "Removed in version %s"
msgstr "Hequr në versionin %s"

#: sphinx/domains/citation.py:71
#, python-format
msgid "duplicate citation %s, other instance in %s"
msgstr "citim i përsëdytur %s, tjetër instancë te %s"

#: sphinx/domains/citation.py:82
#, python-format
msgid "Citation [%s] is not referenced."
msgstr "Përmendja [%s] s’është në referencë."

#: sphinx/domains/javascript.py:165
#, python-format
msgid "%s() (built-in function)"
msgstr "%s() (funksion i brendshëm)"

#: sphinx/domains/javascript.py:166 sphinx/domains/python/__init__.py:240
#, python-format
msgid "%s() (%s method)"
msgstr "%s() (metodë %s)"

#: sphinx/domains/javascript.py:168
#, python-format
msgid "%s() (class)"
msgstr "%s() (klasë)"

#: sphinx/domains/javascript.py:170
#, python-format
msgid "%s (global variable or constant)"
msgstr "%s ( ndryshore globale ose konstante)"

#: sphinx/domains/javascript.py:172 sphinx/domains/python/__init__.py:325
#, python-format
msgid "%s (%s attribute)"
msgstr "%s (atribut %s)"

#: sphinx/domains/javascript.py:255
msgid "Arguments"
msgstr "Argumente"

#: sphinx/domains/cpp/__init__.py:350 sphinx/domains/javascript.py:258
msgid "Throws"
msgstr ""

#: sphinx/domains/c/__init__.py:251 sphinx/domains/cpp/__init__.py:361
#: sphinx/domains/javascript.py:261 sphinx/domains/python/_object.py:175
msgid "Returns"
msgstr "Kthime"

#: sphinx/domains/c/__init__.py:253 sphinx/domains/javascript.py:263
#: sphinx/domains/python/_object.py:177
msgid "Return type"
msgstr "Lloj kthimi"

#: sphinx/domains/javascript.py:331
#, python-format
msgid "%s (module)"
msgstr "%s (modul)"

#: sphinx/domains/c/__init__.py:622 sphinx/domains/cpp/__init__.py:764
#: sphinx/domains/javascript.py:368 sphinx/domains/python/__init__.py:574
msgid "function"
msgstr "funksion"

#: sphinx/domains/javascript.py:369 sphinx/domains/python/__init__.py:578
msgid "method"
msgstr "metodë"

#: sphinx/domains/cpp/__init__.py:762 sphinx/domains/javascript.py:370
#: sphinx/domains/python/__init__.py:576
msgid "class"
msgstr "klasë"

#: sphinx/domains/javascript.py:371 sphinx/domains/python/__init__.py:575
msgid "data"
msgstr "të dhëna"

#: sphinx/domains/javascript.py:372 sphinx/domains/python/__init__.py:581
msgid "attribute"
msgstr "atribut"

#: sphinx/domains/javascript.py:373 sphinx/domains/python/__init__.py:583
msgid "module"
msgstr "modul"

#: sphinx/domains/javascript.py:404
#, python-format
msgid "duplicate %s description of %s, other %s in %s"
msgstr "përshkrim %s i përsëdytur i %s, tjetër %s në %s"

#: sphinx/domains/math.py:63
#, python-format
msgid "duplicate label of equation %s, other instance in %s"
msgstr "etiketë e përsëdytur ekuacioni %s, instancë tjetër te %s"

#: sphinx/domains/math.py:118 sphinx/writers/latex.py:2252
#, python-format
msgid "Invalid math_eqref_format: %r"
msgstr "math_eqref_format i pavlefshëm: %r"

#: sphinx/domains/rst.py:127 sphinx/domains/rst.py:184
#, python-format
msgid "%s (directive)"
msgstr "%s (direktivë)"

#: sphinx/domains/rst.py:185 sphinx/domains/rst.py:189
#, python-format
msgid ":%s: (directive option)"
msgstr ":%s: (mundësi direktive)"

#: sphinx/domains/rst.py:213
#, python-format
msgid "%s (role)"
msgstr "%s (rol)"

#: sphinx/domains/rst.py:223
msgid "directive"
msgstr "direktivë"

#: sphinx/domains/rst.py:224
msgid "directive-option"
msgstr ""

#: sphinx/domains/rst.py:225
msgid "role"
msgstr "rol"

#: sphinx/domains/rst.py:247
#, python-format
msgid "duplicate description of %s %s, other instance in %s"
msgstr "përshkrim i përsëdytur %s %s, instancë tjetër te %s"

#: sphinx/domains/c/__init__.py:146
#, python-format
msgid "%s (C %s)"
msgstr "%s (C %s)"

#: sphinx/domains/c/__init__.py:207 sphinx/domains/c/_symbol.py:552
#, python-format
msgid ""
"Duplicate C declaration, also defined at %s:%s.\n"
"Declaration is '.. c:%s:: %s'."
msgstr "Deklarim C i përsëdytur, përkufizuar edhe te %s:%s.\nDeklarimi është '.. c:%s:: %s'."

#: sphinx/domains/c/__init__.py:245 sphinx/domains/cpp/__init__.py:344
#: sphinx/domains/python/_object.py:163 sphinx/ext/napoleon/docstring.py:762
msgid "Parameters"
msgstr "Parametra"

#: sphinx/domains/c/__init__.py:248 sphinx/domains/cpp/__init__.py:357
msgid "Return values"
msgstr ""

#: sphinx/domains/c/__init__.py:620 sphinx/domains/cpp/__init__.py:765
msgid "member"
msgstr "anëtar"

#: sphinx/domains/c/__init__.py:621
msgid "variable"
msgstr "ndryshore"

#: sphinx/domains/c/__init__.py:623
msgid "macro"
msgstr "makro"

#: sphinx/domains/c/__init__.py:624
msgid "struct"
msgstr ""

#: sphinx/domains/c/__init__.py:625 sphinx/domains/cpp/__init__.py:763
msgid "union"
msgstr "bashkim"

#: sphinx/domains/c/__init__.py:626 sphinx/domains/cpp/__init__.py:768
msgid "enum"
msgstr ""

#: sphinx/domains/c/__init__.py:627 sphinx/domains/cpp/__init__.py:769
msgid "enumerator"
msgstr ""

#: sphinx/domains/c/__init__.py:628 sphinx/domains/cpp/__init__.py:766
msgid "type"
msgstr "lloj"

#: sphinx/domains/c/__init__.py:630 sphinx/domains/cpp/__init__.py:771
msgid "function parameter"
msgstr "parametër funksioni"

#: sphinx/domains/cpp/__init__.py:63
msgid "Template Parameters"
msgstr "Parametra Gjedhesh"

#: sphinx/domains/cpp/__init__.py:185
#, python-format
msgid "%s (C++ %s)"
msgstr "%s (C++ %s)"

#: sphinx/domains/cpp/__init__.py:268 sphinx/domains/cpp/_symbol.py:790
#, python-format
msgid ""
"Duplicate C++ declaration, also defined at %s:%s.\n"
"Declaration is '.. cpp:%s:: %s'."
msgstr "Deklarim C++ i përsëdytur, përkufizuar edhe te %s:%s.\nDeklarimi është '.. cpp:%s:: %s'."

#: sphinx/domains/cpp/__init__.py:767
msgid "concept"
msgstr "koncept"

#: sphinx/domains/cpp/__init__.py:772
msgid "template parameter"
msgstr "parametër gjedheje"

#: sphinx/domains/python/__init__.py:94 sphinx/domains/python/__init__.py:231
#, python-format
msgid "%s() (in module %s)"
msgstr "%s() (te moduli %s)"

#: sphinx/domains/python/__init__.py:154 sphinx/domains/python/__init__.py:321
#: sphinx/domains/python/__init__.py:372
#, python-format
msgid "%s (in module %s)"
msgstr "%s (te moduli %s)"

#: sphinx/domains/python/__init__.py:156
#, python-format
msgid "%s (built-in variable)"
msgstr "%s (ndryshore e brendshme)"

#: sphinx/domains/python/__init__.py:181
#, python-format
msgid "%s (built-in class)"
msgstr "%s (klasë e brendshme)"

#: sphinx/domains/python/__init__.py:182
#, python-format
msgid "%s (class in %s)"
msgstr "%s (klasë te %s)"

#: sphinx/domains/python/__init__.py:236
#, python-format
msgid "%s() (%s class method)"
msgstr "%s() (metodë klase %s)"

#: sphinx/domains/python/__init__.py:238
#, python-format
msgid "%s() (%s static method)"
msgstr "%s() (metodë statike %s)"

#: sphinx/domains/python/__init__.py:376
#, python-format
msgid "%s (%s property)"
msgstr "%s (veti %s)"

#: sphinx/domains/python/__init__.py:502
msgid "Python Module Index"
msgstr "Tregues Modulesh Python"

#: sphinx/domains/python/__init__.py:503
msgid "modules"
msgstr "module"

#: sphinx/domains/python/__init__.py:552
msgid "Deprecated"
msgstr "Nxjerrë nga përdorimi"

#: sphinx/domains/python/__init__.py:577
msgid "exception"
msgstr "përjashtim"

#: sphinx/domains/python/__init__.py:579
msgid "class method"
msgstr "metodë klase"

#: sphinx/domains/python/__init__.py:580
msgid "static method"
msgstr "metodë statike"

#: sphinx/domains/python/__init__.py:582
msgid "property"
msgstr "veti"

#: sphinx/domains/python/__init__.py:640
#, python-format
msgid ""
"duplicate object description of %s, other instance in %s, use :no-index: for"
" one of them"
msgstr "përshkrim i përsëdytur objekti %s, hasje tjetër te %s, për njërin prej tyre përdorni :no-index:"

#: sphinx/domains/python/__init__.py:760
#, python-format
msgid "more than one target found for cross-reference %r: %s"
msgstr "për ndërreferencën %r u gjet më shumë se një objektiv: %s"

#: sphinx/domains/python/__init__.py:821
msgid " (deprecated)"
msgstr " (nxjerrë nga përdorimi)"

#: sphinx/domains/python/_object.py:168
msgid "Variables"
msgstr "Ndryshore"

#: sphinx/domains/python/_object.py:172
msgid "Raises"
msgstr ""

#: sphinx/domains/std/__init__.py:80 sphinx/domains/std/__init__.py:97
#, python-format
msgid "environment variable; %s"
msgstr "ndryshore mjedisi; %s"

#: sphinx/domains/std/__init__.py:157
#, python-format
msgid ""
"Malformed option description %r, should look like \"opt\", \"-opt args\", \""
"--opt args\", \"/opt args\" or \"+opt args\""
msgstr "Përshkrim i keqformuar mundësie %r, duhet të duket si \"opt\", \"-opt args\", \"--opt args\", \"/opt args\" ose \"+opt args\""

#: sphinx/domains/std/__init__.py:228
#, python-format
msgid "%s command line option"
msgstr "Mundësi për rresht urdhrash %s"

#: sphinx/domains/std/__init__.py:230
msgid "command line option"
msgstr "mundësi për rresht urdhrash"

#: sphinx/domains/std/__init__.py:348
msgid "glossary term must be preceded by empty line"
msgstr "termi i fjalorthit duhet të paraprihet nga një rresht i zbrazët"

#: sphinx/domains/std/__init__.py:356
msgid "glossary terms must not be separated by empty lines"
msgstr "termat e fjalorthit s’duhet të paraprihet nga rreshta të zbrazët"

#: sphinx/domains/std/__init__.py:362 sphinx/domains/std/__init__.py:375
msgid "glossary seems to be misformatted, check indentation"
msgstr "fjalorthi duket të jetë i keformatuar, kontrolloni shmangie kryeradhe"

#: sphinx/domains/std/__init__.py:518
msgid "glossary term"
msgstr "term fjalorthi"

#: sphinx/domains/std/__init__.py:519
msgid "grammar token"
msgstr ""

#: sphinx/domains/std/__init__.py:520
msgid "reference label"
msgstr "etiketë reference"

#: sphinx/domains/std/__init__.py:522
msgid "environment variable"
msgstr "ndryshore mjedisi"

#: sphinx/domains/std/__init__.py:523
msgid "program option"
msgstr "mundësi programi"

#: sphinx/domains/std/__init__.py:524
msgid "document"
msgstr "dokument"

#: sphinx/domains/std/__init__.py:560 sphinx/domains/std/__init__.py:572
msgid "Module Index"
msgstr "Tregues Modulesh"

#: sphinx/domains/std/__init__.py:561 sphinx/domains/std/__init__.py:573
#: sphinx/themes/basic/defindex.html:25
msgid "Search Page"
msgstr "Faqe Kërkimesh"

#: sphinx/domains/std/__init__.py:616 sphinx/domains/std/__init__.py:722
#: sphinx/ext/autosectionlabel.py:53
#, python-format
msgid "duplicate label %s, other instance in %s"
msgstr "etiketë %s e përsëdytur, tjetër instancë te %s"

#: sphinx/domains/std/__init__.py:635
#, python-format
msgid "duplicate %s description of %s, other instance in %s"
msgstr "përshkrim %s i përsëdytur për %s, tjetër instancë te %s"

#: sphinx/domains/std/__init__.py:841
msgid "numfig is disabled. :numref: is ignored."
msgstr "numfig është i çaktivizuar. :numref: është shpërfillur."

#: sphinx/domains/std/__init__.py:849
#, python-format
msgid "Failed to create a cross reference. Any number is not assigned: %s"
msgstr "S’u arrit të krijohej një ndërreferencë. S’u caktua ndonjë numër: %s"

#: sphinx/domains/std/__init__.py:861
#, python-format
msgid "the link has no caption: %s"
msgstr "lidhja s’ka titull: %s"

#: sphinx/domains/std/__init__.py:875
#, python-format
msgid "invalid numfig_format: %s (%r)"
msgstr "numfig_format i pavlefshëm: %s (%r)"

#: sphinx/domains/std/__init__.py:878
#, python-format
msgid "invalid numfig_format: %s"
msgstr "numfig_format i pavlefshëm: %s"

#: sphinx/domains/std/__init__.py:1109
#, python-format
msgid "undefined label: %r"
msgstr "etiketë e papërkufizuar: %r"

#: sphinx/domains/std/__init__.py:1111
#, python-format
msgid "Failed to create a cross reference. A title or caption not found: %r"
msgstr "S’u arrit të krijohet ndërreferencë. S’u gjet një titull, ose një përshkrim: %r"

#: sphinx/environment/__init__.py:71
msgid "new config"
msgstr "formësim i ri"

#: sphinx/environment/__init__.py:72
msgid "config changed"
msgstr "formësimi ndryshoi"

#: sphinx/environment/__init__.py:73
msgid "extensions changed"
msgstr "zgjerimet u ndryshuan"

#: sphinx/environment/__init__.py:279
msgid "build environment version not current"
msgstr "version jo i tanishëm i mjedisit të montimit"

#: sphinx/environment/__init__.py:281
msgid "source directory has changed"
msgstr "drejtoria burim ka ndryshuar"

#: sphinx/environment/__init__.py:360
msgid ""
"This environment is incompatible with the selected builder, please choose "
"another doctree directory."
msgstr "Ky mjedis është i papërputhshëm me montuesin e përzgjedhur, ju lutemi, zgjidhni një tjetër drejtori doctree."

#: sphinx/environment/__init__.py:459
#, python-format
msgid "Failed to scan documents in %s: %r"
msgstr "S’u arrit të skanohen dokumente te %s: %r"

#: sphinx/environment/__init__.py:596
#, python-format
msgid "Domain %r is not registered"
msgstr "Përkatësia %r s’është e regjistruar"

#: sphinx/environment/__init__.py:730
msgid "document isn't included in any toctree"
msgstr "dokumenti s’është i përfshirë në ndonjë toctree"

#: sphinx/environment/__init__.py:766
msgid "self referenced toctree found. Ignored."
msgstr "U gjet “toctree” që i referohet vetes. U shpërfill."

#: sphinx/environment/adapters/indexentries.py:69
#, python-format
msgid "see %s"
msgstr "shihni %s"

#: sphinx/environment/adapters/indexentries.py:73
#, python-format
msgid "see also %s"
msgstr "shihni edhe %s"

#: sphinx/environment/adapters/indexentries.py:76
#, python-format
msgid "unknown index entry type %r"
msgstr "lloj i panjohur zëri treguesi: %r"

#: sphinx/environment/adapters/indexentries.py:187
#: sphinx/templates/latex/sphinxmessages.sty_t:11
msgid "Symbols"
msgstr "Simbole"

#: sphinx/environment/adapters/toctree.py:297
#, python-format
msgid "circular toctree references detected, ignoring: %s <- %s"
msgstr "u pikasën referenca rrethore toctree-je, po shpërfllen: %s <- %s"

#: sphinx/environment/adapters/toctree.py:317
#, python-format
msgid ""
"toctree contains reference to document %r that doesn't have a title: no link"
" will be generated"
msgstr "toctree përmban referencë ndaj dokumenti %r që s’ka titull: s’do të prodhohet ndonjë lidhje"

#: sphinx/environment/adapters/toctree.py:326
#, python-format
msgid "toctree contains reference to non-included document %r"
msgstr "toctree përmban referencë dokumenti të papërfshirë %r"

#: sphinx/environment/collectors/asset.py:89
#, python-format
msgid "image file not readable: %s"
msgstr "kartelë figure jo e lexueshme: %s"

#: sphinx/environment/collectors/asset.py:108
#, python-format
msgid "image file %s not readable: %s"
msgstr "kartelë figure %s jo e lexueshme: %s"

#: sphinx/environment/collectors/asset.py:134
#, python-format
msgid "download file not readable: %s"
msgstr "kartelë shkarkimi jo e lexueshme: %s"

#: sphinx/environment/collectors/toctree.py:225
#, python-format
msgid "%s is already assigned section numbers (nested numbered toctree?)"
msgstr ""

#: sphinx/ext/apidoc.py:86
#, python-format
msgid "Would create file %s."
msgstr "Do të krijonte kartelë %s."

#: sphinx/ext/apidoc.py:318
msgid ""
"\n"
"Look recursively in <MODULE_PATH> for Python modules and packages and create\n"
"one reST file with automodule directives per package in the <OUTPUT_PATH>.\n"
"\n"
"The <EXCLUDE_PATTERN>s can be file and/or directory patterns that will be\n"
"excluded from generation.\n"
"\n"
"Note: By default this script will not overwrite already created files."
msgstr "\nShih në mënyrë rekursive te <MODULE_PATH> për module dhe\npaketa Python dhe krijo një kartelë reST me direktiva\nautomodulesh për paketë te <OUTPUT_PATH>.\n\n<EXCLUDE_PATTERN>s mund të jetë shprehje kartelash dhe/ose\ndrejtorish që mund të përjashtohen nga prodhimi.\n\nShënim: Si parazgjedhje, ky programth s’do të anashkalojë\nkartela të krijuara tashmë."

#: sphinx/ext/apidoc.py:331
msgid "path to module to document"
msgstr "shteg për te modul për te dokumenti"

#: sphinx/ext/apidoc.py:333
msgid ""
"fnmatch-style file and/or directory patterns to exclude from generation"
msgstr "kartelë fnmatch-style dhe/ose rregullsi drejtorish për t’u përjashtuar prej prodhimit"

#: sphinx/ext/apidoc.py:338
msgid "directory to place all output"
msgstr "drejtori ku të vendosen krejt përfundimet"

#: sphinx/ext/apidoc.py:343
msgid "maximum depth of submodules to show in the TOC (default: 4)"
msgstr "thellësi maksimum nënmodulesh për shfaqje te TEL (parazgjedhje: 4)"

#: sphinx/ext/apidoc.py:346
msgid "overwrite existing files"
msgstr "mbishkruaj kartela ekzistuese"

#: sphinx/ext/apidoc.py:349
msgid ""
"follow symbolic links. Powerful when combined with "
"collective.recipe.omelette."
msgstr "ndiq lidhje simbolike. E fuqishme, kur ndërthuret me collective.recipe.omelette."

#: sphinx/ext/apidoc.py:352
msgid "run the script without creating files"
msgstr "xhiroje programthin pa krijuar kartela"

#: sphinx/ext/apidoc.py:355
msgid "put documentation for each module on its own page"
msgstr "vendose dokumentim për çdo modul në faqe më vete"

#: sphinx/ext/apidoc.py:358
msgid "include \"_private\" modules"
msgstr "përfshi modulet \"_private\""

#: sphinx/ext/apidoc.py:360
msgid "filename of table of contents (default: modules)"
msgstr "emër kartele për tryezën e lëndës (parazgjedhje: modules)"

#: sphinx/ext/apidoc.py:362
msgid "don't create a table of contents file"
msgstr "mos krijo një kartelë tryeze lënde"

#: sphinx/ext/apidoc.py:365
msgid ""
"don't create headings for the module/package packages (e.g. when the "
"docstrings already contain them)"
msgstr "mos krijo krye për paketat modul/paketë (për shembull, kur ato i përmban tashmë docstrings)"

#: sphinx/ext/apidoc.py:370
msgid "put module documentation before submodule documentation"
msgstr "vendose dokumentimin e modulit përpara dokumentimit të nënmodulit"

#: sphinx/ext/apidoc.py:374
msgid ""
"interpret module paths according to PEP-0420 implicit namespaces "
"specification"
msgstr "interpreto shtigje modulesh sipas specifikimeve impicite PEP-0420 për emërhapësira"

#: sphinx/ext/apidoc.py:378
msgid "file suffix (default: rst)"
msgstr "prapashtesë kartele (parazgjedhje: rst)"

#: sphinx/ext/apidoc.py:380
msgid "generate a full project with sphinx-quickstart"
msgstr "prodho me sphinx-quickstart një projekt të plotë"

#: sphinx/ext/apidoc.py:383
msgid "append module_path to sys.path, used when --full is given"
msgstr "shto module_path pas sys.path, e përdorur kur është dhënë --full"

#: sphinx/ext/apidoc.py:385
msgid "project name (default: root module name)"
msgstr "emër projekti (parazgjedhje: emër moduli rrënjë)"

#: sphinx/ext/apidoc.py:387
msgid "project author(s), used when --full is given"
msgstr "autor(ë) projekti, e përdorur kur është dhënë --full"

#: sphinx/ext/apidoc.py:389
msgid "project version, used when --full is given"
msgstr "version projekti, e përdorur kur është dhënë --full"

#: sphinx/ext/apidoc.py:391
msgid "project release, used when --full is given, defaults to --doc-version"
msgstr "hedhje në qarkullim e projektit, e përdorur kur është dhënë --full, si parazgjedhje merr --doc-version"

#: sphinx/ext/apidoc.py:394
msgid "extension options"
msgstr "mundësi zgjatimi"

#: sphinx/ext/apidoc.py:427
#, python-format
msgid "%s is not a directory."
msgstr "%s s’është drejtori."

#: sphinx/ext/autosectionlabel.py:49
#, python-format
msgid "section \"%s\" gets labeled as \"%s\""
msgstr "pjesa “%s” etiketohet si “%s”"

#: sphinx/ext/coverage.py:46
#, python-format
msgid "invalid regex %r in %s"
msgstr "shprehje e rregullt e pavlefshme %r te %s"

#: sphinx/ext/coverage.py:75
#, python-format
msgid ""
"Testing of coverage in the sources finished, look at the results in "
"%(outdir)spython.txt."
msgstr "Testimi i mbulimit te burimet përfundoi, shihni te përfundimet në %(outdir)spython.txt."

#: sphinx/ext/coverage.py:89
#, python-format
msgid "invalid regex %r in coverage_c_regexes"
msgstr "shprehje e rregullt %r e pavlefshme te coverage_c_regexes"

#: sphinx/ext/coverage.py:157
#, python-format
msgid "undocumented c api: %s [%s] in file %s"
msgstr "API C e padokumentuar: %s [%s] te kartela %s"

#: sphinx/ext/coverage.py:189
#, python-format
msgid "module %s could not be imported: %s"
msgstr "moduli %s s’u importua dot: %s"

#: sphinx/ext/coverage.py:340
#, python-format
msgid "undocumented python function: %s :: %s"
msgstr "funksion python i padokumentuar: %s :: %s"

#: sphinx/ext/coverage.py:356
#, python-format
msgid "undocumented python class: %s :: %s"
msgstr "klasë python e padokumentuar: %s :: %s"

#: sphinx/ext/coverage.py:369
#, python-format
msgid "undocumented python method: %s :: %s :: %s"
msgstr "metodë python e padokumentuar: %s :: %s :: %s"

#: sphinx/ext/doctest.py:115
#, python-format
msgid "missing '+' or '-' in '%s' option."
msgstr "mungon '+' ose '-' te mundësia '%s'."

#: sphinx/ext/doctest.py:120
#, python-format
msgid "'%s' is not a valid option."
msgstr "'%s' s’është mundësi e vlefshme."

#: sphinx/ext/doctest.py:134
#, python-format
msgid "'%s' is not a valid pyversion option"
msgstr "'%s' s’është mundësi pyversion e vlefshme"

#: sphinx/ext/doctest.py:220
msgid "invalid TestCode type"
msgstr "lloj TestCode i pavlefshëm"

#: sphinx/ext/doctest.py:281
#, python-format
msgid ""
"Testing of doctests in the sources finished, look at the results in "
"%(outdir)s/output.txt."
msgstr "Testimi i doctests-eve te burimet përfundoi, shihni te përfundimet në %(outdir)s/output.txt."

#: sphinx/ext/doctest.py:438
#, python-format
msgid "no code/output in %s block at %s:%s"
msgstr "s’ka kod/dhënie te blloku %s në %s:%s"

#: sphinx/ext/doctest.py:526
#, python-format
msgid "ignoring invalid doctest code: %r"
msgstr "po shpërfillet kod “doctest” i pavlefshëm: %r"

#: sphinx/ext/duration.py:77
msgid ""
"====================== slowest reading durations ======================="
msgstr "=================== kohëzgjatjet më të ngadalta të leximit ==================="

#: sphinx/ext/extlinks.py:82
#, python-format
msgid ""
"hardcoded link %r could be replaced by an extlink (try using %r instead)"
msgstr ""

#: sphinx/ext/graphviz.py:135
msgid "Graphviz directive cannot have both content and a filename argument"
msgstr "Një direktivë Graphviz s’mund të ketë edhe lëndë, edhe argument emri kartelash"

#: sphinx/ext/graphviz.py:145
#, python-format
msgid "External Graphviz file %r not found or reading it failed"
msgstr "S’u gjet kartelë e jashtme Graphviz %r, ose dështoi leximi i saj"

#: sphinx/ext/graphviz.py:152
msgid "Ignoring \"graphviz\" directive without content."
msgstr "Po shpërfillet direktivë “graphviz” pa lëndë."

#: sphinx/ext/graphviz.py:268
#, python-format
msgid "graphviz_dot executable path must be set! %r"
msgstr "Duhet ujdisur shteg të ekzekutueshmi graphviz_dot! %r"

#: sphinx/ext/graphviz.py:303
#, python-format
msgid ""
"dot command %r cannot be run (needed for graphviz output), check the "
"graphviz_dot setting"
msgstr "urdhri dot %r s’mund të xhirohet (i nevojshëm për çka prodhon graphviz), kontrolloni rregullimin graphviz_dot"

#: sphinx/ext/graphviz.py:310
#, python-format
msgid ""
"dot exited with error:\n"
"[stderr]\n"
"%r\n"
"[stdout]\n"
"%r"
msgstr "dot përfundoi me gabim:\n[stderr]\n%r\n[stdout]\n%r"

#: sphinx/ext/graphviz.py:313
#, python-format
msgid ""
"dot did not produce an output file:\n"
"[stderr]\n"
"%r\n"
"[stdout]\n"
"%r"
msgstr "dot s’prodhoi kartelë përfundim:\n[stderr]\n%r\n[stdout]\n%r"

#: sphinx/ext/graphviz.py:329
#, python-format
msgid "graphviz_output_format must be one of 'png', 'svg', but is %r"
msgstr "graphviz_output_format duhet të jetë një nga 'png', 'svg', por është %r"

#: sphinx/ext/graphviz.py:333 sphinx/ext/graphviz.py:386
#: sphinx/ext/graphviz.py:423
#, python-format
msgid "dot code %r: %s"
msgstr "kod dot %r: %s"

#: sphinx/ext/graphviz.py:436 sphinx/ext/graphviz.py:444
#, python-format
msgid "[graph: %s]"
msgstr "[grafik: %s]"

#: sphinx/ext/graphviz.py:438 sphinx/ext/graphviz.py:446
msgid "[graph]"
msgstr "[grafik]"

#: sphinx/ext/imgconverter.py:39
#, python-format
msgid ""
"Unable to run the image conversion command %r. 'sphinx.ext.imgconverter' requires ImageMagick by default. Ensure it is installed, or set the 'image_converter' option to a custom conversion command.\n"
"\n"
"Traceback: %s"
msgstr "S’arrihet të xhirohet urdhri %r. për shndërrim figure. Si parazgjedhje, 'sphinx.ext.imgconverter' lyp ImageMagick. Sigurohuni se është i instaluar, ose për mundësinë 'image_converter' caktoni një urdhër vetjak shndërrimi .\n\nTraceback: %s"

#: sphinx/ext/imgconverter.py:48 sphinx/ext/imgconverter.py:72
#, python-format
msgid ""
"convert exited with error:\n"
"[stderr]\n"
"%r\n"
"[stdout]\n"
"%r"
msgstr "shndërrimi përfundoi me gabimin:\n[stderr]\n%r\n[stdout]\n%r"

#: sphinx/ext/imgconverter.py:67
#, python-format
msgid "convert command %r cannot be run, check the image_converter setting"
msgstr "s’mund të xhirohet urdhër shndërrimi %r, kontrolloni rregullimin image_converter"

#: sphinx/ext/imgmath.py:158
#, python-format
msgid ""
"LaTeX command %r cannot be run (needed for math display), check the "
"imgmath_latex setting"
msgstr "Urdhri LaTeX %r s’mund të xhirohet (i nevojshëm për shfaqje formulash matematikore), kontrolloni rregullimin imgmath_late"

#: sphinx/ext/imgmath.py:173
#, python-format
msgid ""
"%s command %r cannot be run (needed for math display), check the imgmath_%s "
"setting"
msgstr "Urdhri %s %r s’mund të xhirohet (i nevojshëm për shfaqje formulash matematikore), kontrolloni rregullimin imgmath_%s"

#: sphinx/ext/imgmath.py:327
#, python-format
msgid "display latex %r: %s"
msgstr "shfaq latex %r: %s"

#: sphinx/ext/imgmath.py:361
#, python-format
msgid "inline latex %r: %s"
msgstr "latex brendazi %r: %s"

#: sphinx/ext/imgmath.py:368 sphinx/ext/mathjax.py:53
msgid "Link to this equation"
msgstr "Lidhje për te ky ekuacion"

#: sphinx/ext/intersphinx.py:195
#, python-format
msgid "intersphinx inventory has moved: %s -> %s"
msgstr "inventari intersphinx është lëvizur: %s -> %s"

#: sphinx/ext/intersphinx.py:230
#, python-format
msgid "loading intersphinx inventory from %s..."
msgstr "po ngarkohet inventari intersphinx prej %s…"

#: sphinx/ext/intersphinx.py:244
msgid ""
"encountered some issues with some of the inventories, but they had working "
"alternatives:"
msgstr "u hasën disa probleme me disa nga inventare, por kishin alternativa funksionale:"

#: sphinx/ext/intersphinx.py:250
msgid "failed to reach any of the inventories with the following issues:"
msgstr "s’u arrit të kapej ndonjë inventar me problemet vijuese:"

#: sphinx/ext/intersphinx.py:303
#, python-format
msgid "(in %s v%s)"
msgstr "(te %s v%s)"

#: sphinx/ext/intersphinx.py:305
#, python-format
msgid "(in %s)"
msgstr "(te %s)"

#: sphinx/ext/intersphinx.py:538
#, python-format
msgid "inventory for external cross-reference not found: %r"
msgstr ""

#: sphinx/ext/intersphinx.py:546
#, python-format
msgid "invalid external cross-reference suffix: %r"
msgstr ""

#: sphinx/ext/intersphinx.py:557
#, python-format
msgid "domain for external cross-reference not found: %r"
msgstr ""

#: sphinx/ext/intersphinx.py:750
#, python-format
msgid "external %s:%s reference target not found: %s"
msgstr "s’u gjet objektiv reference të jashtme %s:%s: %s"

#: sphinx/ext/intersphinx.py:775
#, python-format
msgid "intersphinx identifier %r is not string. Ignored"
msgstr "identifikuesi intersphinx %r s’është varg. U shpërfill"

#: sphinx/ext/intersphinx.py:797
#, python-format
msgid "Failed to read intersphinx_mapping[%s], ignored: %r"
msgstr "S’u arrit të lexohej intersphinx_mapping[%s], u shpërfill: %r"

#: sphinx/ext/linkcode.py:69 sphinx/ext/viewcode.py:199
msgid "[source]"
msgstr "[burim]"

#: sphinx/ext/todo.py:69
msgid "Todo"
msgstr "Për T’u Bërë"

#: sphinx/ext/todo.py:102
#, python-format
msgid "TODO entry found: %s"
msgstr "U gjet zë Për T’u Bërë: %s"

#: sphinx/ext/todo.py:161
msgid "<<original entry>>"
msgstr "<<zëri origjinal>>"

#: sphinx/ext/todo.py:163
#, python-format
msgid "(The <<original entry>> is located in %s, line %d.)"
msgstr "(<<original entry>> gjendet te %s, rreshti %d.)"

#: sphinx/ext/todo.py:173
msgid "original entry"
msgstr "zëri origjinal"

#: sphinx/ext/viewcode.py:256
msgid "highlighting module code... "
msgstr "po theksohet kod moduli… "

#: sphinx/ext/viewcode.py:284
msgid "[docs]"
msgstr "[dokumente]"

#: sphinx/ext/viewcode.py:304
msgid "Module code"
msgstr "Kod moduli"

#: sphinx/ext/viewcode.py:310
#, python-format
msgid "<h1>Source code for %s</h1>"
msgstr "<h1>Kod burim për %s</h1>"

#: sphinx/ext/viewcode.py:336
msgid "Overview: module code"
msgstr "Përmbledhje: kod moduli"

#: sphinx/ext/viewcode.py:337
msgid "<h1>All modules for which code is available</h1>"
msgstr "<h1>Krejt modulet për të cilët ka kod të gatshëm</h1>"

#: sphinx/ext/autodoc/__init__.py:135
#, python-format
msgid "invalid value for member-order option: %s"
msgstr "vlerë e pavlefshme mundësie për member-order: %s"

#: sphinx/ext/autodoc/__init__.py:143
#, python-format
msgid "invalid value for class-doc-from option: %s"
msgstr "vlerë e pavlefshme për mundësinë class-doc-from: %s"

#: sphinx/ext/autodoc/__init__.py:399
#, python-format
msgid "invalid signature for auto%s (%r)"
msgstr "nënshkrim i pavlefshëm për auto%s (%r)"

#: sphinx/ext/autodoc/__init__.py:515
#, python-format
msgid "error while formatting arguments for %s: %s"
msgstr "gabim gjatë formatimi argumentesh për %s: %s"

#: sphinx/ext/autodoc/__init__.py:798
#, python-format
msgid ""
"autodoc: failed to determine %s.%s (%r) to be documented, the following exception was raised:\n"
"%s"
msgstr "autodoc: s’u arrit të përcaktohet %s.%s (%r) për t’u dokumentuar, u shfaq përjashtimi vijues:\n%s"

#: sphinx/ext/autodoc/__init__.py:893
#, python-format
msgid ""
"don't know which module to import for autodocumenting %r (try placing a "
"\"module\" or \"currentmodule\" directive in the document, or giving an "
"explicit module name)"
msgstr "s’dihet cili modul të importohet për vetëdokumentim të %r (provoni të vendosni te dokumenti një direktivë \"module\" ose \"currentmodule\", ose të jepni shprehimisht një emër moduli)"

#: sphinx/ext/autodoc/__init__.py:937
#, python-format
msgid "A mocked object is detected: %r"
msgstr ""

#: sphinx/ext/autodoc/__init__.py:956
#, python-format
msgid "error while formatting signature for %s: %s"
msgstr "gabim gjatë formatimi nënshkrimesh për %s: %s"

#: sphinx/ext/autodoc/__init__.py:1019
msgid "\"::\" in automodule name doesn't make sense"
msgstr "\"::\" në emër automoduli nuk ka kuptim"

#: sphinx/ext/autodoc/__init__.py:1026
#, python-format
msgid "signature arguments or return annotation given for automodule %s"
msgstr ""

#: sphinx/ext/autodoc/__init__.py:1039
#, python-format
msgid ""
"__all__ should be a list of strings, not %r (in module %s) -- ignoring "
"__all__"
msgstr "__all__ should duhet të jetë një listë vargjesh, jo %r (në module %s) -- ignoring __all__"

#: sphinx/ext/autodoc/__init__.py:1105
#, python-format
msgid ""
"missing attribute mentioned in :members: option: module %s, attribute %s"
msgstr "u përmend atribut që mungon në :members: mundësi: modul %s, atributi %s"

#: sphinx/ext/autodoc/__init__.py:1327 sphinx/ext/autodoc/__init__.py:1404
#: sphinx/ext/autodoc/__init__.py:2824
#, python-format
msgid "Failed to get a function signature for %s: %s"
msgstr "S’u arrit të merret një nënshkrim funksioni për %s: %s"

#: sphinx/ext/autodoc/__init__.py:1618
#, python-format
msgid "Failed to get a constructor signature for %s: %s"
msgstr "S’u arrit të merrej nënshkrim konstruktori për %s: %s"

#: sphinx/ext/autodoc/__init__.py:1745
#, python-format
msgid "Bases: %s"
msgstr "Baza: %s"

#: sphinx/ext/autodoc/__init__.py:1759
#, python-format
msgid "missing attribute %s in object %s"
msgstr "atribut %s që mungon te objekt %s"

#: sphinx/ext/autodoc/__init__.py:1858 sphinx/ext/autodoc/__init__.py:1895
#: sphinx/ext/autodoc/__init__.py:1990
#, python-format
msgid "alias of %s"
msgstr "alias për %s"

#: sphinx/ext/autodoc/__init__.py:1878
#, python-format
msgid "alias of TypeVar(%s)"
msgstr "alias për TypeVar(%s)"

#: sphinx/ext/autodoc/__init__.py:2216 sphinx/ext/autodoc/__init__.py:2316
#, python-format
msgid "Failed to get a method signature for %s: %s"
msgstr "S’u arrit të merre një nënshkrim metode për %s: %s"

#: sphinx/ext/autodoc/__init__.py:2447
#, python-format
msgid "Invalid __slots__ found on %s. Ignored."
msgstr "U gjet __slots__ i pavlefshëm në %s. U shpërfill."

#: sphinx/ext/autodoc/preserve_defaults.py:190
#, python-format
msgid "Failed to parse a default argument value for %r: %s"
msgstr "S’u arrit të përtypej një vlerë parazgjedhje argumenti për %r: %s"

#: sphinx/ext/autodoc/type_comment.py:132
#, python-format
msgid "Failed to update signature for %r: parameter not found: %s"
msgstr "S’u arrit të përditësohet nënshkrim për %r: s’u gjet parametër: %s"

#: sphinx/ext/autodoc/type_comment.py:135
#, python-format
msgid "Failed to parse type_comment for %r: %s"
msgstr "S’u arrit të përtypet type_comment për %r: %s"

#: sphinx/ext/autosummary/__init__.py:251
#, python-format
msgid "autosummary references excluded document %r. Ignored."
msgstr "referenca vetëpërmbledhjeje përjashtuan dokumentin %r. U shpërfill."

#: sphinx/ext/autosummary/__init__.py:253
#, python-format
msgid ""
"autosummary: stub file not found %r. Check your autosummary_generate "
"setting."
msgstr "vetëpërmbledhje: s’u gjet kartelë stub %r. Kontrolloni rregullimin tuaj autosummary_generate."

#: sphinx/ext/autosummary/__init__.py:272
msgid "A captioned autosummary requires :toctree: option. ignored."
msgstr ""

#: sphinx/ext/autosummary/__init__.py:325
#, python-format
msgid ""
"autosummary: failed to import %s.\n"
"Possible hints:\n"
"%s"
msgstr "përmbledhje e automatizuar: s’u arrit të importohej %s.\nNdihmëza të mundshme:\n%s"

#: sphinx/ext/autosummary/__init__.py:339
#, python-format
msgid "failed to parse name %s"
msgstr "s’u arrit të përtypej emri %s"

#: sphinx/ext/autosummary/__init__.py:344
#, python-format
msgid "failed to import object %s"
msgstr "s’u arrit të importohej objekti %s"

#: sphinx/ext/autosummary/__init__.py:802
#, python-format
msgid "autosummary_generate: file not found: %s"
msgstr "autosummary_generate: s’u gjet kartelë: %s"

#: sphinx/ext/autosummary/__init__.py:810
msgid ""
"autosummary generates .rst files internally. But your source_suffix does not"
" contain .rst. Skipped."
msgstr "veçoria e përmbledhjes së automatizuar prodhon kartela .rst së brendshmi. Por source_suffix juaj nuk përmban .rst. U anashkalua."

#: sphinx/ext/autosummary/generate.py:200
#: sphinx/ext/autosummary/generate.py:358
#, python-format
msgid ""
"autosummary: failed to determine %r to be documented, the following exception was raised:\n"
"%s"
msgstr "vetëpërmbledhje: s’u arrit të përcaktohet %r për t’u dokumentuar, u shfaq përjashtimi vijues:\n%s"

#: sphinx/ext/autosummary/generate.py:470
#, python-format
msgid "[autosummary] generating autosummary for: %s"
msgstr "[vetëpërmbledhje] prodhim vetëpërmbledhje për: %s"

#: sphinx/ext/autosummary/generate.py:474
#, python-format
msgid "[autosummary] writing to %s"
msgstr "[vetëpërmbledhje] po shkruhet te %s"

#: sphinx/ext/autosummary/generate.py:517
#, python-format
msgid ""
"[autosummary] failed to import %s.\n"
"Possible hints:\n"
"%s"
msgstr "[autosummary] s’u arrit të importohej %s.\nNdihmëza të mundshme:\n%s"

#: sphinx/ext/autosummary/generate.py:690
msgid ""
"\n"
"Generate ReStructuredText using autosummary directives.\n"
"\n"
"sphinx-autogen is a frontend to sphinx.ext.autosummary.generate. It generates\n"
"the reStructuredText files from the autosummary directives contained in the\n"
"given input files.\n"
"\n"
"The format of the autosummary directive is documented in the\n"
"``sphinx.ext.autosummary`` Python module and can be read using::\n"
"\n"
"  pydoc sphinx.ext.autosummary\n"
msgstr "\nProdhoni ReStructuredText duke përdorur direktiva vetëpërmbledhje.\n\nsphinx-autogen është një ndërfaqe pamore për sphinx.ext.autosummary.generate. Prodhon\nkartela reStructuredText nga direktiva vetëpërmbledhjeje që përmbahen te\nkartelat e dhëna.\n\nFormati i direktivës vetëpërmbledhje dokumentohet te\nmoduli Python ``sphinx.ext.autosummary`` dhe mund të lexohet duke përdorur::\n\n  pydoc sphinx.ext.autosummary\n"

#: sphinx/ext/autosummary/generate.py:707
msgid "source files to generate rST files for"
msgstr "kartela burim për të cilat të krijohen kartela rST"

#: sphinx/ext/autosummary/generate.py:711
msgid "directory to place all output in"
msgstr "drejtori ku të vendosen krejt përfundimet"

#: sphinx/ext/autosummary/generate.py:714
#, python-format
msgid "default suffix for files (default: %(default)s)"
msgstr "prapashtesë parazgjedhje për kartela (parazgjedhje: %(default)s)"

#: sphinx/ext/autosummary/generate.py:718
#, python-format
msgid "custom template directory (default: %(default)s)"
msgstr "drejtori gjedhesh vetjake (parazgjedhje: %(default)s)"

#: sphinx/ext/autosummary/generate.py:722
#, python-format
msgid "document imported members (default: %(default)s)"
msgstr "pjesë të importuara të dokumentit (parazgjedhje: %(default)s)"

#: sphinx/ext/autosummary/generate.py:726
#, python-format
msgid ""
"document exactly the members in module __all__ attribute. (default: "
"%(default)s)"
msgstr "dokumentoni saktësisht pjesët te moduli __all__ attribute. (parazgjedhje: %(default)s)"

#: sphinx/ext/napoleon/__init__.py:341 sphinx/ext/napoleon/docstring.py:728
msgid "Keyword Arguments"
msgstr "Argumente Fjalëkyçi"

#: sphinx/ext/napoleon/docstring.py:682
msgid "Example"
msgstr "Shembull"

#: sphinx/ext/napoleon/docstring.py:683
msgid "Examples"
msgstr "Shembuj"

#: sphinx/ext/napoleon/docstring.py:744
msgid "Notes"
msgstr "Shënime"

#: sphinx/ext/napoleon/docstring.py:753
msgid "Other Parameters"
msgstr "Parametra të Tjerë"

#: sphinx/ext/napoleon/docstring.py:789
msgid "Receives"
msgstr ""

#: sphinx/ext/napoleon/docstring.py:793
msgid "References"
msgstr "Referenca"

#: sphinx/ext/napoleon/docstring.py:825
msgid "Warns"
msgstr "Sinjalizime"

#: sphinx/ext/napoleon/docstring.py:829
msgid "Yields"
msgstr ""

#: sphinx/ext/napoleon/docstring.py:987
#, python-format
msgid "invalid value set (missing closing brace): %s"
msgstr "u caktua vlerë e pavlefshme (mungon kllapë mbyllëse): %s"

#: sphinx/ext/napoleon/docstring.py:994
#, python-format
msgid "invalid value set (missing opening brace): %s"
msgstr "u caktua vlerë e pavlefshme (mungon kllapë hapëse): %s"

#: sphinx/ext/napoleon/docstring.py:1001
#, python-format
msgid "malformed string literal (missing closing quote): %s"
msgstr "shprehje vargu e keqformuar (mungon thonjëz mbyllëse): %s"

#: sphinx/ext/napoleon/docstring.py:1008
#, python-format
msgid "malformed string literal (missing opening quote): %s"
msgstr "shprehje vargu e keqformuar (mungon thonjëz hapëse): %s"

#: sphinx/locale/__init__.py:228
msgid "Attention"
msgstr "Vëmendje"

#: sphinx/locale/__init__.py:229
msgid "Caution"
msgstr "Kujdes"

#: sphinx/locale/__init__.py:230
msgid "Danger"
msgstr "Rrezik"

#: sphinx/locale/__init__.py:231
msgid "Error"
msgstr "Gabim"

#: sphinx/locale/__init__.py:232
msgid "Hint"
msgstr "Ndihmëz"

#: sphinx/locale/__init__.py:233
msgid "Important"
msgstr "E rëndësishme"

#: sphinx/locale/__init__.py:234
msgid "Note"
msgstr "Shënim"

#: sphinx/locale/__init__.py:235
msgid "See also"
msgstr "Shihni edhe"

#: sphinx/locale/__init__.py:236
msgid "Tip"
msgstr "Ndihmëz"

#: sphinx/locale/__init__.py:237
msgid "Warning"
msgstr "Sinjalizim"

#: sphinx/templates/latex/longtable.tex_t:52
#: sphinx/templates/latex/sphinxmessages.sty_t:8
msgid "continued from previous page"
msgstr "vazhduar nga faqja e mëparshme"

#: sphinx/templates/latex/longtable.tex_t:63
#: sphinx/templates/latex/sphinxmessages.sty_t:9
msgid "continues on next page"
msgstr "vazhdon në faqen pasuese"

#: sphinx/templates/latex/sphinxmessages.sty_t:10
msgid "Non-alphabetical"
msgstr "Joalfabetike"

#: sphinx/templates/latex/sphinxmessages.sty_t:12
msgid "Numbers"
msgstr "Numra"

#: sphinx/templates/latex/sphinxmessages.sty_t:13
msgid "page"
msgstr "faqe"

#: sphinx/themes/agogo/layout.html:38 sphinx/themes/basic/globaltoc.html:10
#: sphinx/themes/basic/localtoc.html:12 sphinx/themes/scrolls/layout.html:41
msgid "Table of Contents"
msgstr "Tryeza e Lëndës"

#: sphinx/themes/agogo/layout.html:43 sphinx/themes/basic/layout.html:141
#: sphinx/themes/basic/search.html:11 sphinx/themes/basic/search.html:23
msgid "Search"
msgstr "Kērko"

#: sphinx/themes/agogo/layout.html:46 sphinx/themes/basic/searchbox.html:16
#: sphinx/themes/basic/searchfield.html:18
msgid "Go"
msgstr "Shko"

#: sphinx/themes/agogo/layout.html:90 sphinx/themes/basic/sourcelink.html:15
msgid "Show Source"
msgstr "Shfaq Burimin"

#: sphinx/themes/basic/defindex.html:11
msgid "Overview"
msgstr "Përmbledhje"

#: sphinx/themes/basic/defindex.html:15
msgid "Welcome! This is"
msgstr "Mirë se vini! Ky është"

#: sphinx/themes/basic/defindex.html:16
msgid "the documentation for"
msgstr "dokumentimi për"

#: sphinx/themes/basic/defindex.html:17
msgid "last updated"
msgstr "përditësuar së fundi më"

#: sphinx/themes/basic/defindex.html:20
msgid "Indices and tables:"
msgstr "Tregues dhe tabela:"

#: sphinx/themes/basic/defindex.html:23
msgid "Complete Table of Contents"
msgstr "Tryezë e Plotë e Lëndës"

#: sphinx/themes/basic/defindex.html:24
msgid "lists all sections and subsections"
msgstr "paraqet krejt ndarjet dhe nënndarjet"

#: sphinx/themes/basic/defindex.html:26
msgid "search this documentation"
msgstr "kërkoni te ky dokumentim"

#: sphinx/themes/basic/defindex.html:28
msgid "Global Module Index"
msgstr "Tregues Global Modulesh"

#: sphinx/themes/basic/defindex.html:29
msgid "quick access to all modules"
msgstr "hyrje e shpejtë te krejt modulet"

#: sphinx/themes/basic/defindex.html:31
msgid "all functions, classes, terms"
msgstr "krejt funksionet, klasat, termat"

#: sphinx/themes/basic/genindex-single.html:33
#, python-format
msgid "Index &ndash; %(key)s"
msgstr "Tregues &ndash; %(key)s"

#: sphinx/themes/basic/genindex-single.html:61
#: sphinx/themes/basic/genindex-split.html:24
#: sphinx/themes/basic/genindex-split.html:38
#: sphinx/themes/basic/genindex.html:73
msgid "Full index on one page"
msgstr "Tregues i plotë në një faqe"

#: sphinx/themes/basic/genindex-split.html:16
msgid "Index pages by letter"
msgstr "Faqe treguesi sipas shkronjash"

#: sphinx/themes/basic/genindex-split.html:25
msgid "can be huge"
msgstr "mund të jetë i stërmadh"

#: sphinx/themes/basic/layout.html:26
msgid "Navigation"
msgstr "Lëvizje"

#: sphinx/themes/basic/layout.html:126
#, python-format
msgid "Search within %(docstitle)s"
msgstr "Kërkoni brenda %(docstitle)s"

#: sphinx/themes/basic/layout.html:135
msgid "About these documents"
msgstr "Mbi këto dokumente"

#: sphinx/themes/basic/layout.html:144 sphinx/themes/basic/layout.html:188
#: sphinx/themes/basic/layout.html:190
msgid "Copyright"
msgstr "Të drejta kopjimi"

#: sphinx/themes/basic/layout.html:194 sphinx/themes/basic/layout.html:200
#, python-format
msgid "&#169; %(copyright_prefix)s %(copyright)s."
msgstr "&#169; %(copyright_prefix)s %(copyright)s."

#: sphinx/themes/basic/layout.html:212
#, python-format
msgid "Last updated on %(last_updated)s."
msgstr "Përditësuar së fundi më %(last_updated)s."

#: sphinx/themes/basic/layout.html:215
#, python-format
msgid ""
"Created using <a href=\"https://www.sphinx-doc.org/\">Sphinx</a> "
"%(sphinx_version)s."
msgstr "Krijuar duke përdorur <a href=\"https://www.sphinx-doc.org/\">Sphinx</a> %(sphinx_version)s."

#: sphinx/themes/basic/opensearch.xml:4
#, python-format
msgid "Search %(docstitle)s"
msgstr "Kërkoni te %(docstitle)s"

#: sphinx/themes/basic/relations.html:12
msgid "Previous topic"
msgstr "Subjekti i mëparshëm"

#: sphinx/themes/basic/relations.html:14
msgid "previous chapter"
msgstr "kapitulli i mëparshëm"

#: sphinx/themes/basic/relations.html:19
msgid "Next topic"
msgstr "Subjekti pasues"

#: sphinx/themes/basic/relations.html:21
msgid "next chapter"
msgstr "kapitulli pasues"

#: sphinx/themes/basic/search.html:28
msgid ""
"Please activate JavaScript to enable the search\n"
"    functionality."
msgstr "Që të aktivizohet funksioni i kërkimit, ju lutemi, aktivizoni\n    JavaScript-in."

#: sphinx/themes/basic/search.html:36
msgid ""
"Searching for multiple words only shows matches that contain\n"
"    all words."
msgstr "Kërkimi për disa fjalë njëherësh shfaq vetëm përputhje që\n    përmbajnë krejt fjalët."

#: sphinx/themes/basic/search.html:43
msgid "search"
msgstr "kërko"

#: sphinx/themes/basic/searchbox.html:12
msgid "Quick search"
msgstr "Kërkim i shpejtë"

#: sphinx/themes/basic/sourcelink.html:12
msgid "This Page"
msgstr "Kjo Faqe"

#: sphinx/themes/basic/changes/frameset.html:5
#: sphinx/themes/basic/changes/versionchanges.html:12
#, python-format
msgid "Changes in Version %(version)s &#8212; %(docstitle)s"
msgstr "Ndryshe në Versionin %(version)s &#8212; %(docstitle)s"

#: sphinx/themes/basic/changes/rstsource.html:5
#, python-format
msgid "%(filename)s &#8212; %(docstitle)s"
msgstr "%(filename)s &#8212; %(docstitle)s"

#: sphinx/themes/basic/changes/versionchanges.html:17
#, python-format
msgid "Automatically generated list of changes in version %(version)s"
msgstr "Listë ndryshime në versionin %(version)s e prodhuar automatikisht"

#: sphinx/themes/basic/changes/versionchanges.html:18
msgid "Library changes"
msgstr "Ndryshime librarie"

#: sphinx/themes/basic/changes/versionchanges.html:23
msgid "C API changes"
msgstr "Ndryshime API C"

#: sphinx/themes/basic/changes/versionchanges.html:25
msgid "Other changes"
msgstr "Ndryshime të tjera"

#: sphinx/themes/basic/static/searchtools.js:112
msgid "Search Results"
msgstr "Përfundime Kërkimi"

#: sphinx/themes/basic/static/searchtools.js:114
msgid ""
"Your search did not match any documents. Please make sure that all words are"
" spelled correctly and that you've selected enough categories."
msgstr "Kërkimi juaj s’gjeti përputhje me ndonjë dokument. Ju lutemi, sigurohuni se janë shkruar saktë krejt fjalët dhe se keni përzgjedhur aq kategori sa duhen."

#: sphinx/themes/basic/static/searchtools.js:118
msgid ""
"Search finished, found ${resultCount} page(s) matching the search query."
msgstr "Kërkimi përfundoi, u gjetën ${resultCount} faqe me përputhje me vargun e kërkimit."

#: sphinx/themes/basic/static/searchtools.js:246
msgid "Searching"
msgstr "Kërkim"

#: sphinx/themes/basic/static/searchtools.js:262
msgid "Preparing search..."
msgstr "Po përgatitet kërkim..."

#: sphinx/themes/basic/static/searchtools.js:463
msgid ", in "
msgstr ", në "

#: sphinx/themes/basic/static/sphinx_highlight.js:112
msgid "Hide Search Matches"
msgstr "Fshih Përputhje Kërkimi"

#: sphinx/themes/classic/layout.html:20
#: sphinx/themes/classic/static/sidebar.js_t:57
msgid "Collapse sidebar"
msgstr "Tkurre anështyllën"

#: sphinx/themes/classic/static/sidebar.js_t:48
msgid "Expand sidebar"
msgstr "Zgjeroje anështyllën"

#: sphinx/themes/haiku/layout.html:24
msgid "Contents"
msgstr "Lëndë"

#: sphinx/transforms/__init__.py:128
msgid "could not calculate translation progress!"
msgstr "s’u njehsua dot ecuri përkthimi!"

#: sphinx/transforms/__init__.py:133
msgid "no translated elements!"
msgstr "pa elementë të përkthyer!"

#: sphinx/transforms/__init__.py:250
#, python-format
msgid ""
"4 column based index found. It might be a bug of extensions you use: %r"
msgstr "U gjet tregues me bazë 4 shtylla. Mund të jetë një e metë e zgjerimeve që përdorni: %r"

#: sphinx/transforms/__init__.py:291
#, python-format
msgid "Footnote [%s] is not referenced."
msgstr "Poshtëshënimi [%s] s’është në referencë."

#: sphinx/transforms/__init__.py:297
msgid "Footnote [#] is not referenced."
msgstr "Poshtëshënimi [#] s’është në referencë."

#: sphinx/transforms/i18n.py:205 sphinx/transforms/i18n.py:270
msgid ""
"inconsistent footnote references in translated message. original: {0}, "
"translated: {1}"
msgstr "referenca pa njëtrajtësi, te fundfaqe në mesazhin e përkthyer. origjinali: {0}, përkthimi: {1}"

#: sphinx/transforms/i18n.py:245
msgid ""
"inconsistent references in translated message. original: {0}, translated: "
"{1}"
msgstr "referenca pa njëtrajtësi, te mesazhi i përkthyer. origjinali: {0}, përkthimi: {1}"

#: sphinx/transforms/i18n.py:285
msgid ""
"inconsistent citation references in translated message. original: {0}, "
"translated: {1}"
msgstr "referenca citimi pa njëtrajtësi, te fundfaqe në mesazhin e përkthyer. origjinali: {0}, përkthimi: {1}"

#: sphinx/transforms/i18n.py:302
msgid ""
"inconsistent term references in translated message. original: {0}, "
"translated: {1}"
msgstr "referenca citimi pa njëtrajtësi, te mesazhi i përkthyer. origjinali: {0}, përkthimi: {1}"

#: sphinx/transforms/post_transforms/__init__.py:116
msgid ""
"Could not determine the fallback text for the cross-reference. Might be a "
"bug."
msgstr ""

#: sphinx/transforms/post_transforms/__init__.py:158
#, python-format
msgid "more than one target found for 'any' cross-reference %r: could be %s"
msgstr "u gjet më shumë se një objektiv për ndërreferencën 'any' %r: mund të ishte %s"

#: sphinx/transforms/post_transforms/__init__.py:209
#, python-format
msgid "%s:%s reference target not found: %s"
msgstr "s’u gjet objektiv reference %s:%s: %s"

#: sphinx/transforms/post_transforms/__init__.py:212
#, python-format
msgid "%r reference target not found: %s"
msgstr "s’u gjet objektiv reference %r: %s"

#: sphinx/transforms/post_transforms/images.py:89
#, python-format
msgid "Could not fetch remote image: %s [%d]"
msgstr "S’u pru dot figurë e largët: %s [%d]"

#: sphinx/transforms/post_transforms/images.py:117
#, python-format
msgid "Could not fetch remote image: %s [%s]"
msgstr "S’u soll dot figurë e largët: %s [%s]"

#: sphinx/transforms/post_transforms/images.py:135
#, python-format
msgid "Unknown image format: %s..."
msgstr "Format i panjohur figure: %s…"

#: sphinx/util/__init__.py:168
#, python-format
msgid "undecodable source characters, replacing with \"?\": %r"
msgstr "shenja burimi të padeshifrueshme, po zëvendësohen me \"?\": %r"

#: sphinx/util/display.py:78
msgid "skipped"
msgstr "e anashkaluar"

#: sphinx/util/display.py:83
msgid "failed"
msgstr "e dështuar"

#: sphinx/util/docfields.py:87
#, python-format
msgid ""
"Problem in %s domain: field is supposed to use role '%s', but that role is "
"not in the domain."
msgstr "Problem në përkatësinë %s: fusha supozohet të përdorë rol '%s', por ai rol s’gjendet te përkatësia."

#: sphinx/util/docutils.py:295
#, python-format
msgid "unknown directive or role name: %s:%s"
msgstr "emër direktive ose roli të panjohur: %s:%s"

#: sphinx/util/docutils.py:591
#, python-format
msgid "unknown node type: %r"
msgstr "lloj i panjohur nyjeje: %r"

#: sphinx/util/i18n.py:94
#, python-format
msgid "reading error: %s, %s"
msgstr "gabim leximi: %s, %s"

#: sphinx/util/i18n.py:101
#, python-format
msgid "writing error: %s, %s"
msgstr "gabim shkrimi: %s, %s"

#: sphinx/util/i18n.py:125
#, python-format
msgid "locale_dir %s does not exist"
msgstr "locale_dir %s s’ekziston"

#: sphinx/util/i18n.py:215
#, python-format
msgid ""
"Invalid date format. Quote the string by single quote if you want to output "
"it directly: %s"
msgstr "Format i pavlefshëm datash. Quote the string by single quote Nëse doni të jepet drejtpërsëdrejti, përdorni për vargun thonjëza njëshe: %s"

#: sphinx/util/nodes.py:386
#, python-format
msgid ""
"%r is deprecated for index entries (from entry %r). Use 'pair: %s' instead."
msgstr "%r është nxjerrë nga funksionimi për zëra treguesi (nga zëri %r). Në vend të tij përdorni “pair: %s”."

#: sphinx/util/nodes.py:439
#, python-format
msgid "toctree contains ref to nonexisting file %r"
msgstr "“toctree” përmban referencë për te një kartelë joekzistuese %r"

#: sphinx/util/nodes.py:637
#, python-format
msgid "exception while evaluating only directive expression: %s"
msgstr "përjashtim teksa vlerësohej vetëm shprehje direktive: %s"

#: sphinx/util/rst.py:71
#, python-format
msgid "default role %s not found"
msgstr "s’u gjet rol parazgjedhje %s"

#: sphinx/writers/html5.py:100 sphinx/writers/html5.py:109
msgid "Link to this definition"
msgstr "Lidhje për te ky përkufizim"

#: sphinx/writers/html5.py:397
#, python-format
msgid "numfig_format is not defined for %s"
msgstr "numfig_format s’është i përcaktuar për %s"

#: sphinx/writers/html5.py:407
#, python-format
msgid "Any IDs not assigned for %s node"
msgstr "Çfarëdo ID-sh jo të përshoqëruara për nyjën %s"

#: sphinx/writers/html5.py:462
msgid "Link to this term"
msgstr "Lidhje për te ky term"

#: sphinx/writers/html5.py:496 sphinx/writers/html5.py:501
msgid "Link to this heading"
msgstr "Lidhje për te kjo krye"

#: sphinx/writers/html5.py:505
msgid "Link to this table"
msgstr "Lidhje për te kjo tabelë"

#: sphinx/writers/html5.py:548
msgid "Link to this code"
msgstr "Lidhje për te ky kod"

#: sphinx/writers/html5.py:550
msgid "Link to this image"
msgstr "Lidhje për te kjo figurë"

#: sphinx/writers/html5.py:552
msgid "Link to this toctree"
msgstr "Lidhje për te kjo “toctree”"

#: sphinx/writers/html5.py:688
msgid "Could not obtain image size. :scale: option is ignored."
msgstr "S’u mor dot madhësi figure. Mundësia :scale: u shpërfill."

#: sphinx/writers/latex.py:335
#, python-format
msgid "unknown %r toplevel_sectioning for class %r"
msgstr "toplevel_sectioning %r i panjohur për klasën %r"

#: sphinx/writers/latex.py:386
msgid "too large :maxdepth:, ignored."
msgstr ":maxdepth: shumë i madh, u shpërfill."

#: sphinx/writers/latex.py:625
msgid "document title is not a single Text node"
msgstr "titulli i dokumentit s’është nyje njëshe Teksti"

#: sphinx/writers/latex.py:656 sphinx/writers/texinfo.py:626
msgid ""
"encountered title node not in section, topic, table, admonition or sidebar"
msgstr "u has nyje titulli jo në ndarje, temë, tabelë, paralajmërim ose anështyllë"

#: sphinx/writers/latex.py:959 sphinx/writers/manpage.py:259
#: sphinx/writers/texinfo.py:641
msgid "Footnotes"
msgstr "Poshtëshënime"

#: sphinx/writers/latex.py:1028
msgid ""
"both tabularcolumns and :widths: option are given. :widths: is ignored."
msgstr "janë dhënë që të dyja mundësitë, “tabularcolumns” dhe “:widths:”. shpërfillet :widths:."

#: sphinx/writers/latex.py:1388
#, python-format
msgid "dimension unit %s is invalid. Ignored."
msgstr "njësia e përmasave %s është e pavlefshme. U shpërfill."

#: sphinx/writers/latex.py:1722
#, python-format
msgid "unknown index entry type %s found"
msgstr "u gjet lloj i panjohur %s zërash treguesi"

#: sphinx/writers/manpage.py:305 sphinx/writers/text.py:907
#, python-format
msgid "[image: %s]"
msgstr "[figurë: %s]"

#: sphinx/writers/manpage.py:306 sphinx/writers/text.py:908
msgid "[image]"
msgstr "[figurë]"

#: sphinx/writers/texinfo.py:1197
msgid "caption not inside a figure."
msgstr "titull jo brenda një figure."

#: sphinx/writers/texinfo.py:1284
#, python-format
msgid "unimplemented node type: %r"
msgstr "lloj nyjeje i pasendërtuar: %r"
