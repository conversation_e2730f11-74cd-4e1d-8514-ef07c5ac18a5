import cv2
import json
import logging
import time
import threading
import numpy as np
from pyzbar import pyzbar
from database import DatabaseManager
from config import CAMERA_INDEX, CAPTURE_INTERVAL, CAMERA_PREVIEW_ENABLED, CAMERA_PREVIEW_SIZE, CAMERA_PREVIEW_POSITION

class CameraMonitor:
    def __init__(self, db_manager):
        self.db_manager = db_manager
        self.camera = None
        self.is_running = False
        self.monitor_thread = None
        self.preview_thread = None
        self.capture_lock = threading.Lock()
        self.preview_enabled = CAMERA_PREVIEW_ENABLED
        self.preview_window_name = "Camera Preview - DataTransmission"
        self.preview_closed = False
        self.latest_frame = None
        self.latest_qr_codes = []
        self.frame_lock = threading.Lock()
    
    def initialize_camera(self):
        """初始化摄像头"""
        try:
            self.camera = cv2.VideoCapture(CAMERA_INDEX)
            if not self.camera.isOpened():
                raise Exception(f"无法打开摄像头 {CAMERA_INDEX}")
            
            # 设置摄像头参数
            self.camera.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
            self.camera.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)
            self.camera.set(cv2.CAP_PROP_FPS, 30)
            
            logging.info(f"摄像头 {CAMERA_INDEX} 初始化成功")
            return True

        except Exception as e:
            logging.error(f"初始化摄像头时出错: {e}")
            return False

    def setup_preview_window(self):
        """设置预览窗口"""
        try:
            # 创建窗口
            cv2.namedWindow(self.preview_window_name, cv2.WINDOW_NORMAL | cv2.WINDOW_KEEPRATIO)
            cv2.resizeWindow(self.preview_window_name, CAMERA_PREVIEW_SIZE[0], CAMERA_PREVIEW_SIZE[1])
            cv2.moveWindow(self.preview_window_name, CAMERA_PREVIEW_POSITION[0], CAMERA_PREVIEW_POSITION[1])

            # 设置窗口属性
            try:
                cv2.setWindowProperty(self.preview_window_name, cv2.WND_PROP_TOPMOST, 1)
            except:
                pass  # 某些系统可能不支持置顶

            logging.info(f"摄像头预览窗口已创建: {CAMERA_PREVIEW_SIZE}")
            return True
        except Exception as e:
            logging.error(f"创建预览窗口时出错: {e}")
            self.preview_enabled = False
            return False
    
    def capture_frame(self):
        """捕获一帧图像"""
        if self.camera is None or not self.camera.isOpened():
            return None
        
        with self.capture_lock:
            ret, frame = self.camera.read()
            if ret:
                return frame
            else:
                logging.warning("无法从摄像头读取帧")
                return None
    
    def decode_qr_codes(self, frame):
        """解码图像中的二维码"""
        try:
            # 转换为灰度图像以提高识别效果
            gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
            
            # 使用pyzbar解码二维码
            qr_codes = pyzbar.decode(gray)
            
            decoded_data = []
            for qr_code in qr_codes:
                # 获取二维码数据
                qr_data = qr_code.data.decode('utf-8')
                qr_type = qr_code.type
                
                # 获取二维码位置信息
                points = qr_code.polygon
                if len(points) == 4:
                    # 在原图上绘制二维码边框（可选，用于调试）
                    pts = [(point.x, point.y) for point in points]
                    cv2.polylines(frame, [np.array(pts, dtype=np.int32)], True, (0, 255, 0), 2)
                
                decoded_data.append({
                    'data': qr_data,
                    'type': qr_type,
                    'points': points
                })
                
                logging.debug(f"检测到二维码: {qr_data}")
            
            return decoded_data
            
        except Exception as e:
            logging.error(f"解码二维码时出错: {e}")
            return []
    
    def process_qr_data(self, qr_data_string):
        """处理二维码数据"""
        try:
            # 尝试解析JSON数据
            data_dict = json.loads(qr_data_string)
            
            # 验证必需字段
            required_fields = ['id', 'type', 'data']
            for field in required_fields:
                if field not in data_dict:
                    logging.warning(f"二维码数据缺少必需字段: {field}")
                    return False
            
            id_val = str(data_dict['id'])
            type_val = int(data_dict['type'])
            data_val = str(data_dict['data'])
            
            # 存储到receive_data表
            success = self.db_manager.insert_receive_data(id_val, type_val, data_val)
            
            if success:
                logging.info(f"二维码数据处理成功: id={id_val}, type={type_val}")
            else:
                logging.info(f"二维码数据已存在，丢弃: id={id_val}, type={type_val}")
            
            return success
            
        except json.JSONDecodeError:
            logging.warning(f"二维码数据不是有效的JSON格式: {qr_data_string}")
            return False
        except ValueError as e:
            logging.warning(f"二维码数据格式错误: {e}")
            return False
        except Exception as e:
            logging.error(f"处理二维码数据时出错: {e}")
            return False
    
    def monitor_loop(self):
        """摄像头监控主循环"""
        logging.info("开始摄像头监控")
        
        while self.is_running:
            try:
                # 捕获帧
                frame = self.capture_frame()
                if frame is None:
                    time.sleep(0.1)
                    continue
                
                # 解码二维码
                qr_codes = self.decode_qr_codes(frame)

                # 处理检测到的二维码
                for qr_info in qr_codes:
                    self.process_qr_data(qr_info['data'])

                # 更新预览数据（非阻塞）
                if self.preview_enabled and not self.preview_closed:
                    with self.frame_lock:
                        self.latest_frame = frame.copy()
                        self.latest_qr_codes = qr_codes.copy()

                # 等待指定间隔
                time.sleep(CAPTURE_INTERVAL)
                
            except Exception as e:
                logging.error(f"监控循环中出错: {e}")
                time.sleep(1)
        
        logging.info("摄像头监控已停止")

    def preview_loop(self):
        """预览窗口循环（独立线程）"""
        logging.info("启动预览窗口线程")

        # 设置预览窗口
        if not self.setup_preview_window():
            return

        try:
            while self.is_running and self.preview_enabled and not self.preview_closed:
                try:
                    # 检查窗口是否被关闭
                    if cv2.getWindowProperty(self.preview_window_name, cv2.WND_PROP_VISIBLE) < 1:
                        self.preview_closed = True
                        self.preview_enabled = False
                        logging.info("预览窗口已被用户关闭")
                        break

                    # 获取最新帧数据
                    frame = None
                    qr_codes = []
                    with self.frame_lock:
                        if self.latest_frame is not None:
                            frame = self.latest_frame.copy()
                            qr_codes = self.latest_qr_codes.copy()

                    if frame is not None:
                        self.update_preview_display(frame, qr_codes)
                    else:
                        # 显示等待画面
                        self.show_waiting_screen()

                    # 短暂等待，避免CPU占用过高
                    time.sleep(0.033)  # 约30FPS

                except Exception as e:
                    logging.error(f"预览循环中出错: {e}")
                    time.sleep(0.1)

        except Exception as e:
            logging.error(f"预览线程出错: {e}")
        finally:
            try:
                cv2.destroyWindow(self.preview_window_name)
            except:
                pass
            logging.info("预览窗口线程已停止")

    def show_waiting_screen(self):
        """显示等待画面"""
        try:
            # 创建黑色背景
            waiting_frame = np.zeros((CAMERA_PREVIEW_SIZE[1], CAMERA_PREVIEW_SIZE[0], 3), dtype=np.uint8)

            # 添加等待文本
            text = "Waiting for camera..."
            font = cv2.FONT_HERSHEY_SIMPLEX
            font_scale = 0.5
            color = (255, 255, 255)
            thickness = 1

            # 计算文本位置（居中）
            text_size = cv2.getTextSize(text, font, font_scale, thickness)[0]
            text_x = (CAMERA_PREVIEW_SIZE[0] - text_size[0]) // 2
            text_y = (CAMERA_PREVIEW_SIZE[1] + text_size[1]) // 2

            cv2.putText(waiting_frame, text, (text_x, text_y), font, font_scale, color, thickness)

            # 显示等待画面
            cv2.imshow(self.preview_window_name, waiting_frame)
            cv2.waitKey(1)

        except Exception as e:
            logging.error(f"显示等待画面时出错: {e}")

    def update_preview_display(self, frame, qr_codes):
        """更新预览显示内容"""
        try:
            # 创建预览帧的副本
            preview_frame = frame.copy()

            # 在预览帧上绘制二维码检测框
            for qr_info in qr_codes:
                points = qr_info.get('points', [])
                if len(points) == 4:
                    # 绘制二维码边框
                    pts = np.array([(point.x, point.y) for point in points], dtype=np.int32)
                    cv2.polylines(preview_frame, [pts], True, (0, 255, 0), 2)

                    # 在二维码上方显示数据类型
                    try:
                        qr_data = json.loads(qr_info['data'])
                        text = f"Type: {qr_data.get('type', 'Unknown')}"
                        cv2.putText(preview_frame, text, (pts[0][0], pts[0][1] - 10),
                                  cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 1)
                    except:
                        pass

            # 添加状态信息
            status_text = f"Camera: {CAMERA_INDEX} | QR: {len(qr_codes)} detected"
            cv2.putText(preview_frame, status_text, (10, 25),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 1)

            # 添加时间戳
            timestamp = time.strftime("%H:%M:%S", time.localtime())
            cv2.putText(preview_frame, timestamp, (10, preview_frame.shape[0] - 10),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)

            # 调整预览帧大小
            preview_resized = cv2.resize(preview_frame, CAMERA_PREVIEW_SIZE)

            # 显示预览
            cv2.imshow(self.preview_window_name, preview_resized)
            cv2.waitKey(1)  # 非阻塞等待

        except Exception as e:
            logging.error(f"更新预览窗口时出错: {e}")
            self.preview_enabled = False
    
    def start_monitoring(self):
        """启动摄像头监控"""
        if self.is_running:
            logging.warning("摄像头监控已在运行")
            return False
        
        if not self.initialize_camera():
            return False
        
        self.is_running = True
        self.monitor_thread = threading.Thread(target=self.monitor_loop, daemon=True)
        self.monitor_thread.start()

        # 启动预览线程
        if self.preview_enabled and not self.preview_closed:
            self.preview_thread = threading.Thread(target=self.preview_loop, daemon=True)
            self.preview_thread.start()
            logging.info("预览窗口线程已启动")

        logging.info("摄像头监控已启动")
        return True
    
    def stop_monitoring(self):
        """停止摄像头监控"""
        if not self.is_running:
            return
        
        self.is_running = False

        # 停止监控线程
        if self.monitor_thread and self.monitor_thread.is_alive():
            self.monitor_thread.join(timeout=5)

        # 停止预览线程
        if self.preview_thread and self.preview_thread.is_alive():
            self.preview_thread.join(timeout=3)

        # 释放摄像头
        if self.camera:
            self.camera.release()
            self.camera = None

        # 关闭所有OpenCV窗口
        cv2.destroyAllWindows()
        logging.info("摄像头监控已停止")
    
    def capture_single_frame(self):
        """捕获单帧用于测试"""
        if not self.camera or not self.camera.isOpened():
            if not self.initialize_camera():
                return None
        
        frame = self.capture_frame()
        return frame
