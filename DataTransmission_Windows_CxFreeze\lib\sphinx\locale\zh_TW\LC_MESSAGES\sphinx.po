# Translations template for Sphinx.
# Copyright (C) 2024 ORGANIZATION
# This file is distributed under the same license as the Sphinx project.
# 
# Translators:
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2018
# <AUTHOR> <EMAIL>, 2021
# <PERSON> <<EMAIL>>, 2008
# <AUTHOR> <EMAIL>, 2017
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2018
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2016
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2016-2017
# <PERSON> <<EMAIL>>, 2021-2023
msgid ""
msgstr ""
"Project-Id-Version: Sphinx\n"
"Report-Msgid-Bugs-To: EMAIL@ADDRESS\n"
"POT-Creation-Date: 2024-04-14 23:27+0000\n"
"PO-Revision-Date: 2013-04-02 08:44+0000\n"
"Last-Translator: <PERSON> <hsu<PERSON><EMAIL>>, 2021-2023\n"
"Language-Team: Chinese (Taiwan) (http://app.transifex.com/sphinx-doc/sphinx-1/language/zh_TW/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.14.0\n"
"Language: zh_TW\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#: sphinx/application.py:157
#, python-format
msgid "Cannot find source directory (%s)"
msgstr "找不到來源資料夾 (%s)"

#: sphinx/application.py:161
#, python-format
msgid "Output directory (%s) is not a directory"
msgstr "輸出資料夾 (%s) 不是一個資料夾"

#: sphinx/application.py:165
msgid "Source directory and destination directory cannot be identical"
msgstr "來源資料夾與目的資料夾不能為相同"

#: sphinx/application.py:197
#, python-format
msgid "Running Sphinx v%s"
msgstr "正在執行 Sphinx v%s 版本"

#: sphinx/application.py:219
#, python-format
msgid ""
"This project needs at least Sphinx v%s and therefore cannot be built with "
"this version."
msgstr "本專案需要 Sphinx v%s 版或以上，故無法以現版本編譯。"

#: sphinx/application.py:235
msgid "making output directory"
msgstr "正在建立輸出目錄"

#: sphinx/application.py:240 sphinx/registry.py:450
#, python-format
msgid "while setting up extension %s:"
msgstr "正在設置擴充套件 %s 時："

#: sphinx/application.py:246
msgid ""
"'setup' as currently defined in conf.py isn't a Python callable. Please "
"modify its definition to make it a callable function. This is needed for "
"conf.py to behave as a Sphinx extension."
msgstr "目前在 conf.py 裡定義的 'setup' 並非一個 Python 的可呼叫物件。請將其定義修改為一個可呼叫的函式。若要使 conf.py 以 Sphinx 擴充套件的方式運作，這個修改是必須的。"

#: sphinx/application.py:277
#, python-format
msgid "loading translations [%s]... "
msgstr "正在載入翻譯 [%s]..."

#: sphinx/application.py:294 sphinx/util/display.py:85
msgid "done"
msgstr "完成"

#: sphinx/application.py:296
msgid "not available for built-in messages"
msgstr "不是有效的內建訊息"

#: sphinx/application.py:310
msgid "loading pickled environment"
msgstr "正在載入已 pickle 的環境"

#: sphinx/application.py:318
#, python-format
msgid "failed: %s"
msgstr "失敗：%s"

#: sphinx/application.py:332
msgid "No builder selected, using default: html"
msgstr "沒有指定 builder，使用預設：html"

#: sphinx/application.py:365
msgid "succeeded"
msgstr "成功"

#: sphinx/application.py:366
msgid "finished with problems"
msgstr "完成但有問題"

#: sphinx/application.py:370
#, python-format
msgid "build %s, %s warning (with warnings treated as errors)."
msgstr "建立 %s，%s 警告（警告被視為錯誤）。"

#: sphinx/application.py:372
#, python-format
msgid "build %s, %s warnings (with warnings treated as errors)."
msgstr "建立 %s，%s 警告（警告被視為錯誤）。"

#: sphinx/application.py:375
#, python-format
msgid "build %s, %s warning."
msgstr "建立 %s，%s 警告。"

#: sphinx/application.py:377
#, python-format
msgid "build %s, %s warnings."
msgstr "建立 %s，%s 警告。"

#: sphinx/application.py:381
#, python-format
msgid "build %s."
msgstr "建立 %s。"

#: sphinx/application.py:610
#, python-format
msgid "node class %r is already registered, its visitors will be overridden"
msgstr "node class %r 已經被註冊，它的訪客將會被覆寫"

#: sphinx/application.py:689
#, python-format
msgid "directive %r is already registered, it will be overridden"
msgstr "指令 %r 已經被註冊，它將會被覆寫"

#: sphinx/application.py:711 sphinx/application.py:733
#, python-format
msgid "role %r is already registered, it will be overridden"
msgstr "role %r 已經被註冊，它將會被覆寫"

#: sphinx/application.py:1282
#, python-format
msgid ""
"the %s extension does not declare if it is safe for parallel reading, "
"assuming it isn't - please ask the extension author to check and make it "
"explicit"
msgstr "%s 擴充套件並未宣告平行讀取是否安全，假設為否 - 請尋求擴充套件作者以檢查並明確表示"

#: sphinx/application.py:1286
#, python-format
msgid "the %s extension is not safe for parallel reading"
msgstr "%s 擴充套件對於平行讀取是不安全的"

#: sphinx/application.py:1289
#, python-format
msgid ""
"the %s extension does not declare if it is safe for parallel writing, "
"assuming it isn't - please ask the extension author to check and make it "
"explicit"
msgstr "%s 擴充套件並未宣告平行寫入是否安全，假設為否 - 請尋求擴充套件作者以檢查並明確表示"

#: sphinx/application.py:1293
#, python-format
msgid "the %s extension is not safe for parallel writing"
msgstr "%s 擴充套件對於平行寫入是不安全的"

#: sphinx/application.py:1301 sphinx/application.py:1305
#, python-format
msgid "doing serial %s"
msgstr "執行串列 %s"

#: sphinx/config.py:309
#, python-format
msgid "config directory doesn't contain a conf.py file (%s)"
msgstr "config 資料夾沒有包含 conf.py 檔案 (%s)"

#: sphinx/config.py:318
msgid ""
"Invalid configuration value found: 'language = None'. Update your "
"configuration to a valid language code. Falling back to 'en' (English)."
msgstr "找到無效的組態值： 'language = None' 。請以一個有效的語言碼更新您的配置。跳回 'en' （英語）。"

#: sphinx/config.py:341
#, python-format
msgid ""
"cannot override dictionary config setting %r, ignoring (use %r to set "
"individual elements)"
msgstr "無法覆寫資料夾組態設定 %r，忽略中（使用 %r 來設定個別元素）"

#: sphinx/config.py:350
#, python-format
msgid "invalid number %r for config value %r, ignoring"
msgstr "無效的數字 %r 於組態值 %r，忽略中"

#: sphinx/config.py:355
#, python-format
msgid "cannot override config setting %r with unsupported type, ignoring"
msgstr "無法以未支援的型別覆寫組態設定 %r，忽略中"

#: sphinx/config.py:378
#, python-format
msgid "unknown config value %r in override, ignoring"
msgstr "覆寫未知的組態值 %r，忽略中"

#: sphinx/config.py:418
#, python-format
msgid "No such config value: %r"
msgstr ""

#: sphinx/config.py:440
#, python-format
msgid "Config value %r already present"
msgstr "組態值 %r 已經存在"

#: sphinx/config.py:473
#, python-format
msgid "cannot cache unpickable configuration value: %r"
msgstr ""

#: sphinx/config.py:509
#, python-format
msgid "There is a syntax error in your configuration file: %s\n"
msgstr "在您的組態檔中有一個語法錯誤：%s\n"

#: sphinx/config.py:512
msgid ""
"The configuration file (or one of the modules it imports) called sys.exit()"
msgstr "組態檔（或它 import 的其中一個模組）呼叫了 sys.exit()"

#: sphinx/config.py:519
#, python-format
msgid ""
"There is a programmable error in your configuration file:\n"
"\n"
"%s"
msgstr "在您的組態檔中有一個程式化錯誤：\n\n%s"

#: sphinx/config.py:540
#, python-format
msgid "Failed to convert %r to a set or tuple"
msgstr ""

#: sphinx/config.py:565
#, python-format
msgid ""
"The config value `source_suffix' expects a string, list of strings, or "
"dictionary. But `%r' is given."
msgstr "組態值 `source_suffix' 預期是一個字串、一組字串，或字典。但是 `%r' 被給予。"

#: sphinx/config.py:585
#, python-format
msgid "Section %s"
msgstr "章節 %s"

#: sphinx/config.py:586
#, python-format
msgid "Fig. %s"
msgstr "圖 %s"

#: sphinx/config.py:587
#, python-format
msgid "Table %s"
msgstr "表格 %s"

#: sphinx/config.py:588
#, python-format
msgid "Listing %s"
msgstr "列表 %s"

#: sphinx/config.py:663
msgid ""
"The config value `{name}` has to be a one of {candidates}, but `{current}` "
"is given."
msgstr "組態值 `{name}` 必須是 {candidates} 的其中之一，但 `{current}` 被給予。"

#: sphinx/config.py:687
msgid ""
"The config value `{name}' has type `{current.__name__}'; expected "
"{permitted}."
msgstr "組態值 `{name}' 有 `{current.__name__}' 型別；預期 {permitted} 。"

#: sphinx/config.py:700
msgid ""
"The config value `{name}' has type `{current.__name__}', defaults to "
"`{default.__name__}'."
msgstr "組態值 `{name}' 有 `{current.__name__}' 型別；預設為 `{default.__name__}' 。"

#: sphinx/config.py:711
#, python-format
msgid "primary_domain %r not found, ignored."
msgstr "找不到 primary_domain %r，已略過。"

#: sphinx/config.py:723
msgid ""
"Since v2.0, Sphinx uses \"index\" as root_doc by default. Please add "
"\"root_doc = 'contents'\" to your conf.py."
msgstr "從 v2.0 開始，Sphinx 預設使用 \"index\" 作為 root_doc。請在您的 conf.py 加上 \"root_doc = 'contents'\"。"

#: sphinx/events.py:64
#, python-format
msgid "Event %r already present"
msgstr "事件 %r 已經存在"

#: sphinx/events.py:70
#, python-format
msgid "Unknown event name: %s"
msgstr "未知的事件名稱：%s"

#: sphinx/events.py:109
#, python-format
msgid "Handler %r for event %r threw an exception"
msgstr "對於事件 %r 的 handler %r 拋出了一個例外"

#: sphinx/extension.py:55
#, python-format
msgid ""
"The %s extension is required by needs_extensions settings, but it is not "
"loaded."
msgstr "擴充套件 %s 被 needs_extensions 的設定所要求，但它沒有被載入。"

#: sphinx/extension.py:76
#, python-format
msgid ""
"This project needs the extension %s at least in version %s and therefore "
"cannot be built with the loaded version (%s)."
msgstr "這個專案需要擴充套件 %s 的最低版本是 %s，所以無法以載入的版本 (%s) 被建立。"

#: sphinx/highlighting.py:155
#, python-format
msgid "Pygments lexer name %r is not known"
msgstr "Pygments lexer 名稱 %r 不是已知的"

#: sphinx/highlighting.py:189
#, python-format
msgid ""
"Lexing literal_block %r as \"%s\" resulted in an error at token: %r. "
"Retrying in relaxed mode."
msgstr "將 literal_block %r lex 為 \"%s\" 時，在 token %r 造成錯誤。正在以 relaxed 模式重試中。"

#: sphinx/project.py:66
#, python-format
msgid ""
"multiple files found for the document \"%s\": %r\n"
"Use %r for the build."
msgstr "為文件 \"%s\" 找到多個檔案： %r\n使用 %r 來建立。"

#: sphinx/project.py:81
#, python-format
msgid "Ignored unreadable document %r."
msgstr "已略過無法讀取的文件 %r 。"

#: sphinx/registry.py:142
#, python-format
msgid "Builder class %s has no \"name\" attribute"
msgstr "Builder class %s 沒有 \"name\" 屬性"

#: sphinx/registry.py:144
#, python-format
msgid "Builder %r already exists (in module %s)"
msgstr "Builder %r 已存在（於 %s 模組）"

#: sphinx/registry.py:157
#, python-format
msgid "Builder name %s not registered or available through entry point"
msgstr "Builder 名稱 %s 未註冊或無法從 entry point 取得"

#: sphinx/registry.py:164
#, python-format
msgid "Builder name %s not registered"
msgstr "Builder 名稱 %s 未註冊"

#: sphinx/registry.py:171
#, python-format
msgid "domain %s already registered"
msgstr "domain %s 已註冊"

#: sphinx/registry.py:194 sphinx/registry.py:207 sphinx/registry.py:218
#, python-format
msgid "domain %s not yet registered"
msgstr "domain %s 尚未被註冊"

#: sphinx/registry.py:198
#, python-format
msgid "The %r directive is already registered to domain %s"
msgstr "%r 指令已註冊給 domain %s"

#: sphinx/registry.py:210
#, python-format
msgid "The %r role is already registered to domain %s"
msgstr "%r 角色已註冊給 domain %s"

#: sphinx/registry.py:221
#, python-format
msgid "The %r index is already registered to domain %s"
msgstr "%r 索引已註冊給 domain %s"

#: sphinx/registry.py:252
#, python-format
msgid "The %r object_type is already registered"
msgstr "%r object_type 已註冊"

#: sphinx/registry.py:278
#, python-format
msgid "The %r crossref_type is already registered"
msgstr "%r crossref_type 已註冊"

#: sphinx/registry.py:285
#, python-format
msgid "source_suffix %r is already registered"
msgstr "source_suffix %r 已註冊"

#: sphinx/registry.py:294
#, python-format
msgid "source_parser for %r is already registered"
msgstr "對於 %r 的 source_parser 已註冊"

#: sphinx/registry.py:302
#, python-format
msgid "Source parser for %s not registered"
msgstr "對於 %s 的源碼剖析器未註冊"

#: sphinx/registry.py:318
#, python-format
msgid "Translator for %r already exists"
msgstr "對於 %r 的翻譯器已經存在"

#: sphinx/registry.py:334
#, python-format
msgid "kwargs for add_node() must be a (visit, depart) function tuple: %r=%r"
msgstr "對於 add_node() 的 kwargs 必須是一個 (visit, depart) 函式值組：%r=%r"

#: sphinx/registry.py:417
#, python-format
msgid "enumerable_node %r already registered"
msgstr "enumerable_node %r 已註冊"

#: sphinx/registry.py:429
#, python-format
msgid "math renderer %s is already registered"
msgstr "數學描繪器 %s 已註冊"

#: sphinx/registry.py:444
#, python-format
msgid ""
"the extension %r was already merged with Sphinx since version %s; this "
"extension is ignored."
msgstr "擴充套件 %r 已被併入 %s 版以上的 Sphinx：此擴充套件已略過。"

#: sphinx/registry.py:455
msgid "Original exception:\n"
msgstr "原始的例外：\n"

#: sphinx/registry.py:456
#, python-format
msgid "Could not import extension %s"
msgstr "無法引入擴充套件 %s"

#: sphinx/registry.py:461
#, python-format
msgid ""
"extension %r has no setup() function; is it really a Sphinx extension "
"module?"
msgstr "擴充套件 %r 沒有 setup() 函式；它真的是 Sphinx 擴充套件模組嗎？"

#: sphinx/registry.py:470
#, python-format
msgid ""
"The %s extension used by this project needs at least Sphinx v%s; it "
"therefore cannot be built with this version."
msgstr "此專案使用的 %s 擴充套件需要 Sphinx v%s 以上的版本；所以它無法以此版本被建立。"

#: sphinx/registry.py:478
#, python-format
msgid ""
"extension %r returned an unsupported object from its setup() function; it "
"should return None or a metadata dictionary"
msgstr "擴充套件 %r 從它的 setup() 函式回傳一個未支援物件；它應該回傳 None 或一個元數據資料夾"

#: sphinx/roles.py:201
#, python-format
msgid "Python Enhancement Proposals; PEP %s"
msgstr "Python Enhancement Proposals; PEP %s"

#: sphinx/roles.py:222
#, python-format
msgid "invalid PEP number %s"
msgstr "無效的 PEP 號碼 %s"

#: sphinx/roles.py:257
#, python-format
msgid "invalid RFC number %s"
msgstr "無效的 RFC 號碼 %s"

#: sphinx/theming.py:125
#, python-format
msgid "setting %s.%s occurs in none of the searched theme configs"
msgstr "設定 %s。%s 不在已被搜尋的主題組態中出現"

#: sphinx/theming.py:140
#, python-format
msgid "unsupported theme option %r given"
msgstr "未支援的主題選項 %r 被給予"

#: sphinx/theming.py:206
#, python-format
msgid "file %r on theme path is not a valid zipfile or contains no theme"
msgstr "主題路徑中的檔案 %r 不是有效的 zipfile 或未包含主題"

#: sphinx/theming.py:226
#, python-format
msgid "no theme named %r found (missing theme.toml?)"
msgstr ""

#: sphinx/theming.py:259
#, python-format
msgid "The %r theme has circular inheritance"
msgstr ""

#: sphinx/theming.py:262
#, python-format
msgid ""
"The %r theme inherits from %r, which is not a loaded theme. Loaded themes "
"are: %s"
msgstr ""

#: sphinx/theming.py:269
#, python-format
msgid "The %r theme has too many ancestors"
msgstr ""

#: sphinx/theming.py:295
#, python-format
msgid "no theme configuration file found in %r"
msgstr ""

#: sphinx/theming.py:323 sphinx/theming.py:374
#, python-format
msgid "theme %r doesn't have the \"theme\" table"
msgstr ""

#: sphinx/theming.py:327
#, python-format
msgid "The %r theme \"[theme]\" table is not a table"
msgstr ""

#: sphinx/theming.py:331 sphinx/theming.py:377
#, python-format
msgid "The %r theme must define the \"theme.inherit\" setting"
msgstr ""

#: sphinx/theming.py:335
#, python-format
msgid "The %r theme \"[options]\" table is not a table"
msgstr ""

#: sphinx/theming.py:353
#, python-format
msgid "The \"theme.pygments_style\" setting must be a table. Hint: \"%s\""
msgstr ""

#: sphinx/builders/__init__.py:183
#, python-format
msgid "a suitable image for %s builder not found: %s (%s)"
msgstr "未找到對於 %s builder 適用的圖片：%s (%s)"

#: sphinx/builders/__init__.py:187
#, python-format
msgid "a suitable image for %s builder not found: %s"
msgstr "未找到對於 %s builder 適用的圖片：%s"

#: sphinx/builders/__init__.py:207
msgid "building [mo]: "
msgstr "建立 [mo]："

#: sphinx/builders/__init__.py:208 sphinx/builders/__init__.py:574
#: sphinx/builders/__init__.py:601
msgid "writing output... "
msgstr "編寫輸出..."

#: sphinx/builders/__init__.py:217
#, python-format
msgid "all of %d po files"
msgstr "所有的 %d po 檔"

#: sphinx/builders/__init__.py:235
#, python-format
msgid "targets for %d po files that are specified"
msgstr "對於指定的 po 檔 %d 的目標"

#: sphinx/builders/__init__.py:243
#, python-format
msgid "targets for %d po files that are out of date"
msgstr "對於已過期 po 檔 %d 的目標"

#: sphinx/builders/__init__.py:252
msgid "all source files"
msgstr "所有原始檔案"

#: sphinx/builders/__init__.py:262
#, python-format
msgid "file %r given on command line does not exist, "
msgstr "在命令列給的檔案 %r 不存在，"

#: sphinx/builders/__init__.py:267
#, python-format
msgid ""
"file %r given on command line is not under the source directory, ignoring"
msgstr "在命令列給的檔案 %r 不在來源資料夾下，忽略中"

#: sphinx/builders/__init__.py:273
#, python-format
msgid "file %r given on command line is not a valid document, ignoring"
msgstr "在命令列給的檔案 %r 不是有效的文件，忽略中"

#: sphinx/builders/__init__.py:282
#, python-format
msgid "%d source files given on command line"
msgstr "在命令列給了 %d 個原始檔案"

#: sphinx/builders/__init__.py:294
#, python-format
msgid "targets for %d source files that are out of date"
msgstr "%d 個過時原始檔案的目標"

#: sphinx/builders/__init__.py:309 sphinx/builders/gettext.py:243
#, python-format
msgid "building [%s]: "
msgstr "正在建立 [%s]："

#: sphinx/builders/__init__.py:316
msgid "looking for now-outdated files... "
msgstr "正在尋找目前已過期的檔案..."

#: sphinx/builders/__init__.py:320
#, python-format
msgid "%d found"
msgstr "已找到 %d"

#: sphinx/builders/__init__.py:322
msgid "none found"
msgstr "找不到任何結果"

#: sphinx/builders/__init__.py:327
msgid "pickling environment"
msgstr "正在 pickle 環境"

#: sphinx/builders/__init__.py:333
msgid "checking consistency"
msgstr "正在檢查一致性"

#: sphinx/builders/__init__.py:337
msgid "no targets are out of date."
msgstr "沒有過時的目標。"

#: sphinx/builders/__init__.py:376
msgid "updating environment: "
msgstr "正在更新環境："

#: sphinx/builders/__init__.py:397
#, python-format
msgid "%s added, %s changed, %s removed"
msgstr "%s 已新增， %s 已變更， %s 已移除"

#: sphinx/builders/__init__.py:435 sphinx/builders/__init__.py:447
msgid "reading sources... "
msgstr "正在讀取來源..."

#: sphinx/builders/__init__.py:549
#, python-format
msgid "docnames to write: %s"
msgstr "待寫入的 docname: %s"

#: sphinx/builders/__init__.py:558 sphinx/builders/singlehtml.py:157
msgid "preparing documents"
msgstr "正在準備文件"

#: sphinx/builders/__init__.py:561
msgid "copying assets"
msgstr "正在複製資產 (asset)"

#: sphinx/builders/_epub_base.py:215
#, python-format
msgid "duplicated ToC entry found: %s"
msgstr "找到了重複的 ToC 項目： %s"

#: sphinx/builders/_epub_base.py:404 sphinx/builders/html/__init__.py:758
#: sphinx/builders/latex/__init__.py:432 sphinx/builders/texinfo.py:187
msgid "copying images... "
msgstr "正在複製圖片..."

#: sphinx/builders/_epub_base.py:411
#, python-format
msgid "cannot read image file %r: copying it instead"
msgstr "無法讀取圖片檔 %r: 正在複製它做為替代"

#: sphinx/builders/_epub_base.py:417 sphinx/builders/html/__init__.py:766
#: sphinx/builders/latex/__init__.py:440 sphinx/builders/texinfo.py:197
#, python-format
msgid "cannot copy image file %r: %s"
msgstr "無法複製圖片檔 %r: %s"

#: sphinx/builders/_epub_base.py:434
#, python-format
msgid "cannot write image file %r: %s"
msgstr "無法寫入圖片檔 %r: %s"

#: sphinx/builders/_epub_base.py:444
msgid "Pillow not found - copying image files"
msgstr "未找到 Pillow - 正在複製圖片檔"

#: sphinx/builders/_epub_base.py:470
msgid "writing mimetype file..."
msgstr "正在寫入 mimetype 檔案..."

#: sphinx/builders/_epub_base.py:475
msgid "writing META-INF/container.xml file..."
msgstr "正在寫入 META-INF/container.xml 檔案..."

#: sphinx/builders/_epub_base.py:508
msgid "writing content.opf file..."
msgstr "正在寫入 content.opf 檔案..."

#: sphinx/builders/_epub_base.py:539
#, python-format
msgid "unknown mimetype for %s, ignoring"
msgstr "對於 %s 未知的 mimetype，忽略中"

#: sphinx/builders/_epub_base.py:686
msgid "writing toc.ncx file..."
msgstr "正在寫入 toc.ncx 檔案..."

#: sphinx/builders/_epub_base.py:711
#, python-format
msgid "writing %s file..."
msgstr "正在寫入 %s 檔案..."

#: sphinx/builders/changes.py:32
#, python-format
msgid "The overview file is in %(outdir)s."
msgstr "概觀檔案是在 %(outdir)s 。"

#: sphinx/builders/changes.py:59
#, python-format
msgid "no changes in version %s."
msgstr "在 %s 版中無變更"

#: sphinx/builders/changes.py:61
msgid "writing summary file..."
msgstr "正在寫入摘要檔案..."

#: sphinx/builders/changes.py:76
msgid "Builtins"
msgstr "內建"

#: sphinx/builders/changes.py:78
msgid "Module level"
msgstr "模組層次"

#: sphinx/builders/changes.py:123
msgid "copying source files..."
msgstr "正在複製原始檔案..."

#: sphinx/builders/changes.py:130
#, python-format
msgid "could not read %r for changelog creation"
msgstr "在變更日誌建立時無法讀取 %r"

#: sphinx/builders/dummy.py:19
msgid "The dummy builder generates no files."
msgstr "虛擬建立器未產生任何檔案。"

#: sphinx/builders/epub3.py:81
#, python-format
msgid "The ePub file is in %(outdir)s."
msgstr "ePub 檔案是在 %(outdir)s 。"

#: sphinx/builders/epub3.py:185
msgid "writing nav.xhtml file..."
msgstr "正在寫入 nav.xhtml 檔案..."

#: sphinx/builders/epub3.py:211
msgid "conf value \"epub_language\" (or \"language\") should not be empty for EPUB3"
msgstr "conf 值 \"epub_language\" (或 \"language\") 在 EPUB3 不應該為空"

#: sphinx/builders/epub3.py:215
msgid "conf value \"epub_uid\" should be XML NAME for EPUB3"
msgstr "conf 值  \"epub_uid\" 在 EPUB3 應該是 XML NAME"

#: sphinx/builders/epub3.py:218
msgid "conf value \"epub_title\" (or \"html_title\") should not be empty for EPUB3"
msgstr "conf 值 \"epub_title\" (或 \"html_title\") 在 EPUB3 不應該為空"

#: sphinx/builders/epub3.py:222
msgid "conf value \"epub_author\" should not be empty for EPUB3"
msgstr "conf 值  \"epub_author\" 在 EPUB3 不應該為空"

#: sphinx/builders/epub3.py:225
msgid "conf value \"epub_contributor\" should not be empty for EPUB3"
msgstr "conf 值 \"epub_contributor\" 在 EPUB3 不應該為空"

#: sphinx/builders/epub3.py:228
msgid "conf value \"epub_description\" should not be empty for EPUB3"
msgstr "conf 值  \"epub_description\" 在 EPUB3 不應該為空"

#: sphinx/builders/epub3.py:231
msgid "conf value \"epub_publisher\" should not be empty for EPUB3"
msgstr "conf 值 \"epub_publisher\" 在 EPUB3 不應該為空"

#: sphinx/builders/epub3.py:234
msgid "conf value \"epub_copyright\" (or \"copyright\")should not be empty for EPUB3"
msgstr "conf 值 \"epub_copyright\" (或 \"copyright\") 在 EPUB3 不應該為空"

#: sphinx/builders/epub3.py:238
msgid "conf value \"epub_identifier\" should not be empty for EPUB3"
msgstr "conf 值 \"epub_identifier\" 在 EPUB3 不應該為空"

#: sphinx/builders/epub3.py:241
msgid "conf value \"version\" should not be empty for EPUB3"
msgstr "conf 值 \"version\" 在 EPUB3 不應該為空"

#: sphinx/builders/epub3.py:255 sphinx/builders/html/__init__.py:1187
#, python-format
msgid "invalid css_file: %r, ignored"
msgstr "無效的 css_file: %r, 已略過"

#: sphinx/builders/gettext.py:222
#, python-format
msgid "The message catalogs are in %(outdir)s."
msgstr "訊息目錄是在 %(outdir)s"

#: sphinx/builders/gettext.py:244
#, python-format
msgid "targets for %d template files"
msgstr "模板檔 %d 的目標"

#: sphinx/builders/gettext.py:248
msgid "reading templates... "
msgstr "正在讀取模板..."

#: sphinx/builders/gettext.py:282
msgid "writing message catalogs... "
msgstr "正在寫入訊息目錄..."

#: sphinx/builders/linkcheck.py:59
#, python-format
msgid "Look for any errors in the above output or in %(outdir)s/output.txt"
msgstr "尋找以上輸出或 %(outdir)s/output.txt 中的任何錯誤"

#: sphinx/builders/linkcheck.py:137
#, python-format
msgid "broken link: %s (%s)"
msgstr "錯誤連結： %s (%s)"

#: sphinx/builders/linkcheck.py:660
#, python-format
msgid "Failed to compile regex in linkcheck_allowed_redirects: %r %s"
msgstr "在 linkcheck_allowed_redirects 編譯 regex 失敗： %r %s"

#: sphinx/builders/manpage.py:37
#, python-format
msgid "The manual pages are in %(outdir)s."
msgstr "手冊頁面在 %(outdir)s"

#: sphinx/builders/manpage.py:44
msgid "no \"man_pages\" config value found; no manual pages will be written"
msgstr "未找到 \"man_pages\" 組態值：不會編寫任何手冊頁面"

#: sphinx/builders/latex/__init__.py:314 sphinx/builders/manpage.py:53
#: sphinx/builders/singlehtml.py:165 sphinx/builders/texinfo.py:112
msgid "writing"
msgstr "編寫中"

#: sphinx/builders/manpage.py:68
#, python-format
msgid "\"man_pages\" config value references unknown document %s"
msgstr "\"man_pages\" 組態值引用未知的文件 %s"

#: sphinx/builders/singlehtml.py:34
#, python-format
msgid "The HTML page is in %(outdir)s."
msgstr "HTML 頁面在 %(outdir)s 。"

#: sphinx/builders/singlehtml.py:160
msgid "assembling single document"
msgstr "正在組合單一文件"

#: sphinx/builders/singlehtml.py:178
msgid "writing additional files"
msgstr "正在寫入附加檔案"

#: sphinx/builders/texinfo.py:48
#, python-format
msgid "The Texinfo files are in %(outdir)s."
msgstr "Texinfo 檔案在 %(outdir)s 。"

#: sphinx/builders/texinfo.py:50
msgid ""
"\n"
"Run 'make' in that directory to run these through makeinfo\n"
"(use 'make info' here to do that automatically)."
msgstr "\n在該目錄中執行 'make' 以透過 makeinfo 執行這些\n（在此使用 'make info' 以自動執行）"

#: sphinx/builders/texinfo.py:77
msgid "no \"texinfo_documents\" config value found; no documents will be written"
msgstr "未找到 \"texinfo_documents\" 組態值；不會編寫任何文件"

#: sphinx/builders/texinfo.py:85
#, python-format
msgid "\"texinfo_documents\" config value references unknown document %s"
msgstr "\"texinfo_documents\" 組態值引用未知的文件 %s"

#: sphinx/builders/latex/__init__.py:296 sphinx/builders/texinfo.py:108
#, python-format
msgid "processing %s"
msgstr "正在處理 %s"

#: sphinx/builders/latex/__init__.py:369 sphinx/builders/texinfo.py:161
msgid "resolving references..."
msgstr "正在解析參照..."

#: sphinx/builders/latex/__init__.py:380 sphinx/builders/texinfo.py:171
msgid " (in "
msgstr " (於 "

#: sphinx/builders/texinfo.py:202
msgid "copying Texinfo support files"
msgstr "正在複製 Texinfo 支援檔案"

#: sphinx/builders/texinfo.py:206
#, python-format
msgid "error writing file Makefile: %s"
msgstr "錯誤寫入檔案 Makefile： %s"

#: sphinx/builders/text.py:30
#, python-format
msgid "The text files are in %(outdir)s."
msgstr "文字檔案在 %(outdir)s 。"

#: sphinx/builders/html/__init__.py:1138 sphinx/builders/text.py:77
#: sphinx/builders/xml.py:96
#, python-format
msgid "error writing file %s: %s"
msgstr "錯誤寫入檔案 %s: %s"

#: sphinx/builders/xml.py:36
#, python-format
msgid "The XML files are in %(outdir)s."
msgstr "XML 檔案在 %(outdir)s 。"

#: sphinx/builders/xml.py:109
#, python-format
msgid "The pseudo-XML files are in %(outdir)s."
msgstr "pseudo-XML 檔案在 %(outdir)s 。"

#: sphinx/builders/html/__init__.py:130
#, python-format
msgid "build info file is broken: %r"
msgstr "build info 檔案已失效： %r"

#: sphinx/builders/html/__init__.py:168
#, python-format
msgid "The HTML pages are in %(outdir)s."
msgstr "HTML 頁面在 %(outdir)s 。"

#: sphinx/builders/html/__init__.py:394
#, python-format
msgid "Failed to read build info file: %r"
msgstr "讀取 build info 檔失敗： %r"

#: sphinx/builders/html/__init__.py:487 sphinx/builders/latex/__init__.py:189
#: sphinx/transforms/__init__.py:119 sphinx/writers/manpage.py:101
#: sphinx/writers/texinfo.py:227
#, python-format
msgid "%b %d, %Y"
msgstr "%Y 年 %m 月 %d 日"

#: sphinx/builders/html/__init__.py:506 sphinx/themes/basic/defindex.html:30
msgid "General Index"
msgstr "總索引"

#: sphinx/builders/html/__init__.py:506
msgid "index"
msgstr "索引"

#: sphinx/builders/html/__init__.py:579
msgid "next"
msgstr "下一頁"

#: sphinx/builders/html/__init__.py:588
msgid "previous"
msgstr "上一頁"

#: sphinx/builders/html/__init__.py:684
msgid "generating indices"
msgstr "正在產生索引"

#: sphinx/builders/html/__init__.py:699
msgid "writing additional pages"
msgstr "正在編寫附加頁面"

#: sphinx/builders/html/__init__.py:776
msgid "copying downloadable files... "
msgstr "正在複製可下載的檔案..."

#: sphinx/builders/html/__init__.py:784
#, python-format
msgid "cannot copy downloadable file %r: %s"
msgstr "無法複製可下載的檔案 %r: %s"

#: sphinx/builders/html/__init__.py:817 sphinx/builders/html/__init__.py:829
#, python-format
msgid "Failed to copy a file in html_static_file: %s: %r"
msgstr "在 html_static_file 中複製一個檔案失敗： %s: %r "

#: sphinx/builders/html/__init__.py:850
msgid "copying static files"
msgstr "正在複製靜態檔案"

#: sphinx/builders/html/__init__.py:866
#, python-format
msgid "cannot copy static file %r"
msgstr "無法複製靜態檔案 %r"

#: sphinx/builders/html/__init__.py:871
msgid "copying extra files"
msgstr "正在複製額外檔案"

#: sphinx/builders/html/__init__.py:877
#, python-format
msgid "cannot copy extra file %r"
msgstr "無法複製額外檔案 %r"

#: sphinx/builders/html/__init__.py:884
#, python-format
msgid "Failed to write build info file: %r"
msgstr "寫入 build info 檔失敗： %r"

#: sphinx/builders/html/__init__.py:933
msgid ""
"search index couldn't be loaded, but not all documents will be built: the "
"index will be incomplete."
msgstr "搜尋索引無法被載入，但不是所有的文件都會被建置：索引將會是不完全的。"

#: sphinx/builders/html/__init__.py:978
#, python-format
msgid "page %s matches two patterns in html_sidebars: %r and %r"
msgstr "頁面 %s 在 html_sidebars 中符合兩個型樣： %r 和 %r"

#: sphinx/builders/html/__init__.py:1121
#, python-format
msgid ""
"a Unicode error occurred when rendering the page %s. Please make sure all "
"config values that contain non-ASCII content are Unicode strings."
msgstr "在呈現頁面 %s 時發生了一個 Unicode 錯誤。請確認所有包含 non-ASCII 內容的組態值都是 Unicode 字串。"

#: sphinx/builders/html/__init__.py:1126
#, python-format
msgid ""
"An error happened in rendering the page %s.\n"
"Reason: %r"
msgstr "在呈現頁面 %s 時發生了一個錯誤。\n原因： %r"

#: sphinx/builders/html/__init__.py:1154
msgid "dumping object inventory"
msgstr "正在傾印物件庫存"

#: sphinx/builders/html/__init__.py:1162
#, python-format
msgid "dumping search index in %s"
msgstr "正在傾印搜尋索引於 %s"

#: sphinx/builders/html/__init__.py:1210
#, python-format
msgid "invalid js_file: %r, ignored"
msgstr "無效的  js_file: %r, 已略過"

#: sphinx/builders/html/__init__.py:1238
msgid "Many math_renderers are registered. But no math_renderer is selected."
msgstr "多個 math_renderer 已被註冊。但是沒有 math_renderer 被選擇。"

#: sphinx/builders/html/__init__.py:1241
#, python-format
msgid "Unknown math_renderer %r is given."
msgstr "未知的 math_renderer %r 被給予。"

#: sphinx/builders/html/__init__.py:1249
#, python-format
msgid "html_extra_path entry %r does not exist"
msgstr "html_extra_path 項目 %r 不存在"

#: sphinx/builders/html/__init__.py:1253
#, python-format
msgid "html_extra_path entry %r is placed inside outdir"
msgstr "html_extra_path 項目 %r 被放入 outdir"

#: sphinx/builders/html/__init__.py:1262
#, python-format
msgid "html_static_path entry %r does not exist"
msgstr "html_static_path 項目 %r 不存在"

#: sphinx/builders/html/__init__.py:1266
#, python-format
msgid "html_static_path entry %r is placed inside outdir"
msgstr "html_static_path 項目 %r 被放入 outdir"

#: sphinx/builders/html/__init__.py:1275 sphinx/builders/latex/__init__.py:444
#, python-format
msgid "logo file %r does not exist"
msgstr "標誌檔案 %r 不存在"

#: sphinx/builders/html/__init__.py:1284
#, python-format
msgid "favicon file %r does not exist"
msgstr "favicon 檔案 %r 不存在"

#: sphinx/builders/html/__init__.py:1291
msgid ""
"HTML 4 is no longer supported by Sphinx. (\"html4_writer=True\" detected in "
"configuration options)"
msgstr "HTML 4 已不再被 Sphinx 所支援。（在組態選項中偵測到 \"html4_writer=True\"）"

#: sphinx/builders/html/__init__.py:1306
#, python-format
msgid "%s %s documentation"
msgstr "%s %s 說明文件"

#: sphinx/builders/latex/__init__.py:115
#, python-format
msgid "The LaTeX files are in %(outdir)s."
msgstr "LaTeX 檔案在 %(outdir)s 。"

#: sphinx/builders/latex/__init__.py:117
msgid ""
"\n"
"Run 'make' in that directory to run these through (pdf)latex\n"
"(use `make latexpdf' here to do that automatically)."
msgstr "\n在該目錄中執行 'make' 以透過 (pdf)latex 執行這些\n（在此使用 'make latexpdf' 以自動執行）"

#: sphinx/builders/latex/__init__.py:152
msgid "no \"latex_documents\" config value found; no documents will be written"
msgstr "未找到 \"latex_documents\" 組態值；不會編寫任何文件"

#: sphinx/builders/latex/__init__.py:160
#, python-format
msgid "\"latex_documents\" config value references unknown document %s"
msgstr "\"latex_documents\" 組態值引用未知的文件 %s"

#: sphinx/builders/latex/__init__.py:196 sphinx/domains/std/__init__.py:559
#: sphinx/domains/std/__init__.py:571 sphinx/templates/latex/latex.tex_t:106
#: sphinx/themes/basic/genindex-single.html:30
#: sphinx/themes/basic/genindex-single.html:55
#: sphinx/themes/basic/genindex-split.html:11
#: sphinx/themes/basic/genindex-split.html:14
#: sphinx/themes/basic/genindex.html:11 sphinx/themes/basic/genindex.html:34
#: sphinx/themes/basic/genindex.html:67 sphinx/themes/basic/layout.html:138
#: sphinx/writers/texinfo.py:497
msgid "Index"
msgstr "索引"

#: sphinx/builders/latex/__init__.py:199 sphinx/templates/latex/latex.tex_t:91
msgid "Release"
msgstr "發佈"

#: sphinx/builders/latex/__init__.py:213 sphinx/writers/latex.py:370
#, python-format
msgid "no Babel option known for language %r"
msgstr "沒有語言 %r 已知的 Babel 選項"

#: sphinx/builders/latex/__init__.py:394
msgid "copying TeX support files"
msgstr "正在複製 TeX 支援檔案"

#: sphinx/builders/latex/__init__.py:410
msgid "copying TeX support files..."
msgstr "正在複製 TeX 支援檔案..."

#: sphinx/builders/latex/__init__.py:423
msgid "copying additional files"
msgstr "正在複製附加檔案"

#: sphinx/builders/latex/__init__.py:466
#, python-format
msgid "Unknown configure key: latex_elements[%r], ignored."
msgstr "未知的配置鍵： latex_elements[%r]，已略過。"

#: sphinx/builders/latex/__init__.py:474
#, python-format
msgid "Unknown theme option: latex_theme_options[%r], ignored."
msgstr "未知的主題選項： latex_theme_options[%r],，已略過。"

#: sphinx/builders/latex/theming.py:87
#, python-format
msgid "%r doesn't have \"theme\" setting"
msgstr "%r 沒有「主題」設定"

#: sphinx/builders/latex/theming.py:90
#, python-format
msgid "%r doesn't have \"%s\" setting"
msgstr "%r 沒有 \"%s\" 設定"

#: sphinx/builders/latex/transforms.py:120
msgid "Failed to get a docname!"
msgstr "無法取得 docname！"

#: sphinx/builders/latex/transforms.py:121
msgid "Failed to get a docname for source {source!r}!"
msgstr "無法取得來源 {source!r} 的 docname！"

#: sphinx/builders/latex/transforms.py:482
#, python-format
msgid "No footnote was found for given reference node %r"
msgstr "給定的參考節點 %r 找不到註腳"

#: sphinx/cmd/build.py:46
msgid "Exception occurred while building, starting debugger:"
msgstr "在建立時發生例外，正在啟動除錯器："

#: sphinx/cmd/build.py:61
msgid "Interrupted!"
msgstr "已中斷！"

#: sphinx/cmd/build.py:63
msgid "reST markup error:"
msgstr "reST 標示錯誤："

#: sphinx/cmd/build.py:69
msgid "Encoding error:"
msgstr "編碼錯誤："

#: sphinx/cmd/build.py:72 sphinx/cmd/build.py:87
#, python-format
msgid ""
"The full traceback has been saved in %s, if you want to report the issue to "
"the developers."
msgstr "若您想要回報問題給開發者，完整的回溯已被儲存在 %s 中。"

#: sphinx/cmd/build.py:76
msgid "Recursion error:"
msgstr "遞迴錯誤："

#: sphinx/cmd/build.py:79
msgid ""
"This can happen with very large or deeply nested source files. You can "
"carefully increase the default Python recursion limit of 1000 in conf.py "
"with e.g.:"
msgstr "這會發生在非常大或是巢套較深的原始檔案。您可以在 conf.py 中謹慎地增加 Python 的預設 1000 次遞迴上限，例如："

#: sphinx/cmd/build.py:84
msgid "Exception occurred:"
msgstr "發生例外："

#: sphinx/cmd/build.py:90
msgid ""
"Please also report this if it was a user error, so that a better error "
"message can be provided next time."
msgstr "如果這是一個使用者錯誤，請一併回報，如此下次才能提供較佳的錯誤訊息。"

#: sphinx/cmd/build.py:93
msgid ""
"A bug report can be filed in the tracker at <https://github.com/sphinx-"
"doc/sphinx/issues>. Thanks!"
msgstr "錯誤回報可被歸檔於追蹤系統中，它是在 <https://github.com/sphinx-doc/sphinx/issues>. 謝謝！"

#: sphinx/cmd/build.py:109
msgid "job number should be a positive number"
msgstr "工件編號應該是一個正數"

#: sphinx/cmd/build.py:117 sphinx/cmd/quickstart.py:474
#: sphinx/ext/apidoc.py:317 sphinx/ext/autosummary/generate.py:689
msgid "For more information, visit <https://www.sphinx-doc.org/>."
msgstr "需要更多資訊，請拜訪 <https://www.sphinx-doc.org/>."

#: sphinx/cmd/build.py:118
msgid ""
"\n"
"Generate documentation from source files.\n"
"\n"
"sphinx-build generates documentation from the files in SOURCEDIR and places it\n"
"in OUTPUTDIR. It looks for 'conf.py' in SOURCEDIR for the configuration\n"
"settings. The 'sphinx-quickstart' tool may be used to generate template files,\n"
"including 'conf.py'\n"
"\n"
"sphinx-build can create documentation in different formats. A format is\n"
"selected by specifying the builder name on the command line; it defaults to\n"
"HTML. Builders can also perform other tasks related to documentation\n"
"processing.\n"
"\n"
"By default, everything that is outdated is built. Output only for selected\n"
"files can be built by specifying individual filenames.\n"
msgstr "\n從原始檔案產生說明文件。\n\nsphinx-build 會從 SOURCEDIR 中的檔案來產生說明文件，並\n將它置放於 OUTPUTDIR。它會在 SOURCEDIR 中尋找 'conf.py' \n內的組態設定。'sphinx-quickstart' 工具可以用來產生模板檔案，\n包括 'conf.py'\n\nsphinx-build 能以不同的格式建立說明文件。在命令列指定建立\n器的名稱來選擇一種格式；其預設值為 HTML。建立器也能執行\n與處理說明文件有關的其他任務。\n\n在預設情況，所有舊的文件都已經被建立。指定個別的檔名，\n可以在建立時僅限於輸出所選的檔案。\n"

#: sphinx/cmd/build.py:139
msgid "path to documentation source files"
msgstr "到說明文件原始檔案的路徑"

#: sphinx/cmd/build.py:141
msgid "path to output directory"
msgstr "到輸出資料夾的路徑"

#: sphinx/cmd/build.py:143
msgid ""
"(optional) a list of specific files to rebuild. Ignored if --write-all is "
"specified"
msgstr ""

#: sphinx/cmd/build.py:146
msgid "general options"
msgstr "一般選項"

#: sphinx/cmd/build.py:149
msgid "builder to use (default: 'html')"
msgstr ""

#: sphinx/cmd/build.py:152
msgid ""
"run in parallel with N processes, when possible. 'auto' uses the number of "
"CPU cores"
msgstr ""

#: sphinx/cmd/build.py:155
msgid "write all files (default: only write new and changed files)"
msgstr "寫入所有檔案（預設：只寫入新增及已變更檔案）"

#: sphinx/cmd/build.py:158
msgid "don't use a saved environment, always read all files"
msgstr "不要使用已儲存的環境，永遠要讀取全部的檔案"

#: sphinx/cmd/build.py:161
msgid "path options"
msgstr ""

#: sphinx/cmd/build.py:163
msgid ""
"directory for doctree and environment files (default: OUTPUT_DIR/.doctrees)"
msgstr ""

#: sphinx/cmd/build.py:166
msgid "directory for the configuration file (conf.py) (default: SOURCE_DIR)"
msgstr ""

#: sphinx/cmd/build.py:171
msgid "use no configuration file, only use settings from -D options"
msgstr ""

#: sphinx/cmd/build.py:174
msgid "override a setting in configuration file"
msgstr "在組態檔案中置換一項設定"

#: sphinx/cmd/build.py:177
msgid "pass a value into HTML templates"
msgstr "傳遞一個值進入 HTML 模板"

#: sphinx/cmd/build.py:180
msgid "define tag: include \"only\" blocks with TAG"
msgstr "定義 tag：「只」包含有 TAG 的區塊"

#: sphinx/cmd/build.py:182
msgid "nit-picky mode: warn about all missing references"
msgstr ""

#: sphinx/cmd/build.py:184
msgid "console output options"
msgstr "控制台輸出選項"

#: sphinx/cmd/build.py:187
msgid "increase verbosity (can be repeated)"
msgstr "增加贅言（可以被重複）"

#: sphinx/cmd/build.py:189 sphinx/ext/apidoc.py:340
msgid "no output on stdout, just warnings on stderr"
msgstr "在 stdout 無輸出，只有在 stderr 的警告"

#: sphinx/cmd/build.py:191
msgid "no output at all, not even warnings"
msgstr "完全沒有輸出，也沒有警告"

#: sphinx/cmd/build.py:194
msgid "do emit colored output (default: auto-detect)"
msgstr "執行 emit 彩色輸出（預設值：auto-detect）"

#: sphinx/cmd/build.py:197
msgid "do not emit colored output (default: auto-detect)"
msgstr "不執行 emit 彩色輸出（預設值：auto-detect）"

#: sphinx/cmd/build.py:199
msgid "warning control options"
msgstr ""

#: sphinx/cmd/build.py:201
msgid "write warnings (and errors) to given file"
msgstr "寫入警告（及錯誤）至給定的檔案"

#: sphinx/cmd/build.py:203
msgid "turn warnings into errors"
msgstr "將警告轉為錯誤"

#: sphinx/cmd/build.py:205
msgid "with --fail-on-warning, keep going when getting warnings"
msgstr ""

#: sphinx/cmd/build.py:207
msgid "show full traceback on exception"
msgstr "在例外中顯示完整的回溯"

#: sphinx/cmd/build.py:209
msgid "run Pdb on exception"
msgstr "在例外中執行 Pdb"

#: sphinx/cmd/build.py:244
msgid "cannot combine -a option and filenames"
msgstr "無法合併 -a 選項及檔名"

#: sphinx/cmd/build.py:276
#, python-format
msgid "cannot open warning file %r: %s"
msgstr "無法開啟警告檔案 %r: %s"

#: sphinx/cmd/build.py:296
msgid "-D option argument must be in the form name=value"
msgstr "-D 選項引數必須是 name=value 的形式"

#: sphinx/cmd/build.py:303
msgid "-A option argument must be in the form name=value"
msgstr "-A 選項引數必須是 name=value 的形式"

#: sphinx/cmd/quickstart.py:42
msgid "automatically insert docstrings from modules"
msgstr "從模組自動插入說明字串"

#: sphinx/cmd/quickstart.py:43
msgid "automatically test code snippets in doctest blocks"
msgstr "在 doctest 區塊自動測試程式碼片段"

#: sphinx/cmd/quickstart.py:44
msgid "link between Sphinx documentation of different projects"
msgstr "在不同專案的 Sphinx 說明文件中鏈接"

#: sphinx/cmd/quickstart.py:45
msgid "write \"todo\" entries that can be shown or hidden on build"
msgstr "寫入 \"todo\" 項目，它們可以在組建時被顯示或隱藏"

#: sphinx/cmd/quickstart.py:46
msgid "checks for documentation coverage"
msgstr "核對說明文件的涵蓋範圍"

#: sphinx/cmd/quickstart.py:47
msgid "include math, rendered as PNG or SVG images"
msgstr "包含 math，以 PNG 或 SVG 影像被呈現"

#: sphinx/cmd/quickstart.py:48
msgid "include math, rendered in the browser by MathJax"
msgstr "包含 math，被 MathJax 在瀏覽器中呈現"

#: sphinx/cmd/quickstart.py:49
msgid "conditional inclusion of content based on config values"
msgstr "根據組態值有條件地包含內容"

#: sphinx/cmd/quickstart.py:50
msgid "include links to the source code of documented Python objects"
msgstr "包含鏈接至已有說明文件的 Python 物件原始碼"

#: sphinx/cmd/quickstart.py:51
msgid "create .nojekyll file to publish the document on GitHub pages"
msgstr "建立 .nojekyll 檔案以在 GitHub 頁面發布文件"

#: sphinx/cmd/quickstart.py:93
msgid "Please enter a valid path name."
msgstr "請輸入有效的路徑名稱。"

#: sphinx/cmd/quickstart.py:109
msgid "Please enter some text."
msgstr "請輸入一些文字。"

#: sphinx/cmd/quickstart.py:116
#, python-format
msgid "Please enter one of %s."
msgstr "請輸入一種 %s 。"

#: sphinx/cmd/quickstart.py:123
msgid "Please enter either 'y' or 'n'."
msgstr "請輸入 'y' 或 'n'。"

#: sphinx/cmd/quickstart.py:129
msgid "Please enter a file suffix, e.g. '.rst' or '.txt'."
msgstr "請輸入檔案後綴，例如 '.rst' 或 '.txt'。"

#: sphinx/cmd/quickstart.py:208
#, python-format
msgid "Welcome to the Sphinx %s quickstart utility."
msgstr "歡迎進入 Sphinx %s 快速入門公用程式。"

#: sphinx/cmd/quickstart.py:210
msgid ""
"Please enter values for the following settings (just press Enter to\n"
"accept a default value, if one is given in brackets)."
msgstr "請輸入以下設定值（如果括號中有給定預設值，請直接\n按下 Enter 以接受它）。"

#: sphinx/cmd/quickstart.py:215
#, python-format
msgid "Selected root path: %s"
msgstr "被選的根路徑： %s"

#: sphinx/cmd/quickstart.py:218
msgid "Enter the root path for documentation."
msgstr "輸入說明文件的根路徑。"

#: sphinx/cmd/quickstart.py:219
msgid "Root path for the documentation"
msgstr "說明文件的根路徑"

#: sphinx/cmd/quickstart.py:224
msgid "Error: an existing conf.py has been found in the selected root path."
msgstr "錯誤：在被選的根路徑找到一個已存在的 conf.py。"

#: sphinx/cmd/quickstart.py:226
msgid "sphinx-quickstart will not overwrite existing Sphinx projects."
msgstr "sphinx-quickstart 不會重寫已存在的 Sphinx 專案。"

#: sphinx/cmd/quickstart.py:228
msgid "Please enter a new root path (or just Enter to exit)"
msgstr "請輸入一個新的根路徑（或直接按 Enter 離開）"

#: sphinx/cmd/quickstart.py:235
msgid ""
"You have two options for placing the build directory for Sphinx output.\n"
"Either, you use a directory \"_build\" within the root path, or you separate\n"
"\"source\" and \"build\" directories within the root path."
msgstr "您有兩個選擇來為 Sphinx 的輸出放置建立資料夾。\n其一，您可以在根路徑中使用資料夾 \"_build\"，或者，\n您可以在根路徑中分離 \"source\" 和 \"build\" 資料夾。"

#: sphinx/cmd/quickstart.py:238
msgid "Separate source and build directories (y/n)"
msgstr "分離來源並建立資料夾 (y/n)"

#: sphinx/cmd/quickstart.py:242
msgid ""
"Inside the root directory, two more directories will be created; \"_templates\"\n"
"for custom HTML templates and \"_static\" for custom stylesheets and other static\n"
"files. You can enter another prefix (such as \".\") to replace the underscore."
msgstr "在根資料夾內部，另外兩個資料夾會被建立；\"_templates\" \n放置自訂的 HTML 模板，而 \"_static\" 放置自訂的表單及其他\n靜態檔案。您可以輸入另一個前綴（像是 \".\"）來取代底線。"

#: sphinx/cmd/quickstart.py:245
msgid "Name prefix for templates and static dir"
msgstr "用於模板及靜態資料夾的名稱前綴"

#: sphinx/cmd/quickstart.py:249
msgid ""
"The project name will occur in several places in the built documentation."
msgstr "專案名稱會在已建立的說明文件中的多個位置出現。"

#: sphinx/cmd/quickstart.py:250
msgid "Project name"
msgstr "專案名稱"

#: sphinx/cmd/quickstart.py:252
msgid "Author name(s)"
msgstr "作者姓名"

#: sphinx/cmd/quickstart.py:256
msgid ""
"Sphinx has the notion of a \"version\" and a \"release\" for the\n"
"software. Each version can have multiple releases. For example, for\n"
"Python the version is something like 2.5 or 3.0, while the release is\n"
"something like 2.5.1 or 3.0a1. If you don't need this dual structure,\n"
"just set both to the same value."
msgstr "在 Sphinx 中，軟體具有「版本」和「發布版本」的概念。每個\n版本可以有多個發布版本。舉例來說，Python 的版本會像是 2.5 \n或 3.0，而發布版本則像是 2.5.1 或 3.0a1。如果您不需要這個雙\n重的結構，請直接將兩者設為相同的值。"

#: sphinx/cmd/quickstart.py:261
msgid "Project version"
msgstr "專案版本"

#: sphinx/cmd/quickstart.py:263
msgid "Project release"
msgstr "專案發布版本"

#: sphinx/cmd/quickstart.py:267
msgid ""
"If the documents are to be written in a language other than English,\n"
"you can select a language here by its language code. Sphinx will then\n"
"translate text that it generates into that language.\n"
"\n"
"For a list of supported codes, see\n"
"https://www.sphinx-doc.org/en/master/usage/configuration.html#confval-language."
msgstr "如果文件是被英語以外的語言被編寫，您可以根據它的語言碼\n在此選擇一個語言。Sphinx 會將它產生的文本翻譯為該語言。\n\n要了解可支援的語言碼列表，請參閱\nhttps://www.sphinx-doc.org/en/master/usage/configuration.html#confval-language."

#: sphinx/cmd/quickstart.py:275
msgid "Project language"
msgstr "專案語言"

#: sphinx/cmd/quickstart.py:281
msgid ""
"The file name suffix for source files. Commonly, this is either \".txt\"\n"
"or \".rst\". Only files with this suffix are considered documents."
msgstr "用於原始檔的檔案名稱後綴。通常，這會是 \".txt\" 或 \".rst\"。\n只有具有此後綴的檔案才會被認為是文件。"

#: sphinx/cmd/quickstart.py:283
msgid "Source file suffix"
msgstr "原始檔案後綴"

#: sphinx/cmd/quickstart.py:287
msgid ""
"One document is special in that it is considered the top node of the\n"
"\"contents tree\", that is, it is the root of the hierarchical structure\n"
"of the documents. Normally, this is \"index\", but if your \"index\"\n"
"document is a custom template, you can also set this to another filename."
msgstr "一份文件的特別之處在於它會被視為 \"contents tree\" 中的頂端\n節點，也就是說，它是文件階層結構中的根。通常，這會是 \n\"index\"，但如果您的 \"index\" 文件是一個自訂的模板，您也可以\n將它設定為另一個檔名。"

#: sphinx/cmd/quickstart.py:291
msgid "Name of your master document (without suffix)"
msgstr "您的主要文件名稱（不含後綴）"

#: sphinx/cmd/quickstart.py:296
#, python-format
msgid ""
"Error: the master file %s has already been found in the selected root path."
msgstr "錯誤：在被選的根路徑中已經找到主檔 %s 。"

#: sphinx/cmd/quickstart.py:298
msgid "sphinx-quickstart will not overwrite the existing file."
msgstr "sphinx-quickstart 不會重寫已存在的檔案。"

#: sphinx/cmd/quickstart.py:300
msgid ""
"Please enter a new file name, or rename the existing file and press Enter"
msgstr "請輸入一個新的檔案名稱，或將已存在的檔案重新命名並按下 Enter"

#: sphinx/cmd/quickstart.py:304
msgid "Indicate which of the following Sphinx extensions should be enabled:"
msgstr "指示以下哪一個 Sphinx 擴充應該被啟用："

#: sphinx/cmd/quickstart.py:312
msgid ""
"Note: imgmath and mathjax cannot be enabled at the same time. imgmath has "
"been deselected."
msgstr "註解：imgmath 和 mathjax 無法同時被啟用。imgmath 已被取消選擇。"

#: sphinx/cmd/quickstart.py:318
msgid ""
"A Makefile and a Windows command file can be generated for you so that you\n"
"only have to run e.g. `make html' instead of invoking sphinx-build\n"
"directly."
msgstr "會為您產生一個 Makefile 和一個 Windows 命令檔，所以\n您只需要執行像是 `make html' 而不必直接調用 \nsphinx-build。"

#: sphinx/cmd/quickstart.py:321
msgid "Create Makefile? (y/n)"
msgstr "是否建立 Makefile? (y/n)"

#: sphinx/cmd/quickstart.py:324
msgid "Create Windows command file? (y/n)"
msgstr "是否建立 Windows 命令檔？(y/n)"

#: sphinx/cmd/quickstart.py:368 sphinx/ext/apidoc.py:93
#, python-format
msgid "Creating file %s."
msgstr "正在建立檔案 %s 。"

#: sphinx/cmd/quickstart.py:373 sphinx/ext/apidoc.py:90
#, python-format
msgid "File %s already exists, skipping."
msgstr "檔案 %s 已存在，正在跳過。"

#: sphinx/cmd/quickstart.py:418
msgid "Finished: An initial directory structure has been created."
msgstr "已結束：一個初始資料夾結構已被建立。"

#: sphinx/cmd/quickstart.py:420
#, python-format
msgid ""
"You should now populate your master file %s and create other documentation\n"
"source files. "
msgstr "您現在應該在您的主檔 %s 輸入資料並建立其他說明文件\n原始檔。"

#: sphinx/cmd/quickstart.py:423
msgid ""
"Use the Makefile to build the docs, like so:\n"
"   make builder"
msgstr "使用 Makefile 來建立文件，像這樣：\n   make builder"

#: sphinx/cmd/quickstart.py:426
#, python-format
msgid ""
"Use the sphinx-build command to build the docs, like so:\n"
"   sphinx-build -b builder %s %s"
msgstr "使用 sphinx-build 命令來建立文件，像這樣：\n   sphinx-build -b builder %s %s"

#: sphinx/cmd/quickstart.py:428
msgid ""
"where \"builder\" is one of the supported builders, e.g. html, latex or "
"linkcheck."
msgstr "在這裡 \"builder\" 是一種被支援的建立器，例如 html，latex 或 linkcheck。"

#: sphinx/cmd/quickstart.py:464
msgid ""
"\n"
"Generate required files for a Sphinx project.\n"
"\n"
"sphinx-quickstart is an interactive tool that asks some questions about your\n"
"project and then generates a complete documentation directory and sample\n"
"Makefile to be used with sphinx-build.\n"
msgstr "\n為 Sphinx 專案產生需要的檔案。\n\nsphinx-quickstart 是一個互動式工具，它會問一些關於您專案\n的問題，然後產生完整的說明文件資料夾以及用於 sphinx-build \n的 Makefile 樣本。\n"

#: sphinx/cmd/quickstart.py:479
msgid "quiet mode"
msgstr "安靜模式"

#: sphinx/cmd/quickstart.py:484
msgid "project root"
msgstr "專案根"

#: sphinx/cmd/quickstart.py:486
msgid "Structure options"
msgstr "結構選項"

#: sphinx/cmd/quickstart.py:488
msgid "if specified, separate source and build dirs"
msgstr "如果有指定，會分離來源資料夾和 build 資料夾"

#: sphinx/cmd/quickstart.py:490
msgid "if specified, create build dir under source dir"
msgstr "如果有指定，會在來源資料夾下建立 build 資料夾"

#: sphinx/cmd/quickstart.py:492
msgid "replacement for dot in _templates etc."
msgstr "在 _templates 等處進行句號的取代"

#: sphinx/cmd/quickstart.py:494
msgid "Project basic options"
msgstr "專案基本選項"

#: sphinx/cmd/quickstart.py:496
msgid "project name"
msgstr "專案名稱"

#: sphinx/cmd/quickstart.py:498
msgid "author names"
msgstr "作者名"

#: sphinx/cmd/quickstart.py:500
msgid "version of project"
msgstr "專案版本"

#: sphinx/cmd/quickstart.py:502
msgid "release of project"
msgstr "專案發布"

#: sphinx/cmd/quickstart.py:504
msgid "document language"
msgstr "文件語言"

#: sphinx/cmd/quickstart.py:506
msgid "source file suffix"
msgstr "源始檔後綴"

#: sphinx/cmd/quickstart.py:508
msgid "master document name"
msgstr "主文件名稱"

#: sphinx/cmd/quickstart.py:510
msgid "use epub"
msgstr "使用 epub"

#: sphinx/cmd/quickstart.py:512
msgid "Extension options"
msgstr "擴充套件選項"

#: sphinx/cmd/quickstart.py:516 sphinx/ext/apidoc.py:400
#, python-format
msgid "enable %s extension"
msgstr "啟用 %s 擴充套件"

#: sphinx/cmd/quickstart.py:518 sphinx/ext/apidoc.py:396
msgid "enable arbitrary extensions"
msgstr "啟用任意的擴充套件"

#: sphinx/cmd/quickstart.py:520
msgid "Makefile and Batchfile creation"
msgstr "Makefile 及 Batchfile 的建立"

#: sphinx/cmd/quickstart.py:522
msgid "create makefile"
msgstr "建立 makefile"

#: sphinx/cmd/quickstart.py:524
msgid "do not create makefile"
msgstr "不要建立 makefile"

#: sphinx/cmd/quickstart.py:526
msgid "create batchfile"
msgstr "建立 batchfile"

#: sphinx/cmd/quickstart.py:529
msgid "do not create batchfile"
msgstr "不要建立 batchfile"

#: sphinx/cmd/quickstart.py:532
msgid "use make-mode for Makefile/make.bat"
msgstr "使用 make 模式於 Makefile/make.bat"

#: sphinx/cmd/quickstart.py:535
msgid "do not use make-mode for Makefile/make.bat"
msgstr "不要使用 make 模式於 Makefile/make.bat"

#: sphinx/cmd/quickstart.py:537 sphinx/ext/apidoc.py:402
msgid "Project templating"
msgstr "專案模板化中"

#: sphinx/cmd/quickstart.py:540 sphinx/ext/apidoc.py:405
msgid "template directory for template files"
msgstr "用於模板檔案的模板資料夾"

#: sphinx/cmd/quickstart.py:543
msgid "define a template variable"
msgstr "定義一個模板變數"

#: sphinx/cmd/quickstart.py:576
msgid "\"quiet\" is specified, but any of \"project\" or \"author\" is not specified."
msgstr "\"quiet\" 被指定，但 \"project\" 或 \"author\" 的任一項未被指定。"

#: sphinx/cmd/quickstart.py:590
msgid ""
"Error: specified path is not a directory, or sphinx files already exist."
msgstr "錯誤：指定的路徑不是資料夾，或是 sphinx 檔案已經存在。"

#: sphinx/cmd/quickstart.py:592
msgid ""
"sphinx-quickstart only generate into a empty directory. Please specify a new"
" root path."
msgstr "sphinx-quickstart 只能產生於空白資料夾中。請指定一個新的根路徑。"

#: sphinx/cmd/quickstart.py:607
#, python-format
msgid "Invalid template variable: %s"
msgstr "無效的模板變數： %s"

#: sphinx/directives/code.py:61
msgid "non-whitespace stripped by dedent"
msgstr "非空白字元被凸排去除"

#: sphinx/directives/code.py:82
#, python-format
msgid "Invalid caption: %s"
msgstr "無效標題：%s"

#: sphinx/directives/code.py:127 sphinx/directives/code.py:277
#: sphinx/directives/code.py:453
#, python-format
msgid "line number spec is out of range(1-%d): %r"
msgstr "列號規格超出範圍 (1-%d)： %r"

#: sphinx/directives/code.py:206
#, python-format
msgid "Cannot use both \"%s\" and \"%s\" options"
msgstr "不能使用 \"%s\" 及 \"%s\" 兩個選項"

#: sphinx/directives/code.py:220
#, python-format
msgid "Include file %r not found or reading it failed"
msgstr "Include 檔案 %r 未找到或是讀取失敗"

#: sphinx/directives/code.py:223
#, python-format
msgid ""
"Encoding %r used for reading included file %r seems to be wrong, try giving "
"an :encoding: option"
msgstr "編碼 %r 用以讀取被 include 的檔案 %r 似乎有錯，嘗試給定一個 :encoding: 選項"

#: sphinx/directives/code.py:260
#, python-format
msgid "Object named %r not found in include file %r"
msgstr "名為 %r 的物件在 include 檔案 %r 中未找到"

#: sphinx/directives/code.py:286
msgid "Cannot use \"lineno-match\" with a disjoint set of \"lines\""
msgstr "無法以一個 \"lines\" 的互斥集使用 \"lineno-match\" "

#: sphinx/directives/code.py:291
#, python-format
msgid "Line spec %r: no lines pulled from include file %r"
msgstr "Line spec %r: 從 include 檔案 %r 沒有提取任何一行"

#: sphinx/directives/other.py:120
#, python-format
msgid "toctree glob pattern %r didn't match any documents"
msgstr "toctree glob 型樣 %r 未匹配任何文件"

#: sphinx/directives/other.py:146 sphinx/environment/adapters/toctree.py:324
#, python-format
msgid "toctree contains reference to excluded document %r"
msgstr "toctree 包含了指向已排除文件的參照 %r"

#: sphinx/directives/other.py:149 sphinx/environment/adapters/toctree.py:328
#, python-format
msgid "toctree contains reference to nonexisting document %r"
msgstr "toctree 包含了指向不存在文件的參照 %r"

#: sphinx/directives/other.py:160
#, python-format
msgid "duplicated entry found in toctree: %s"
msgstr "在 toctree 中找到重複的項目： %s"

#: sphinx/directives/other.py:193
msgid "Section author: "
msgstr "章節作者："

#: sphinx/directives/other.py:195
msgid "Module author: "
msgstr "模組作者："

#: sphinx/directives/other.py:197
msgid "Code author: "
msgstr "程式作者："

#: sphinx/directives/other.py:199
msgid "Author: "
msgstr "作者："

#: sphinx/directives/other.py:275
msgid ".. acks content is not a list"
msgstr ".. acks 的內容不是一個列表"

#: sphinx/directives/other.py:301
msgid ".. hlist content is not a list"
msgstr ".. hlist 的內容不是一個列表"

#: sphinx/directives/patches.py:66
msgid ""
"\":file:\" option for csv-table directive now recognizes an absolute path as"
" a relative path from source directory. Please update your document."
msgstr "對 csv-table 指令的 \":file:\" 選項現在會將絕對路徑辨識為基於來源資料夾的相對路徑。請更新您的文件。"

#: sphinx/domains/__init__.py:397
#, python-format
msgid "%s %s"
msgstr "%s %s"

#: sphinx/domains/changeset.py:23
#, python-format
msgid "Added in version %s"
msgstr ""

#: sphinx/domains/changeset.py:24
#, python-format
msgid "Changed in version %s"
msgstr "在 %s 版的變更"

#: sphinx/domains/changeset.py:25
#, python-format
msgid "Deprecated since version %s"
msgstr "在 %s 版之後被棄用"

#: sphinx/domains/changeset.py:26
#, python-format
msgid "Removed in version %s"
msgstr ""

#: sphinx/domains/citation.py:71
#, python-format
msgid "duplicate citation %s, other instance in %s"
msgstr "重複的引用 %s，亦出現於 %s"

#: sphinx/domains/citation.py:82
#, python-format
msgid "Citation [%s] is not referenced."
msgstr "引用 [%s] 未被參照。"

#: sphinx/domains/javascript.py:165
#, python-format
msgid "%s() (built-in function)"
msgstr "%s() (內建函式)"

#: sphinx/domains/javascript.py:166 sphinx/domains/python/__init__.py:240
#, python-format
msgid "%s() (%s method)"
msgstr "%s() (%s 的方法)"

#: sphinx/domains/javascript.py:168
#, python-format
msgid "%s() (class)"
msgstr "%s() (類別)"

#: sphinx/domains/javascript.py:170
#, python-format
msgid "%s (global variable or constant)"
msgstr "%s (全域變數或常數)"

#: sphinx/domains/javascript.py:172 sphinx/domains/python/__init__.py:325
#, python-format
msgid "%s (%s attribute)"
msgstr "%s (%s 的屬性)"

#: sphinx/domains/javascript.py:255
msgid "Arguments"
msgstr "引數"

#: sphinx/domains/cpp/__init__.py:350 sphinx/domains/javascript.py:258
msgid "Throws"
msgstr "拋出"

#: sphinx/domains/c/__init__.py:251 sphinx/domains/cpp/__init__.py:361
#: sphinx/domains/javascript.py:261 sphinx/domains/python/_object.py:175
msgid "Returns"
msgstr "回傳"

#: sphinx/domains/c/__init__.py:253 sphinx/domains/javascript.py:263
#: sphinx/domains/python/_object.py:177
msgid "Return type"
msgstr "回傳型別"

#: sphinx/domains/javascript.py:331
#, python-format
msgid "%s (module)"
msgstr "%s (模組)"

#: sphinx/domains/c/__init__.py:622 sphinx/domains/cpp/__init__.py:764
#: sphinx/domains/javascript.py:368 sphinx/domains/python/__init__.py:574
msgid "function"
msgstr "函式"

#: sphinx/domains/javascript.py:369 sphinx/domains/python/__init__.py:578
msgid "method"
msgstr "方法"

#: sphinx/domains/cpp/__init__.py:762 sphinx/domains/javascript.py:370
#: sphinx/domains/python/__init__.py:576
msgid "class"
msgstr "類別"

#: sphinx/domains/javascript.py:371 sphinx/domains/python/__init__.py:575
msgid "data"
msgstr "資料"

#: sphinx/domains/javascript.py:372 sphinx/domains/python/__init__.py:581
msgid "attribute"
msgstr "屬性"

#: sphinx/domains/javascript.py:373 sphinx/domains/python/__init__.py:583
msgid "module"
msgstr "模組"

#: sphinx/domains/javascript.py:404
#, python-format
msgid "duplicate %s description of %s, other %s in %s"
msgstr "%s 的重複 %s 敘述，其他的 %s 在 %s"

#: sphinx/domains/math.py:63
#, python-format
msgid "duplicate label of equation %s, other instance in %s"
msgstr "重複公式標籤 %s，亦出現於 %s"

#: sphinx/domains/math.py:118 sphinx/writers/latex.py:2252
#, python-format
msgid "Invalid math_eqref_format: %r"
msgstr "無效的 math_eqref_format: %r"

#: sphinx/domains/rst.py:127 sphinx/domains/rst.py:184
#, python-format
msgid "%s (directive)"
msgstr "%s （指令）"

#: sphinx/domains/rst.py:185 sphinx/domains/rst.py:189
#, python-format
msgid ":%s: (directive option)"
msgstr ":%s: （指令選項）"

#: sphinx/domains/rst.py:213
#, python-format
msgid "%s (role)"
msgstr "%s （角色）"

#: sphinx/domains/rst.py:223
msgid "directive"
msgstr "指令"

#: sphinx/domains/rst.py:224
msgid "directive-option"
msgstr "指令選項"

#: sphinx/domains/rst.py:225
msgid "role"
msgstr "角色"

#: sphinx/domains/rst.py:247
#, python-format
msgid "duplicate description of %s %s, other instance in %s"
msgstr "%s %s 的重複敘述，其他的實例在 %s"

#: sphinx/domains/c/__init__.py:146
#, python-format
msgid "%s (C %s)"
msgstr "%s (C %s)"

#: sphinx/domains/c/__init__.py:207 sphinx/domains/c/_symbol.py:552
#, python-format
msgid ""
"Duplicate C declaration, also defined at %s:%s.\n"
"Declaration is '.. c:%s:: %s'."
msgstr "重複的 C 宣告，亦被定義於 %s:%s。\n宣告是 '.. c:%s:: %s'。"

#: sphinx/domains/c/__init__.py:245 sphinx/domains/cpp/__init__.py:344
#: sphinx/domains/python/_object.py:163 sphinx/ext/napoleon/docstring.py:762
msgid "Parameters"
msgstr "參數"

#: sphinx/domains/c/__init__.py:248 sphinx/domains/cpp/__init__.py:357
msgid "Return values"
msgstr "回傳值"

#: sphinx/domains/c/__init__.py:620 sphinx/domains/cpp/__init__.py:765
msgid "member"
msgstr "成員函數"

#: sphinx/domains/c/__init__.py:621
msgid "variable"
msgstr "變數"

#: sphinx/domains/c/__init__.py:623
msgid "macro"
msgstr "巨集"

#: sphinx/domains/c/__init__.py:624
msgid "struct"
msgstr "結構"

#: sphinx/domains/c/__init__.py:625 sphinx/domains/cpp/__init__.py:763
msgid "union"
msgstr "union"

#: sphinx/domains/c/__init__.py:626 sphinx/domains/cpp/__init__.py:768
msgid "enum"
msgstr "enum"

#: sphinx/domains/c/__init__.py:627 sphinx/domains/cpp/__init__.py:769
msgid "enumerator"
msgstr "enumerator"

#: sphinx/domains/c/__init__.py:628 sphinx/domains/cpp/__init__.py:766
msgid "type"
msgstr "型別"

#: sphinx/domains/c/__init__.py:630 sphinx/domains/cpp/__init__.py:771
msgid "function parameter"
msgstr "函式參數"

#: sphinx/domains/cpp/__init__.py:63
msgid "Template Parameters"
msgstr "模板參數"

#: sphinx/domains/cpp/__init__.py:185
#, python-format
msgid "%s (C++ %s)"
msgstr "%s (C++ %s)"

#: sphinx/domains/cpp/__init__.py:268 sphinx/domains/cpp/_symbol.py:790
#, python-format
msgid ""
"Duplicate C++ declaration, also defined at %s:%s.\n"
"Declaration is '.. cpp:%s:: %s'."
msgstr "重複的 C++ 宣告，亦被定義於 %s:%s。\n宣告是 '.. cpp:%s:: %s'。"

#: sphinx/domains/cpp/__init__.py:767
msgid "concept"
msgstr "概念"

#: sphinx/domains/cpp/__init__.py:772
msgid "template parameter"
msgstr "模板參數"

#: sphinx/domains/python/__init__.py:94 sphinx/domains/python/__init__.py:231
#, python-format
msgid "%s() (in module %s)"
msgstr "%s() (於 %s 模組中)"

#: sphinx/domains/python/__init__.py:154 sphinx/domains/python/__init__.py:321
#: sphinx/domains/python/__init__.py:372
#, python-format
msgid "%s (in module %s)"
msgstr "%s (於 %s 模組中)"

#: sphinx/domains/python/__init__.py:156
#, python-format
msgid "%s (built-in variable)"
msgstr "%s (內建變數)"

#: sphinx/domains/python/__init__.py:181
#, python-format
msgid "%s (built-in class)"
msgstr "%s (內建類別)"

#: sphinx/domains/python/__init__.py:182
#, python-format
msgid "%s (class in %s)"
msgstr "%s (%s 中的類別)"

#: sphinx/domains/python/__init__.py:236
#, python-format
msgid "%s() (%s class method)"
msgstr "%s() (%s 的類別方法)"

#: sphinx/domains/python/__init__.py:238
#, python-format
msgid "%s() (%s static method)"
msgstr "%s() (%s 的靜態方法)"

#: sphinx/domains/python/__init__.py:376
#, python-format
msgid "%s (%s property)"
msgstr "%s (%s 的特性)"

#: sphinx/domains/python/__init__.py:502
msgid "Python Module Index"
msgstr "Python 模組索引"

#: sphinx/domains/python/__init__.py:503
msgid "modules"
msgstr "模組"

#: sphinx/domains/python/__init__.py:552
msgid "Deprecated"
msgstr "已棄用"

#: sphinx/domains/python/__init__.py:577
msgid "exception"
msgstr "例外"

#: sphinx/domains/python/__init__.py:579
msgid "class method"
msgstr "類別方法"

#: sphinx/domains/python/__init__.py:580
msgid "static method"
msgstr "靜態方法"

#: sphinx/domains/python/__init__.py:582
msgid "property"
msgstr "特性"

#: sphinx/domains/python/__init__.py:640
#, python-format
msgid ""
"duplicate object description of %s, other instance in %s, use :no-index: for"
" one of them"
msgstr "重複的 %s 的物件描述，在 %s 有其他實例，請在它們其中之一使用 :no-index:"

#: sphinx/domains/python/__init__.py:760
#, python-format
msgid "more than one target found for cross-reference %r: %s"
msgstr "為交互參照 %r 找到多於一個目標： %s"

#: sphinx/domains/python/__init__.py:821
msgid " (deprecated)"
msgstr "（已棄用）"

#: sphinx/domains/python/_object.py:168
msgid "Variables"
msgstr "變數"

#: sphinx/domains/python/_object.py:172
msgid "Raises"
msgstr "引發"

#: sphinx/domains/std/__init__.py:80 sphinx/domains/std/__init__.py:97
#, python-format
msgid "environment variable; %s"
msgstr "環境變數; %s"

#: sphinx/domains/std/__init__.py:157
#, python-format
msgid ""
"Malformed option description %r, should look like \"opt\", \"-opt args\", \""
"--opt args\", \"/opt args\" or \"+opt args\""
msgstr "異常的選項敘述 %r ，應該要看起來像 \"opt\", \"-opt args\", \"--opt args\", \"/opt args\" 或 \"+opt args\""

#: sphinx/domains/std/__init__.py:228
#, python-format
msgid "%s command line option"
msgstr "%s 命令列選項"

#: sphinx/domains/std/__init__.py:230
msgid "command line option"
msgstr "命令列選項"

#: sphinx/domains/std/__init__.py:348
msgid "glossary term must be preceded by empty line"
msgstr "術語表項目必須有空白行在前"

#: sphinx/domains/std/__init__.py:356
msgid "glossary terms must not be separated by empty lines"
msgstr "術語表項目不可以被空白行分隔"

#: sphinx/domains/std/__init__.py:362 sphinx/domains/std/__init__.py:375
msgid "glossary seems to be misformatted, check indentation"
msgstr "術語表似乎有格式錯誤，請檢查縮排"

#: sphinx/domains/std/__init__.py:518
msgid "glossary term"
msgstr "雜項術語"

#: sphinx/domains/std/__init__.py:519
msgid "grammar token"
msgstr "語法單詞"

#: sphinx/domains/std/__init__.py:520
msgid "reference label"
msgstr "參照標籤"

#: sphinx/domains/std/__init__.py:522
msgid "environment variable"
msgstr "環境變數"

#: sphinx/domains/std/__init__.py:523
msgid "program option"
msgstr "程式選項"

#: sphinx/domains/std/__init__.py:524
msgid "document"
msgstr "文件"

#: sphinx/domains/std/__init__.py:560 sphinx/domains/std/__init__.py:572
msgid "Module Index"
msgstr "模組索引"

#: sphinx/domains/std/__init__.py:561 sphinx/domains/std/__init__.py:573
#: sphinx/themes/basic/defindex.html:25
msgid "Search Page"
msgstr "搜尋頁面"

#: sphinx/domains/std/__init__.py:616 sphinx/domains/std/__init__.py:722
#: sphinx/ext/autosectionlabel.py:53
#, python-format
msgid "duplicate label %s, other instance in %s"
msgstr "重複的標籤 %s，亦出現於 %s"

#: sphinx/domains/std/__init__.py:635
#, python-format
msgid "duplicate %s description of %s, other instance in %s"
msgstr "重複 %s 的描述 %s，亦出現於 %s"

#: sphinx/domains/std/__init__.py:841
msgid "numfig is disabled. :numref: is ignored."
msgstr "numfig 已停用。 :numref: 已略過。"

#: sphinx/domains/std/__init__.py:849
#, python-format
msgid "Failed to create a cross reference. Any number is not assigned: %s"
msgstr "無法建立一個交互參照。任一數字未被指定： %s"

#: sphinx/domains/std/__init__.py:861
#, python-format
msgid "the link has no caption: %s"
msgstr "這個連結沒有標題： %s"

#: sphinx/domains/std/__init__.py:875
#, python-format
msgid "invalid numfig_format: %s (%r)"
msgstr "無效的 numfig_format: %s (%r)"

#: sphinx/domains/std/__init__.py:878
#, python-format
msgid "invalid numfig_format: %s"
msgstr "無效的 numfig_format: %s"

#: sphinx/domains/std/__init__.py:1109
#, python-format
msgid "undefined label: %r"
msgstr "未定義的標籤： %r"

#: sphinx/domains/std/__init__.py:1111
#, python-format
msgid "Failed to create a cross reference. A title or caption not found: %r"
msgstr "無法建立一個交互參照。未找到標題或說明： %r"

#: sphinx/environment/__init__.py:71
msgid "new config"
msgstr "新的組態"

#: sphinx/environment/__init__.py:72
msgid "config changed"
msgstr "組態已變更"

#: sphinx/environment/__init__.py:73
msgid "extensions changed"
msgstr "擴充套件已變更"

#: sphinx/environment/__init__.py:279
msgid "build environment version not current"
msgstr "建立環境的版本不是目前的"

#: sphinx/environment/__init__.py:281
msgid "source directory has changed"
msgstr "來源資料夾已變更"

#: sphinx/environment/__init__.py:360
msgid ""
"This environment is incompatible with the selected builder, please choose "
"another doctree directory."
msgstr "這個環境與所選的 builder 不相容，請選擇另一個 doctree 資料夾。"

#: sphinx/environment/__init__.py:459
#, python-format
msgid "Failed to scan documents in %s: %r"
msgstr "無法掃描 %s 中的文件： %r"

#: sphinx/environment/__init__.py:596
#, python-format
msgid "Domain %r is not registered"
msgstr "Domain %r 未被註冊"

#: sphinx/environment/__init__.py:730
msgid "document isn't included in any toctree"
msgstr "文件未被包含於任何 toctree"

#: sphinx/environment/__init__.py:766
msgid "self referenced toctree found. Ignored."
msgstr "找到自我參照的 toctree。已略過。"

#: sphinx/environment/adapters/indexentries.py:69
#, python-format
msgid "see %s"
msgstr "參考 %s"

#: sphinx/environment/adapters/indexentries.py:73
#, python-format
msgid "see also %s"
msgstr "也參考 %s"

#: sphinx/environment/adapters/indexentries.py:76
#, python-format
msgid "unknown index entry type %r"
msgstr "未知的索引項目型別 %r"

#: sphinx/environment/adapters/indexentries.py:187
#: sphinx/templates/latex/sphinxmessages.sty_t:11
msgid "Symbols"
msgstr "符號"

#: sphinx/environment/adapters/toctree.py:297
#, python-format
msgid "circular toctree references detected, ignoring: %s <- %s"
msgstr "偵測到循環的 toctree 參照，忽略中： %s <- %s"

#: sphinx/environment/adapters/toctree.py:317
#, python-format
msgid ""
"toctree contains reference to document %r that doesn't have a title: no link"
" will be generated"
msgstr "toctree 包含了到文件 %r 的參照，該文件沒有標題：不會產生任何鏈接"

#: sphinx/environment/adapters/toctree.py:326
#, python-format
msgid "toctree contains reference to non-included document %r"
msgstr "toctree 包含了 non-included 文件 %r 的參照"

#: sphinx/environment/collectors/asset.py:89
#, python-format
msgid "image file not readable: %s"
msgstr "影像檔案無法讀取： %s"

#: sphinx/environment/collectors/asset.py:108
#, python-format
msgid "image file %s not readable: %s"
msgstr "影像檔案 %s 無法讀取： %s"

#: sphinx/environment/collectors/asset.py:134
#, python-format
msgid "download file not readable: %s"
msgstr "下載檔案無法讀取： %s"

#: sphinx/environment/collectors/toctree.py:225
#, python-format
msgid "%s is already assigned section numbers (nested numbered toctree?)"
msgstr "%s 已經被指定段落編號（巢狀編號的 toctree？）"

#: sphinx/ext/apidoc.py:86
#, python-format
msgid "Would create file %s."
msgstr "將會建立檔案 %s 。"

#: sphinx/ext/apidoc.py:318
msgid ""
"\n"
"Look recursively in <MODULE_PATH> for Python modules and packages and create\n"
"one reST file with automodule directives per package in the <OUTPUT_PATH>.\n"
"\n"
"The <EXCLUDE_PATTERN>s can be file and/or directory patterns that will be\n"
"excluded from generation.\n"
"\n"
"Note: By default this script will not overwrite already created files."
msgstr "\n在 <MODULE_PATH> 中遞迴查找 Python 模組及套件，並在 <OUTPUT_PATH> 中\n為每個套件建立一個帶有 automodule 指令的 reST 檔。\n\n<EXCLUDE_PATTERN> 可以是檔案及/或資料夾型樣，它們將在生成時被\n移除。\n\n備註：在預設情況，此腳本不會重寫已經被建立的檔案。"

#: sphinx/ext/apidoc.py:331
msgid "path to module to document"
msgstr "要生成文件的模組路徑"

#: sphinx/ext/apidoc.py:333
msgid ""
"fnmatch-style file and/or directory patterns to exclude from generation"
msgstr "fnmatch 風格的檔案及/或資料夾模式，將在生成時移除。"

#: sphinx/ext/apidoc.py:338
msgid "directory to place all output"
msgstr "要放置所有輸出的資料夾"

#: sphinx/ext/apidoc.py:343
msgid "maximum depth of submodules to show in the TOC (default: 4)"
msgstr "能顯示 TOC 的子模組最大深度（預設值：4）"

#: sphinx/ext/apidoc.py:346
msgid "overwrite existing files"
msgstr "重寫已存在的檔案"

#: sphinx/ext/apidoc.py:349
msgid ""
"follow symbolic links. Powerful when combined with "
"collective.recipe.omelette."
msgstr "跟隨符號鏈接。與 collective.recipe.omelette 結合時很有用。"

#: sphinx/ext/apidoc.py:352
msgid "run the script without creating files"
msgstr "執行腳本而不建立檔案"

#: sphinx/ext/apidoc.py:355
msgid "put documentation for each module on its own page"
msgstr "為每個模組在它自己的頁面置放說明文件"

#: sphinx/ext/apidoc.py:358
msgid "include \"_private\" modules"
msgstr "包含 \"_private\" 模組"

#: sphinx/ext/apidoc.py:360
msgid "filename of table of contents (default: modules)"
msgstr "目錄的檔名（預設值：模組）"

#: sphinx/ext/apidoc.py:362
msgid "don't create a table of contents file"
msgstr "不要建立目錄檔案"

#: sphinx/ext/apidoc.py:365
msgid ""
"don't create headings for the module/package packages (e.g. when the "
"docstrings already contain them)"
msgstr "不要為模組/套件建立標頭（例如：當說明字串已經包含它們時）"

#: sphinx/ext/apidoc.py:370
msgid "put module documentation before submodule documentation"
msgstr "在子模組說明文件之前置放模組說明文件"

#: sphinx/ext/apidoc.py:374
msgid ""
"interpret module paths according to PEP-0420 implicit namespaces "
"specification"
msgstr "根據 PEP-0420 隱式命名空間規範來解譯模組路徑"

#: sphinx/ext/apidoc.py:378
msgid "file suffix (default: rst)"
msgstr "檔案後綴（預設值：rst）"

#: sphinx/ext/apidoc.py:380
msgid "generate a full project with sphinx-quickstart"
msgstr "以 sphinx-quickstart 生成一個完全的專案"

#: sphinx/ext/apidoc.py:383
msgid "append module_path to sys.path, used when --full is given"
msgstr "附加 module_path 到 sys.path，在給予 --full 時使用"

#: sphinx/ext/apidoc.py:385
msgid "project name (default: root module name)"
msgstr "專案名稱（預設值：根模組名稱）"

#: sphinx/ext/apidoc.py:387
msgid "project author(s), used when --full is given"
msgstr "專案作者（們），在給予 --full 時使用"

#: sphinx/ext/apidoc.py:389
msgid "project version, used when --full is given"
msgstr "專案版本，在給予 --full 時使用"

#: sphinx/ext/apidoc.py:391
msgid "project release, used when --full is given, defaults to --doc-version"
msgstr "專案發布，在給予 --full 時使用，預設為 --doc-version"

#: sphinx/ext/apidoc.py:394
msgid "extension options"
msgstr "擴充套件選項"

#: sphinx/ext/apidoc.py:427
#, python-format
msgid "%s is not a directory."
msgstr "%s 不是資料夾"

#: sphinx/ext/autosectionlabel.py:49
#, python-format
msgid "section \"%s\" gets labeled as \"%s\""
msgstr "段落 \"%s\" 取得標籤 \"%s\""

#: sphinx/ext/coverage.py:46
#, python-format
msgid "invalid regex %r in %s"
msgstr "無效的 regex %r 在 %s"

#: sphinx/ext/coverage.py:75
#, python-format
msgid ""
"Testing of coverage in the sources finished, look at the results in "
"%(outdir)spython.txt."
msgstr "來源的涵蓋測試已結束，在 %(outdir)spython.txt 中查看結果。"

#: sphinx/ext/coverage.py:89
#, python-format
msgid "invalid regex %r in coverage_c_regexes"
msgstr "無效的 regex %r 在 coverage_c_regexes"

#: sphinx/ext/coverage.py:157
#, python-format
msgid "undocumented c api: %s [%s] in file %s"
msgstr "未文件化的 c api： %s [%s] 在檔案 %s 中"

#: sphinx/ext/coverage.py:189
#, python-format
msgid "module %s could not be imported: %s"
msgstr "模組 %s 無法被 import： %s"

#: sphinx/ext/coverage.py:340
#, python-format
msgid "undocumented python function: %s :: %s"
msgstr "未文件化的 python 函式： %s :: %s"

#: sphinx/ext/coverage.py:356
#, python-format
msgid "undocumented python class: %s :: %s"
msgstr "未文件化的 python class： %s :: %s"

#: sphinx/ext/coverage.py:369
#, python-format
msgid "undocumented python method: %s :: %s :: %s"
msgstr "未文件化的 python method： %s :: %s :: %s"

#: sphinx/ext/doctest.py:115
#, python-format
msgid "missing '+' or '-' in '%s' option."
msgstr "在 '%s' 選項中遺漏 '+' 或 '-'。"

#: sphinx/ext/doctest.py:120
#, python-format
msgid "'%s' is not a valid option."
msgstr "'%s' 不是有效的選項"

#: sphinx/ext/doctest.py:134
#, python-format
msgid "'%s' is not a valid pyversion option"
msgstr "'%s' 不是有效的 pyversion 選項"

#: sphinx/ext/doctest.py:220
msgid "invalid TestCode type"
msgstr "無效的 TestCode 型別"

#: sphinx/ext/doctest.py:281
#, python-format
msgid ""
"Testing of doctests in the sources finished, look at the results in "
"%(outdir)s/output.txt."
msgstr "來源的 doctests 測試已結束，在 %(outdir)s/output.txt 中查看結果。"

#: sphinx/ext/doctest.py:438
#, python-format
msgid "no code/output in %s block at %s:%s"
msgstr "在 %s 區塊中的 %s:%s 沒有程式碼/輸出"

#: sphinx/ext/doctest.py:526
#, python-format
msgid "ignoring invalid doctest code: %r"
msgstr "正在忽略無效的 doctest 碼： %r"

#: sphinx/ext/duration.py:77
msgid ""
"====================== slowest reading durations ======================="
msgstr "====================== 最慢的讀取歷時 ======================="

#: sphinx/ext/extlinks.py:82
#, python-format
msgid ""
"hardcoded link %r could be replaced by an extlink (try using %r instead)"
msgstr "hardcoded link %r 可以被一個 extlink 所取代（試試改用 %r）"

#: sphinx/ext/graphviz.py:135
msgid "Graphviz directive cannot have both content and a filename argument"
msgstr "Graphviz 指令不能同時有內容及檔名引數"

#: sphinx/ext/graphviz.py:145
#, python-format
msgid "External Graphviz file %r not found or reading it failed"
msgstr "外部的 Graphviz 檔案 %r 未找到或是讀取失敗"

#: sphinx/ext/graphviz.py:152
msgid "Ignoring \"graphviz\" directive without content."
msgstr "正在略過沒有內容的 \"graphviz\" 指令"

#: sphinx/ext/graphviz.py:268
#, python-format
msgid "graphviz_dot executable path must be set! %r"
msgstr "graphviz_dot 可執行路徑必須設定！ %r"

#: sphinx/ext/graphviz.py:303
#, python-format
msgid ""
"dot command %r cannot be run (needed for graphviz output), check the "
"graphviz_dot setting"
msgstr "dot 命令 %r 無法被執行（graphviz 輸出所需要），請檢查 graphviz_dot 設定"

#: sphinx/ext/graphviz.py:310
#, python-format
msgid ""
"dot exited with error:\n"
"[stderr]\n"
"%r\n"
"[stdout]\n"
"%r"
msgstr "退出 dot，發生錯誤：\n[stderr]\n%r\n[stdout]\n%r"

#: sphinx/ext/graphviz.py:313
#, python-format
msgid ""
"dot did not produce an output file:\n"
"[stderr]\n"
"%r\n"
"[stdout]\n"
"%r"
msgstr "dot 並未製作一個輸出檔案：\n[stderr]\n%r\n[stdout]\n%r"

#: sphinx/ext/graphviz.py:329
#, python-format
msgid "graphviz_output_format must be one of 'png', 'svg', but is %r"
msgstr "graphviz_output_format 必須是 'png' 和 'svg' 之一，但卻是 %r"

#: sphinx/ext/graphviz.py:333 sphinx/ext/graphviz.py:386
#: sphinx/ext/graphviz.py:423
#, python-format
msgid "dot code %r: %s"
msgstr "點碼 %r: %s"

#: sphinx/ext/graphviz.py:436 sphinx/ext/graphviz.py:444
#, python-format
msgid "[graph: %s]"
msgstr "[圖：%s]"

#: sphinx/ext/graphviz.py:438 sphinx/ext/graphviz.py:446
msgid "[graph]"
msgstr "[圖]"

#: sphinx/ext/imgconverter.py:39
#, python-format
msgid ""
"Unable to run the image conversion command %r. 'sphinx.ext.imgconverter' requires ImageMagick by default. Ensure it is installed, or set the 'image_converter' option to a custom conversion command.\n"
"\n"
"Traceback: %s"
msgstr "無法執行影像轉換命令 %r。 'sphinx.ext.imgconverter' 預設為需要 ImageMagick。請確認它已被安裝，或是設定 'image_converter' 選項為一個自訂轉換命令。\n\n回溯: %s"

#: sphinx/ext/imgconverter.py:48 sphinx/ext/imgconverter.py:72
#, python-format
msgid ""
"convert exited with error:\n"
"[stderr]\n"
"%r\n"
"[stdout]\n"
"%r"
msgstr "退出轉換，發生錯誤：\n[stderr]\n%r\n[stdout]\n%r"

#: sphinx/ext/imgconverter.py:67
#, python-format
msgid "convert command %r cannot be run, check the image_converter setting"
msgstr "轉換命令 %r 無法被執行，請檢查 image_converter 設定"

#: sphinx/ext/imgmath.py:158
#, python-format
msgid ""
"LaTeX command %r cannot be run (needed for math display), check the "
"imgmath_latex setting"
msgstr "LaTeX 命令 %r 無法被執行（數學顯示所需要），請檢查 imgmath_latex 設定"

#: sphinx/ext/imgmath.py:173
#, python-format
msgid ""
"%s command %r cannot be run (needed for math display), check the imgmath_%s "
"setting"
msgstr "%s 命令 %r 無法被執行（數學顯示所需要），請檢查 imgmath_%s 設定"

#: sphinx/ext/imgmath.py:327
#, python-format
msgid "display latex %r: %s"
msgstr "顯示 latex %r: %s"

#: sphinx/ext/imgmath.py:361
#, python-format
msgid "inline latex %r: %s"
msgstr "行內 latex %r: %s"

#: sphinx/ext/imgmath.py:368 sphinx/ext/mathjax.py:53
msgid "Link to this equation"
msgstr "連結到這個方程式"

#: sphinx/ext/intersphinx.py:195
#, python-format
msgid "intersphinx inventory has moved: %s -> %s"
msgstr "intersphinx 庫存已移動： %s -> %s"

#: sphinx/ext/intersphinx.py:230
#, python-format
msgid "loading intersphinx inventory from %s..."
msgstr "正在從 %s 載入 intersphinx 庫存... "

#: sphinx/ext/intersphinx.py:244
msgid ""
"encountered some issues with some of the inventories, but they had working "
"alternatives:"
msgstr "從一些庫存中遇到一些問題，但他們已在進行替代方案："

#: sphinx/ext/intersphinx.py:250
msgid "failed to reach any of the inventories with the following issues:"
msgstr "無法到達任何的庫存，遇到以下問題："

#: sphinx/ext/intersphinx.py:303
#, python-format
msgid "(in %s v%s)"
msgstr "(於 %s v%s)"

#: sphinx/ext/intersphinx.py:305
#, python-format
msgid "(in %s)"
msgstr "(於 %s)"

#: sphinx/ext/intersphinx.py:538
#, python-format
msgid "inventory for external cross-reference not found: %r"
msgstr ""

#: sphinx/ext/intersphinx.py:546
#, python-format
msgid "invalid external cross-reference suffix: %r"
msgstr ""

#: sphinx/ext/intersphinx.py:557
#, python-format
msgid "domain for external cross-reference not found: %r"
msgstr ""

#: sphinx/ext/intersphinx.py:750
#, python-format
msgid "external %s:%s reference target not found: %s"
msgstr "未找到外部的 %s:%s 參照目標： %s"

#: sphinx/ext/intersphinx.py:775
#, python-format
msgid "intersphinx identifier %r is not string. Ignored"
msgstr "intersphinx identifier %r 不是字串。已略過"

#: sphinx/ext/intersphinx.py:797
#, python-format
msgid "Failed to read intersphinx_mapping[%s], ignored: %r"
msgstr "無法讀取 intersphinx_mapping[%s], 已略過: %r"

#: sphinx/ext/linkcode.py:69 sphinx/ext/viewcode.py:199
msgid "[source]"
msgstr "[原始碼]"

#: sphinx/ext/todo.py:69
msgid "Todo"
msgstr "Todo"

#: sphinx/ext/todo.py:102
#, python-format
msgid "TODO entry found: %s"
msgstr "找到 TODO 項目： %s"

#: sphinx/ext/todo.py:161
msgid "<<original entry>>"
msgstr "<<original entry>>"

#: sphinx/ext/todo.py:163
#, python-format
msgid "(The <<original entry>> is located in %s, line %d.)"
msgstr "(<<original entry>> 見 %s ，第 %d 行)"

#: sphinx/ext/todo.py:173
msgid "original entry"
msgstr "原始記錄"

#: sphinx/ext/viewcode.py:256
msgid "highlighting module code... "
msgstr "正在 highlight 模組程式碼..."

#: sphinx/ext/viewcode.py:284
msgid "[docs]"
msgstr "[文件]"

#: sphinx/ext/viewcode.py:304
msgid "Module code"
msgstr "模組原始碼"

#: sphinx/ext/viewcode.py:310
#, python-format
msgid "<h1>Source code for %s</h1>"
msgstr "<h1>%s 的原始碼</h1>"

#: sphinx/ext/viewcode.py:336
msgid "Overview: module code"
msgstr "概要：模組原始碼"

#: sphinx/ext/viewcode.py:337
msgid "<h1>All modules for which code is available</h1>"
msgstr "<h1>所有可得程式碼的模組</h1>"

#: sphinx/ext/autodoc/__init__.py:135
#, python-format
msgid "invalid value for member-order option: %s"
msgstr "對於 member-order 選項無效的值： %s"

#: sphinx/ext/autodoc/__init__.py:143
#, python-format
msgid "invalid value for class-doc-from option: %s"
msgstr "對於 class-doc-from 選項無效的值： %s"

#: sphinx/ext/autodoc/__init__.py:399
#, python-format
msgid "invalid signature for auto%s (%r)"
msgstr "無效的簽章給 auto%s (%r)"

#: sphinx/ext/autodoc/__init__.py:515
#, python-format
msgid "error while formatting arguments for %s: %s"
msgstr "在為 %s 格式化引數時有錯誤： %s"

#: sphinx/ext/autodoc/__init__.py:798
#, python-format
msgid ""
"autodoc: failed to determine %s.%s (%r) to be documented, the following exception was raised:\n"
"%s"
msgstr "autodoc: 決定 %s.%s (%r) 被文件化失敗，引發以下的例外：\n%s"

#: sphinx/ext/autodoc/__init__.py:893
#, python-format
msgid ""
"don't know which module to import for autodocumenting %r (try placing a "
"\"module\" or \"currentmodule\" directive in the document, or giving an "
"explicit module name)"
msgstr "不清楚要 import 哪個模組來 autodocument %r （試試看在文件中加入 \"module\" 或 \"currentmodule\" 指令，或是給予一個明確的模組名稱）"

#: sphinx/ext/autodoc/__init__.py:937
#, python-format
msgid "A mocked object is detected: %r"
msgstr "一個 mocked 物件被偵測到： %r"

#: sphinx/ext/autodoc/__init__.py:956
#, python-format
msgid "error while formatting signature for %s: %s"
msgstr "正在為 %s 格式化簽名時發生錯誤： %s"

#: sphinx/ext/autodoc/__init__.py:1019
msgid "\"::\" in automodule name doesn't make sense"
msgstr "\"::\" 在 automodule 的名稱中並不合理"

#: sphinx/ext/autodoc/__init__.py:1026
#, python-format
msgid "signature arguments or return annotation given for automodule %s"
msgstr "簽名引數或回傳註釋給予 automodule %s"

#: sphinx/ext/autodoc/__init__.py:1039
#, python-format
msgid ""
"__all__ should be a list of strings, not %r (in module %s) -- ignoring "
"__all__"
msgstr "__all__  應該是一個字串的列表，不是 %r （在 %s 模組中）-- 正在忽略 __all__"

#: sphinx/ext/autodoc/__init__.py:1105
#, python-format
msgid ""
"missing attribute mentioned in :members: option: module %s, attribute %s"
msgstr "缺少 :members: 選項中所述的屬性：模組 %s ，屬性 %s"

#: sphinx/ext/autodoc/__init__.py:1327 sphinx/ext/autodoc/__init__.py:1404
#: sphinx/ext/autodoc/__init__.py:2824
#, python-format
msgid "Failed to get a function signature for %s: %s"
msgstr "無法取得一個函式簽名給 %s: %s"

#: sphinx/ext/autodoc/__init__.py:1618
#, python-format
msgid "Failed to get a constructor signature for %s: %s"
msgstr "無法取得一個 constructor 簽名給 %s: %s"

#: sphinx/ext/autodoc/__init__.py:1745
#, python-format
msgid "Bases: %s"
msgstr "基礎類別：%s"

#: sphinx/ext/autodoc/__init__.py:1759
#, python-format
msgid "missing attribute %s in object %s"
msgstr "遺漏屬性 %s 在物件 %s"

#: sphinx/ext/autodoc/__init__.py:1858 sphinx/ext/autodoc/__init__.py:1895
#: sphinx/ext/autodoc/__init__.py:1990
#, python-format
msgid "alias of %s"
msgstr "%s 的別名"

#: sphinx/ext/autodoc/__init__.py:1878
#, python-format
msgid "alias of TypeVar(%s)"
msgstr "TypeVar(%s) 的別名"

#: sphinx/ext/autodoc/__init__.py:2216 sphinx/ext/autodoc/__init__.py:2316
#, python-format
msgid "Failed to get a method signature for %s: %s"
msgstr "無法取得一個 method 簽名給 %s: %s"

#: sphinx/ext/autodoc/__init__.py:2447
#, python-format
msgid "Invalid __slots__ found on %s. Ignored."
msgstr "在 %s 找到無效的 __slots__。已略過。"

#: sphinx/ext/autodoc/preserve_defaults.py:190
#, python-format
msgid "Failed to parse a default argument value for %r: %s"
msgstr "無法為 %r 剖析一個預設引數： %s"

#: sphinx/ext/autodoc/type_comment.py:132
#, python-format
msgid "Failed to update signature for %r: parameter not found: %s"
msgstr "無法為 %r 更新簽名：未找到參數： %s"

#: sphinx/ext/autodoc/type_comment.py:135
#, python-format
msgid "Failed to parse type_comment for %r: %s"
msgstr "無法為 %r 剖析 type_comment： %s"

#: sphinx/ext/autosummary/__init__.py:251
#, python-format
msgid "autosummary references excluded document %r. Ignored."
msgstr "autosummary 參照已排除文件 %r 。已略過。"

#: sphinx/ext/autosummary/__init__.py:253
#, python-format
msgid ""
"autosummary: stub file not found %r. Check your autosummary_generate "
"setting."
msgstr "autosummary: 未找到 stub 檔 %r 。請檢查您的 autosummary_generate 設定。"

#: sphinx/ext/autosummary/__init__.py:272
msgid "A captioned autosummary requires :toctree: option. ignored."
msgstr "一個有標題的 autosummary 需要 :toctree: 選項。已略過。 "

#: sphinx/ext/autosummary/__init__.py:325
#, python-format
msgid ""
"autosummary: failed to import %s.\n"
"Possible hints:\n"
"%s"
msgstr "autosummary: import %s 失敗。\n可能的提示：\n%s"

#: sphinx/ext/autosummary/__init__.py:339
#, python-format
msgid "failed to parse name %s"
msgstr "剖析名稱 %s 失敗"

#: sphinx/ext/autosummary/__init__.py:344
#, python-format
msgid "failed to import object %s"
msgstr "import 物件 %s 失敗"

#: sphinx/ext/autosummary/__init__.py:802
#, python-format
msgid "autosummary_generate: file not found: %s"
msgstr "autosummary_generate: 檔案未找到： %s"

#: sphinx/ext/autosummary/__init__.py:810
msgid ""
"autosummary generates .rst files internally. But your source_suffix does not"
" contain .rst. Skipped."
msgstr ""

#: sphinx/ext/autosummary/generate.py:200
#: sphinx/ext/autosummary/generate.py:358
#, python-format
msgid ""
"autosummary: failed to determine %r to be documented, the following exception was raised:\n"
"%s"
msgstr "autosummary: 無法決定 %r 被記錄，以下例外被引發：\n%s"

#: sphinx/ext/autosummary/generate.py:470
#, python-format
msgid "[autosummary] generating autosummary for: %s"
msgstr "[autosummary] 正在產生 autosummary 給： %s"

#: sphinx/ext/autosummary/generate.py:474
#, python-format
msgid "[autosummary] writing to %s"
msgstr "[autosummary] 正在寫入 %s"

#: sphinx/ext/autosummary/generate.py:517
#, python-format
msgid ""
"[autosummary] failed to import %s.\n"
"Possible hints:\n"
"%s"
msgstr "[autosummary] import %s 失敗。\n可能的提示：\n%s"

#: sphinx/ext/autosummary/generate.py:690
msgid ""
"\n"
"Generate ReStructuredText using autosummary directives.\n"
"\n"
"sphinx-autogen is a frontend to sphinx.ext.autosummary.generate. It generates\n"
"the reStructuredText files from the autosummary directives contained in the\n"
"given input files.\n"
"\n"
"The format of the autosummary directive is documented in the\n"
"``sphinx.ext.autosummary`` Python module and can be read using::\n"
"\n"
"  pydoc sphinx.ext.autosummary\n"
msgstr "\n使用 autosummary 指令產生 ReStructuredText。\n\nsphinx-autogen 是 sphinx.ext.autosummary.generate 的一個前端。它會從給定的\n輸入檔案中所包含的 autosummary 指令，產生 reStructuredText 檔案。\n\nautosummary 指令的格式被記錄在 ``sphinx.ext.autosummary`` Python 模組中，\n它可以使用此方法來讀取::\n\npydoc sphinx.ext.autosummary\n"

#: sphinx/ext/autosummary/generate.py:707
msgid "source files to generate rST files for"
msgstr "原始檔案以產生 rST 檔案給"

#: sphinx/ext/autosummary/generate.py:711
msgid "directory to place all output in"
msgstr "資料夾來放置所有輸出在"

#: sphinx/ext/autosummary/generate.py:714
#, python-format
msgid "default suffix for files (default: %(default)s)"
msgstr "檔案的預設後綴（預設： %(default)s ）"

#: sphinx/ext/autosummary/generate.py:718
#, python-format
msgid "custom template directory (default: %(default)s)"
msgstr "自訂模板資料夾（預設： %(default)s ）"

#: sphinx/ext/autosummary/generate.py:722
#, python-format
msgid "document imported members (default: %(default)s)"
msgstr "文件引入成員（預設： %(default)s ）"

#: sphinx/ext/autosummary/generate.py:726
#, python-format
msgid ""
"document exactly the members in module __all__ attribute. (default: "
"%(default)s)"
msgstr "文件確實是在模組 __all__ 屬性中的成員。（預設值： %(default)s）"

#: sphinx/ext/napoleon/__init__.py:341 sphinx/ext/napoleon/docstring.py:728
msgid "Keyword Arguments"
msgstr "關鍵字引數"

#: sphinx/ext/napoleon/docstring.py:682
msgid "Example"
msgstr "範例"

#: sphinx/ext/napoleon/docstring.py:683
msgid "Examples"
msgstr "範例"

#: sphinx/ext/napoleon/docstring.py:744
msgid "Notes"
msgstr "備註"

#: sphinx/ext/napoleon/docstring.py:753
msgid "Other Parameters"
msgstr "其他參數"

#: sphinx/ext/napoleon/docstring.py:789
msgid "Receives"
msgstr "接收"

#: sphinx/ext/napoleon/docstring.py:793
msgid "References"
msgstr "參照"

#: sphinx/ext/napoleon/docstring.py:825
msgid "Warns"
msgstr "警告"

#: sphinx/ext/napoleon/docstring.py:829
msgid "Yields"
msgstr "產出"

#: sphinx/ext/napoleon/docstring.py:987
#, python-format
msgid "invalid value set (missing closing brace): %s"
msgstr "無效的值集合（缺少右括號）： %s"

#: sphinx/ext/napoleon/docstring.py:994
#, python-format
msgid "invalid value set (missing opening brace): %s"
msgstr "無效的值集合（缺少左括號）： %s"

#: sphinx/ext/napoleon/docstring.py:1001
#, python-format
msgid "malformed string literal (missing closing quote): %s"
msgstr "異常的字串文本（缺少右括號）： %s"

#: sphinx/ext/napoleon/docstring.py:1008
#, python-format
msgid "malformed string literal (missing opening quote): %s"
msgstr "異常的字串文本（缺少左括號）： %s"

#: sphinx/locale/__init__.py:228
msgid "Attention"
msgstr "注意"

#: sphinx/locale/__init__.py:229
msgid "Caution"
msgstr "警示"

#: sphinx/locale/__init__.py:230
msgid "Danger"
msgstr "危險"

#: sphinx/locale/__init__.py:231
msgid "Error"
msgstr "錯誤"

#: sphinx/locale/__init__.py:232
msgid "Hint"
msgstr "提示"

#: sphinx/locale/__init__.py:233
msgid "Important"
msgstr "重要"

#: sphinx/locale/__init__.py:234
msgid "Note"
msgstr "備註"

#: sphinx/locale/__init__.py:235
msgid "See also"
msgstr "也參考"

#: sphinx/locale/__init__.py:236
msgid "Tip"
msgstr "小訣竅"

#: sphinx/locale/__init__.py:237
msgid "Warning"
msgstr "警告"

#: sphinx/templates/latex/longtable.tex_t:52
#: sphinx/templates/latex/sphinxmessages.sty_t:8
msgid "continued from previous page"
msgstr "繼續上一頁"

#: sphinx/templates/latex/longtable.tex_t:63
#: sphinx/templates/latex/sphinxmessages.sty_t:9
msgid "continues on next page"
msgstr "繼續下一頁"

#: sphinx/templates/latex/sphinxmessages.sty_t:10
msgid "Non-alphabetical"
msgstr "非依字母順序"

#: sphinx/templates/latex/sphinxmessages.sty_t:12
msgid "Numbers"
msgstr "數字"

#: sphinx/templates/latex/sphinxmessages.sty_t:13
msgid "page"
msgstr "頁"

#: sphinx/themes/agogo/layout.html:38 sphinx/themes/basic/globaltoc.html:10
#: sphinx/themes/basic/localtoc.html:12 sphinx/themes/scrolls/layout.html:41
msgid "Table of Contents"
msgstr "目錄"

#: sphinx/themes/agogo/layout.html:43 sphinx/themes/basic/layout.html:141
#: sphinx/themes/basic/search.html:11 sphinx/themes/basic/search.html:23
msgid "Search"
msgstr "搜尋"

#: sphinx/themes/agogo/layout.html:46 sphinx/themes/basic/searchbox.html:16
#: sphinx/themes/basic/searchfield.html:18
msgid "Go"
msgstr "前往"

#: sphinx/themes/agogo/layout.html:90 sphinx/themes/basic/sourcelink.html:15
msgid "Show Source"
msgstr "顯示原始碼"

#: sphinx/themes/basic/defindex.html:11
msgid "Overview"
msgstr "概要"

#: sphinx/themes/basic/defindex.html:15
msgid "Welcome! This is"
msgstr "歡迎！本"

#: sphinx/themes/basic/defindex.html:16
msgid "the documentation for"
msgstr "說明文件介紹"

#: sphinx/themes/basic/defindex.html:17
msgid "last updated"
msgstr "最後更新於"

#: sphinx/themes/basic/defindex.html:20
msgid "Indices and tables:"
msgstr "索引與表格："

#: sphinx/themes/basic/defindex.html:23
msgid "Complete Table of Contents"
msgstr "完整目錄"

#: sphinx/themes/basic/defindex.html:24
msgid "lists all sections and subsections"
msgstr "列出所有章節與小節"

#: sphinx/themes/basic/defindex.html:26
msgid "search this documentation"
msgstr "搜尋這份說明文件"

#: sphinx/themes/basic/defindex.html:28
msgid "Global Module Index"
msgstr "全域模組索引"

#: sphinx/themes/basic/defindex.html:29
msgid "quick access to all modules"
msgstr "迅速找到所有模組"

#: sphinx/themes/basic/defindex.html:31
msgid "all functions, classes, terms"
msgstr "所有函式、類別、術語"

#: sphinx/themes/basic/genindex-single.html:33
#, python-format
msgid "Index &ndash; %(key)s"
msgstr "索引 &ndash; %(key)s"

#: sphinx/themes/basic/genindex-single.html:61
#: sphinx/themes/basic/genindex-split.html:24
#: sphinx/themes/basic/genindex-split.html:38
#: sphinx/themes/basic/genindex.html:73
msgid "Full index on one page"
msgstr "單頁完整索引"

#: sphinx/themes/basic/genindex-split.html:16
msgid "Index pages by letter"
msgstr "按字母索引頁面"

#: sphinx/themes/basic/genindex-split.html:25
msgid "can be huge"
msgstr "可能會很大"

#: sphinx/themes/basic/layout.html:26
msgid "Navigation"
msgstr "瀏覽"

#: sphinx/themes/basic/layout.html:126
#, python-format
msgid "Search within %(docstitle)s"
msgstr "在 %(docstitle)s 中搜尋"

#: sphinx/themes/basic/layout.html:135
msgid "About these documents"
msgstr "關於這些文件"

#: sphinx/themes/basic/layout.html:144 sphinx/themes/basic/layout.html:188
#: sphinx/themes/basic/layout.html:190
msgid "Copyright"
msgstr "版權所有"

#: sphinx/themes/basic/layout.html:194 sphinx/themes/basic/layout.html:200
#, python-format
msgid "&#169; %(copyright_prefix)s %(copyright)s."
msgstr "&#169; %(copyright_prefix)s %(copyright)s."

#: sphinx/themes/basic/layout.html:212
#, python-format
msgid "Last updated on %(last_updated)s."
msgstr "最後更新於 %(last_updated)s。"

#: sphinx/themes/basic/layout.html:215
#, python-format
msgid ""
"Created using <a href=\"https://www.sphinx-doc.org/\">Sphinx</a> "
"%(sphinx_version)s."
msgstr "使用 <a href=\"https://www.sphinx-doc.org/\">Sphinx</a> %(sphinx_version)s 建立。"

#: sphinx/themes/basic/opensearch.xml:4
#, python-format
msgid "Search %(docstitle)s"
msgstr "搜尋 %(docstitle)s"

#: sphinx/themes/basic/relations.html:12
msgid "Previous topic"
msgstr "上個主題"

#: sphinx/themes/basic/relations.html:14
msgid "previous chapter"
msgstr "上一章"

#: sphinx/themes/basic/relations.html:19
msgid "Next topic"
msgstr "下個主題"

#: sphinx/themes/basic/relations.html:21
msgid "next chapter"
msgstr "下一章"

#: sphinx/themes/basic/search.html:28
msgid ""
"Please activate JavaScript to enable the search\n"
"    functionality."
msgstr "請啟用 Javascript 以開啟搜尋功能。"

#: sphinx/themes/basic/search.html:36
msgid ""
"Searching for multiple words only shows matches that contain\n"
"    all words."
msgstr "搜尋多個關鍵字時，只會顯示包含所有關鍵字的結果。"

#: sphinx/themes/basic/search.html:43
msgid "search"
msgstr "搜尋"

#: sphinx/themes/basic/searchbox.html:12
msgid "Quick search"
msgstr "快速搜尋"

#: sphinx/themes/basic/sourcelink.html:12
msgid "This Page"
msgstr "本頁"

#: sphinx/themes/basic/changes/frameset.html:5
#: sphinx/themes/basic/changes/versionchanges.html:12
#, python-format
msgid "Changes in Version %(version)s &#8212; %(docstitle)s"
msgstr "於 %(version)s 版中的所有變更 &#8212; %(docstitle)s"

#: sphinx/themes/basic/changes/rstsource.html:5
#, python-format
msgid "%(filename)s &#8212; %(docstitle)s"
msgstr "%(filename)s &#8212; %(docstitle)s"

#: sphinx/themes/basic/changes/versionchanges.html:17
#, python-format
msgid "Automatically generated list of changes in version %(version)s"
msgstr "自動產生的 %(version)s 版變更列表"

#: sphinx/themes/basic/changes/versionchanges.html:18
msgid "Library changes"
msgstr "程式庫的變更"

#: sphinx/themes/basic/changes/versionchanges.html:23
msgid "C API changes"
msgstr "C API 的變更"

#: sphinx/themes/basic/changes/versionchanges.html:25
msgid "Other changes"
msgstr "其他變更"

#: sphinx/themes/basic/static/searchtools.js:112
msgid "Search Results"
msgstr "搜尋結果"

#: sphinx/themes/basic/static/searchtools.js:114
msgid ""
"Your search did not match any documents. Please make sure that all words are"
" spelled correctly and that you've selected enough categories."
msgstr "您的搜尋找不到任何滿足條件的文件。請確定是否所有的搜尋詞都正確地拼寫且您已選擇足夠的分類。"

#: sphinx/themes/basic/static/searchtools.js:118
msgid ""
"Search finished, found ${resultCount} page(s) matching the search query."
msgstr "搜尋結束，共找到 ${resultCount} 個頁面符合搜尋條件。"

#: sphinx/themes/basic/static/searchtools.js:246
msgid "Searching"
msgstr "搜尋中"

#: sphinx/themes/basic/static/searchtools.js:262
msgid "Preparing search..."
msgstr "準備搜尋中…"

#: sphinx/themes/basic/static/searchtools.js:463
msgid ", in "
msgstr "，於 "

#: sphinx/themes/basic/static/sphinx_highlight.js:112
msgid "Hide Search Matches"
msgstr "隱藏符合搜尋"

#: sphinx/themes/classic/layout.html:20
#: sphinx/themes/classic/static/sidebar.js_t:57
msgid "Collapse sidebar"
msgstr "收合側邊欄"

#: sphinx/themes/classic/static/sidebar.js_t:48
msgid "Expand sidebar"
msgstr "展開側邊欄"

#: sphinx/themes/haiku/layout.html:24
msgid "Contents"
msgstr "內容"

#: sphinx/transforms/__init__.py:128
msgid "could not calculate translation progress!"
msgstr "無法計算翻譯進度！"

#: sphinx/transforms/__init__.py:133
msgid "no translated elements!"
msgstr "沒有已翻譯的元素！"

#: sphinx/transforms/__init__.py:250
#, python-format
msgid ""
"4 column based index found. It might be a bug of extensions you use: %r"
msgstr "找到基於 4 欄位的索引。它可能是您使用的擴充套件的一個錯誤： %r"

#: sphinx/transforms/__init__.py:291
#, python-format
msgid "Footnote [%s] is not referenced."
msgstr "註腳 [%s] 未被參照。"

#: sphinx/transforms/__init__.py:297
msgid "Footnote [#] is not referenced."
msgstr "註腳 [#] 未被參照。"

#: sphinx/transforms/i18n.py:205 sphinx/transforms/i18n.py:270
msgid ""
"inconsistent footnote references in translated message. original: {0}, "
"translated: {1}"
msgstr "被翻譯訊息中有不一致的註腳參照。原文： {0}，譯文： {1}"

#: sphinx/transforms/i18n.py:245
msgid ""
"inconsistent references in translated message. original: {0}, translated: "
"{1}"
msgstr "被翻譯訊息中有不一致的參照。原文： {0}，譯文： {1}"

#: sphinx/transforms/i18n.py:285
msgid ""
"inconsistent citation references in translated message. original: {0}, "
"translated: {1}"
msgstr "被翻譯訊息中有不一致的引用。原文： {0}，譯文： {1}"

#: sphinx/transforms/i18n.py:302
msgid ""
"inconsistent term references in translated message. original: {0}, "
"translated: {1}"
msgstr "被翻譯訊息中有不一致的術語參照。原文： {0}，譯文： {1}"

#: sphinx/transforms/post_transforms/__init__.py:116
msgid ""
"Could not determine the fallback text for the cross-reference. Might be a "
"bug."
msgstr "無法為交互參照決定備用文字。可能是個錯誤。"

#: sphinx/transforms/post_transforms/__init__.py:158
#, python-format
msgid "more than one target found for 'any' cross-reference %r: could be %s"
msgstr "為「任一個」交互參照 %r 找到多於一個目標：可能是 %s"

#: sphinx/transforms/post_transforms/__init__.py:209
#, python-format
msgid "%s:%s reference target not found: %s"
msgstr "%s:%s 參照目標未找到： %s"

#: sphinx/transforms/post_transforms/__init__.py:212
#, python-format
msgid "%r reference target not found: %s"
msgstr "%r 參照目標未找到： %s"

#: sphinx/transforms/post_transforms/images.py:89
#, python-format
msgid "Could not fetch remote image: %s [%d]"
msgstr "無法提取遠端圖片： %s [%d]"

#: sphinx/transforms/post_transforms/images.py:117
#, python-format
msgid "Could not fetch remote image: %s [%s]"
msgstr "無法提取遠端圖片： %s [%s]"

#: sphinx/transforms/post_transforms/images.py:135
#, python-format
msgid "Unknown image format: %s..."
msgstr "未知的圖片格式： %s..."

#: sphinx/util/__init__.py:168
#, python-format
msgid "undecodable source characters, replacing with \"?\": %r"
msgstr "無法解碼的原始字元，以 \"?\" 取代： %r"

#: sphinx/util/display.py:78
msgid "skipped"
msgstr "已省略"

#: sphinx/util/display.py:83
msgid "failed"
msgstr "失敗"

#: sphinx/util/docfields.py:87
#, python-format
msgid ""
"Problem in %s domain: field is supposed to use role '%s', but that role is "
"not in the domain."
msgstr "在 %s domain 中的問題：欄位應該要用角色 '%s' ，但是那個角色並不在該 domain。"

#: sphinx/util/docutils.py:295
#, python-format
msgid "unknown directive or role name: %s:%s"
msgstr "未知的指令或角色名稱： %s:%s"

#: sphinx/util/docutils.py:591
#, python-format
msgid "unknown node type: %r"
msgstr "未知的節點型別： %r"

#: sphinx/util/i18n.py:94
#, python-format
msgid "reading error: %s, %s"
msgstr "讀取錯誤： %s, %s"

#: sphinx/util/i18n.py:101
#, python-format
msgid "writing error: %s, %s"
msgstr "寫入錯誤： %s, %s"

#: sphinx/util/i18n.py:125
#, python-format
msgid "locale_dir %s does not exist"
msgstr "locale_dir %s 不存在"

#: sphinx/util/i18n.py:215
#, python-format
msgid ""
"Invalid date format. Quote the string by single quote if you want to output "
"it directly: %s"
msgstr "無效的日期格式。如果您要直接將它輸出，則以單引號引用該字串： %s"

#: sphinx/util/nodes.py:386
#, python-format
msgid ""
"%r is deprecated for index entries (from entry %r). Use 'pair: %s' instead."
msgstr "%r 已棄用於索引項目（從項目 %r）。請改用 'pair: %s'。"

#: sphinx/util/nodes.py:439
#, python-format
msgid "toctree contains ref to nonexisting file %r"
msgstr "toctree 包含了不存在的檔案 %r 的參照 "

#: sphinx/util/nodes.py:637
#, python-format
msgid "exception while evaluating only directive expression: %s"
msgstr "在評估只有指令的運算式時發生例外： %s"

#: sphinx/util/rst.py:71
#, python-format
msgid "default role %s not found"
msgstr "預設角色 %s 未找到"

#: sphinx/writers/html5.py:100 sphinx/writers/html5.py:109
msgid "Link to this definition"
msgstr "連結到這個定義"

#: sphinx/writers/html5.py:397
#, python-format
msgid "numfig_format is not defined for %s"
msgstr "numfig_format 未被定義給 %s"

#: sphinx/writers/html5.py:407
#, python-format
msgid "Any IDs not assigned for %s node"
msgstr "任一個 ID 未被指定給 %s 節點"

#: sphinx/writers/html5.py:462
msgid "Link to this term"
msgstr "連結到這個項目"

#: sphinx/writers/html5.py:496 sphinx/writers/html5.py:501
msgid "Link to this heading"
msgstr "連結到這個標頭"

#: sphinx/writers/html5.py:505
msgid "Link to this table"
msgstr "連結到這個表格"

#: sphinx/writers/html5.py:548
msgid "Link to this code"
msgstr "連結到這個程式碼"

#: sphinx/writers/html5.py:550
msgid "Link to this image"
msgstr "連結到這個圖片"

#: sphinx/writers/html5.py:552
msgid "Link to this toctree"
msgstr "連結到這個 toctree"

#: sphinx/writers/html5.py:688
msgid "Could not obtain image size. :scale: option is ignored."
msgstr "無法取得圖片大小。 :scale: 選項已略過。"

#: sphinx/writers/latex.py:335
#, python-format
msgid "unknown %r toplevel_sectioning for class %r"
msgstr "未知的 %r toplevel_sectioning 對於 class %r"

#: sphinx/writers/latex.py:386
msgid "too large :maxdepth:, ignored."
msgstr ":maxdepth: 太大，已略過。"

#: sphinx/writers/latex.py:625
msgid "document title is not a single Text node"
msgstr "文件標題不是單一的 Text 節點"

#: sphinx/writers/latex.py:656 sphinx/writers/texinfo.py:626
msgid ""
"encountered title node not in section, topic, table, admonition or sidebar"
msgstr "遇到的標題節點不是在段落、主題、表格、警告或側邊欄"

#: sphinx/writers/latex.py:959 sphinx/writers/manpage.py:259
#: sphinx/writers/texinfo.py:641
msgid "Footnotes"
msgstr "註腳"

#: sphinx/writers/latex.py:1028
msgid ""
"both tabularcolumns and :widths: option are given. :widths: is ignored."
msgstr "同時被給予 tabularcolumns 及 :widths: 選項。 :widths: 已略過。"

#: sphinx/writers/latex.py:1388
#, python-format
msgid "dimension unit %s is invalid. Ignored."
msgstr "維度單位 %s 是無效的。已略過。"

#: sphinx/writers/latex.py:1722
#, python-format
msgid "unknown index entry type %s found"
msgstr "找到了未知的索引條目型別 %s"

#: sphinx/writers/manpage.py:305 sphinx/writers/text.py:907
#, python-format
msgid "[image: %s]"
msgstr "[圖片：%s]"

#: sphinx/writers/manpage.py:306 sphinx/writers/text.py:908
msgid "[image]"
msgstr "[圖片]"

#: sphinx/writers/texinfo.py:1197
msgid "caption not inside a figure."
msgstr "標題不在圖之內。"

#: sphinx/writers/texinfo.py:1284
#, python-format
msgid "unimplemented node type: %r"
msgstr "未實作的節點型別： %r"
