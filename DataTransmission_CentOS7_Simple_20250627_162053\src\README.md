# 数据传输客户端

基于Python 3.10开发的数据传输客户端，支持HTTP接口数据接收、二维码生成显示和摄像头二维码识别功能。

## 功能特性

### 主要功能一：HTTP数据接收接口
- 提供 `receiveData` HTTP接口接收传输数据
- 接口格式：`POST /receiveData`
- 参数：`{"id": "时间戳", "type": 数据类型, "data": "数据内容"}`
- 自动去重：根据id、type和status判断数据是否已存在

### 主要功能二：二维码生成和显示
- 定时任务从数据库获取待传输数据（status=0）
- 将数据转换为JSON格式生成二维码
- 二维码在屏幕中央显示，尺寸为900x900像素
- 二维码显示2秒后自动关闭
- 处理完成后更新数据状态为已处理（status=1）

### 主要功能三：摄像头监控和二维码识别
- 实时监控摄像头，每秒截取图像
- 自动识别图像中的二维码数据
- 解析二维码JSON数据并存储到数据库
- 自动去重处理
- **新增**: 左上角实时预览窗口（320x240像素）
- **新增**: 预览窗口显示二维码检测框和状态信息
- **新增**: 可手动关闭预览窗口（关闭后需重启服务才能重新开启）

## 系统要求

- 操作系统：Ubuntu 18.04+
- Python版本：3.10
- 数据库：MySQL 5.7+
- 摄像头：支持USB摄像头或内置摄像头

## 安装步骤

### 1. 快速安装
```bash
chmod +x install.sh
./install.sh
```

### 2. 手动安装

#### 安装系统依赖
```bash
sudo apt update
sudo apt install -y python3.10 python3.10-pip python3.10-venv
sudo apt install -y python3.10-dev libzbar0 libzbar-dev
sudo apt install -y libgl1-mesa-glx libglib2.0-0
sudo apt install -y mysql-server mysql-client
```

#### 创建Python虚拟环境
```bash
python3.10 -m venv venv
source venv/bin/activate
pip install --upgrade pip
pip install -r requirements.txt
```

#### 配置MySQL数据库
```sql
mysql -u root -p
CREATE DATABASE DataTransmission CHARACTER SET utf8 COLLATE utf8_general_ci;
CREATE USER 'datatrans'@'localhost' IDENTIFIED BY 'your_password';
GRANT ALL PRIVILEGES ON DataTransmission.* TO 'datatrans'@'localhost';
FLUSH PRIVILEGES;
EXIT;
```

## 配置说明

编辑 `config.py` 文件，修改数据库连接信息：

```python
DATABASE_CONFIG = {
    'host': 'localhost',
    'port': 3306,
    'database': 'DataTransmission',
    'user': 'datatrans',        # 修改为你的用户名
    'password': 'your_password', # 修改为你的密码
    'charset': 'utf8'
}

# 二维码显示配置
QR_DISPLAY_TIME = 2    # 二维码显示时间（秒）
QR_DISPLAY_SIZE = 900  # 二维码显示尺寸（像素）

# 摄像头预览配置
CAMERA_PREVIEW_ENABLED = True        # 是否启用摄像头预览窗口
CAMERA_PREVIEW_SIZE = (320, 240)     # 预览窗口尺寸 (宽, 高)
CAMERA_PREVIEW_POSITION = (10, 10)   # 预览窗口位置 (x, y) - 左上角
```

## 使用方法

### 启动服务
```bash
./start.sh
# 或者
source venv/bin/activate
python main.py
```

### 停止服务
```bash
./stop.sh
# 或者按 Ctrl+C
```

### 测试API接口
```bash
python test_client.py
```

### 测试二维码显示功能
```bash
# 基本二维码显示测试
python test_qr_display.py

# 修复后的二维码显示测试
python test_qr_display_fix.py

# 二维码显示调试工具
python debug_qr_display.py
```

### 测试摄像头预览功能
```bash
python test_camera_preview.py
```

## API接口文档

### 1. 数据接收接口
- **URL**: `POST /receiveData`
- **Content-Type**: `application/json`
- **参数**:
  ```json
  {
    "id": "时间戳字符串",
    "type": 数据类型(整数),
    "data": "数据内容字符串"
  }
  ```
- **响应**:
  ```json
  {
    "success": true/false,
    "message": "响应消息"
  }
  ```

### 2. 健康检查接口
- **URL**: `GET /health`
- **响应**:
  ```json
  {
    "status": "healthy",
    "timestamp": 时间戳
  }
  ```

### 3. 服务信息接口
- **URL**: `GET /`
- **响应**: 服务基本信息和可用接口列表

## 数据库结构

### receive_data表（接收数据）
| 字段 | 类型 | 说明 |
|------|------|------|
| id | VARCHAR(255) | 主键，时间戳 |
| type | INT | 数据类型 |
| data | VARCHAR(1000) | 数据内容 |

### transmission_data表（传输数据）
| 字段 | 类型 | 说明 |
|------|------|------|
| id | VARCHAR(255) | 时间戳 |
| type | INT | 数据类型 |
| data | VARCHAR(1000) | 数据内容 |
| status | INT | 状态(0:待处理, 1:已处理) |

## 日志文件

程序运行日志保存在 `data_transmission.log` 文件中，包含：
- 服务启动/停止信息
- API请求处理记录
- 二维码生成和识别记录
- 数据库操作记录
- 错误和异常信息

## 故障排除

### 1. 摄像头无法打开
- 检查摄像头是否被其他程序占用
- 修改 `config.py` 中的 `CAMERA_INDEX` 值
- 确认摄像头驱动正常安装

### 2. 数据库连接失败
- 检查MySQL服务是否启动：`sudo systemctl status mysql`
- 验证数据库连接信息是否正确
- 确认数据库用户权限设置

### 3. 二维码识别失败
- 确保二维码清晰可见
- 检查摄像头焦距和光线条件
- 验证二维码内容为有效JSON格式

### 4. HTTP接口无法访问
- 检查防火墙设置：`sudo ufw status`
- 确认端口5000未被占用：`netstat -tlnp | grep 5000`
- 查看程序日志获取详细错误信息

## 开发和扩展

项目采用模块化设计，主要模块：
- `database.py`: 数据库操作
- `web_server.py`: HTTP服务器
- `qr_generator.py`: 二维码生成
- `camera_monitor.py`: 摄像头监控
- `main.py`: 主程序入口

可根据需要扩展功能或修改配置。
