import cv2
import json
import logging
import time
import threading
import numpy as np
from pyzbar import pyzbar
from database import DatabaseManager
from config import CAMERA_INDEX, CAPTURE_INTERVAL

class CameraMonitor:
    def __init__(self, db_manager):
        self.db_manager = db_manager
        self.camera = None
        self.is_running = False
        self.monitor_thread = None
        self.capture_lock = threading.Lock()
    
    def initialize_camera(self):
        """初始化摄像头"""
        try:
            self.camera = cv2.VideoCapture(CAMERA_INDEX)
            if not self.camera.isOpened():
                raise Exception(f"无法打开摄像头 {CAMERA_INDEX}")
            
            # 设置摄像头参数
            self.camera.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
            self.camera.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)
            self.camera.set(cv2.CAP_PROP_FPS, 30)
            
            logging.info(f"摄像头 {CAMERA_INDEX} 初始化成功")
            return True
            
        except Exception as e:
            logging.error(f"初始化摄像头时出错: {e}")
            return False
    
    def capture_frame(self):
        """捕获一帧图像"""
        if self.camera is None or not self.camera.isOpened():
            return None
        
        with self.capture_lock:
            ret, frame = self.camera.read()
            if ret:
                return frame
            else:
                logging.warning("无法从摄像头读取帧")
                return None
    
    def decode_qr_codes(self, frame):
        """解码图像中的二维码"""
        try:
            # 转换为灰度图像以提高识别效果
            gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
            
            # 使用pyzbar解码二维码
            qr_codes = pyzbar.decode(gray)
            
            decoded_data = []
            for qr_code in qr_codes:
                # 获取二维码数据
                qr_data = qr_code.data.decode('utf-8')
                qr_type = qr_code.type
                
                # 获取二维码位置信息
                points = qr_code.polygon
                if len(points) == 4:
                    # 在原图上绘制二维码边框（可选，用于调试）
                    pts = [(point.x, point.y) for point in points]
                    cv2.polylines(frame, [np.array(pts, dtype=np.int32)], True, (0, 255, 0), 2)
                
                decoded_data.append({
                    'data': qr_data,
                    'type': qr_type,
                    'points': points
                })
                
                logging.debug(f"检测到二维码: {qr_data}")
            
            return decoded_data
            
        except Exception as e:
            logging.error(f"解码二维码时出错: {e}")
            return []
    
    def process_qr_data(self, qr_data_string):
        """处理二维码数据"""
        try:
            # 尝试解析JSON数据
            data_dict = json.loads(qr_data_string)
            
            # 验证必需字段
            required_fields = ['id', 'type', 'data']
            for field in required_fields:
                if field not in data_dict:
                    logging.warning(f"二维码数据缺少必需字段: {field}")
                    return False
            
            id_val = str(data_dict['id'])
            type_val = int(data_dict['type'])
            data_val = str(data_dict['data'])
            
            # 存储到receive_data表
            success = self.db_manager.insert_receive_data(id_val, type_val, data_val)
            
            if success:
                logging.info(f"二维码数据处理成功: id={id_val}, type={type_val}")
            else:
                logging.info(f"二维码数据已存在，丢弃: id={id_val}, type={type_val}")
            
            return success
            
        except json.JSONDecodeError:
            logging.warning(f"二维码数据不是有效的JSON格式: {qr_data_string}")
            return False
        except ValueError as e:
            logging.warning(f"二维码数据格式错误: {e}")
            return False
        except Exception as e:
            logging.error(f"处理二维码数据时出错: {e}")
            return False
    
    def monitor_loop(self):
        """摄像头监控主循环"""
        logging.info("开始摄像头监控")
        
        while self.is_running:
            try:
                # 捕获帧
                frame = self.capture_frame()
                if frame is None:
                    time.sleep(0.1)
                    continue
                
                # 解码二维码
                qr_codes = self.decode_qr_codes(frame)
                
                # 处理检测到的二维码
                for qr_info in qr_codes:
                    self.process_qr_data(qr_info['data'])
                
                # 等待指定间隔
                time.sleep(CAPTURE_INTERVAL)
                
            except Exception as e:
                logging.error(f"监控循环中出错: {e}")
                time.sleep(1)
        
        logging.info("摄像头监控已停止")
    
    def start_monitoring(self):
        """启动摄像头监控"""
        if self.is_running:
            logging.warning("摄像头监控已在运行")
            return False
        
        if not self.initialize_camera():
            return False
        
        self.is_running = True
        self.monitor_thread = threading.Thread(target=self.monitor_loop, daemon=True)
        self.monitor_thread.start()
        
        logging.info("摄像头监控已启动")
        return True
    
    def stop_monitoring(self):
        """停止摄像头监控"""
        if not self.is_running:
            return
        
        self.is_running = False
        
        if self.monitor_thread and self.monitor_thread.is_alive():
            self.monitor_thread.join(timeout=5)
        
        if self.camera:
            self.camera.release()
            self.camera = None
        
        cv2.destroyAllWindows()
        logging.info("摄像头监控已停止")
    
    def capture_single_frame(self):
        """捕获单帧用于测试"""
        if not self.camera or not self.camera.isOpened():
            if not self.initialize_camera():
                return None
        
        frame = self.capture_frame()
        return frame
