# MySQL优化配置文件
# 专门针对DataTransmission应用优化，减少死锁

[mysqld]
# 基本设置
port = 3306
bind-address = 0.0.0.0
default-storage-engine = InnoDB
character-set-server = utf8mb4
collation-server = utf8mb4_unicode_ci

# 连接设置
max_connections = 200
max_connect_errors = 1000
connect_timeout = 10
wait_timeout = 600
interactive_timeout = 600

# InnoDB设置 - 减少死锁的关键配置
innodb_buffer_pool_size = 256M
innodb_log_file_size = 64M
innodb_log_buffer_size = 16M
innodb_flush_log_at_trx_commit = 2
innodb_file_per_table = 1

# 死锁检测和处理
innodb_deadlock_detect = ON
innodb_lock_wait_timeout = 10
innodb_rollback_on_timeout = ON

# 事务隔离级别 - 使用READ-COMMITTED减少锁冲突
transaction-isolation = READ-COMMITTED

# 查询缓存
query_cache_type = 1
query_cache_size = 32M
query_cache_limit = 2M

# 临时表设置
tmp_table_size = 64M
max_heap_table_size = 64M

# 排序和分组
sort_buffer_size = 2M
read_buffer_size = 1M
read_rnd_buffer_size = 2M

# 日志设置
log-error = /var/log/mysql/error.log
slow_query_log = 1
slow_query_log_file = /var/log/mysql/slow.log
long_query_time = 2

# 二进制日志
log-bin = mysql-bin
binlog_format = ROW
expire_logs_days = 7

[mysql]
default-character-set = utf8mb4

[client]
default-character-set = utf8mb4
