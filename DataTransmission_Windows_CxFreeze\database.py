#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
优化的数据库管理器
解决死锁和并发问题
"""

import mysql.connector
from mysql.connector import Error
import logging
import threading
import time
import contextlib
from config import DATABASE_CONFIG

class OptimizedDatabaseManager:
    def __init__(self):
        self.connection_pool = None
        self.lock = threading.RLock()  # 使用可重入锁
        self.max_retries = 3
        self.retry_delay = 0.1
        self.connection_timeout = 10
        self.init_connection_pool()
        self.create_tables()
    
    def init_connection_pool(self):
        """初始化连接池"""
        try:
            # 优化的数据库配置
            pool_config = DATABASE_CONFIG.copy()
            pool_config.update({
                'pool_name': 'datatransmission_pool',
                'pool_size': 5,  # 连接池大小
                'pool_reset_session': True,
                'autocommit': False,  # 显式控制事务
                'connection_timeout': self.connection_timeout,
                'sql_mode': 'TRADITIONAL',
                'isolation_level': 'READ COMMITTED',  # 降低锁级别
                'charset': 'utf8mb4',
                'collation': 'utf8mb4_unicode_ci'
            })
            
            self.connection_pool = mysql.connector.pooling.MySQLConnectionPool(**pool_config)
            logging.info("数据库连接池初始化成功")
            
        except Error as e:
            logging.error(f"初始化连接池时出错: {e}")
            raise
    
    @contextlib.contextmanager
    def get_connection(self):
        """获取数据库连接的上下文管理器"""
        connection = None
        try:
            connection = self.connection_pool.get_connection()
            yield connection
        except Error as e:
            if connection:
                connection.rollback()
            logging.error(f"数据库连接错误: {e}")
            raise
        finally:
            if connection and connection.is_connected():
                connection.close()
    
    @contextlib.contextmanager
    def get_cursor(self, connection, dictionary=False):
        """获取游标的上下文管理器"""
        cursor = None
        try:
            cursor = connection.cursor(dictionary=dictionary)
            yield cursor
        finally:
            if cursor:
                cursor.close()
    
    def execute_with_retry(self, operation, *args, **kwargs):
        """带重试机制的数据库操作"""
        for attempt in range(self.max_retries):
            try:
                return operation(*args, **kwargs)
            except Error as e:
                if e.errno in (1205, 1213):  # 死锁错误码
                    if attempt < self.max_retries - 1:
                        logging.warning(f"检测到死锁，第{attempt + 1}次重试...")
                        time.sleep(self.retry_delay * (2 ** attempt))  # 指数退避
                        continue
                    else:
                        logging.error(f"重试{self.max_retries}次后仍然死锁: {e}")
                        raise
                else:
                    logging.error(f"数据库操作错误: {e}")
                    raise
    
    def create_tables(self):
        """创建数据库表"""
        with self.lock:
            with self.get_connection() as connection:
                with self.get_cursor(connection) as cursor:
                    
                    # 优化的receive_data表结构
                    create_receive_data_table = """
                    CREATE TABLE IF NOT EXISTS receive_data (
                        id VARCHAR(255) NOT NULL,
                        type INT NOT NULL,
                        data TEXT NOT NULL,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        PRIMARY KEY (id, type),
                        INDEX idx_created_at (created_at)
                    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
                    """
                    
                    # 优化的transmission_data表结构
                    create_transmission_data_table = """
                    CREATE TABLE IF NOT EXISTS transmission_data (
                        id VARCHAR(255) NOT NULL,
                        type INT NOT NULL,
                        data TEXT NOT NULL,
                        status TINYINT DEFAULT 0,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                        PRIMARY KEY (id, type),
                        INDEX idx_status_created (status, created_at),
                        INDEX idx_updated_at (updated_at)
                    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
                    """
                    
                    try:
                        cursor.execute(create_receive_data_table)
                        cursor.execute(create_transmission_data_table)
                        connection.commit()
                        logging.info("数据库表创建/更新成功")
                    except Error as e:
                        connection.rollback()
                        logging.error(f"创建表时出错: {e}")
                        raise
    
    def insert_transmission_data(self, id_val, type_val, data_val):
        """插入数据到transmission_data表（优化版）"""
        def _insert_operation():
            with self.get_connection() as connection:
                with self.get_cursor(connection) as cursor:
                    
                    # 使用INSERT ... ON DUPLICATE KEY UPDATE避免死锁
                    insert_query = """
                    INSERT INTO transmission_data (id, type, data, status) 
                    VALUES (%s, %s, %s, 0)
                    ON DUPLICATE KEY UPDATE 
                        data = VALUES(data),
                        updated_at = CURRENT_TIMESTAMP
                    """
                    
                    cursor.execute(insert_query, (id_val, type_val, data_val))
                    affected_rows = cursor.rowcount
                    connection.commit()
                    
                    if affected_rows == 1:
                        logging.info(f"数据插入成功: id={id_val}, type={type_val}")
                        return True
                    elif affected_rows == 2:
                        logging.info(f"数据更新成功: id={id_val}, type={type_val}")
                        return True
                    else:
                        logging.info(f"数据无变化: id={id_val}, type={type_val}")
                        return False
        
        return self.execute_with_retry(_insert_operation)
    
    def get_next_transmission_data(self):
        """获取下一条待传输的数据（优化版）"""
        def _get_operation():
            with self.get_connection() as connection:
                with self.get_cursor(connection, dictionary=True) as cursor:
                    
                    # 使用SELECT ... FOR UPDATE SKIP LOCKED避免死锁
                    query = """
                    SELECT id, type, data 
                    FROM transmission_data 
                    WHERE status = 0 
                    ORDER BY created_at ASC 
                    LIMIT 1 
                    FOR UPDATE SKIP LOCKED
                    """
                    
                    cursor.execute(query)
                    result = cursor.fetchone()
                    connection.commit()  # 释放锁
                    
                    return result
        
        return self.execute_with_retry(_get_operation)
    
    def update_transmission_status(self, id_val, type_val, new_status=1):
        """更新传输数据状态（优化版）"""
        def _update_operation():
            with self.get_connection() as connection:
                with self.get_cursor(connection) as cursor:
                    
                    # 使用乐观锁机制
                    update_query = """
                    UPDATE transmission_data 
                    SET status = %s, updated_at = CURRENT_TIMESTAMP 
                    WHERE id = %s AND type = %s AND status = 0
                    """
                    
                    cursor.execute(update_query, (new_status, id_val, type_val))
                    affected_rows = cursor.rowcount
                    connection.commit()
                    
                    if affected_rows > 0:
                        logging.info(f"状态更新成功: id={id_val}, type={type_val}, status={new_status}")
                        return True
                    else:
                        logging.warning(f"状态更新失败，可能已被其他进程处理: id={id_val}, type={type_val}")
                        return False
        
        return self.execute_with_retry(_update_operation)
    
    def insert_receive_data(self, id_val, type_val, data_val):
        """插入数据到receive_data表（优化版）"""
        def _insert_operation():
            with self.get_connection() as connection:
                with self.get_cursor(connection) as cursor:
                    
                    # 使用INSERT IGNORE避免重复插入错误
                    insert_query = """
                    INSERT IGNORE INTO receive_data (id, type, data) 
                    VALUES (%s, %s, %s)
                    """
                    
                    cursor.execute(insert_query, (id_val, type_val, data_val))
                    affected_rows = cursor.rowcount
                    connection.commit()
                    
                    if affected_rows > 0:
                        logging.info(f"接收数据插入成功: id={id_val}, type={type_val}")
                        return True
                    else:
                        logging.info(f"接收数据已存在，跳过: id={id_val}, type={type_val}")
                        return False
        
        return self.execute_with_retry(_insert_operation)
    
    def batch_update_transmission_status(self, id_type_pairs, new_status=1):
        """批量更新传输状态（减少数据库交互）"""
        if not id_type_pairs:
            return True
        
        def _batch_update_operation():
            with self.get_connection() as connection:
                with self.get_cursor(connection) as cursor:
                    
                    # 构建批量更新查询
                    placeholders = ','.join(['(%s, %s)'] * len(id_type_pairs))
                    update_query = f"""
                    UPDATE transmission_data 
                    SET status = %s, updated_at = CURRENT_TIMESTAMP 
                    WHERE (id, type) IN ({placeholders}) AND status = 0
                    """
                    
                    # 准备参数
                    params = [new_status]
                    for id_val, type_val in id_type_pairs:
                        params.extend([id_val, type_val])
                    
                    cursor.execute(update_query, params)
                    affected_rows = cursor.rowcount
                    connection.commit()
                    
                    logging.info(f"批量状态更新完成: 影响行数={affected_rows}")
                    return affected_rows > 0
        
        return self.execute_with_retry(_batch_update_operation)
    
    def cleanup_old_data(self, days_old=7):
        """清理旧数据（定期维护）"""
        def _cleanup_operation():
            with self.get_connection() as connection:
                with self.get_cursor(connection) as cursor:
                    
                    # 清理已处理的旧传输数据
                    cleanup_transmission_query = """
                    DELETE FROM transmission_data 
                    WHERE status = 1 AND updated_at < DATE_SUB(NOW(), INTERVAL %s DAY)
                    LIMIT 1000
                    """
                    
                    # 清理旧接收数据
                    cleanup_receive_query = """
                    DELETE FROM receive_data 
                    WHERE created_at < DATE_SUB(NOW(), INTERVAL %s DAY)
                    LIMIT 1000
                    """
                    
                    cursor.execute(cleanup_transmission_query, (days_old,))
                    transmission_deleted = cursor.rowcount
                    
                    cursor.execute(cleanup_receive_query, (days_old,))
                    receive_deleted = cursor.rowcount
                    
                    connection.commit()
                    
                    logging.info(f"数据清理完成: 传输数据删除{transmission_deleted}条, 接收数据删除{receive_deleted}条")
                    return transmission_deleted + receive_deleted
        
        return self.execute_with_retry(_cleanup_operation)
    
    def close(self):
        """关闭连接池"""
        with self.lock:
            if self.connection_pool:
                # 连接池会自动关闭所有连接
                logging.info("数据库连接池已关闭")

# 主要的数据库管理器类
DatabaseManager = OptimizedDatabaseManager
