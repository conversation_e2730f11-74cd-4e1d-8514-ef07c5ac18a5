# 数据库配置
DATABASE_CONFIG = {
    'host': 'localhost',
    'port': 3306,
    'database': 'DataTransmission',
    'user': 'root',  # 请根据实际情况修改
    'password': 'JKga#123',  # 请根据实际情况修改
    'charset': 'utf8'
}

# Flask配置
FLASK_CONFIG = {
    'host': '0.0.0.0',
    'port': 5000,
    'debug': False
}

# 二维码显示配置
QR_DISPLAY_TIME = 2  # 二维码显示时间（秒）
QR_DISPLAY_SIZE = 900  # 二维码显示尺寸（像素）

# 摄像头配置
CAMERA_INDEX = 0  # 摄像头索引
CAPTURE_INTERVAL = 1  # 截图间隔（秒）

# 摄像头预览配置
CAMERA_PREVIEW_ENABLED = True  # 是否启用摄像头预览窗口
CAMERA_PREVIEW_SIZE = (320, 240)  # 预览窗口尺寸 (宽, 高)
CAMERA_PREVIEW_POSITION = (10, 10)  # 预览窗口位置 (x, y) - 左上角
