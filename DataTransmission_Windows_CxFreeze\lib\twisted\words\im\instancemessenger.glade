<?xml version="1.0"?>
<GTK-Interface>

<project>
  <name>InstanceMessenger</name>
  <program_name>instancemessenger</program_name>
  <directory></directory>
  <source_directory>src</source_directory>
  <pixmaps_directory>pixmaps</pixmaps_directory>
  <language>C</language>
  <gnome_support>True</gnome_support>
  <gettext_support>True</gettext_support>
  <use_widget_names>True</use_widget_names>
</project>

<widget>
  <class>GtkWindow</class>
  <name>UnseenConversationWindow</name>
  <visible>False</visible>
  <title>Unseen Conversation Window</title>
  <type>GTK_WINDOW_TOPLEVEL</type>
  <position>GTK_WIN_POS_NONE</position>
  <modal>False</modal>
  <allow_shrink>False</allow_shrink>
  <allow_grow>True</allow_grow>
  <auto_shrink>False</auto_shrink>

  <widget>
    <class>GtkVBox</class>
    <name>ConversationWidget</name>
    <homogeneous>False</homogeneous>
    <spacing>0</spacing>

    <widget>
      <class>GtkVPaned</class>
      <name>vpaned1</name>
      <handle_size>10</handle_size>
      <gutter_size>6</gutter_size>
      <position>0</position>
      <child>
	<padding>0</padding>
	<expand>True</expand>
	<fill>True</fill>
      </child>

      <widget>
	<class>GtkScrolledWindow</class>
	<name>scrolledwindow10</name>
	<hscrollbar_policy>GTK_POLICY_NEVER</hscrollbar_policy>
	<vscrollbar_policy>GTK_POLICY_ALWAYS</vscrollbar_policy>
	<hupdate_policy>GTK_UPDATE_CONTINUOUS</hupdate_policy>
	<vupdate_policy>GTK_UPDATE_CONTINUOUS</vupdate_policy>
	<child>
	  <shrink>False</shrink>
	  <resize>True</resize>
	</child>

	<widget>
	  <class>GtkText</class>
	  <name>ConversationOutput</name>
	  <editable>False</editable>
	  <text></text>
	</widget>
      </widget>

      <widget>
	<class>GtkScrolledWindow</class>
	<name>scrolledwindow11</name>
	<hscrollbar_policy>GTK_POLICY_NEVER</hscrollbar_policy>
	<vscrollbar_policy>GTK_POLICY_AUTOMATIC</vscrollbar_policy>
	<hupdate_policy>GTK_UPDATE_CONTINUOUS</hupdate_policy>
	<vupdate_policy>GTK_UPDATE_CONTINUOUS</vupdate_policy>
	<child>
	  <shrink>True</shrink>
	  <resize>False</resize>
	</child>

	<widget>
	  <class>GtkText</class>
	  <name>ConversationMessageEntry</name>
	  <can_focus>True</can_focus>
	  <has_focus>True</has_focus>
	  <signal>
	    <name>key_press_event</name>
	    <handler>handle_key_press_event</handler>
	    <last_modification_time>Tue, 29 Jan 2002 12:42:58 GMT</last_modification_time>
	  </signal>
	  <editable>True</editable>
	  <text></text>
	</widget>
      </widget>
    </widget>

    <widget>
      <class>GtkHBox</class>
      <name>hbox9</name>
      <homogeneous>True</homogeneous>
      <spacing>0</spacing>
      <child>
	<padding>3</padding>
	<expand>False</expand>
	<fill>True</fill>
      </child>

      <widget>
	<class>GtkButton</class>
	<name>button42</name>
	<can_focus>True</can_focus>
	<label> Send Message </label>
	<relief>GTK_RELIEF_NORMAL</relief>
	<child>
	  <padding>3</padding>
	  <expand>True</expand>
	  <fill>True</fill>
	</child>
      </widget>

      <widget>
	<class>GtkButton</class>
	<name>AddRemoveContact</name>
	<can_focus>True</can_focus>
	<label> Add Contact </label>
	<relief>GTK_RELIEF_NORMAL</relief>
	<child>
	  <padding>3</padding>
	  <expand>True</expand>
	  <fill>True</fill>
	</child>
      </widget>

      <widget>
	<class>GtkButton</class>
	<name>CloseContact</name>
	<can_focus>True</can_focus>
	<label> Close </label>
	<relief>GTK_RELIEF_NORMAL</relief>
	<child>
	  <padding>3</padding>
	  <expand>True</expand>
	  <fill>True</fill>
	</child>
      </widget>
    </widget>
  </widget>
</widget>

<widget>
  <class>GtkWindow</class>
  <name>MainIMWindow</name>
  <signal>
    <name>destroy</name>
    <handler>on_MainIMWindow_destroy</handler>
    <last_modification_time>Sun, 21 Jul 2002 08:16:08 GMT</last_modification_time>
  </signal>
  <title>Instance Messenger</title>
  <type>GTK_WINDOW_TOPLEVEL</type>
  <position>GTK_WIN_POS_NONE</position>
  <modal>False</modal>
  <allow_shrink>True</allow_shrink>
  <allow_grow>True</allow_grow>
  <auto_shrink>False</auto_shrink>

  <widget>
    <class>GtkNotebook</class>
    <name>ContactsNotebook</name>
    <can_focus>True</can_focus>
    <signal>
      <name>key_press_event</name>
      <handler>on_ContactsWidget_key_press_event</handler>
      <last_modification_time>Tue, 07 May 2002 03:02:33 GMT</last_modification_time>
    </signal>
    <show_tabs>True</show_tabs>
    <show_border>True</show_border>
    <tab_pos>GTK_POS_TOP</tab_pos>
    <scrollable>False</scrollable>
    <tab_hborder>2</tab_hborder>
    <tab_vborder>2</tab_vborder>
    <popup_enable>False</popup_enable>

    <widget>
      <class>GtkVBox</class>
      <name>vbox11</name>
      <homogeneous>False</homogeneous>
      <spacing>0</spacing>

      <widget>
	<class>GtkLabel</class>
	<name>OnlineCount</name>
	<label>Online: %d</label>
	<justify>GTK_JUSTIFY_CENTER</justify>
	<wrap>False</wrap>
	<xalign>0.5</xalign>
	<yalign>0.5</yalign>
	<xpad>0</xpad>
	<ypad>0</ypad>
	<child>
	  <padding>0</padding>
	  <expand>False</expand>
	  <fill>False</fill>
	</child>
      </widget>

      <widget>
	<class>GtkScrolledWindow</class>
	<name>scrolledwindow14</name>
	<hscrollbar_policy>GTK_POLICY_AUTOMATIC</hscrollbar_policy>
	<vscrollbar_policy>GTK_POLICY_AUTOMATIC</vscrollbar_policy>
	<hupdate_policy>GTK_UPDATE_CONTINUOUS</hupdate_policy>
	<vupdate_policy>GTK_UPDATE_CONTINUOUS</vupdate_policy>
	<child>
	  <padding>0</padding>
	  <expand>True</expand>
	  <fill>True</fill>
	</child>

	<widget>
	  <class>GtkCTree</class>
	  <name>OnlineContactsTree</name>
	  <can_focus>True</can_focus>
	  <signal>
	    <name>tree_select_row</name>
	    <handler>on_OnlineContactsTree_tree_select_row</handler>
	    <last_modification_time>Tue, 07 May 2002 03:06:32 GMT</last_modification_time>
	  </signal>
	  <signal>
	    <name>select_row</name>
	    <handler>on_OnlineContactsTree_select_row</handler>
	    <last_modification_time>Tue, 07 May 2002 04:36:10 GMT</last_modification_time>
	  </signal>
	  <columns>4</columns>
	  <column_widths>109,35,23,80</column_widths>
	  <selection_mode>GTK_SELECTION_SINGLE</selection_mode>
	  <show_titles>True</show_titles>
	  <shadow_type>GTK_SHADOW_IN</shadow_type>

	  <widget>
	    <class>GtkLabel</class>
	    <child_name>CTree:title</child_name>
	    <name>label77</name>
	    <label>Alias</label>
	    <justify>GTK_JUSTIFY_CENTER</justify>
	    <wrap>False</wrap>
	    <xalign>0.5</xalign>
	    <yalign>0.5</yalign>
	    <xpad>0</xpad>
	    <ypad>0</ypad>
	  </widget>

	  <widget>
	    <class>GtkLabel</class>
	    <child_name>CTree:title</child_name>
	    <name>label78</name>
	    <label>Status</label>
	    <justify>GTK_JUSTIFY_CENTER</justify>
	    <wrap>False</wrap>
	    <xalign>0.5</xalign>
	    <yalign>0.5</yalign>
	    <xpad>0</xpad>
	    <ypad>0</ypad>
	  </widget>

	  <widget>
	    <class>GtkLabel</class>
	    <child_name>CTree:title</child_name>
	    <name>label79</name>
	    <label>Idle</label>
	    <justify>GTK_JUSTIFY_CENTER</justify>
	    <wrap>False</wrap>
	    <xalign>0.5</xalign>
	    <yalign>0.5</yalign>
	    <xpad>0</xpad>
	    <ypad>0</ypad>
	  </widget>

	  <widget>
	    <class>GtkLabel</class>
	    <child_name>CTree:title</child_name>
	    <name>label80</name>
	    <label>Account</label>
	    <justify>GTK_JUSTIFY_CENTER</justify>
	    <wrap>False</wrap>
	    <xalign>0.5</xalign>
	    <yalign>0.5</yalign>
	    <xpad>0</xpad>
	    <ypad>0</ypad>
	  </widget>
	</widget>
      </widget>

      <widget>
	<class>GtkVBox</class>
	<name>vbox30</name>
	<homogeneous>False</homogeneous>
	<spacing>2</spacing>
	<child>
	  <padding>1</padding>
	  <expand>False</expand>
	  <fill>True</fill>
	</child>

	<widget>
	  <class>GtkEntry</class>
	  <name>ContactNameEntry</name>
	  <can_focus>True</can_focus>
	  <signal>
	    <name>activate</name>
	    <handler>on_ContactNameEntry_activate</handler>
	    <last_modification_time>Tue, 07 May 2002 04:07:25 GMT</last_modification_time>
	  </signal>
	  <editable>True</editable>
	  <text_visible>True</text_visible>
	  <text_max_length>0</text_max_length>
	  <text></text>
	  <child>
	    <padding>0</padding>
	    <expand>False</expand>
	    <fill>False</fill>
	  </child>
	</widget>

	<widget>
	  <class>GtkOptionMenu</class>
	  <name>AccountsListPopup</name>
	  <can_focus>True</can_focus>
	  <items>Nothing
To
Speak
Of
</items>
	  <initial_choice>1</initial_choice>
	  <child>
	    <padding>0</padding>
	    <expand>False</expand>
	    <fill>False</fill>
	  </child>
	</widget>

	<widget>
	  <class>GtkHBox</class>
	  <name>hbox7</name>
	  <homogeneous>False</homogeneous>
	  <spacing>0</spacing>
	  <child>
	    <padding>0</padding>
	    <expand>True</expand>
	    <fill>True</fill>
	  </child>

	  <widget>
	    <class>GtkButton</class>
	    <name>PlainSendIM</name>
	    <can_focus>True</can_focus>
	    <signal>
	      <name>clicked</name>
	      <handler>on_PlainSendIM_clicked</handler>
	      <last_modification_time>Tue, 29 Jan 2002 03:17:35 GMT</last_modification_time>
	    </signal>
	    <label> Send IM </label>
	    <relief>GTK_RELIEF_NORMAL</relief>
	    <child>
	      <padding>0</padding>
	      <expand>True</expand>
	      <fill>False</fill>
	    </child>
	  </widget>

	  <widget>
	    <class>GtkButton</class>
	    <name>PlainGetInfo</name>
	    <can_focus>True</can_focus>
	    <signal>
	      <name>clicked</name>
	      <handler>on_PlainGetInfo_clicked</handler>
	      <last_modification_time>Tue, 07 May 2002 04:06:59 GMT</last_modification_time>
	    </signal>
	    <label> Get Info </label>
	    <relief>GTK_RELIEF_NORMAL</relief>
	    <child>
	      <padding>0</padding>
	      <expand>True</expand>
	      <fill>False</fill>
	    </child>
	  </widget>

	  <widget>
	    <class>GtkButton</class>
	    <name>PlainJoinChat</name>
	    <can_focus>True</can_focus>
	    <signal>
	      <name>clicked</name>
	      <handler>on_PlainJoinChat_clicked</handler>
	      <last_modification_time>Tue, 29 Jan 2002 13:04:49 GMT</last_modification_time>
	    </signal>
	    <label> Join Group </label>
	    <relief>GTK_RELIEF_NORMAL</relief>
	    <child>
	      <padding>0</padding>
	      <expand>True</expand>
	      <fill>False</fill>
	    </child>
	  </widget>

	  <widget>
	    <class>GtkButton</class>
	    <name>PlainGoAway</name>
	    <can_focus>True</can_focus>
	    <signal>
	      <name>clicked</name>
	      <handler>on_PlainGoAway_clicked</handler>
	      <last_modification_time>Tue, 07 May 2002 04:06:53 GMT</last_modification_time>
	    </signal>
	    <label> Go Away </label>
	    <relief>GTK_RELIEF_NORMAL</relief>
	    <child>
	      <padding>0</padding>
	      <expand>True</expand>
	      <fill>False</fill>
	    </child>
	  </widget>
	</widget>

	<widget>
	  <class>GtkHBox</class>
	  <name>hbox8</name>
	  <homogeneous>False</homogeneous>
	  <spacing>0</spacing>
	  <child>
	    <padding>0</padding>
	    <expand>True</expand>
	    <fill>True</fill>
	  </child>

	  <widget>
	    <class>GtkButton</class>
	    <name>AddContactButton</name>
	    <can_focus>True</can_focus>
	    <signal>
	      <name>clicked</name>
	      <handler>on_AddContactButton_clicked</handler>
	      <last_modification_time>Tue, 07 May 2002 04:06:33 GMT</last_modification_time>
	    </signal>
	    <label> Add Contact </label>
	    <relief>GTK_RELIEF_NORMAL</relief>
	    <child>
	      <padding>0</padding>
	      <expand>True</expand>
	      <fill>False</fill>
	    </child>
	  </widget>

	  <widget>
	    <class>GtkButton</class>
	    <name>RemoveContactButton</name>
	    <can_focus>True</can_focus>
	    <signal>
	      <name>clicked</name>
	      <handler>on_RemoveContactButton_clicked</handler>
	      <last_modification_time>Tue, 07 May 2002 04:06:28 GMT</last_modification_time>
	    </signal>
	    <label> Remove Contact </label>
	    <relief>GTK_RELIEF_NORMAL</relief>
	    <child>
	      <padding>0</padding>
	      <expand>True</expand>
	      <fill>False</fill>
	    </child>
	  </widget>
	</widget>
      </widget>
    </widget>

    <widget>
      <class>GtkLabel</class>
      <child_name>Notebook:tab</child_name>
      <name>label35</name>
      <label> Online Contacts </label>
      <justify>GTK_JUSTIFY_CENTER</justify>
      <wrap>False</wrap>
      <xalign>0.5</xalign>
      <yalign>0.5</yalign>
      <xpad>0</xpad>
      <ypad>0</ypad>
    </widget>

    <widget>
      <class>GtkVBox</class>
      <name>vbox14</name>
      <homogeneous>False</homogeneous>
      <spacing>0</spacing>

      <widget>
	<class>GtkScrolledWindow</class>
	<name>OfflineContactsScroll</name>
	<hscrollbar_policy>GTK_POLICY_AUTOMATIC</hscrollbar_policy>
	<vscrollbar_policy>GTK_POLICY_ALWAYS</vscrollbar_policy>
	<hupdate_policy>GTK_UPDATE_CONTINUOUS</hupdate_policy>
	<vupdate_policy>GTK_UPDATE_CONTINUOUS</vupdate_policy>
	<child>
	  <padding>0</padding>
	  <expand>True</expand>
	  <fill>True</fill>
	</child>

	<widget>
	  <class>GtkCList</class>
	  <name>OfflineContactsList</name>
	  <can_focus>True</can_focus>
	  <signal>
	    <name>select_row</name>
	    <handler>on_OfflineContactsList_select_row</handler>
	    <last_modification_time>Tue, 07 May 2002 03:00:07 GMT</last_modification_time>
	  </signal>
	  <columns>4</columns>
	  <column_widths>66,80,80,80</column_widths>
	  <selection_mode>GTK_SELECTION_SINGLE</selection_mode>
	  <show_titles>True</show_titles>
	  <shadow_type>GTK_SHADOW_IN</shadow_type>

	  <widget>
	    <class>GtkLabel</class>
	    <child_name>CList:title</child_name>
	    <name>label41</name>
	    <label>Contact</label>
	    <justify>GTK_JUSTIFY_CENTER</justify>
	    <wrap>False</wrap>
	    <xalign>0.5</xalign>
	    <yalign>0.5</yalign>
	    <xpad>0</xpad>
	    <ypad>0</ypad>
	  </widget>

	  <widget>
	    <class>GtkLabel</class>
	    <child_name>CList:title</child_name>
	    <name>label42</name>
	    <label>Account</label>
	    <justify>GTK_JUSTIFY_CENTER</justify>
	    <wrap>False</wrap>
	    <xalign>0.5</xalign>
	    <yalign>0.5</yalign>
	    <xpad>0</xpad>
	    <ypad>0</ypad>
	  </widget>

	  <widget>
	    <class>GtkLabel</class>
	    <child_name>CList:title</child_name>
	    <name>label43</name>
	    <label>Alias</label>
	    <justify>GTK_JUSTIFY_CENTER</justify>
	    <wrap>False</wrap>
	    <xalign>0.5</xalign>
	    <yalign>0.5</yalign>
	    <xpad>0</xpad>
	    <ypad>0</ypad>
	  </widget>

	  <widget>
	    <class>GtkLabel</class>
	    <child_name>CList:title</child_name>
	    <name>label44</name>
	    <label>Group</label>
	    <justify>GTK_JUSTIFY_CENTER</justify>
	    <wrap>False</wrap>
	    <xalign>0.5</xalign>
	    <yalign>0.5</yalign>
	    <xpad>0</xpad>
	    <ypad>0</ypad>
	  </widget>
	</widget>
      </widget>
    </widget>

    <widget>
      <class>GtkLabel</class>
      <child_name>Notebook:tab</child_name>
      <name>label36</name>
      <label> All Contacts </label>
      <justify>GTK_JUSTIFY_CENTER</justify>
      <wrap>False</wrap>
      <xalign>0.5</xalign>
      <yalign>0.5</yalign>
      <xpad>0</xpad>
      <ypad>0</ypad>
    </widget>

    <widget>
      <class>GtkVBox</class>
      <name>AccountManWidget</name>
      <homogeneous>False</homogeneous>
      <spacing>0</spacing>

      <widget>
	<class>GtkScrolledWindow</class>
	<name>scrolledwindow12</name>
	<hscrollbar_policy>GTK_POLICY_AUTOMATIC</hscrollbar_policy>
	<vscrollbar_policy>GTK_POLICY_ALWAYS</vscrollbar_policy>
	<hupdate_policy>GTK_UPDATE_CONTINUOUS</hupdate_policy>
	<vupdate_policy>GTK_UPDATE_CONTINUOUS</vupdate_policy>
	<child>
	  <padding>0</padding>
	  <expand>True</expand>
	  <fill>True</fill>
	</child>

	<widget>
	  <class>GtkCList</class>
	  <name>accountsList</name>
	  <can_focus>True</can_focus>
	  <columns>4</columns>
	  <column_widths>80,36,34,80</column_widths>
	  <selection_mode>GTK_SELECTION_SINGLE</selection_mode>
	  <show_titles>True</show_titles>
	  <shadow_type>GTK_SHADOW_IN</shadow_type>

	  <widget>
	    <class>GtkLabel</class>
	    <child_name>CList:title</child_name>
	    <name>label45</name>
	    <label>Service Name</label>
	    <justify>GTK_JUSTIFY_CENTER</justify>
	    <wrap>False</wrap>
	    <xalign>0.5</xalign>
	    <yalign>0.5</yalign>
	    <xpad>0</xpad>
	    <ypad>0</ypad>
	  </widget>

	  <widget>
	    <class>GtkLabel</class>
	    <child_name>CList:title</child_name>
	    <name>label46</name>
	    <label>Online</label>
	    <justify>GTK_JUSTIFY_CENTER</justify>
	    <wrap>False</wrap>
	    <xalign>0.5</xalign>
	    <yalign>0.5</yalign>
	    <xpad>0</xpad>
	    <ypad>0</ypad>
	  </widget>

	  <widget>
	    <class>GtkLabel</class>
	    <child_name>CList:title</child_name>
	    <name>label47</name>
	    <label>Auto</label>
	    <justify>GTK_JUSTIFY_CENTER</justify>
	    <wrap>False</wrap>
	    <xalign>0.5</xalign>
	    <yalign>0.5</yalign>
	    <xpad>0</xpad>
	    <ypad>0</ypad>
	  </widget>

	  <widget>
	    <class>GtkLabel</class>
	    <child_name>CList:title</child_name>
	    <name>label48</name>
	    <label>Gateway</label>
	    <justify>GTK_JUSTIFY_CENTER</justify>
	    <wrap>False</wrap>
	    <xalign>0.5</xalign>
	    <yalign>0.5</yalign>
	    <xpad>0</xpad>
	    <ypad>0</ypad>
	  </widget>
	</widget>
      </widget>

      <widget>
	<class>GtkTable</class>
	<name>table5</name>
	<rows>2</rows>
	<columns>3</columns>
	<homogeneous>False</homogeneous>
	<row_spacing>0</row_spacing>
	<column_spacing>0</column_spacing>
	<child>
	  <padding>3</padding>
	  <expand>False</expand>
	  <fill>True</fill>
	</child>

	<widget>
	  <class>GtkButton</class>
	  <name>NewAccountButton</name>
	  <can_default>True</can_default>
	  <can_focus>True</can_focus>
	  <signal>
	    <name>clicked</name>
	    <handler>on_NewAccountButton_clicked</handler>
	    <last_modification_time>Sun, 27 Jan 2002 10:32:20 GMT</last_modification_time>
	  </signal>
	  <label>New Account</label>
	  <relief>GTK_RELIEF_NORMAL</relief>
	  <child>
	    <left_attach>0</left_attach>
	    <right_attach>1</right_attach>
	    <top_attach>0</top_attach>
	    <bottom_attach>1</bottom_attach>
	    <xpad>0</xpad>
	    <ypad>0</ypad>
	    <xexpand>False</xexpand>
	    <yexpand>False</yexpand>
	    <xshrink>False</xshrink>
	    <yshrink>False</yshrink>
	    <xfill>True</xfill>
	    <yfill>False</yfill>
	  </child>
	</widget>

	<widget>
	  <class>GtkButton</class>
	  <name>button46</name>
	  <sensitive>False</sensitive>
	  <can_default>True</can_default>
	  <label>Modify Account</label>
	  <relief>GTK_RELIEF_NORMAL</relief>
	  <child>
	    <left_attach>1</left_attach>
	    <right_attach>2</right_attach>
	    <top_attach>0</top_attach>
	    <bottom_attach>1</bottom_attach>
	    <xpad>0</xpad>
	    <ypad>0</ypad>
	    <xexpand>False</xexpand>
	    <yexpand>False</yexpand>
	    <xshrink>False</xshrink>
	    <yshrink>False</yshrink>
	    <xfill>True</xfill>
	    <yfill>False</yfill>
	  </child>
	</widget>

	<widget>
	  <class>GtkButton</class>
	  <name>LogOnButton</name>
	  <can_default>True</can_default>
	  <has_default>True</has_default>
	  <can_focus>True</can_focus>
	  <has_focus>True</has_focus>
	  <signal>
	    <name>clicked</name>
	    <handler>on_LogOnButton_clicked</handler>
	    <last_modification_time>Mon, 28 Jan 2002 04:06:23 GMT</last_modification_time>
	  </signal>
	  <label>Logon</label>
	  <relief>GTK_RELIEF_NORMAL</relief>
	  <child>
	    <left_attach>2</left_attach>
	    <right_attach>3</right_attach>
	    <top_attach>1</top_attach>
	    <bottom_attach>2</bottom_attach>
	    <xpad>0</xpad>
	    <ypad>0</ypad>
	    <xexpand>False</xexpand>
	    <yexpand>False</yexpand>
	    <xshrink>False</xshrink>
	    <yshrink>False</yshrink>
	    <xfill>True</xfill>
	    <yfill>False</yfill>
	  </child>
	</widget>

	<widget>
	  <class>GtkButton</class>
	  <name>DeleteAccountButton</name>
	  <can_default>True</can_default>
	  <can_focus>True</can_focus>
	  <signal>
	    <name>clicked</name>
	    <handler>on_DeleteAccountButton_clicked</handler>
	    <last_modification_time>Mon, 28 Jan 2002 00:18:22 GMT</last_modification_time>
	  </signal>
	  <label>Delete Account</label>
	  <relief>GTK_RELIEF_NORMAL</relief>
	  <child>
	    <left_attach>2</left_attach>
	    <right_attach>3</right_attach>
	    <top_attach>0</top_attach>
	    <bottom_attach>1</bottom_attach>
	    <xpad>0</xpad>
	    <ypad>0</ypad>
	    <xexpand>False</xexpand>
	    <yexpand>False</yexpand>
	    <xshrink>False</xshrink>
	    <yshrink>False</yshrink>
	    <xfill>True</xfill>
	    <yfill>False</yfill>
	  </child>
	</widget>

	<widget>
	  <class>GtkButton</class>
	  <name>ConsoleButton</name>
	  <can_default>True</can_default>
	  <can_focus>True</can_focus>
	  <signal>
	    <name>clicked</name>
	    <handler>on_ConsoleButton_clicked</handler>
	    <last_modification_time>Mon, 29 Apr 2002 09:13:32 GMT</last_modification_time>
	  </signal>
	  <label>Console</label>
	  <relief>GTK_RELIEF_NORMAL</relief>
	  <child>
	    <left_attach>1</left_attach>
	    <right_attach>2</right_attach>
	    <top_attach>1</top_attach>
	    <bottom_attach>2</bottom_attach>
	    <xpad>0</xpad>
	    <ypad>0</ypad>
	    <xexpand>False</xexpand>
	    <yexpand>False</yexpand>
	    <xshrink>False</xshrink>
	    <yshrink>False</yshrink>
	    <xfill>True</xfill>
	    <yfill>False</yfill>
	  </child>
	</widget>

	<widget>
	  <class>GtkButton</class>
	  <name>button75</name>
	  <can_default>True</can_default>
	  <can_focus>True</can_focus>
	  <label>Quit</label>
	  <relief>GTK_RELIEF_NORMAL</relief>
	  <child>
	    <left_attach>0</left_attach>
	    <right_attach>1</right_attach>
	    <top_attach>1</top_attach>
	    <bottom_attach>2</bottom_attach>
	    <xpad>0</xpad>
	    <ypad>0</ypad>
	    <xexpand>True</xexpand>
	    <yexpand>True</yexpand>
	    <xshrink>False</xshrink>
	    <yshrink>False</yshrink>
	    <xfill>True</xfill>
	    <yfill>True</yfill>
	  </child>
	</widget>
      </widget>
    </widget>

    <widget>
      <class>GtkLabel</class>
      <child_name>Notebook:tab</child_name>
      <name>label107</name>
      <label>Accounts</label>
      <justify>GTK_JUSTIFY_CENTER</justify>
      <wrap>False</wrap>
      <xalign>0.5</xalign>
      <yalign>0.5</yalign>
      <xpad>0</xpad>
      <ypad>0</ypad>
    </widget>
  </widget>
</widget>

<widget>
  <class>GtkWindow</class>
  <name>UnseenGroupWindow</name>
  <visible>False</visible>
  <title>Unseen Group Window</title>
  <type>GTK_WINDOW_TOPLEVEL</type>
  <position>GTK_WIN_POS_NONE</position>
  <modal>False</modal>
  <allow_shrink>False</allow_shrink>
  <allow_grow>True</allow_grow>
  <auto_shrink>False</auto_shrink>

  <widget>
    <class>GtkVBox</class>
    <name>GroupChatBox</name>
    <homogeneous>False</homogeneous>
    <spacing>0</spacing>

    <widget>
      <class>GtkHBox</class>
      <name>hbox5</name>
      <homogeneous>False</homogeneous>
      <spacing>0</spacing>
      <child>
	<padding>0</padding>
	<expand>False</expand>
	<fill>True</fill>
      </child>

      <widget>
	<class>GtkEntry</class>
	<name>TopicEntry</name>
	<can_focus>True</can_focus>
	<signal>
	  <name>activate</name>
	  <handler>on_TopicEntry_activate</handler>
	  <last_modification_time>Sat, 23 Feb 2002 02:57:41 GMT</last_modification_time>
	</signal>
	<signal>
	  <name>focus_out_event</name>
	  <handler>on_TopicEntry_focus_out_event</handler>
	  <last_modification_time>Sun, 21 Jul 2002 09:36:54 GMT</last_modification_time>
	</signal>
	<editable>True</editable>
	<text_visible>True</text_visible>
	<text_max_length>0</text_max_length>
	<text>&lt;TOPIC NOT RECEIVED&gt;</text>
	<child>
	  <padding>0</padding>
	  <expand>True</expand>
	  <fill>True</fill>
	</child>
      </widget>

      <widget>
	<class>GtkLabel</class>
	<name>AuthorLabel</name>
	<label>&lt;nobody&gt;</label>
	<justify>GTK_JUSTIFY_CENTER</justify>
	<wrap>False</wrap>
	<xalign>0.5</xalign>
	<yalign>0.5</yalign>
	<xpad>0</xpad>
	<ypad>0</ypad>
	<child>
	  <padding>0</padding>
	  <expand>False</expand>
	  <fill>False</fill>
	</child>
      </widget>

      <widget>
	<class>GtkButton</class>
	<name>HideButton</name>
	<can_focus>True</can_focus>
	<signal>
	  <name>clicked</name>
	  <handler>on_HideButton_clicked</handler>
	  <last_modification_time>Tue, 29 Jan 2002 14:10:00 GMT</last_modification_time>
	</signal>
	<label>&lt;</label>
	<relief>GTK_RELIEF_NORMAL</relief>
	<child>
	  <padding>0</padding>
	  <expand>False</expand>
	  <fill>False</fill>
	</child>
      </widget>
    </widget>

    <widget>
      <class>GtkVPaned</class>
      <name>vpaned2</name>
      <handle_size>10</handle_size>
      <gutter_size>6</gutter_size>
      <position>0</position>
      <child>
	<padding>0</padding>
	<expand>True</expand>
	<fill>True</fill>
      </child>

      <widget>
	<class>GtkHPaned</class>
	<name>GroupHPaned</name>
	<handle_size>6</handle_size>
	<gutter_size>6</gutter_size>
	<child>
	  <shrink>False</shrink>
	  <resize>True</resize>
	</child>

	<widget>
	  <class>GtkScrolledWindow</class>
	  <name>scrolledwindow4</name>
	  <hscrollbar_policy>GTK_POLICY_NEVER</hscrollbar_policy>
	  <vscrollbar_policy>GTK_POLICY_ALWAYS</vscrollbar_policy>
	  <hupdate_policy>GTK_UPDATE_CONTINUOUS</hupdate_policy>
	  <vupdate_policy>GTK_UPDATE_CONTINUOUS</vupdate_policy>
	  <child>
	    <shrink>False</shrink>
	    <resize>True</resize>
	  </child>

	  <widget>
	    <class>GtkText</class>
	    <name>GroupOutput</name>
	    <can_focus>True</can_focus>
	    <editable>False</editable>
	    <text></text>
	  </widget>
	</widget>

	<widget>
	  <class>GtkVBox</class>
	  <name>actionvbox</name>
	  <width>110</width>
	  <homogeneous>False</homogeneous>
	  <spacing>1</spacing>
	  <child>
	    <shrink>True</shrink>
	    <resize>False</resize>
	  </child>

	  <widget>
	    <class>GtkScrolledWindow</class>
	    <name>scrolledwindow5</name>
	    <hscrollbar_policy>GTK_POLICY_NEVER</hscrollbar_policy>
	    <vscrollbar_policy>GTK_POLICY_ALWAYS</vscrollbar_policy>
	    <hupdate_policy>GTK_UPDATE_CONTINUOUS</hupdate_policy>
	    <vupdate_policy>GTK_UPDATE_CONTINUOUS</vupdate_policy>
	    <child>
	      <padding>0</padding>
	      <expand>True</expand>
	      <fill>True</fill>
	    </child>

	    <widget>
	      <class>GtkCList</class>
	      <name>ParticipantList</name>
	      <can_focus>True</can_focus>
	      <signal>
		<name>select_row</name>
		<handler>on_ParticipantList_select_row</handler>
		<last_modification_time>Sat, 13 Jul 2002 08:11:12 GMT</last_modification_time>
	      </signal>
	      <signal>
		<name>unselect_row</name>
		<handler>on_ParticipantList_unselect_row</handler>
		<last_modification_time>Sat, 13 Jul 2002 08:23:25 GMT</last_modification_time>
	      </signal>
	      <columns>1</columns>
	      <column_widths>80</column_widths>
	      <selection_mode>GTK_SELECTION_SINGLE</selection_mode>
	      <show_titles>False</show_titles>
	      <shadow_type>GTK_SHADOW_IN</shadow_type>

	      <widget>
		<class>GtkLabel</class>
		<child_name>CList:title</child_name>
		<name>label18</name>
		<label>Users</label>
		<justify>GTK_JUSTIFY_CENTER</justify>
		<wrap>False</wrap>
		<xalign>0.5</xalign>
		<yalign>0.5</yalign>
		<xpad>0</xpad>
		<ypad>0</ypad>
	      </widget>
	    </widget>
	  </widget>

	  <widget>
	    <class>GtkFrame</class>
	    <name>frame10</name>
	    <label>Group</label>
	    <label_xalign>0</label_xalign>
	    <shadow_type>GTK_SHADOW_ETCHED_IN</shadow_type>
	    <child>
	      <padding>0</padding>
	      <expand>False</expand>
	      <fill>False</fill>
	    </child>

	    <widget>
	      <class>GtkVBox</class>
	      <name>GroupActionsBox</name>
	      <homogeneous>False</homogeneous>
	      <spacing>0</spacing>

	      <widget>
		<class>Placeholder</class>
	      </widget>

	      <widget>
		<class>Placeholder</class>
	      </widget>

	      <widget>
		<class>Placeholder</class>
	      </widget>
	    </widget>
	  </widget>

	  <widget>
	    <class>GtkFrame</class>
	    <name>PersonFrame</name>
	    <label>Person</label>
	    <label_xalign>0</label_xalign>
	    <shadow_type>GTK_SHADOW_ETCHED_IN</shadow_type>
	    <child>
	      <padding>0</padding>
	      <expand>False</expand>
	      <fill>False</fill>
	    </child>

	    <widget>
	      <class>GtkVBox</class>
	      <name>PersonActionsBox</name>
	      <homogeneous>False</homogeneous>
	      <spacing>0</spacing>

	      <widget>
		<class>Placeholder</class>
	      </widget>

	      <widget>
		<class>Placeholder</class>
	      </widget>

	      <widget>
		<class>Placeholder</class>
	      </widget>
	    </widget>
	  </widget>
	</widget>
      </widget>

      <widget>
	<class>GtkHBox</class>
	<name>hbox6</name>
	<homogeneous>False</homogeneous>
	<spacing>0</spacing>
	<child>
	  <shrink>True</shrink>
	  <resize>False</resize>
	</child>

	<widget>
	  <class>GtkLabel</class>
	  <name>NickLabel</name>
	  <label>&lt;no nick&gt;</label>
	  <justify>GTK_JUSTIFY_CENTER</justify>
	  <wrap>False</wrap>
	  <xalign>0.5</xalign>
	  <yalign>0.5</yalign>
	  <xpad>0</xpad>
	  <ypad>0</ypad>
	  <child>
	    <padding>4</padding>
	    <expand>False</expand>
	    <fill>False</fill>
	  </child>
	</widget>

	<widget>
	  <class>GtkScrolledWindow</class>
	  <name>scrolledwindow9</name>
	  <hscrollbar_policy>GTK_POLICY_NEVER</hscrollbar_policy>
	  <vscrollbar_policy>GTK_POLICY_AUTOMATIC</vscrollbar_policy>
	  <hupdate_policy>GTK_UPDATE_CONTINUOUS</hupdate_policy>
	  <vupdate_policy>GTK_UPDATE_CONTINUOUS</vupdate_policy>
	  <child>
	    <padding>0</padding>
	    <expand>True</expand>
	    <fill>True</fill>
	  </child>

	  <widget>
	    <class>GtkText</class>
	    <name>GroupInput</name>
	    <can_focus>True</can_focus>
	    <has_focus>True</has_focus>
	    <signal>
	      <name>key_press_event</name>
	      <handler>handle_key_press_event</handler>
	      <last_modification_time>Tue, 29 Jan 2002 12:41:03 GMT</last_modification_time>
	    </signal>
	    <editable>True</editable>
	    <text></text>
	  </widget>
	</widget>
      </widget>
    </widget>
  </widget>
</widget>

<widget>
  <class>GtkWindow</class>
  <name>NewAccountWindow</name>
  <border_width>3</border_width>
  <visible>False</visible>
  <signal>
    <name>destroy</name>
    <handler>on_NewAccountWindow_destroy</handler>
    <last_modification_time>Sun, 27 Jan 2002 10:35:19 GMT</last_modification_time>
  </signal>
  <title>New Account</title>
  <type>GTK_WINDOW_TOPLEVEL</type>
  <position>GTK_WIN_POS_NONE</position>
  <modal>False</modal>
  <allow_shrink>False</allow_shrink>
  <allow_grow>True</allow_grow>
  <auto_shrink>True</auto_shrink>

  <widget>
    <class>GtkVBox</class>
    <name>vbox17</name>
    <homogeneous>False</homogeneous>
    <spacing>0</spacing>

    <widget>
      <class>GtkHBox</class>
      <name>hbox11</name>
      <homogeneous>False</homogeneous>
      <spacing>0</spacing>
      <child>
	<padding>3</padding>
	<expand>False</expand>
	<fill>True</fill>
      </child>

      <widget>
	<class>GtkLabel</class>
	<name>label49</name>
	<label>Gateway:</label>
	<justify>GTK_JUSTIFY_CENTER</justify>
	<wrap>False</wrap>
	<xalign>0.5</xalign>
	<yalign>0.5</yalign>
	<xpad>0</xpad>
	<ypad>0</ypad>
	<child>
	  <padding>0</padding>
	  <expand>False</expand>
	  <fill>True</fill>
	</child>
      </widget>

      <widget>
	<class>GtkOptionMenu</class>
	<name>GatewayOptionMenu</name>
	<can_focus>True</can_focus>
	<items>Twisted (Perspective Broker)
Internet Relay Chat
AIM (TOC)
AIM (OSCAR)
</items>
	<initial_choice>0</initial_choice>
	<child>
	  <padding>4</padding>
	  <expand>True</expand>
	  <fill>True</fill>
	</child>
      </widget>
    </widget>

    <widget>
      <class>GtkFrame</class>
      <name>GatewayFrame</name>
      <border_width>3</border_width>
      <label>Gateway Options</label>
      <label_xalign>0</label_xalign>
      <shadow_type>GTK_SHADOW_ETCHED_IN</shadow_type>
      <child>
	<padding>0</padding>
	<expand>True</expand>
	<fill>True</fill>
      </child>

      <widget>
	<class>Placeholder</class>
      </widget>
    </widget>

    <widget>
      <class>GtkFrame</class>
      <name>frame2</name>
      <border_width>3</border_width>
      <label>Standard Options</label>
      <label_xalign>0</label_xalign>
      <shadow_type>GTK_SHADOW_ETCHED_IN</shadow_type>
      <child>
	<padding>0</padding>
	<expand>False</expand>
	<fill>True</fill>
      </child>

      <widget>
	<class>GtkTable</class>
	<name>table1</name>
	<border_width>3</border_width>
	<rows>2</rows>
	<columns>2</columns>
	<homogeneous>False</homogeneous>
	<row_spacing>0</row_spacing>
	<column_spacing>0</column_spacing>

	<widget>
	  <class>GtkCheckButton</class>
	  <name>AutoLogin</name>
	  <can_focus>True</can_focus>
	  <label>Automatically Log In</label>
	  <active>False</active>
	  <draw_indicator>True</draw_indicator>
	  <child>
	    <left_attach>1</left_attach>
	    <right_attach>2</right_attach>
	    <top_attach>0</top_attach>
	    <bottom_attach>1</bottom_attach>
	    <xpad>0</xpad>
	    <ypad>0</ypad>
	    <xexpand>True</xexpand>
	    <yexpand>True</yexpand>
	    <xshrink>False</xshrink>
	    <yshrink>False</yshrink>
	    <xfill>True</xfill>
	    <yfill>False</yfill>
	  </child>
	</widget>

	<widget>
	  <class>GtkEntry</class>
	  <name>accountName</name>
	  <can_focus>True</can_focus>
	  <editable>True</editable>
	  <text_visible>True</text_visible>
	  <text_max_length>0</text_max_length>
	  <text></text>
	  <child>
	    <left_attach>1</left_attach>
	    <right_attach>2</right_attach>
	    <top_attach>1</top_attach>
	    <bottom_attach>2</bottom_attach>
	    <xpad>0</xpad>
	    <ypad>0</ypad>
	    <xexpand>True</xexpand>
	    <yexpand>True</yexpand>
	    <xshrink>False</xshrink>
	    <yshrink>False</yshrink>
	    <xfill>True</xfill>
	    <yfill>False</yfill>
	  </child>
	</widget>

	<widget>
	  <class>GtkLabel</class>
	  <name>label50</name>
	  <label>   Auto-Login: </label>
	  <justify>GTK_JUSTIFY_RIGHT</justify>
	  <wrap>False</wrap>
	  <xalign>0</xalign>
	  <yalign>0.5</yalign>
	  <xpad>0</xpad>
	  <ypad>0</ypad>
	  <child>
	    <left_attach>0</left_attach>
	    <right_attach>1</right_attach>
	    <top_attach>0</top_attach>
	    <bottom_attach>1</bottom_attach>
	    <xpad>0</xpad>
	    <ypad>0</ypad>
	    <xexpand>False</xexpand>
	    <yexpand>True</yexpand>
	    <xshrink>False</xshrink>
	    <yshrink>False</yshrink>
	    <xfill>True</xfill>
	    <yfill>True</yfill>
	  </child>
	</widget>

	<widget>
	  <class>GtkLabel</class>
	  <name>label51</name>
	  <label>Account Name: </label>
	  <justify>GTK_JUSTIFY_RIGHT</justify>
	  <wrap>False</wrap>
	  <xalign>0</xalign>
	  <yalign>0.5</yalign>
	  <xpad>0</xpad>
	  <ypad>0</ypad>
	  <child>
	    <left_attach>0</left_attach>
	    <right_attach>1</right_attach>
	    <top_attach>1</top_attach>
	    <bottom_attach>2</bottom_attach>
	    <xpad>0</xpad>
	    <ypad>0</ypad>
	    <xexpand>False</xexpand>
	    <yexpand>True</yexpand>
	    <xshrink>False</xshrink>
	    <yshrink>False</yshrink>
	    <xfill>True</xfill>
	    <yfill>True</yfill>
	  </child>
	</widget>
      </widget>
    </widget>

    <widget>
      <class>GtkHButtonBox</class>
      <name>hbuttonbox2</name>
      <layout_style>GTK_BUTTONBOX_SPREAD</layout_style>
      <spacing>30</spacing>
      <child_min_width>85</child_min_width>
      <child_min_height>27</child_min_height>
      <child_ipad_x>7</child_ipad_x>
      <child_ipad_y>0</child_ipad_y>
      <child>
	<padding>0</padding>
	<expand>False</expand>
	<fill>True</fill>
      </child>

      <widget>
	<class>GtkButton</class>
	<name>button50</name>
	<can_default>True</can_default>
	<can_focus>True</can_focus>
	<signal>
	  <name>clicked</name>
	  <handler>createAccount</handler>
	  <last_modification_time>Sun, 27 Jan 2002 11:25:05 GMT</last_modification_time>
	</signal>
	<label>OK</label>
	<relief>GTK_RELIEF_NORMAL</relief>
      </widget>

      <widget>
	<class>GtkButton</class>
	<name>button51</name>
	<can_default>True</can_default>
	<can_focus>True</can_focus>
	<signal>
	  <name>clicked</name>
	  <handler>destroyMe</handler>
	  <last_modification_time>Sun, 27 Jan 2002 11:27:12 GMT</last_modification_time>
	</signal>
	<label>Cancel</label>
	<relief>GTK_RELIEF_NORMAL</relief>
      </widget>
    </widget>
  </widget>
</widget>

<widget>
  <class>GtkWindow</class>
  <name>PBAccountWindow</name>
  <visible>False</visible>
  <title>PB Account Window</title>
  <type>GTK_WINDOW_TOPLEVEL</type>
  <position>GTK_WIN_POS_NONE</position>
  <modal>False</modal>
  <allow_shrink>False</allow_shrink>
  <allow_grow>True</allow_grow>
  <auto_shrink>False</auto_shrink>

  <widget>
    <class>GtkVBox</class>
    <name>PBAccountWidget</name>
    <border_width>4</border_width>
    <homogeneous>False</homogeneous>
    <spacing>0</spacing>

    <widget>
      <class>GtkTable</class>
      <name>table3</name>
      <rows>4</rows>
      <columns>2</columns>
      <homogeneous>False</homogeneous>
      <row_spacing>0</row_spacing>
      <column_spacing>0</column_spacing>
      <child>
	<padding>0</padding>
	<expand>False</expand>
	<fill>True</fill>
      </child>

      <widget>
	<class>GtkEntry</class>
	<name>hostname</name>
	<can_focus>True</can_focus>
	<editable>True</editable>
	<text_visible>True</text_visible>
	<text_max_length>0</text_max_length>
	<text>twistedmatrix.com</text>
	<child>
	  <left_attach>1</left_attach>
	  <right_attach>2</right_attach>
	  <top_attach>2</top_attach>
	  <bottom_attach>3</bottom_attach>
	  <xpad>0</xpad>
	  <ypad>0</ypad>
	  <xexpand>True</xexpand>
	  <yexpand>False</yexpand>
	  <xshrink>False</xshrink>
	  <yshrink>False</yshrink>
	  <xfill>True</xfill>
	  <yfill>False</yfill>
	</child>
      </widget>

      <widget>
	<class>GtkEntry</class>
	<name>identity</name>
	<can_focus>True</can_focus>
	<has_focus>True</has_focus>
	<signal>
	  <name>changed</name>
	  <handler>on_identity_changed</handler>
	  <last_modification_time>Sun, 27 Jan 2002 11:52:17 GMT</last_modification_time>
	</signal>
	<editable>True</editable>
	<text_visible>True</text_visible>
	<text_max_length>0</text_max_length>
	<text></text>
	<child>
	  <left_attach>1</left_attach>
	  <right_attach>2</right_attach>
	  <top_attach>0</top_attach>
	  <bottom_attach>1</bottom_attach>
	  <xpad>0</xpad>
	  <ypad>0</ypad>
	  <xexpand>True</xexpand>
	  <yexpand>False</yexpand>
	  <xshrink>False</xshrink>
	  <yshrink>False</yshrink>
	  <xfill>True</xfill>
	  <yfill>False</yfill>
	</child>
      </widget>

      <widget>
	<class>GtkLabel</class>
	<name>label52</name>
	<label>     Hostname: </label>
	<justify>GTK_JUSTIFY_RIGHT</justify>
	<wrap>False</wrap>
	<xalign>0</xalign>
	<yalign>0.5</yalign>
	<xpad>0</xpad>
	<ypad>0</ypad>
	<child>
	  <left_attach>0</left_attach>
	  <right_attach>1</right_attach>
	  <top_attach>2</top_attach>
	  <bottom_attach>3</bottom_attach>
	  <xpad>0</xpad>
	  <ypad>0</ypad>
	  <xexpand>False</xexpand>
	  <yexpand>False</yexpand>
	  <xshrink>False</xshrink>
	  <yshrink>False</yshrink>
	  <xfill>True</xfill>
	  <yfill>False</yfill>
	</child>
      </widget>

      <widget>
	<class>GtkLabel</class>
	<name>label54</name>
	<label>Identity Name: </label>
	<justify>GTK_JUSTIFY_RIGHT</justify>
	<wrap>False</wrap>
	<xalign>0</xalign>
	<yalign>0.5</yalign>
	<xpad>0</xpad>
	<ypad>0</ypad>
	<child>
	  <left_attach>0</left_attach>
	  <right_attach>1</right_attach>
	  <top_attach>0</top_attach>
	  <bottom_attach>1</bottom_attach>
	  <xpad>0</xpad>
	  <ypad>0</ypad>
	  <xexpand>False</xexpand>
	  <yexpand>False</yexpand>
	  <xshrink>False</xshrink>
	  <yshrink>False</yshrink>
	  <xfill>True</xfill>
	  <yfill>False</yfill>
	</child>
      </widget>

      <widget>
	<class>GtkEntry</class>
	<name>password</name>
	<can_focus>True</can_focus>
	<editable>True</editable>
	<text_visible>False</text_visible>
	<text_max_length>0</text_max_length>
	<text></text>
	<child>
	  <left_attach>1</left_attach>
	  <right_attach>2</right_attach>
	  <top_attach>1</top_attach>
	  <bottom_attach>2</bottom_attach>
	  <xpad>0</xpad>
	  <ypad>0</ypad>
	  <xexpand>True</xexpand>
	  <yexpand>False</yexpand>
	  <xshrink>False</xshrink>
	  <yshrink>False</yshrink>
	  <xfill>True</xfill>
	  <yfill>False</yfill>
	</child>
      </widget>

      <widget>
	<class>GtkEntry</class>
	<name>portno</name>
	<can_focus>True</can_focus>
	<editable>True</editable>
	<text_visible>True</text_visible>
	<text_max_length>0</text_max_length>
	<text>8787</text>
	<child>
	  <left_attach>1</left_attach>
	  <right_attach>2</right_attach>
	  <top_attach>3</top_attach>
	  <bottom_attach>4</bottom_attach>
	  <xpad>0</xpad>
	  <ypad>0</ypad>
	  <xexpand>True</xexpand>
	  <yexpand>False</yexpand>
	  <xshrink>False</xshrink>
	  <yshrink>False</yshrink>
	  <xfill>True</xfill>
	  <yfill>False</yfill>
	</child>
      </widget>

      <widget>
	<class>GtkLabel</class>
	<name>label55</name>
	<label>     Password: </label>
	<justify>GTK_JUSTIFY_RIGHT</justify>
	<wrap>False</wrap>
	<xalign>0</xalign>
	<yalign>0.5</yalign>
	<xpad>0</xpad>
	<ypad>0</ypad>
	<child>
	  <left_attach>0</left_attach>
	  <right_attach>1</right_attach>
	  <top_attach>1</top_attach>
	  <bottom_attach>2</bottom_attach>
	  <xpad>0</xpad>
	  <ypad>0</ypad>
	  <xexpand>False</xexpand>
	  <yexpand>False</yexpand>
	  <xshrink>False</xshrink>
	  <yshrink>False</yshrink>
	  <xfill>True</xfill>
	  <yfill>False</yfill>
	</child>
      </widget>

      <widget>
	<class>GtkLabel</class>
	<name>label53</name>
	<label>  Port Number: </label>
	<justify>GTK_JUSTIFY_RIGHT</justify>
	<wrap>False</wrap>
	<xalign>0</xalign>
	<yalign>0.5</yalign>
	<xpad>0</xpad>
	<ypad>0</ypad>
	<child>
	  <left_attach>0</left_attach>
	  <right_attach>1</right_attach>
	  <top_attach>3</top_attach>
	  <bottom_attach>4</bottom_attach>
	  <xpad>0</xpad>
	  <ypad>0</ypad>
	  <xexpand>False</xexpand>
	  <yexpand>False</yexpand>
	  <xshrink>False</xshrink>
	  <yshrink>False</yshrink>
	  <xfill>True</xfill>
	  <yfill>False</yfill>
	</child>
      </widget>
    </widget>

    <widget>
      <class>GtkFrame</class>
      <name>frame3</name>
      <label>Perspectives</label>
      <label_xalign>0</label_xalign>
      <shadow_type>GTK_SHADOW_ETCHED_IN</shadow_type>
      <child>
	<padding>0</padding>
	<expand>True</expand>
	<fill>True</fill>
      </child>

      <widget>
	<class>GtkVBox</class>
	<name>vbox19</name>
	<border_width>3</border_width>
	<homogeneous>False</homogeneous>
	<spacing>0</spacing>

	<widget>
	  <class>GtkScrolledWindow</class>
	  <name>scrolledwindow13</name>
	  <hscrollbar_policy>GTK_POLICY_AUTOMATIC</hscrollbar_policy>
	  <vscrollbar_policy>GTK_POLICY_ALWAYS</vscrollbar_policy>
	  <hupdate_policy>GTK_UPDATE_CONTINUOUS</hupdate_policy>
	  <vupdate_policy>GTK_UPDATE_CONTINUOUS</vupdate_policy>
	  <child>
	    <padding>0</padding>
	    <expand>True</expand>
	    <fill>True</fill>
	  </child>

	  <widget>
	    <class>GtkCList</class>
	    <name>serviceList</name>
	    <can_focus>True</can_focus>
	    <signal>
	      <name>select_row</name>
	      <handler>on_serviceList_select_row</handler>
	      <last_modification_time>Sun, 27 Jan 2002 12:04:38 GMT</last_modification_time>
	    </signal>
	    <columns>3</columns>
	    <column_widths>80,80,80</column_widths>
	    <selection_mode>GTK_SELECTION_SINGLE</selection_mode>
	    <show_titles>True</show_titles>
	    <shadow_type>GTK_SHADOW_IN</shadow_type>

	    <widget>
	      <class>GtkLabel</class>
	      <child_name>CList:title</child_name>
	      <name>label60</name>
	      <label>Service Type</label>
	      <justify>GTK_JUSTIFY_CENTER</justify>
	      <wrap>False</wrap>
	      <xalign>0.5</xalign>
	      <yalign>0.5</yalign>
	      <xpad>0</xpad>
	      <ypad>0</ypad>
	    </widget>

	    <widget>
	      <class>GtkLabel</class>
	      <child_name>CList:title</child_name>
	      <name>label61</name>
	      <label>Service Name</label>
	      <justify>GTK_JUSTIFY_CENTER</justify>
	      <wrap>False</wrap>
	      <xalign>0.5</xalign>
	      <yalign>0.5</yalign>
	      <xpad>0</xpad>
	      <ypad>0</ypad>
	    </widget>

	    <widget>
	      <class>GtkLabel</class>
	      <child_name>CList:title</child_name>
	      <name>label62</name>
	      <label>Perspective Name</label>
	      <justify>GTK_JUSTIFY_CENTER</justify>
	      <wrap>False</wrap>
	      <xalign>0.5</xalign>
	      <yalign>0.5</yalign>
	      <xpad>0</xpad>
	      <ypad>0</ypad>
	    </widget>
	  </widget>
	</widget>

	<widget>
	  <class>GtkTable</class>
	  <name>table4</name>
	  <rows>3</rows>
	  <columns>2</columns>
	  <homogeneous>False</homogeneous>
	  <row_spacing>0</row_spacing>
	  <column_spacing>0</column_spacing>
	  <child>
	    <padding>0</padding>
	    <expand>False</expand>
	    <fill>True</fill>
	  </child>

	  <widget>
	    <class>GtkLabel</class>
	    <name>label63</name>
	    <label>Perspective Name: </label>
	    <justify>GTK_JUSTIFY_RIGHT</justify>
	    <wrap>False</wrap>
	    <xalign>0</xalign>
	    <yalign>0.5</yalign>
	    <xpad>0</xpad>
	    <ypad>0</ypad>
	    <child>
	      <left_attach>0</left_attach>
	      <right_attach>1</right_attach>
	      <top_attach>2</top_attach>
	      <bottom_attach>3</bottom_attach>
	      <xpad>0</xpad>
	      <ypad>0</ypad>
	      <xexpand>False</xexpand>
	      <yexpand>False</yexpand>
	      <xshrink>False</xshrink>
	      <yshrink>False</yshrink>
	      <xfill>True</xfill>
	      <yfill>False</yfill>
	    </child>
	  </widget>

	  <widget>
	    <class>GtkLabel</class>
	    <name>label59</name>
	    <label>    Service Type: </label>
	    <justify>GTK_JUSTIFY_RIGHT</justify>
	    <wrap>False</wrap>
	    <xalign>0</xalign>
	    <yalign>0.5</yalign>
	    <xpad>0</xpad>
	    <ypad>0</ypad>
	    <child>
	      <left_attach>0</left_attach>
	      <right_attach>1</right_attach>
	      <top_attach>0</top_attach>
	      <bottom_attach>1</bottom_attach>
	      <xpad>0</xpad>
	      <ypad>0</ypad>
	      <xexpand>False</xexpand>
	      <yexpand>False</yexpand>
	      <xshrink>False</xshrink>
	      <yshrink>False</yshrink>
	      <xfill>True</xfill>
	      <yfill>False</yfill>
	    </child>
	  </widget>

	  <widget>
	    <class>GtkCombo</class>
	    <name>serviceCombo</name>
	    <value_in_list>False</value_in_list>
	    <ok_if_empty>True</ok_if_empty>
	    <case_sensitive>False</case_sensitive>
	    <use_arrows>True</use_arrows>
	    <use_arrows_always>False</use_arrows_always>
	    <items>twisted.words
twisted.reality
</items>
	    <child>
	      <left_attach>1</left_attach>
	      <right_attach>2</right_attach>
	      <top_attach>0</top_attach>
	      <bottom_attach>1</bottom_attach>
	      <xpad>0</xpad>
	      <ypad>0</ypad>
	      <xexpand>True</xexpand>
	      <yexpand>False</yexpand>
	      <xshrink>False</xshrink>
	      <yshrink>False</yshrink>
	      <xfill>True</xfill>
	      <yfill>False</yfill>
	    </child>

	    <widget>
	      <class>GtkEntry</class>
	      <child_name>GtkCombo:entry</child_name>
	      <name>serviceType</name>
	      <can_focus>True</can_focus>
	      <signal>
		<name>changed</name>
		<handler>on_serviceType_changed</handler>
		<last_modification_time>Sun, 27 Jan 2002 11:49:07 GMT</last_modification_time>
	      </signal>
	      <editable>True</editable>
	      <text_visible>True</text_visible>
	      <text_max_length>0</text_max_length>
	      <text>twisted.words</text>
	    </widget>
	  </widget>

	  <widget>
	    <class>GtkLabel</class>
	    <name>label64</name>
	    <label>    Service Name: </label>
	    <justify>GTK_JUSTIFY_RIGHT</justify>
	    <wrap>False</wrap>
	    <xalign>0</xalign>
	    <yalign>0.5</yalign>
	    <xpad>0</xpad>
	    <ypad>0</ypad>
	    <child>
	      <left_attach>0</left_attach>
	      <right_attach>1</right_attach>
	      <top_attach>1</top_attach>
	      <bottom_attach>2</bottom_attach>
	      <xpad>0</xpad>
	      <ypad>0</ypad>
	      <xexpand>False</xexpand>
	      <yexpand>False</yexpand>
	      <xshrink>False</xshrink>
	      <yshrink>False</yshrink>
	      <xfill>True</xfill>
	      <yfill>False</yfill>
	    </child>
	  </widget>

	  <widget>
	    <class>GtkEntry</class>
	    <name>serviceName</name>
	    <can_focus>True</can_focus>
	    <editable>True</editable>
	    <text_visible>True</text_visible>
	    <text_max_length>0</text_max_length>
	    <text></text>
	    <child>
	      <left_attach>1</left_attach>
	      <right_attach>2</right_attach>
	      <top_attach>1</top_attach>
	      <bottom_attach>2</bottom_attach>
	      <xpad>0</xpad>
	      <ypad>0</ypad>
	      <xexpand>True</xexpand>
	      <yexpand>False</yexpand>
	      <xshrink>False</xshrink>
	      <yshrink>False</yshrink>
	      <xfill>True</xfill>
	      <yfill>False</yfill>
	    </child>
	  </widget>

	  <widget>
	    <class>GtkEntry</class>
	    <name>perspectiveName</name>
	    <can_focus>True</can_focus>
	    <editable>True</editable>
	    <text_visible>True</text_visible>
	    <text_max_length>0</text_max_length>
	    <text></text>
	    <child>
	      <left_attach>1</left_attach>
	      <right_attach>2</right_attach>
	      <top_attach>2</top_attach>
	      <bottom_attach>3</bottom_attach>
	      <xpad>0</xpad>
	      <ypad>0</ypad>
	      <xexpand>True</xexpand>
	      <yexpand>False</yexpand>
	      <xshrink>False</xshrink>
	      <yshrink>False</yshrink>
	      <xfill>True</xfill>
	      <yfill>False</yfill>
	    </child>
	  </widget>
	</widget>

	<widget>
	  <class>GtkHBox</class>
	  <name>hbox13</name>
	  <homogeneous>False</homogeneous>
	  <spacing>0</spacing>
	  <child>
	    <padding>0</padding>
	    <expand>False</expand>
	    <fill>True</fill>
	  </child>

	  <widget>
	    <class>GtkButton</class>
	    <name>button53</name>
	    <can_focus>True</can_focus>
	    <signal>
	      <name>clicked</name>
	      <handler>addPerspective</handler>
	      <last_modification_time>Mon, 28 Jan 2002 01:07:15 GMT</last_modification_time>
	    </signal>
	    <label> Add Perspective </label>
	    <relief>GTK_RELIEF_NORMAL</relief>
	    <child>
	      <padding>0</padding>
	      <expand>True</expand>
	      <fill>False</fill>
	    </child>
	  </widget>

	  <widget>
	    <class>GtkButton</class>
	    <name>button54</name>
	    <can_focus>True</can_focus>
	    <signal>
	      <name>clicked</name>
	      <handler>removePerspective</handler>
	      <last_modification_time>Sun, 27 Jan 2002 11:34:36 GMT</last_modification_time>
	    </signal>
	    <label>Remove Perspective</label>
	    <relief>GTK_RELIEF_NORMAL</relief>
	    <child>
	      <padding>0</padding>
	      <expand>True</expand>
	      <fill>False</fill>
	    </child>
	  </widget>
	</widget>
      </widget>
    </widget>
  </widget>
</widget>

<widget>
  <class>GtkWindow</class>
  <name>IRCAccountWindow</name>
  <title>IRC Account Window</title>
  <type>GTK_WINDOW_TOPLEVEL</type>
  <position>GTK_WIN_POS_NONE</position>
  <modal>False</modal>
  <allow_shrink>False</allow_shrink>
  <allow_grow>True</allow_grow>
  <auto_shrink>False</auto_shrink>

  <widget>
    <class>GtkTable</class>
    <name>IRCAccountWidget</name>
    <rows>5</rows>
    <columns>2</columns>
    <homogeneous>False</homogeneous>
    <row_spacing>0</row_spacing>
    <column_spacing>0</column_spacing>

    <widget>
      <class>GtkLabel</class>
      <name>label65</name>
      <label> Nickname: </label>
      <justify>GTK_JUSTIFY_RIGHT</justify>
      <wrap>False</wrap>
      <xalign>0</xalign>
      <yalign>0.5</yalign>
      <xpad>0</xpad>
      <ypad>0</ypad>
      <child>
	<left_attach>0</left_attach>
	<right_attach>1</right_attach>
	<top_attach>0</top_attach>
	<bottom_attach>1</bottom_attach>
	<xpad>0</xpad>
	<ypad>0</ypad>
	<xexpand>False</xexpand>
	<yexpand>False</yexpand>
	<xshrink>False</xshrink>
	<yshrink>False</yshrink>
	<xfill>True</xfill>
	<yfill>False</yfill>
      </child>
    </widget>

    <widget>
      <class>GtkLabel</class>
      <name>label66</name>
      <label>   Server: </label>
      <justify>GTK_JUSTIFY_RIGHT</justify>
      <wrap>False</wrap>
      <xalign>0</xalign>
      <yalign>0.5</yalign>
      <xpad>0</xpad>
      <ypad>0</ypad>
      <child>
	<left_attach>0</left_attach>
	<right_attach>1</right_attach>
	<top_attach>1</top_attach>
	<bottom_attach>2</bottom_attach>
	<xpad>0</xpad>
	<ypad>0</ypad>
	<xexpand>False</xexpand>
	<yexpand>False</yexpand>
	<xshrink>False</xshrink>
	<yshrink>False</yshrink>
	<xfill>True</xfill>
	<yfill>False</yfill>
      </child>
    </widget>

    <widget>
      <class>GtkLabel</class>
      <name>label67</name>
      <label>     Port: </label>
      <justify>GTK_JUSTIFY_RIGHT</justify>
      <wrap>False</wrap>
      <xalign>0</xalign>
      <yalign>0.5</yalign>
      <xpad>0</xpad>
      <ypad>0</ypad>
      <child>
	<left_attach>0</left_attach>
	<right_attach>1</right_attach>
	<top_attach>2</top_attach>
	<bottom_attach>3</bottom_attach>
	<xpad>0</xpad>
	<ypad>0</ypad>
	<xexpand>False</xexpand>
	<yexpand>False</yexpand>
	<xshrink>False</xshrink>
	<yshrink>False</yshrink>
	<xfill>True</xfill>
	<yfill>False</yfill>
      </child>
    </widget>

    <widget>
      <class>GtkLabel</class>
      <name>label68</name>
      <label> Channels: </label>
      <justify>GTK_JUSTIFY_RIGHT</justify>
      <wrap>False</wrap>
      <xalign>0</xalign>
      <yalign>0.5</yalign>
      <xpad>0</xpad>
      <ypad>0</ypad>
      <child>
	<left_attach>0</left_attach>
	<right_attach>1</right_attach>
	<top_attach>3</top_attach>
	<bottom_attach>4</bottom_attach>
	<xpad>0</xpad>
	<ypad>0</ypad>
	<xexpand>False</xexpand>
	<yexpand>False</yexpand>
	<xshrink>False</xshrink>
	<yshrink>False</yshrink>
	<xfill>True</xfill>
	<yfill>False</yfill>
      </child>
    </widget>

    <widget>
      <class>GtkLabel</class>
      <name>label69</name>
      <label> Password: </label>
      <justify>GTK_JUSTIFY_RIGHT</justify>
      <wrap>False</wrap>
      <xalign>0</xalign>
      <yalign>0.5</yalign>
      <xpad>0</xpad>
      <ypad>0</ypad>
      <child>
	<left_attach>0</left_attach>
	<right_attach>1</right_attach>
	<top_attach>4</top_attach>
	<bottom_attach>5</bottom_attach>
	<xpad>0</xpad>
	<ypad>0</ypad>
	<xexpand>False</xexpand>
	<yexpand>False</yexpand>
	<xshrink>False</xshrink>
	<yshrink>False</yshrink>
	<xfill>True</xfill>
	<yfill>False</yfill>
      </child>
    </widget>

    <widget>
      <class>GtkEntry</class>
      <name>ircNick</name>
      <can_focus>True</can_focus>
      <editable>True</editable>
      <text_visible>True</text_visible>
      <text_max_length>0</text_max_length>
      <text></text>
      <child>
	<left_attach>1</left_attach>
	<right_attach>2</right_attach>
	<top_attach>0</top_attach>
	<bottom_attach>1</bottom_attach>
	<xpad>0</xpad>
	<ypad>0</ypad>
	<xexpand>True</xexpand>
	<yexpand>False</yexpand>
	<xshrink>False</xshrink>
	<yshrink>False</yshrink>
	<xfill>True</xfill>
	<yfill>False</yfill>
      </child>
    </widget>

    <widget>
      <class>GtkEntry</class>
      <name>ircServer</name>
      <can_focus>True</can_focus>
      <editable>True</editable>
      <text_visible>True</text_visible>
      <text_max_length>0</text_max_length>
      <text></text>
      <child>
	<left_attach>1</left_attach>
	<right_attach>2</right_attach>
	<top_attach>1</top_attach>
	<bottom_attach>2</bottom_attach>
	<xpad>0</xpad>
	<ypad>0</ypad>
	<xexpand>True</xexpand>
	<yexpand>False</yexpand>
	<xshrink>False</xshrink>
	<yshrink>False</yshrink>
	<xfill>True</xfill>
	<yfill>False</yfill>
      </child>
    </widget>

    <widget>
      <class>GtkEntry</class>
      <name>ircPort</name>
      <can_focus>True</can_focus>
      <editable>True</editable>
      <text_visible>True</text_visible>
      <text_max_length>0</text_max_length>
      <text>6667</text>
      <child>
	<left_attach>1</left_attach>
	<right_attach>2</right_attach>
	<top_attach>2</top_attach>
	<bottom_attach>3</bottom_attach>
	<xpad>0</xpad>
	<ypad>0</ypad>
	<xexpand>True</xexpand>
	<yexpand>False</yexpand>
	<xshrink>False</xshrink>
	<yshrink>False</yshrink>
	<xfill>True</xfill>
	<yfill>False</yfill>
      </child>
    </widget>

    <widget>
      <class>GtkEntry</class>
      <name>ircChannels</name>
      <can_focus>True</can_focus>
      <editable>True</editable>
      <text_visible>True</text_visible>
      <text_max_length>0</text_max_length>
      <text></text>
      <child>
	<left_attach>1</left_attach>
	<right_attach>2</right_attach>
	<top_attach>3</top_attach>
	<bottom_attach>4</bottom_attach>
	<xpad>0</xpad>
	<ypad>0</ypad>
	<xexpand>True</xexpand>
	<yexpand>False</yexpand>
	<xshrink>False</xshrink>
	<yshrink>False</yshrink>
	<xfill>True</xfill>
	<yfill>False</yfill>
      </child>
    </widget>

    <widget>
      <class>GtkEntry</class>
      <name>ircPassword</name>
      <can_focus>True</can_focus>
      <editable>True</editable>
      <text_visible>True</text_visible>
      <text_max_length>0</text_max_length>
      <text></text>
      <child>
	<left_attach>1</left_attach>
	<right_attach>2</right_attach>
	<top_attach>4</top_attach>
	<bottom_attach>5</bottom_attach>
	<xpad>0</xpad>
	<ypad>0</ypad>
	<xexpand>True</xexpand>
	<yexpand>False</yexpand>
	<xshrink>False</xshrink>
	<yshrink>False</yshrink>
	<xfill>True</xfill>
	<yfill>False</yfill>
      </child>
    </widget>
  </widget>
</widget>

<widget>
  <class>GtkWindow</class>
  <name>TOCAccountWindow</name>
  <title>TOC Account Window</title>
  <type>GTK_WINDOW_TOPLEVEL</type>
  <position>GTK_WIN_POS_NONE</position>
  <modal>False</modal>
  <allow_shrink>False</allow_shrink>
  <allow_grow>True</allow_grow>
  <auto_shrink>False</auto_shrink>

  <widget>
    <class>GtkTable</class>
    <name>TOCAccountWidget</name>
    <rows>4</rows>
    <columns>2</columns>
    <homogeneous>False</homogeneous>
    <row_spacing>0</row_spacing>
    <column_spacing>0</column_spacing>

    <widget>
      <class>GtkLabel</class>
      <name>label70</name>
      <label> Screen Name: </label>
      <justify>GTK_JUSTIFY_CENTER</justify>
      <wrap>False</wrap>
      <xalign>0</xalign>
      <yalign>0.5</yalign>
      <xpad>0</xpad>
      <ypad>0</ypad>
      <child>
	<left_attach>0</left_attach>
	<right_attach>1</right_attach>
	<top_attach>0</top_attach>
	<bottom_attach>1</bottom_attach>
	<xpad>0</xpad>
	<ypad>0</ypad>
	<xexpand>False</xexpand>
	<yexpand>False</yexpand>
	<xshrink>False</xshrink>
	<yshrink>False</yshrink>
	<xfill>True</xfill>
	<yfill>False</yfill>
      </child>
    </widget>

    <widget>
      <class>GtkLabel</class>
      <name>label71</name>
      <label>    Password: </label>
      <justify>GTK_JUSTIFY_CENTER</justify>
      <wrap>False</wrap>
      <xalign>0</xalign>
      <yalign>0.5</yalign>
      <xpad>0</xpad>
      <ypad>0</ypad>
      <child>
	<left_attach>0</left_attach>
	<right_attach>1</right_attach>
	<top_attach>1</top_attach>
	<bottom_attach>2</bottom_attach>
	<xpad>0</xpad>
	<ypad>0</ypad>
	<xexpand>False</xexpand>
	<yexpand>False</yexpand>
	<xshrink>False</xshrink>
	<yshrink>False</yshrink>
	<xfill>True</xfill>
	<yfill>False</yfill>
      </child>
    </widget>

    <widget>
      <class>GtkLabel</class>
      <name>label72</name>
      <label>        Host: </label>
      <justify>GTK_JUSTIFY_CENTER</justify>
      <wrap>False</wrap>
      <xalign>0</xalign>
      <yalign>0.5</yalign>
      <xpad>0</xpad>
      <ypad>0</ypad>
      <child>
	<left_attach>0</left_attach>
	<right_attach>1</right_attach>
	<top_attach>2</top_attach>
	<bottom_attach>3</bottom_attach>
	<xpad>0</xpad>
	<ypad>0</ypad>
	<xexpand>False</xexpand>
	<yexpand>False</yexpand>
	<xshrink>False</xshrink>
	<yshrink>False</yshrink>
	<xfill>True</xfill>
	<yfill>False</yfill>
      </child>
    </widget>

    <widget>
      <class>GtkLabel</class>
      <name>label73</name>
      <label>        Port: </label>
      <justify>GTK_JUSTIFY_CENTER</justify>
      <wrap>False</wrap>
      <xalign>0</xalign>
      <yalign>0.5</yalign>
      <xpad>0</xpad>
      <ypad>0</ypad>
      <child>
	<left_attach>0</left_attach>
	<right_attach>1</right_attach>
	<top_attach>3</top_attach>
	<bottom_attach>4</bottom_attach>
	<xpad>0</xpad>
	<ypad>0</ypad>
	<xexpand>False</xexpand>
	<yexpand>False</yexpand>
	<xshrink>False</xshrink>
	<yshrink>False</yshrink>
	<xfill>True</xfill>
	<yfill>False</yfill>
      </child>
    </widget>

    <widget>
      <class>GtkEntry</class>
      <name>TOCName</name>
      <can_focus>True</can_focus>
      <editable>True</editable>
      <text_visible>True</text_visible>
      <text_max_length>0</text_max_length>
      <text></text>
      <child>
	<left_attach>1</left_attach>
	<right_attach>2</right_attach>
	<top_attach>0</top_attach>
	<bottom_attach>1</bottom_attach>
	<xpad>0</xpad>
	<ypad>0</ypad>
	<xexpand>True</xexpand>
	<yexpand>False</yexpand>
	<xshrink>False</xshrink>
	<yshrink>False</yshrink>
	<xfill>True</xfill>
	<yfill>False</yfill>
      </child>
    </widget>

    <widget>
      <class>GtkEntry</class>
      <name>TOCPass</name>
      <can_focus>True</can_focus>
      <editable>True</editable>
      <text_visible>False</text_visible>
      <text_max_length>0</text_max_length>
      <text></text>
      <child>
	<left_attach>1</left_attach>
	<right_attach>2</right_attach>
	<top_attach>1</top_attach>
	<bottom_attach>2</bottom_attach>
	<xpad>0</xpad>
	<ypad>0</ypad>
	<xexpand>True</xexpand>
	<yexpand>False</yexpand>
	<xshrink>False</xshrink>
	<yshrink>False</yshrink>
	<xfill>True</xfill>
	<yfill>False</yfill>
      </child>
    </widget>

    <widget>
      <class>GtkEntry</class>
      <name>TOCHost</name>
      <can_focus>True</can_focus>
      <editable>True</editable>
      <text_visible>True</text_visible>
      <text_max_length>0</text_max_length>
      <text>toc.oscar.aol.com</text>
      <child>
	<left_attach>1</left_attach>
	<right_attach>2</right_attach>
	<top_attach>2</top_attach>
	<bottom_attach>3</bottom_attach>
	<xpad>0</xpad>
	<ypad>0</ypad>
	<xexpand>True</xexpand>
	<yexpand>False</yexpand>
	<xshrink>False</xshrink>
	<yshrink>False</yshrink>
	<xfill>True</xfill>
	<yfill>False</yfill>
      </child>
    </widget>

    <widget>
      <class>GtkEntry</class>
      <name>TOCPort</name>
      <can_focus>True</can_focus>
      <editable>True</editable>
      <text_visible>True</text_visible>
      <text_max_length>0</text_max_length>
      <text>9898</text>
      <child>
	<left_attach>1</left_attach>
	<right_attach>2</right_attach>
	<top_attach>3</top_attach>
	<bottom_attach>4</bottom_attach>
	<xpad>0</xpad>
	<ypad>0</ypad>
	<xexpand>True</xexpand>
	<yexpand>False</yexpand>
	<xshrink>False</xshrink>
	<yshrink>False</yshrink>
	<xfill>True</xfill>
	<yfill>False</yfill>
      </child>
    </widget>
  </widget>
</widget>

<widget>
  <class>GtkWindow</class>
  <name>JoinGroupWindow</name>
  <border_width>5</border_width>
  <visible>False</visible>
  <title>Group to Join</title>
  <type>GTK_WINDOW_TOPLEVEL</type>
  <position>GTK_WIN_POS_NONE</position>
  <modal>False</modal>
  <allow_shrink>False</allow_shrink>
  <allow_grow>True</allow_grow>
  <auto_shrink>False</auto_shrink>

  <widget>
    <class>GtkVBox</class>
    <name>vbox20</name>
    <homogeneous>False</homogeneous>
    <spacing>0</spacing>

    <widget>
      <class>GtkOptionMenu</class>
      <name>AccountSelector</name>
      <can_focus>True</can_focus>
      <items>None
In
Particular
</items>
      <initial_choice>0</initial_choice>
      <child>
	<padding>0</padding>
	<expand>False</expand>
	<fill>False</fill>
      </child>
    </widget>

    <widget>
      <class>GtkHBox</class>
      <name>hbox15</name>
      <homogeneous>False</homogeneous>
      <spacing>5</spacing>
      <child>
	<padding>0</padding>
	<expand>True</expand>
	<fill>True</fill>
      </child>

      <widget>
	<class>GtkEntry</class>
	<name>GroupNameEntry</name>
	<can_focus>True</can_focus>
	<has_focus>True</has_focus>
	<signal>
	  <name>activate</name>
	  <handler>on_GroupJoinButton_clicked</handler>
	  <last_modification_time>Tue, 29 Jan 2002 13:27:18 GMT</last_modification_time>
	</signal>
	<editable>True</editable>
	<text_visible>True</text_visible>
	<text_max_length>0</text_max_length>
	<text></text>
	<child>
	  <padding>0</padding>
	  <expand>True</expand>
	  <fill>True</fill>
	</child>
      </widget>

      <widget>
	<class>GtkButton</class>
	<name>GroupJoinButton</name>
	<can_default>True</can_default>
	<has_default>True</has_default>
	<can_focus>True</can_focus>
	<signal>
	  <name>clicked</name>
	  <handler>on_GroupJoinButton_clicked</handler>
	  <last_modification_time>Tue, 29 Jan 2002 13:16:50 GMT</last_modification_time>
	</signal>
	<label>Join</label>
	<relief>GTK_RELIEF_NORMAL</relief>
	<child>
	  <padding>0</padding>
	  <expand>False</expand>
	  <fill>False</fill>
	</child>
      </widget>
    </widget>
  </widget>
</widget>

<widget>
  <class>GtkWindow</class>
  <name>UnifiedWindow</name>
  <title>Twisted Instance Messenger</title>
  <type>GTK_WINDOW_TOPLEVEL</type>
  <position>GTK_WIN_POS_NONE</position>
  <modal>False</modal>
  <allow_shrink>False</allow_shrink>
  <allow_grow>True</allow_grow>
  <auto_shrink>False</auto_shrink>

  <widget>
    <class>GtkVBox</class>
    <name>vbox25</name>
    <homogeneous>False</homogeneous>
    <spacing>0</spacing>

    <widget>
      <class>GtkHBox</class>
      <name>hbox28</name>
      <homogeneous>False</homogeneous>
      <spacing>0</spacing>
      <child>
	<padding>0</padding>
	<expand>False</expand>
	<fill>True</fill>
      </child>

      <widget>
	<class>GtkButton</class>
	<name>button74</name>
	<can_focus>True</can_focus>
	<label>&gt;</label>
	<relief>GTK_RELIEF_NORMAL</relief>
	<child>
	  <padding>0</padding>
	  <expand>False</expand>
	  <fill>False</fill>
	</child>
      </widget>

      <widget>
	<class>GtkEntry</class>
	<name>entry3</name>
	<can_focus>True</can_focus>
	<editable>True</editable>
	<text_visible>True</text_visible>
	<text_max_length>0</text_max_length>
	<text></text>
	<child>
	  <padding>0</padding>
	  <expand>True</expand>
	  <fill>True</fill>
	</child>
      </widget>

      <widget>
	<class>GtkOptionMenu</class>
	<name>optionmenu3</name>
	<items>List
Of
Online
Accounts
</items>
	<initial_choice>0</initial_choice>
	<child>
	  <padding>0</padding>
	  <expand>False</expand>
	  <fill>False</fill>
	</child>
      </widget>

      <widget>
	<class>GtkOptionMenu</class>
	<name>optionmenu4</name>
	<can_focus>True</can_focus>
	<items>Contact
Person
Group
Account
</items>
	<initial_choice>0</initial_choice>
	<child>
	  <padding>0</padding>
	  <expand>False</expand>
	  <fill>False</fill>
	</child>
      </widget>
    </widget>

    <widget>
      <class>GtkHPaned</class>
      <name>hpaned1</name>
      <handle_size>10</handle_size>
      <gutter_size>6</gutter_size>
      <position>0</position>
      <child>
	<padding>0</padding>
	<expand>True</expand>
	<fill>True</fill>
      </child>

      <widget>
	<class>GtkVBox</class>
	<name>vbox26</name>
	<homogeneous>False</homogeneous>
	<spacing>0</spacing>
	<child>
	  <shrink>True</shrink>
	  <resize>False</resize>
	</child>

	<widget>
	  <class>GtkFrame</class>
	  <name>frame7</name>
	  <border_width>2</border_width>
	  <label>Accounts</label>
	  <label_xalign>0</label_xalign>
	  <shadow_type>GTK_SHADOW_ETCHED_IN</shadow_type>
	  <child>
	    <padding>0</padding>
	    <expand>True</expand>
	    <fill>True</fill>
	  </child>

	  <widget>
	    <class>GtkVBox</class>
	    <name>vbox27</name>
	    <homogeneous>False</homogeneous>
	    <spacing>0</spacing>

	    <widget>
	      <class>GtkScrolledWindow</class>
	      <name>scrolledwindow18</name>
	      <hscrollbar_policy>GTK_POLICY_AUTOMATIC</hscrollbar_policy>
	      <vscrollbar_policy>GTK_POLICY_AUTOMATIC</vscrollbar_policy>
	      <hupdate_policy>GTK_UPDATE_CONTINUOUS</hupdate_policy>
	      <vupdate_policy>GTK_UPDATE_CONTINUOUS</vupdate_policy>
	      <child>
		<padding>0</padding>
		<expand>True</expand>
		<fill>True</fill>
	      </child>

	      <widget>
		<class>GtkCList</class>
		<name>clist4</name>
		<columns>4</columns>
		<column_widths>18,25,25,80</column_widths>
		<selection_mode>GTK_SELECTION_SINGLE</selection_mode>
		<show_titles>False</show_titles>
		<shadow_type>GTK_SHADOW_IN</shadow_type>

		<widget>
		  <class>GtkLabel</class>
		  <child_name>CList:title</child_name>
		  <name>label95</name>
		  <label>label87</label>
		  <justify>GTK_JUSTIFY_CENTER</justify>
		  <wrap>False</wrap>
		  <xalign>0.5</xalign>
		  <yalign>0.5</yalign>
		  <xpad>0</xpad>
		  <ypad>0</ypad>
		</widget>

		<widget>
		  <class>GtkLabel</class>
		  <child_name>CList:title</child_name>
		  <name>label96</name>
		  <label>label88</label>
		  <justify>GTK_JUSTIFY_CENTER</justify>
		  <wrap>False</wrap>
		  <xalign>0.5</xalign>
		  <yalign>0.5</yalign>
		  <xpad>0</xpad>
		  <ypad>0</ypad>
		</widget>

		<widget>
		  <class>GtkLabel</class>
		  <child_name>CList:title</child_name>
		  <name>label97</name>
		  <label>label89</label>
		  <justify>GTK_JUSTIFY_CENTER</justify>
		  <wrap>False</wrap>
		  <xalign>0.5</xalign>
		  <yalign>0.5</yalign>
		  <xpad>0</xpad>
		  <ypad>0</ypad>
		</widget>

		<widget>
		  <class>GtkLabel</class>
		  <child_name>CList:title</child_name>
		  <name>label98</name>
		  <label>label90</label>
		  <justify>GTK_JUSTIFY_CENTER</justify>
		  <wrap>False</wrap>
		  <xalign>0.5</xalign>
		  <yalign>0.5</yalign>
		  <xpad>0</xpad>
		  <ypad>0</ypad>
		</widget>
	      </widget>
	    </widget>

	    <widget>
	      <class>GtkHBox</class>
	      <name>hbox23</name>
	      <homogeneous>True</homogeneous>
	      <spacing>2</spacing>
	      <child>
		<padding>0</padding>
		<expand>True</expand>
		<fill>True</fill>
	      </child>

	      <widget>
		<class>GtkButton</class>
		<name>button65</name>
		<label>New</label>
		<relief>GTK_RELIEF_NORMAL</relief>
		<child>
		  <padding>0</padding>
		  <expand>True</expand>
		  <fill>True</fill>
		</child>
	      </widget>

	      <widget>
		<class>GtkButton</class>
		<name>button66</name>
		<label>Delete</label>
		<relief>GTK_RELIEF_NORMAL</relief>
		<child>
		  <padding>0</padding>
		  <expand>True</expand>
		  <fill>True</fill>
		</child>
	      </widget>

	      <widget>
		<class>GtkButton</class>
		<name>button67</name>
		<label>Connect</label>
		<relief>GTK_RELIEF_NORMAL</relief>
		<child>
		  <padding>0</padding>
		  <expand>True</expand>
		  <fill>True</fill>
		</child>
	      </widget>
	    </widget>
	  </widget>
	</widget>

	<widget>
	  <class>GtkFrame</class>
	  <name>frame8</name>
	  <border_width>2</border_width>
	  <label>Contacts</label>
	  <label_xalign>0</label_xalign>
	  <shadow_type>GTK_SHADOW_ETCHED_IN</shadow_type>
	  <child>
	    <padding>0</padding>
	    <expand>True</expand>
	    <fill>True</fill>
	  </child>

	  <widget>
	    <class>GtkVBox</class>
	    <name>vbox28</name>
	    <homogeneous>False</homogeneous>
	    <spacing>0</spacing>

	    <widget>
	      <class>GtkScrolledWindow</class>
	      <name>scrolledwindow19</name>
	      <hscrollbar_policy>GTK_POLICY_AUTOMATIC</hscrollbar_policy>
	      <vscrollbar_policy>GTK_POLICY_AUTOMATIC</vscrollbar_policy>
	      <hupdate_policy>GTK_UPDATE_CONTINUOUS</hupdate_policy>
	      <vupdate_policy>GTK_UPDATE_CONTINUOUS</vupdate_policy>
	      <child>
		<padding>0</padding>
		<expand>True</expand>
		<fill>True</fill>
	      </child>

	      <widget>
		<class>GtkCList</class>
		<name>clist5</name>
		<columns>3</columns>
		<column_widths>18,17,80</column_widths>
		<selection_mode>GTK_SELECTION_SINGLE</selection_mode>
		<show_titles>False</show_titles>
		<shadow_type>GTK_SHADOW_IN</shadow_type>

		<widget>
		  <class>GtkLabel</class>
		  <child_name>CList:title</child_name>
		  <name>label99</name>
		  <label>label84</label>
		  <justify>GTK_JUSTIFY_CENTER</justify>
		  <wrap>False</wrap>
		  <xalign>0.5</xalign>
		  <yalign>0.5</yalign>
		  <xpad>0</xpad>
		  <ypad>0</ypad>
		</widget>

		<widget>
		  <class>GtkLabel</class>
		  <child_name>CList:title</child_name>
		  <name>label100</name>
		  <label>label85</label>
		  <justify>GTK_JUSTIFY_CENTER</justify>
		  <wrap>False</wrap>
		  <xalign>0.5</xalign>
		  <yalign>0.5</yalign>
		  <xpad>0</xpad>
		  <ypad>0</ypad>
		</widget>

		<widget>
		  <class>GtkLabel</class>
		  <child_name>CList:title</child_name>
		  <name>label101</name>
		  <label>label86</label>
		  <justify>GTK_JUSTIFY_CENTER</justify>
		  <wrap>False</wrap>
		  <xalign>0.5</xalign>
		  <yalign>0.5</yalign>
		  <xpad>0</xpad>
		  <ypad>0</ypad>
		</widget>
	      </widget>
	    </widget>

	    <widget>
	      <class>GtkHBox</class>
	      <name>hbox24</name>
	      <homogeneous>True</homogeneous>
	      <spacing>2</spacing>
	      <child>
		<padding>0</padding>
		<expand>False</expand>
		<fill>True</fill>
	      </child>

	      <widget>
		<class>GtkButton</class>
		<name>button68</name>
		<can_focus>True</can_focus>
		<label>Talk</label>
		<relief>GTK_RELIEF_NORMAL</relief>
		<child>
		  <padding>0</padding>
		  <expand>True</expand>
		  <fill>True</fill>
		</child>
	      </widget>

	      <widget>
		<class>GtkButton</class>
		<name>button69</name>
		<can_focus>True</can_focus>
		<label>Info</label>
		<relief>GTK_RELIEF_NORMAL</relief>
		<child>
		  <padding>0</padding>
		  <expand>True</expand>
		  <fill>True</fill>
		</child>
	      </widget>

	      <widget>
		<class>GtkButton</class>
		<name>button70</name>
		<can_focus>True</can_focus>
		<label>Add</label>
		<relief>GTK_RELIEF_NORMAL</relief>
		<child>
		  <padding>0</padding>
		  <expand>True</expand>
		  <fill>True</fill>
		</child>
	      </widget>

	      <widget>
		<class>GtkButton</class>
		<name>button71</name>
		<can_focus>True</can_focus>
		<label>Remove</label>
		<relief>GTK_RELIEF_NORMAL</relief>
		<child>
		  <padding>0</padding>
		  <expand>False</expand>
		  <fill>False</fill>
		</child>
	      </widget>
	    </widget>
	  </widget>
	</widget>

	<widget>
	  <class>GtkFrame</class>
	  <name>frame9</name>
	  <border_width>2</border_width>
	  <label>Groups</label>
	  <label_xalign>0</label_xalign>
	  <shadow_type>GTK_SHADOW_ETCHED_IN</shadow_type>
	  <child>
	    <padding>0</padding>
	    <expand>True</expand>
	    <fill>True</fill>
	  </child>

	  <widget>
	    <class>GtkVBox</class>
	    <name>vbox29</name>
	    <homogeneous>False</homogeneous>
	    <spacing>0</spacing>

	    <widget>
	      <class>GtkScrolledWindow</class>
	      <name>scrolledwindow20</name>
	      <hscrollbar_policy>GTK_POLICY_AUTOMATIC</hscrollbar_policy>
	      <vscrollbar_policy>GTK_POLICY_AUTOMATIC</vscrollbar_policy>
	      <hupdate_policy>GTK_UPDATE_CONTINUOUS</hupdate_policy>
	      <vupdate_policy>GTK_UPDATE_CONTINUOUS</vupdate_policy>
	      <child>
		<padding>0</padding>
		<expand>True</expand>
		<fill>True</fill>
	      </child>

	      <widget>
		<class>GtkCList</class>
		<name>clist6</name>
		<columns>3</columns>
		<column_widths>21,75,80</column_widths>
		<selection_mode>GTK_SELECTION_SINGLE</selection_mode>
		<show_titles>False</show_titles>
		<shadow_type>GTK_SHADOW_IN</shadow_type>

		<widget>
		  <class>GtkLabel</class>
		  <child_name>CList:title</child_name>
		  <name>label102</name>
		  <label>label91</label>
		  <justify>GTK_JUSTIFY_CENTER</justify>
		  <wrap>False</wrap>
		  <xalign>0.5</xalign>
		  <yalign>0.5</yalign>
		  <xpad>0</xpad>
		  <ypad>0</ypad>
		</widget>

		<widget>
		  <class>GtkLabel</class>
		  <child_name>CList:title</child_name>
		  <name>label103</name>
		  <label>label92</label>
		  <justify>GTK_JUSTIFY_CENTER</justify>
		  <wrap>False</wrap>
		  <xalign>0.5</xalign>
		  <yalign>0.5</yalign>
		  <xpad>0</xpad>
		  <ypad>0</ypad>
		</widget>

		<widget>
		  <class>GtkLabel</class>
		  <child_name>CList:title</child_name>
		  <name>label104</name>
		  <label>label93</label>
		  <justify>GTK_JUSTIFY_CENTER</justify>
		  <wrap>False</wrap>
		  <xalign>0.5</xalign>
		  <yalign>0.5</yalign>
		  <xpad>0</xpad>
		  <ypad>0</ypad>
		</widget>
	      </widget>
	    </widget>

	    <widget>
	      <class>GtkHBox</class>
	      <name>hbox27</name>
	      <homogeneous>True</homogeneous>
	      <spacing>2</spacing>
	      <child>
		<padding>0</padding>
		<expand>False</expand>
		<fill>True</fill>
	      </child>

	      <widget>
		<class>GtkButton</class>
		<name>button72</name>
		<label>Join</label>
		<relief>GTK_RELIEF_NORMAL</relief>
		<child>
		  <padding>0</padding>
		  <expand>True</expand>
		  <fill>True</fill>
		</child>
	      </widget>

	      <widget>
		<class>GtkButton</class>
		<name>button73</name>
		<label>Leave</label>
		<relief>GTK_RELIEF_NORMAL</relief>
		<child>
		  <padding>0</padding>
		  <expand>True</expand>
		  <fill>True</fill>
		</child>
	      </widget>
	    </widget>
	  </widget>
	</widget>

	<widget>
	  <class>GtkHSeparator</class>
	  <name>hseparator2</name>
	  <child>
	    <padding>0</padding>
	    <expand>True</expand>
	    <fill>True</fill>
	  </child>
	</widget>

	<widget>
	  <class>GtkLabel</class>
	  <name>label105</name>
	  <label>Twisted IM V. %s</label>
	  <justify>GTK_JUSTIFY_CENTER</justify>
	  <wrap>False</wrap>
	  <xalign>0.5</xalign>
	  <yalign>0.5</yalign>
	  <xpad>0</xpad>
	  <ypad>3</ypad>
	  <child>
	    <padding>0</padding>
	    <expand>False</expand>
	    <fill>False</fill>
	  </child>
	</widget>
      </widget>

      <widget>
	<class>GtkLabel</class>
	<name>label106</name>
	<label>This
Space
Left
Intentionally
Blank
(Here is where the UI for the currently
selected element
for interaction
will go.)</label>
	<justify>GTK_JUSTIFY_CENTER</justify>
	<wrap>False</wrap>
	<xalign>0.5</xalign>
	<yalign>0.5</yalign>
	<xpad>0</xpad>
	<ypad>0</ypad>
	<child>
	  <shrink>True</shrink>
	  <resize>True</resize>
	</child>
      </widget>
    </widget>
  </widget>
</widget>

</GTK-Interface>
