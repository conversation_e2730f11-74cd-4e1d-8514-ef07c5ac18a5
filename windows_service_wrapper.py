#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Windows服务包装器
将DataTransmission包装成Windows服务
"""

import sys
import os
import time
import logging
import threading
from pathlib import Path

# 添加当前目录到Python路径
current_dir = Path(__file__).parent.absolute()
sys.path.insert(0, str(current_dir))

try:
    import win32serviceutil
    import win32service
    import win32event
    import servicemanager
    WINDOWS_SERVICE_AVAILABLE = True
except ImportError:
    WINDOWS_SERVICE_AVAILABLE = False
    print("警告: pywin32未安装，无法创建Windows服务")
    print("安装命令: pip install pywin32")

# 导入主程序模块
try:
    from main import DataTransmissionClient
except ImportError as e:
    print(f"错误: 无法导入主程序模块: {e}")
    sys.exit(1)

class DataTransmissionService(win32serviceutil.ServiceFramework):
    """DataTransmission Windows服务类"""
    
    _svc_name_ = "DataTransmissionService"
    _svc_display_name_ = "Data Transmission Service"
    _svc_description_ = "数据传输服务 - 提供HTTP接口、二维码生成和摄像头监控功能"
    
    def __init__(self, args):
        win32serviceutil.ServiceFramework.__init__(self, args)
        self.hWaitStop = win32event.CreateEvent(None, 0, 0, None)
        self.is_running = True
        self.client = None
        
        # 设置日志
        self.setup_logging()
    
    def setup_logging(self):
        """设置日志配置"""
        log_dir = Path(current_dir) / "logs"
        log_dir.mkdir(exist_ok=True)
        
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_dir / 'service.log', encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        
        # 记录服务启动
        servicemanager.LogMsg(
            servicemanager.EVENTLOG_INFORMATION_TYPE,
            servicemanager.PYS_SERVICE_STARTED,
            (self._svc_name_, '')
        )
    
    def SvcStop(self):
        """停止服务"""
        self.ReportServiceStatus(win32service.SERVICE_STOP_PENDING)
        win32event.SetEvent(self.hWaitStop)
        self.is_running = False
        
        # 停止主程序
        if self.client:
            try:
                self.client.stop_services()
                logging.info("DataTransmission服务已停止")
            except Exception as e:
                logging.error(f"停止服务时出错: {e}")
        
        # 记录服务停止
        servicemanager.LogMsg(
            servicemanager.EVENTLOG_INFORMATION_TYPE,
            servicemanager.PYS_SERVICE_STOPPED,
            (self._svc_name_, '')
        )
    
    def SvcDoRun(self):
        """运行服务"""
        try:
            # 记录服务启动
            logging.info("DataTransmission服务正在启动...")
            
            # 创建并启动主程序
            self.client = DataTransmissionClient()
            
            # 在单独线程中运行主程序
            def run_client():
                try:
                    self.client.run()
                except Exception as e:
                    logging.error(f"主程序运行出错: {e}")
                    self.SvcStop()
            
            client_thread = threading.Thread(target=run_client, daemon=True)
            client_thread.start()
            
            logging.info("DataTransmission服务已启动")
            
            # 等待停止信号
            win32event.WaitForSingleObject(self.hWaitStop, win32event.INFINITE)
            
        except Exception as e:
            logging.error(f"服务运行出错: {e}")
            # 记录错误
            servicemanager.LogErrorMsg(f"服务运行出错: {e}")

def install_service():
    """安装Windows服务"""
    if not WINDOWS_SERVICE_AVAILABLE:
        print("错误: 无法安装Windows服务，请先安装pywin32")
        print("安装命令: pip install pywin32")
        return False
    
    try:
        win32serviceutil.InstallService(
            DataTransmissionService,
            DataTransmissionService._svc_name_,
            DataTransmissionService._svc_display_name_,
            description=DataTransmissionService._svc_description_
        )
        print(f"✓ Windows服务 '{DataTransmissionService._svc_display_name_}' 安装成功")
        return True
    except Exception as e:
        print(f"✗ 服务安装失败: {e}")
        return False

def uninstall_service():
    """卸载Windows服务"""
    if not WINDOWS_SERVICE_AVAILABLE:
        print("错误: 无法卸载Windows服务，请先安装pywin32")
        return False
    
    try:
        win32serviceutil.RemoveService(DataTransmissionService._svc_name_)
        print(f"✓ Windows服务 '{DataTransmissionService._svc_display_name_}' 卸载成功")
        return True
    except Exception as e:
        print(f"✗ 服务卸载失败: {e}")
        return False

def start_service():
    """启动Windows服务"""
    if not WINDOWS_SERVICE_AVAILABLE:
        print("错误: 无法启动Windows服务，请先安装pywin32")
        return False
    
    try:
        win32serviceutil.StartService(DataTransmissionService._svc_name_)
        print(f"✓ Windows服务 '{DataTransmissionService._svc_display_name_}' 启动成功")
        return True
    except Exception as e:
        print(f"✗ 服务启动失败: {e}")
        return False

def stop_service():
    """停止Windows服务"""
    if not WINDOWS_SERVICE_AVAILABLE:
        print("错误: 无法停止Windows服务，请先安装pywin32")
        return False
    
    try:
        win32serviceutil.StopService(DataTransmissionService._svc_name_)
        print(f"✓ Windows服务 '{DataTransmissionService._svc_display_name_}' 停止成功")
        return True
    except Exception as e:
        print(f"✗ 服务停止失败: {e}")
        return False

def main():
    """主函数"""
    if len(sys.argv) == 1:
        # 作为服务运行
        if WINDOWS_SERVICE_AVAILABLE:
            servicemanager.Initialize()
            servicemanager.PrepareToHostSingle(DataTransmissionService)
            servicemanager.StartServiceCtrlDispatcher()
        else:
            print("错误: 无法启动Windows服务，请先安装pywin32")
            print("或者直接运行: python main.py")
    else:
        # 命令行参数处理
        command = sys.argv[1].lower()
        
        if command == 'install':
            install_service()
        elif command == 'uninstall':
            uninstall_service()
        elif command == 'start':
            start_service()
        elif command == 'stop':
            stop_service()
        elif command == 'restart':
            stop_service()
            time.sleep(2)
            start_service()
        else:
            print("用法:")
            print("  python windows_service_wrapper.py install   - 安装服务")
            print("  python windows_service_wrapper.py uninstall - 卸载服务")
            print("  python windows_service_wrapper.py start     - 启动服务")
            print("  python windows_service_wrapper.py stop      - 停止服务")
            print("  python windows_service_wrapper.py restart   - 重启服务")

if __name__ == '__main__':
    main()
