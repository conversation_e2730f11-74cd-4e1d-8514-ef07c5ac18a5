('E:\\01.代码\\嘉兴公安自主申报\\DataTransmission\\dist\\DataTransmission.exe',
 True,
 False,
 False,
 'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\PyInstaller\\bootloader\\images\\icon-console.ico',
 None,
 <PERSON>alse,
 False,
 b'<?xml version="1.0" encoding="UTF-8" standalone="yes"?>\n<assembly xmlns='
 b'"urn:schemas-microsoft-com:asm.v1" manifestVersion="1.0">\n  <trustInfo x'
 b'mlns="urn:schemas-microsoft-com:asm.v3">\n    <security>\n      <requested'
 b'Privileges>\n        <requestedExecutionLevel level="asInvoker" uiAccess='
 b'"false"/>\n      </requestedPrivileges>\n    </security>\n  </trustInfo>\n  '
 b'<compatibility xmlns="urn:schemas-microsoft-com:compatibility.v1">\n    <'
 b'application>\n      <supportedOS Id="{e2011457-1546-43c5-a5fe-008deee3d3f'
 b'0}"/>\n      <supportedOS Id="{35138b9a-5d96-4fbd-8e2d-a2440225f93a}"/>\n '
 b'     <supportedOS Id="{4a2f28e3-53b9-4441-ba9c-d69d4a4a6e38}"/>\n      <s'
 b'upportedOS Id="{1f676c76-80e1-4239-95bb-83d0f6d0da78}"/>\n      <supporte'
 b'dOS Id="{8e0f7a12-bfb3-4fe8-b9a5-48fd50a15a9a}"/>\n    </application>\n  <'
 b'/compatibility>\n  <application xmlns="urn:schemas-microsoft-com:asm.v3">'
 b'\n    <windowsSettings>\n      <longPathAware xmlns="http://schemas.micros'
 b'oft.com/SMI/2016/WindowsSettings">true</longPathAware>\n    </windowsSett'
 b'ings>\n  </application>\n  <dependency>\n    <dependentAssembly>\n      <ass'
 b'emblyIdentity type="win32" name="Microsoft.Windows.Common-Controls" version='
 b'"6.0.0.0" processorArchitecture="*" publicKeyToken="6595b64144ccf1df" langua'
 b'ge="*"/>\n    </dependentAssembly>\n  </dependency>\n</assembly>',
 True,
 False,
 None,
 None,
 None,
 'E:\\01.代码\\嘉兴公安自主申报\\DataTransmission\\build\\DataTransmission_optimized\\DataTransmission.pkg',
 [('pyi-contents-directory _internal', '', 'OPTION'),
  ('PYZ-00.pyz',
   'E:\\01.代码\\嘉兴公安自主申报\\DataTransmission\\build\\DataTransmission_optimized\\PYZ-00.pyz',
   'PYZ'),
  ('struct',
   'E:\\01.代码\\嘉兴公安自主申报\\DataTransmission\\build\\DataTransmission_optimized\\localpycs\\struct.pyc',
   'PYMODULE'),
  ('pyimod01_archive',
   'E:\\01.代码\\嘉兴公安自主申报\\DataTransmission\\build\\DataTransmission_optimized\\localpycs\\pyimod01_archive.pyc',
   'PYMODULE'),
  ('pyimod02_importers',
   'E:\\01.代码\\嘉兴公安自主申报\\DataTransmission\\build\\DataTransmission_optimized\\localpycs\\pyimod02_importers.pyc',
   'PYMODULE'),
  ('pyimod03_ctypes',
   'E:\\01.代码\\嘉兴公安自主申报\\DataTransmission\\build\\DataTransmission_optimized\\localpycs\\pyimod03_ctypes.pyc',
   'PYMODULE'),
  ('pyimod04_pywin32',
   'E:\\01.代码\\嘉兴公安自主申报\\DataTransmission\\build\\DataTransmission_optimized\\localpycs\\pyimod04_pywin32.pyc',
   'PYMODULE'),
  ('pyiboot01_bootstrap',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\PyInstaller\\loader\\pyiboot01_bootstrap.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'D:\\anaconda3\\envs\\dt\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   'D:\\anaconda3\\envs\\dt\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth__tkinter',
   'D:\\anaconda3\\envs\\dt\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth__tkinter.py',
   'PYSOURCE'),
  ('main', 'E:\\01.代码\\嘉兴公安自主申报\\DataTransmission\\main.py', 'PYSOURCE'),
  ('python310.dll', 'D:\\anaconda3\\envs\\dt\\python310.dll', 'BINARY'),
  ('numpy\\.libs\\libopenblas64__v0.3.21-gcc_10_3_0.dll',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\numpy\\.libs\\libopenblas64__v0.3.21-gcc_10_3_0.dll',
   'BINARY'),
  ('cv2\\opencv_videoio_ffmpeg481_64.dll',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\cv2\\opencv_videoio_ffmpeg481_64.dll',
   'BINARY'),
  ('_lzma.pyd', 'D:\\anaconda3\\envs\\dt\\DLLs\\_lzma.pyd', 'EXTENSION'),
  ('_bz2.pyd', 'D:\\anaconda3\\envs\\dt\\DLLs\\_bz2.pyd', 'EXTENSION'),
  ('select.pyd', 'D:\\anaconda3\\envs\\dt\\DLLs\\select.pyd', 'EXTENSION'),
  ('_decimal.pyd', 'D:\\anaconda3\\envs\\dt\\DLLs\\_decimal.pyd', 'EXTENSION'),
  ('unicodedata.pyd',
   'D:\\anaconda3\\envs\\dt\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('_uuid.pyd', 'D:\\anaconda3\\envs\\dt\\DLLs\\_uuid.pyd', 'EXTENSION'),
  ('_hashlib.pyd', 'D:\\anaconda3\\envs\\dt\\DLLs\\_hashlib.pyd', 'EXTENSION'),
  ('_ssl.pyd', 'D:\\anaconda3\\envs\\dt\\DLLs\\_ssl.pyd', 'EXTENSION'),
  ('_socket.pyd', 'D:\\anaconda3\\envs\\dt\\DLLs\\_socket.pyd', 'EXTENSION'),
  ('_sqlite3.pyd', 'D:\\anaconda3\\envs\\dt\\DLLs\\_sqlite3.pyd', 'EXTENSION'),
  ('charset_normalizer\\md__mypyc.cp310-win_amd64.pyd',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\charset_normalizer\\md__mypyc.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md.cp310-win_amd64.pyd',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\charset_normalizer\\md.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\core\\_multiarray_tests.cp310-win_amd64.pyd',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\numpy\\core\\_multiarray_tests.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\linalg\\lapack_lite.cp310-win_amd64.pyd',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\numpy\\linalg\\lapack_lite.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\linalg\\_umath_linalg.cp310-win_amd64.pyd',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\numpy\\linalg\\_umath_linalg.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\mtrand.cp310-win_amd64.pyd',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\numpy\\random\\mtrand.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_sfc64.cp310-win_amd64.pyd',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\numpy\\random\\_sfc64.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_philox.cp310-win_amd64.pyd',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\numpy\\random\\_philox.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_pcg64.cp310-win_amd64.pyd',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\numpy\\random\\_pcg64.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_mt19937.cp310-win_amd64.pyd',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\numpy\\random\\_mt19937.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\bit_generator.cp310-win_amd64.pyd',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\numpy\\random\\bit_generator.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_generator.cp310-win_amd64.pyd',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\numpy\\random\\_generator.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_bounded_integers.cp310-win_amd64.pyd',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\numpy\\random\\_bounded_integers.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_common.cp310-win_amd64.pyd',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\numpy\\random\\_common.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\fft\\_pocketfft_internal.cp310-win_amd64.pyd',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\numpy\\fft\\_pocketfft_internal.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\core\\_multiarray_umath.cp310-win_amd64.pyd',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\numpy\\core\\_multiarray_umath.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('_ctypes.pyd', 'D:\\anaconda3\\envs\\dt\\DLLs\\_ctypes.pyd', 'EXTENSION'),
  ('markupsafe\\_speedups.cp310-win_amd64.pyd',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\markupsafe\\_speedups.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('_zoneinfo.pyd',
   'D:\\anaconda3\\envs\\dt\\DLLs\\_zoneinfo.pyd',
   'EXTENSION'),
  ('_tkinter.pyd', 'D:\\anaconda3\\envs\\dt\\DLLs\\_tkinter.pyd', 'EXTENSION'),
  ('PIL\\_imagingtk.cp310-win_amd64.pyd',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\PIL\\_imagingtk.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingft.cp310-win_amd64.pyd',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\PIL\\_imagingft.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_webp.cp310-win_amd64.pyd',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\PIL\\_webp.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingcms.cp310-win_amd64.pyd',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\PIL\\_imagingcms.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imaging.cp310-win_amd64.pyd',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\PIL\\_imaging.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('cv2\\cv2.pyd',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\cv2\\cv2.pyd',
   'EXTENSION'),
  ('_mysql_connector.cp310-win_amd64.pyd',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\_mysql_connector.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('api-ms-win-crt-stdio-l1-1-0.dll',
   'D:\\anaconda3\\envs\\dt\\api-ms-win-crt-stdio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-string-l1-1-0.dll',
   'D:\\anaconda3\\envs\\dt\\api-ms-win-crt-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-math-l1-1-0.dll',
   'D:\\anaconda3\\envs\\dt\\api-ms-win-crt-math-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-time-l1-1-0.dll',
   'D:\\anaconda3\\envs\\dt\\api-ms-win-crt-time-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-filesystem-l1-1-0.dll',
   'D:\\anaconda3\\envs\\dt\\api-ms-win-crt-filesystem-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-environment-l1-1-0.dll',
   'D:\\anaconda3\\envs\\dt\\api-ms-win-crt-environment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-runtime-l1-1-0.dll',
   'D:\\anaconda3\\envs\\dt\\api-ms-win-crt-runtime-l1-1-0.dll',
   'BINARY'),
  ('zlib.dll', 'D:\\anaconda3\\envs\\dt\\zlib.dll', 'BINARY'),
  ('api-ms-win-crt-convert-l1-1-0.dll',
   'D:\\anaconda3\\envs\\dt\\api-ms-win-crt-convert-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-locale-l1-1-0.dll',
   'D:\\anaconda3\\envs\\dt\\api-ms-win-crt-locale-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-conio-l1-1-0.dll',
   'D:\\anaconda3\\envs\\dt\\api-ms-win-crt-conio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-process-l1-1-0.dll',
   'D:\\anaconda3\\envs\\dt\\api-ms-win-crt-process-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-heap-l1-1-0.dll',
   'D:\\anaconda3\\envs\\dt\\api-ms-win-crt-heap-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140.dll', 'D:\\anaconda3\\envs\\dt\\VCRUNTIME140.dll', 'BINARY'),
  ('api-ms-win-crt-private-l1-1-0.dll',
   'D:\\anaconda3\\envs\\dt\\api-ms-win-crt-private-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-utility-l1-1-0.dll',
   'D:\\anaconda3\\envs\\dt\\api-ms-win-crt-utility-l1-1-0.dll',
   'BINARY'),
  ('liblzma.dll',
   'D:\\anaconda3\\envs\\dt\\Library\\bin\\liblzma.dll',
   'BINARY'),
  ('LIBBZ2.dll', 'D:\\anaconda3\\envs\\dt\\Library\\bin\\LIBBZ2.dll', 'BINARY'),
  ('libcrypto-3-x64.dll',
   'D:\\anaconda3\\envs\\dt\\Library\\bin\\libcrypto-3-x64.dll',
   'BINARY'),
  ('libssl-3-x64.dll',
   'D:\\anaconda3\\envs\\dt\\Library\\bin\\libssl-3-x64.dll',
   'BINARY'),
  ('sqlite3.dll',
   'D:\\anaconda3\\envs\\dt\\Library\\bin\\sqlite3.dll',
   'BINARY'),
  ('ffi.dll', 'D:\\anaconda3\\envs\\dt\\Library\\bin\\ffi.dll', 'BINARY'),
  ('tcl86t.dll', 'D:\\anaconda3\\envs\\dt\\Library\\bin\\tcl86t.dll', 'BINARY'),
  ('tk86t.dll', 'D:\\anaconda3\\envs\\dt\\Library\\bin\\tk86t.dll', 'BINARY'),
  ('python3.dll', 'D:\\anaconda3\\envs\\dt\\python3.dll', 'BINARY'),
  ('libmysql.dll',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\libmysql.dll',
   'BINARY'),
  ('ucrtbase.dll', 'D:\\anaconda3\\envs\\dt\\ucrtbase.dll', 'BINARY'),
  ('MSVCP140.dll', 'D:\\anaconda3\\envs\\dt\\MSVCP140.dll', 'BINARY'),
  ('VCRUNTIME140_1.dll',
   'D:\\anaconda3\\envs\\dt\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('api-ms-win-core-memory-l1-1-0.dll',
   'D:\\anaconda3\\envs\\dt\\api-ms-win-core-memory-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-heap-l1-1-0.dll',
   'D:\\anaconda3\\envs\\dt\\api-ms-win-core-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-debug-l1-1-0.dll',
   'D:\\anaconda3\\envs\\dt\\api-ms-win-core-debug-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-localization-l1-2-0.dll',
   'D:\\anaconda3\\envs\\dt\\api-ms-win-core-localization-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-2-0.dll',
   'D:\\anaconda3\\envs\\dt\\api-ms-win-core-synch-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-rtlsupport-l1-1-0.dll',
   'D:\\anaconda3\\envs\\dt\\api-ms-win-core-rtlsupport-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-2-0.dll',
   'D:\\anaconda3\\envs\\dt\\api-ms-win-core-file-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-timezone-l1-1-0.dll',
   'D:\\anaconda3\\envs\\dt\\api-ms-win-core-timezone-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-1-0.dll',
   'D:\\anaconda3\\envs\\dt\\api-ms-win-core-file-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-profile-l1-1-0.dll',
   'D:\\anaconda3\\envs\\dt\\api-ms-win-core-profile-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-util-l1-1-0.dll',
   'D:\\anaconda3\\envs\\dt\\api-ms-win-core-util-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-libraryloader-l1-1-0.dll',
   'D:\\anaconda3\\envs\\dt\\api-ms-win-core-libraryloader-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-datetime-l1-1-0.dll',
   'D:\\anaconda3\\envs\\dt\\api-ms-win-core-datetime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-interlocked-l1-1-0.dll',
   'D:\\anaconda3\\envs\\dt\\api-ms-win-core-interlocked-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-console-l1-1-0.dll',
   'D:\\anaconda3\\envs\\dt\\api-ms-win-core-console-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-namedpipe-l1-1-0.dll',
   'D:\\anaconda3\\envs\\dt\\api-ms-win-core-namedpipe-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l2-1-0.dll',
   'D:\\anaconda3\\envs\\dt\\api-ms-win-core-file-l2-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-string-l1-1-0.dll',
   'D:\\anaconda3\\envs\\dt\\api-ms-win-core-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-1-0.dll',
   'D:\\anaconda3\\envs\\dt\\api-ms-win-core-synch-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-errorhandling-l1-1-0.dll',
   'D:\\anaconda3\\envs\\dt\\api-ms-win-core-errorhandling-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-1.dll',
   'D:\\anaconda3\\envs\\dt\\api-ms-win-core-processthreads-l1-1-1.dll',
   'BINARY'),
  ('api-ms-win-core-fibers-l1-1-0.dll',
   'D:\\anaconda3\\envs\\dt\\api-ms-win-core-fibers-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processenvironment-l1-1-0.dll',
   'D:\\anaconda3\\envs\\dt\\api-ms-win-core-processenvironment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-0.dll',
   'D:\\anaconda3\\envs\\dt\\api-ms-win-core-processthreads-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-handle-l1-1-0.dll',
   'D:\\anaconda3\\envs\\dt\\api-ms-win-core-handle-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-sysinfo-l1-1-0.dll',
   'D:\\anaconda3\\envs\\dt\\api-ms-win-core-sysinfo-l1-1-0.dll',
   'BINARY'),
  ('README.md', 'E:\\01.代码\\嘉兴公安自主申报\\DataTransmission\\README.md', 'DATA'),
  ('config.py', 'E:\\01.代码\\嘉兴公安自主申报\\DataTransmission\\config.py', 'DATA'),
  ('certifi\\cacert.pem',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\certifi\\cacert.pem',
   'DATA'),
  ('certifi\\py.typed',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\certifi\\py.typed',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Johns',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\America\\St_Johns',
   'DATA'),
  ('pytz\\zoneinfo\\EST',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\EST',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Bratislava',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Bratislava',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Dublin',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Dublin',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Boise',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\America\\Boise',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Tortola',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\America\\Tortola',
   'DATA'),
  ('pytz\\zoneinfo\\Libya',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Libya',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Santiago',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\America\\Santiago',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Mexico_City',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\America\\Mexico_City',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rosario',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\America\\Rosario',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Novokuznetsk',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Novokuznetsk',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Muscat',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Muscat',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Glace_Bay',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\America\\Glace_Bay',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\South_Georgia',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\South_Georgia',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Mauritius',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Mauritius',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Yellowknife',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\America\\Yellowknife',
   'DATA'),
  ('pytz\\zoneinfo\\leapseconds',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\leapseconds',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Adak',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\America\\Adak',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ust-Nera',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ust-Nera',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Tripoli',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Tripoli',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Tell_City',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Tell_City',
   'DATA'),
  ('pytz\\zoneinfo\\PST8PDT',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\PST8PDT',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Tahiti',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Tahiti',
   'DATA'),
  ('pytz\\zoneinfo\\Eire',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Eire',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Hawaii',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\US\\Hawaii',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Thimbu',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Thimbu',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Blantyre',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Blantyre',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Brisbane',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Brisbane',
   'DATA'),
  ('pytz\\zoneinfo\\zonenow.tab',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\zonenow.tab',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Brussels',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Brussels',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Fort_Wayne',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\America\\Fort_Wayne',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Kiev',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Kiev',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Volgograd',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Volgograd',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Thimphu',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Thimphu',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Danmarkshavn',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\America\\Danmarkshavn',
   'DATA'),
  ('pytz\\zoneinfo\\CET',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\CET',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Dominica',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\America\\Dominica',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Tegucigalpa',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\America\\Tegucigalpa',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Casey',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Casey',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Manaus',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\America\\Manaus',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Hovd',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Hovd',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Sakhalin',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Sakhalin',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Eastern',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Canada\\Eastern',
   'DATA'),
  ('pytz\\zoneinfo\\EST5EDT',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\EST5EDT',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Anadyr',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Anadyr',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Araguaina',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\America\\Araguaina',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-8',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-8',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Samara',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Samara',
   'DATA'),
  ('pytz\\zoneinfo\\Jamaica',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Jamaica',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Zurich',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Zurich',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Malta',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Malta',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Truk',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Truk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Boa_Vista',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\America\\Boa_Vista',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Lucia',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\America\\St_Lucia',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tomsk',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tomsk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Port-au-Prince',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\America\\Port-au-Prince',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Kinshasa',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Kinshasa',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-14',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-14',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Gibraltar',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Gibraltar',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Godthab',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\America\\Godthab',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Gambier',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Gambier',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Anguilla',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\America\\Anguilla',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Rangoon',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Rangoon',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Yap',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Yap',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Addis_Ababa',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Addis_Ababa',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Canberra',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Canberra',
   'DATA'),
  ('pytz\\zoneinfo\\Universal',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Universal',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rio_Branco',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\America\\Rio_Branco',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Johannesburg',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Johannesburg',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Maputo',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Maputo',
   'DATA'),
  ('pytz\\zoneinfo\\Navajo',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Navajo',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Arizona',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\US\\Arizona',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\NSW',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\NSW',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Helsinki',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Helsinki',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tbilisi',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tbilisi',
   'DATA'),
  ('pytz\\zoneinfo\\America\\La_Paz',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\America\\La_Paz',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Kwajalein',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Kwajalein',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Kentucky\\Louisville',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\America\\Kentucky\\Louisville',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Algiers',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Algiers',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Jan_Mayen',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Jan_Mayen',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Istanbul',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Istanbul',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\Acre',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Brazil\\Acre',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Ulyanovsk',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Ulyanovsk',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Paris',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Paris',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Libreville',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Libreville',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Sofia',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Sofia',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Pangnirtung',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\America\\Pangnirtung',
   'DATA'),
  ('pytz\\zoneinfo\\tzdata.zi',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\tzdata.zi',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Simferopol',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Simferopol',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Vevay',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Vevay',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Havana',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\America\\Havana',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\La_Rioja',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\La_Rioja',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Jujuy',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Jujuy',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Athens',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Athens',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Pyongyang',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Pyongyang',
   'DATA'),
  ('pytz\\zoneinfo\\Japan',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Japan',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vilnius',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Vilnius',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lubumbashi',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Lubumbashi',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Manila',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Manila',
   'DATA'),
  ('pytz\\zoneinfo\\Iceland',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Iceland',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Martinique',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\America\\Martinique',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Pago_Pago',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Pago_Pago',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\El_Aaiun',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\El_Aaiun',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Coyhaique',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\America\\Coyhaique',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Phoenix',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\America\\Phoenix',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dubai',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dubai',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Costa_Rica',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\America\\Costa_Rica',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Vincent',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\America\\St_Vincent',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\West',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Brazil\\West',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Harbin',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Harbin',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Adelaide',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Adelaide',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Creston',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\America\\Creston',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+2',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+2',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\St_Helena',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\St_Helena',
   'DATA'),
  ('pytz\\zoneinfo\\Chile\\Continental',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Chile\\Continental',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Conakry',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Conakry',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Tallinn',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Tallinn',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\ACT',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\ACT',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Santo_Domingo',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\America\\Santo_Domingo',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yekaterinburg',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Yekaterinburg',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Nairobi',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Nairobi',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rainy_River',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\America\\Rainy_River',
   'DATA'),
  ('pytz\\zoneinfo\\GMT',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\GMT',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Inuvik',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\America\\Inuvik',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Mazatlan',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\America\\Mazatlan',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kabul',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kabul',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Warsaw',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Warsaw',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nipigon',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\America\\Nipigon',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Amman',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Amman',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Bermuda',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Bermuda',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Novosibirsk',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Novosibirsk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Thomas',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\America\\St_Thomas',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Detroit',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\America\\Detroit',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Chita',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Chita',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Bangkok',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Bangkok',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Tasmania',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Tasmania',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Grenada',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\America\\Grenada',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Palau',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Palau',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nassau',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\America\\Nassau',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Nicosia',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Nicosia',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Barnaul',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Barnaul',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Moncton',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\America\\Moncton',
   'DATA'),
  ('pytz\\zoneinfo\\GMT0',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\GMT0',
   'DATA'),
  ('pytz\\zoneinfo\\Cuba',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Cuba',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kuala_Lumpur',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kuala_Lumpur',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Menominee',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\America\\Menominee',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Berlin',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Berlin',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Belize',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\America\\Belize',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Faroe',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Faroe',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lusaka',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Lusaka',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ulaanbaatar',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ulaanbaatar',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Krasnoyarsk',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Krasnoyarsk',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Riga',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Riga',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Buenos_Aires',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\America\\Buenos_Aires',
   'DATA'),
  ('pytz\\zoneinfo\\GB',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\GB',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-3',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-3',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Luxembourg',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Luxembourg',
   'DATA'),
  ('pytz\\zoneinfo\\Hongkong',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Hongkong',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Canary',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Canary',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Ojinaga',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\America\\Ojinaga',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Knox_IN',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\America\\Knox_IN',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Lindeman',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Lindeman',
   'DATA'),
  ('pytz\\zoneinfo\\America\\New_York',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\America\\New_York',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Galapagos',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Galapagos',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-13',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-13',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\West',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\West',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Montserrat',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\America\\Montserrat',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Samoa',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\US\\Samoa',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Toronto',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\America\\Toronto',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Noronha',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\America\\Noronha',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Windhoek',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Windhoek',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Aden',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Aden',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+11',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+11',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Noumea',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Noumea',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Majuro',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Majuro',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Wake',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Wake',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Port_Moresby',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Port_Moresby',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Barthelemy',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\America\\St_Barthelemy',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Zaporozhye',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Zaporozhye',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Marigot',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\America\\Marigot',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Almaty',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Almaty',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Aleutian',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\US\\Aleutian',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yakutsk',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Yakutsk',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Phnom_Penh',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Phnom_Penh',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Atka',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\America\\Atka',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Troll',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Troll',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Lisbon',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Lisbon',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Singapore',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Singapore',
   'DATA'),
  ('pytz\\zoneinfo\\Poland',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Poland',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Darwin',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Darwin',
   'DATA'),
  ('pytz\\zoneinfo\\ROK',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\ROK',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Madeira',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Madeira',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Dawson_Creek',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\America\\Dawson_Creek',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dhaka',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dhaka',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Ouagadougou',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Ouagadougou',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Monaco',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Monaco',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Madrid',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Madrid',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-4',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-4',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Marquesas',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Marquesas',
   'DATA'),
  ('pytz\\zoneinfo\\W-SU',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\W-SU',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dacca',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dacca',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Tarawa',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Tarawa',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Victoria',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Victoria',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Antananarivo',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Antananarivo',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Funafuti',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Funafuti',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Irkutsk',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Irkutsk',
   'DATA'),
  ('pytz\\zoneinfo\\iso3166.tab',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\iso3166.tab',
   'DATA'),
  ('pytz\\zoneinfo\\NZ-CHAT',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\NZ-CHAT',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Goose_Bay',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\America\\Goose_Bay',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Katmandu',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Katmandu',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Taipei',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Taipei',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Saskatchewan',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Canada\\Saskatchewan',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Accra',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Accra',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Mendoza',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Mendoza',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Port_of_Spain',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\America\\Port_of_Spain',
   'DATA'),
  ('pytz\\zoneinfo\\MET',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\MET',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Central',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\US\\Central',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Freetown',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Freetown',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Blanc-Sablon',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\America\\Blanc-Sablon',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Moscow',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Moscow',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Broken_Hill',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Broken_Hill',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Harare',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Harare',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\Greenwich',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\Greenwich',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Barbados',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\America\\Barbados',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Oslo',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Oslo',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Guernsey',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Guernsey',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Skopje',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Skopje',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kuching',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kuching',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Pitcairn',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Pitcairn',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Ljubljana',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Ljubljana',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Kiritimati',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Kiritimati',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Swift_Current',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\America\\Swift_Current',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kuwait',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kuwait',
   'DATA'),
  ('pytz\\zoneinfo\\MST',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\MST',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Palmer',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Palmer',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Istanbul',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Istanbul',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-2',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-2',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Sitka',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\America\\Sitka',
   'DATA'),
  ('pytz\\zoneinfo\\HST',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\HST',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Tiraspol',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Tiraspol',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Montreal',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\America\\Montreal',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Efate',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Efate',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cayenne',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\America\\Cayenne',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Porto-Novo',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Porto-Novo',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Busingen',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Busingen',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Niue',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Niue',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Faeroe',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Faeroe',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Makassar',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Makassar',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Kosrae',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Kosrae',
   'DATA'),
  ('pytz\\zoneinfo\\Mexico\\BajaNorte',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Mexico\\BajaNorte',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Fiji',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Fiji',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Famagusta',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Famagusta',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Currie',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Currie',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bangui',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Bangui',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ulan_Bator',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ulan_Bator',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Alaska',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\US\\Alaska',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Campo_Grande',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\America\\Campo_Grande',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Yancowinna',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Yancowinna',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Syowa',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Syowa',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Qyzylorda',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Qyzylorda',
   'DATA'),
  ('pytz\\zoneinfo\\America\\North_Dakota\\New_Salem',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\America\\North_Dakota\\New_Salem',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Jujuy',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\America\\Jujuy',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Porto_Velho',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\America\\Porto_Velho',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-1',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-1',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+7',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+7',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Macau',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Macau',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Comoro',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Comoro',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Chatham',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Chatham',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Sarajevo',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Sarajevo',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Tunis',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Tunis',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tehran',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tehran',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Los_Angeles',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\America\\Los_Angeles',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Ensenada',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\America\\Ensenada',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Juneau',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\America\\Juneau',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Pontianak',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Pontianak',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guayaquil',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\America\\Guayaquil',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Petersburg',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Petersburg',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Kralendijk',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\America\\Kralendijk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Anchorage',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\America\\Anchorage',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Managua',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\America\\Managua',
   'DATA'),
  ('pytz\\zoneinfo\\Iran',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Iran',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Juba',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Juba',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Stanley',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Stanley',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\UTC',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\UTC',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cuiaba',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\America\\Cuiaba',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Perth',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Perth',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Prague',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Prague',
   'DATA'),
  ('pytz\\zoneinfo\\Factory',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Factory',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Bogota',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\America\\Bogota',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Maceio',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\America\\Maceio',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\McMurdo',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\McMurdo',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Magadan',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Magadan',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Belem',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\America\\Belem',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Dakar',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Dakar',
   'DATA'),
  ('pytz\\zoneinfo\\Chile\\EasterIsland',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Chile\\EasterIsland',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Niamey',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Niamey',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Cairo',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Cairo',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Melbourne',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Melbourne',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ho_Chi_Minh',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ho_Chi_Minh',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+1',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+1',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Copenhagen',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Copenhagen',
   'DATA'),
  ('pytz\\zoneinfo\\Singapore',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Singapore',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Brunei',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Brunei',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Puerto_Rico',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\America\\Puerto_Rico',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Vientiane',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Vientiane',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Aruba',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\America\\Aruba',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indianapolis',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indianapolis',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Asmera',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Asmera',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Thule',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\America\\Thule',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kamchatka',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kamchatka',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT0',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT0',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cayman',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\America\\Cayman',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Recife',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\America\\Recife',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Reunion',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Reunion',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dili',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dili',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Aqtobe',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Aqtobe',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Dawson',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\America\\Dawson',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-6',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-6',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Azores',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Azores',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\North',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\North',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Norfolk',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Norfolk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Chihuahua',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\America\\Chihuahua',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ashkhabad',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ashkhabad',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Virgin',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\America\\Virgin',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Panama',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\America\\Panama',
   'DATA'),
  ('pytz\\zoneinfo\\America\\North_Dakota\\Center',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\America\\North_Dakota\\Center',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guyana',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\America\\Guyana',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Knox',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Knox',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Kentucky\\Monticello',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\America\\Kentucky\\Monticello',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kashgar',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kashgar',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Chungking',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Chungking',
   'DATA'),
  ('pytz\\zoneinfo\\EET',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\EET',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Rome',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Rome',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Bahia',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\America\\Bahia',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Samarkand',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Samarkand',
   'DATA'),
  ('pytz\\zoneinfo\\zone.tab',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\zone.tab',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Minsk',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Minsk',
   'DATA'),
  ('pytz\\zoneinfo\\GMT-0',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\GMT-0',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-12',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-12',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Monterrey',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\America\\Monterrey',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Atlantic',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Canada\\Atlantic',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-0',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-0',
   'DATA'),
  ('pytz\\zoneinfo\\CST6CDT',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\CST6CDT',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Wallis',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Wallis',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Macquarie',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Macquarie',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Halifax',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\America\\Halifax',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Mogadishu',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Mogadishu',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nuuk',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\America\\Nuuk',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tokyo',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tokyo',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Cocos',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Cocos',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Guam',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Guam',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Denver',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\America\\Denver',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Kitts',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\America\\St_Kitts',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Fort_Nelson',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\America\\Fort_Nelson',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Winamac',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Winamac',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Zagreb',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Zagreb',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Mbabane',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Mbabane',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Nicosia',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Nicosia',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Atyrau',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Atyrau',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Pohnpei',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Pohnpei',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ashgabat',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ashgabat',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Brazzaville',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Brazzaville',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Midway',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Midway',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kolkata',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kolkata',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Queensland',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Queensland',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Ushuaia',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Ushuaia',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-7',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-7',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\DumontDUrville',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\DumontDUrville',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Kampala',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Kampala',
   'DATA'),
  ('pytz\\zoneinfo\\ROC',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\ROC',
   'DATA'),
  ('pytz\\zoneinfo\\UCT',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\UCT',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Marengo',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Marengo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\San_Luis',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\San_Luis',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Maseru',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Maseru',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Karachi',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Karachi',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Vincennes',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Vincennes',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+4',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+4',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Khartoum',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Khartoum',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Easter',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Easter',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ujung_Pandang',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ujung_Pandang',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Central',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Canada\\Central',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Ndjamena',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Ndjamena',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Baku',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Baku',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Caracas',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\America\\Caracas',
   'DATA'),
  ('pytz\\zoneinfo\\Mexico\\General',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Mexico\\General',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Samoa',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Samoa',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Eirunepe',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\America\\Eirunepe',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vatican',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Vatican',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Bahia_Banderas',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\America\\Bahia_Banderas',
   'DATA'),
  ('pytz\\zoneinfo\\America\\North_Dakota\\Beulah',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\America\\North_Dakota\\Beulah',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Khandyga',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Khandyga',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Matamoros',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\America\\Matamoros',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Cordoba',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Cordoba',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Tucuman',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Tucuman',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+6',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+6',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Mariehamn',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Mariehamn',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Christmas',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Christmas',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\UCT',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\UCT',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Kigali',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Kigali',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bamako',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Bamako',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Kaliningrad',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Kaliningrad',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Santarem',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\America\\Santarem',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Punta_Arenas',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\America\\Punta_Arenas',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Eucla',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Eucla',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Tongatapu',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Tongatapu',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Asuncion',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\America\\Asuncion',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Timbuktu',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Timbuktu',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bujumbura',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Bujumbura',
   'DATA'),
  ('pytz\\zoneinfo\\PRC',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\PRC',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\LHI',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\LHI',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Ponape',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Ponape',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Omsk',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Omsk',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Budapest',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Budapest',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dushanbe',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dushanbe',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bissau',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Bissau',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guadeloupe',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\America\\Guadeloupe',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Merida',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\America\\Merida',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Chagos',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Chagos',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Kirov',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Kirov',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Belgrade',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Belgrade',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Stockholm',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Stockholm',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Vancouver',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\America\\Vancouver',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Winnipeg',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\America\\Winnipeg',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vaduz',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Vaduz',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vienna',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Vienna',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Tirane',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Tirane',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Colombo',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Colombo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Shiprock',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\America\\Shiprock',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Louisville',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\America\\Louisville',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Iqaluit',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\America\\Iqaluit',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cordoba',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\America\\Cordoba',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Jersey',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Jersey',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Guadalcanal',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Guadalcanal',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yerevan',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Yerevan',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Bahrain',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Bahrain',
   'DATA'),
  ('pytz\\zoneinfo\\UTC',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\UTC',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Lord_Howe',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Lord_Howe',
   'DATA'),
  ('pytz\\zoneinfo\\Greenwich',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Greenwich',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Sydney',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Sydney',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Lower_Princes',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\America\\Lower_Princes',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Beirut',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Beirut',
   'DATA'),
  ('pytz\\zoneinfo\\WET',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\WET',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Chicago',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\America\\Chicago',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Yakutat',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\America\\Yakutat',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Yukon',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Canada\\Yukon',
   'DATA'),
  ('pytz\\zoneinfo\\US\\East-Indiana',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\US\\East-Indiana',
   'DATA'),
  ('pytz\\zoneinfo\\Mexico\\BajaSur',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Mexico\\BajaSur',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Monrovia',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Monrovia',
   'DATA'),
  ('pytz\\zoneinfo\\zone1970.tab',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\zone1970.tab',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Bishkek',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Bishkek',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Johnston',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Johnston',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cancun',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\America\\Cancun',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Dar_es_Salaam',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Dar_es_Salaam',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Mountain',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Canada\\Mountain',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Uzhgorod',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Uzhgorod',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Metlakatla',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\America\\Metlakatla',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Urumqi',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Urumqi',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Gaborone',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Gaborone',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Enderbury',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Enderbury',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Saigon',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Saigon',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Damascus',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Damascus',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\Universal',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\Universal',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Curacao',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\America\\Curacao',
   'DATA'),
  ('pytz\\zoneinfo\\Turkey',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Turkey',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Chongqing',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Chongqing',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Indiana-Starke',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\US\\Indiana-Starke',
   'DATA'),
  ('pytz\\zoneinfo\\GB-Eire',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\GB-Eire',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Regina',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\America\\Regina',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Bucharest',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Bucharest',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Casablanca',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Casablanca',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Hobart',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Hobart',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Jerusalem',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Jerusalem',
   'DATA'),
  ('pytz\\zoneinfo\\Arctic\\Longyearbyen',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Arctic\\Longyearbyen',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Astrakhan',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Astrakhan',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Hebron',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Hebron',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Banjul',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Banjul',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lome',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Lome',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Catamarca',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\America\\Catamarca',
   'DATA'),
  ('pytz\\zoneinfo\\America\\El_Salvador',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\America\\El_Salvador',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Gaza',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Gaza',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Mayotte',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Mayotte',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Amsterdam',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Amsterdam',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Douala',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Douala',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Ceuta',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Ceuta',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Srednekolymsk',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Srednekolymsk',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Auckland',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Auckland',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Djibouti',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Djibouti',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Nouakchott',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Nouakchott',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\East',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Brazil\\East',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Mendoza',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\America\\Mendoza',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Kerguelen',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Kerguelen',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Porto_Acre',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\America\\Porto_Acre',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Hermosillo',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\America\\Hermosillo',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-11',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-11',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Jakarta',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Jakarta',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Podgorica',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Podgorica',
   'DATA'),
  ('pytz\\zoneinfo\\Zulu',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Zulu',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Isle_of_Man',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Isle_of_Man',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\South',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\South',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Thunder_Bay',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\America\\Thunder_Bay',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tashkent',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tashkent',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Choibalsan',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Choibalsan',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Aqtau',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Aqtau',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rankin_Inlet',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\America\\Rankin_Inlet',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Tijuana',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\America\\Tijuana',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kathmandu',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kathmandu',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Maldives',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Maldives',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Fakaofo',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Fakaofo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Montevideo',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\America\\Montevideo',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Saratov',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Saratov',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Rarotonga',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Rarotonga',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Lima',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\America\\Lima',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Apia',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Apia',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Buenos_Aires',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Buenos_Aires',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Davis',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Davis',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Fortaleza',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\America\\Fortaleza',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Whitehorse',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\America\\Whitehorse',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nome',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\America\\Nome',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Sao_Tome',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Sao_Tome',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\San_Marino',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\San_Marino',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Oral',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Oral',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Miquelon',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\America\\Miquelon',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Antigua',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\America\\Antigua',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\South_Pole',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\South_Pole',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Atikokan',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\America\\Atikokan',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Hong_Kong',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Hong_Kong',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+5',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+5',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Mahe',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Mahe',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\London',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\London',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lagos',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Lagos',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Calcutta',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Calcutta',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Mawson',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Mawson',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Riyadh',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Riyadh',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+12',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+12',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Jayapura',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Jayapura',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-9',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-9',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Rio_Gallegos',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Rio_Gallegos',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Pacific',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Canada\\Pacific',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Andorra',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Andorra',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Sao_Paulo',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\America\\Sao_Paulo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Jamaica',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\America\\Jamaica',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guatemala',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\America\\Guatemala',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+9',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+9',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Vostok',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Vostok',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-10',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-10',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Malabo',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Malabo',
   'DATA'),
  ('pytz\\zoneinfo\\Portugal',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Portugal',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Newfoundland',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Canada\\Newfoundland',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Kyiv',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Kyiv',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Baghdad',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Baghdad',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Nauru',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Nauru',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Edmonton',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\America\\Edmonton',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tel_Aviv',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tel_Aviv',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Abidjan',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Abidjan',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Honolulu',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Honolulu',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-5',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-5',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Saipan',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Saipan',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\DeNoronha',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Brazil\\DeNoronha',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Cape_Verde',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Cape_Verde',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\Zulu',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\Zulu',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cambridge_Bay',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\America\\Cambridge_Bay',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Rothera',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Rothera',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Grand_Turk',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\America\\Grand_Turk',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Reykjavik',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Reykjavik',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Bougainville',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Bougainville',
   'DATA'),
  ('pytz\\zoneinfo\\Israel',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Israel',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Ciudad_Juarez',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\America\\Ciudad_Juarez',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Paramaribo',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\America\\Paramaribo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Indianapolis',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Indianapolis',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\San_Juan',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\San_Juan',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Macao',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Macao',
   'DATA'),
  ('pytz\\zoneinfo\\Egypt',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Egypt',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Shanghai',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Shanghai',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Michigan',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\US\\Michigan',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Catamarca',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Catamarca',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Kanton',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Kanton',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\ComodRivadavia',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\ComodRivadavia',
   'DATA'),
  ('pytz\\zoneinfo\\MST7MDT',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\MST7MDT',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+3',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+3',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Asmara',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Asmara',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Coral_Harbour',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\America\\Coral_Harbour',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Chisinau',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Chisinau',
   'DATA'),
  ('pytz\\zoneinfo\\GMT+0',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\GMT+0',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Luanda',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Luanda',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Salta',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Salta',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+10',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+10',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Belfast',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Belfast',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Scoresbysund',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\America\\Scoresbysund',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Chuuk',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Chuuk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Resolute',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\America\\Resolute',
   'DATA'),
  ('pytz\\zoneinfo\\NZ',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\NZ',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+0',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+0',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Qatar',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Qatar',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yangon',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Yangon',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Seoul',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Seoul',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Pacific',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\US\\Pacific',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Vladivostok',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Vladivostok',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Qostanay',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Qostanay',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Eastern',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\US\\Eastern',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+8',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+8',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Santa_Isabel',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\America\\Santa_Isabel',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Mountain',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\US\\Mountain',
   'DATA'),
  ('pytz\\zoneinfo\\Kwajalein',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\zoneinfo\\Kwajalein',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Buenos_Aires',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Buenos_Aires',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Aqtobe',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Aqtobe',
   'DATA'),
  ('tzdata\\zoneinfo\\Antarctica\\Casey',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Antarctica\\Casey',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Paramaribo',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Paramaribo',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Chongqing',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Chongqing',
   'DATA'),
  ('tzdata\\zoneinfo\\Antarctica\\Macquarie',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Antarctica\\Macquarie',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Douala',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Douala',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Maceio',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Maceio',
   'DATA'),
  ('tzdata\\zoneinfo\\Australia\\Queensland',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Australia\\Queensland',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Conakry',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Conakry',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Glace_Bay',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Glace_Bay',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Galapagos',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Galapagos',
   'DATA'),
  ('tzdata\\zoneinfo\\Eire',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Eire',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Niamey',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Niamey',
   'DATA'),
  ('tzdata\\zoneinfo\\Brazil\\Acre',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Brazil\\Acre',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Shiprock',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Shiprock',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT-12',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT-12',
   'DATA'),
  ('tzdata\\zoneinfo\\Chile\\EasterIsland',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Chile\\EasterIsland',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Caracas',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Caracas',
   'DATA'),
  ('tzdata\\zoneinfo\\Indian\\Chagos',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Indian\\Chagos',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Belgrade',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Belgrade',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Sitka',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Sitka',
   'DATA'),
  ('tzdata\\zoneinfo\\MET',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\MET',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Regina',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Regina',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Kirov',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Kirov',
   'DATA'),
  ('tzdata\\zoneinfo\\Antarctica\\Palmer',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Antarctica\\Palmer',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Irkutsk',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Irkutsk',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Phoenix',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Phoenix',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Argentina\\Jujuy',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Argentina\\Jujuy',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Maseru',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Maseru',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Chicago',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Chicago',
   'DATA'),
  ('tzdata\\zoneinfo\\zone1970.tab',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\zone1970.tab',
   'DATA'),
  ('tzdata\\zoneinfo\\CET',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\CET',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Astrakhan',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Astrakhan',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT-9',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT-9',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT0',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT0',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Nipigon',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Nipigon',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\North_Dakota\\Beulah',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\North_Dakota\\Beulah',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Bissau',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Bissau',
   'DATA'),
  ('tzdata\\zoneinfo\\Mexico\\General',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Mexico\\General',
   'DATA'),
  ('tzdata\\zoneinfo\\zonenow.tab',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\zonenow.tab',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Guam',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Guam',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Ceuta',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Ceuta',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Kuala_Lumpur',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Kuala_Lumpur',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT+1',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT+1',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Dominica',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Dominica',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Porto_Acre',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Porto_Acre',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Magadan',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Magadan',
   'DATA'),
  ('tzdata\\zoneinfo\\MST',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\MST',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Cayenne',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Cayenne',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Dili',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Dili',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Kinshasa',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Kinshasa',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\St_Lucia',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\St_Lucia',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Monrovia',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Monrovia',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Omsk',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Omsk',
   'DATA'),
  ('tzdata\\zoneinfo\\Australia\\Brisbane',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Australia\\Brisbane',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Karachi',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Karachi',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Vaduz',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Vaduz',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Havana',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Havana',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Pitcairn',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Pitcairn',
   'DATA'),
  ('tzdata\\zoneinfo\\Antarctica\\South_Pole',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Antarctica\\South_Pole',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Swift_Current',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Swift_Current',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Luanda',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Luanda',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Oral',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Oral',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Ulaanbaatar',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Ulaanbaatar',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Denver',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Denver',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Anchorage',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Anchorage',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Norfolk',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Norfolk',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Paris',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Paris',
   'DATA'),
  ('tzdata\\zoneinfo\\Zulu',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Zulu',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Cuiaba',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Cuiaba',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Atikokan',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Atikokan',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Nome',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Nome',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Rio_Branco',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Rio_Branco',
   'DATA'),
  ('tzdata\\zoneinfo\\Australia\\Eucla',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Australia\\Eucla',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Enderbury',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Enderbury',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Gaborone',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Gaborone',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Winnipeg',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Winnipeg',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Rankin_Inlet',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Rankin_Inlet',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Tegucigalpa',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Tegucigalpa',
   'DATA'),
  ('tzdata\\zones',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zones',
   'DATA'),
  ('tzdata\\zoneinfo\\Indian\\Antananarivo',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Indian\\Antananarivo',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Sao_Tome',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Sao_Tome',
   'DATA'),
  ('tzdata\\zoneinfo\\iso3166.tab',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\iso3166.tab',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Minsk',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Minsk',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Choibalsan',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Choibalsan',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Curacao',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Curacao',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Istanbul',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Istanbul',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Pontianak',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Pontianak',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Yellowknife',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Yellowknife',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Barbados',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Barbados',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Menominee',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Menominee',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Yakutsk',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Yakutsk',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Madrid',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Madrid',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Costa_Rica',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Costa_Rica',
   'DATA'),
  ('tzdata\\zoneinfo\\Canada\\Yukon',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Canada\\Yukon',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Maputo',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Maputo',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Krasnoyarsk',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Krasnoyarsk',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Qostanay',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Qostanay',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Lima',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Lima',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Coyhaique',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Coyhaique',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Kanton',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Kanton',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Scoresbysund',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Scoresbysund',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Wake',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Wake',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Argentina\\Cordoba',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Argentina\\Cordoba',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\St_Kitts',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\St_Kitts',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Katmandu',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Katmandu',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Noronha',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Noronha',
   'DATA'),
  ('tzdata\\zoneinfo\\Hongkong',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Hongkong',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Tunis',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Tunis',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Chita',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Chita',
   'DATA'),
  ('tzdata\\zoneinfo\\ROK',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\ROK',
   'DATA'),
  ('tzdata\\zoneinfo\\Australia\\Hobart',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Australia\\Hobart',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT-0',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT-0',
   'DATA'),
  ('tzdata\\zoneinfo\\US\\Arizona',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\US\\Arizona',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Isle_of_Man',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Isle_of_Man',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Guatemala',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Guatemala',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Danmarkshavn',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Danmarkshavn',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Belfast',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Belfast',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Mexico_City',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Mexico_City',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Windhoek',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Windhoek',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Boise',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Boise',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Catamarca',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Catamarca',
   'DATA'),
  ('tzdata\\zoneinfo\\Indian\\Kerguelen',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Indian\\Kerguelen',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Vientiane',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Vientiane',
   'DATA'),
  ('tzdata\\zoneinfo\\Libya',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Libya',
   'DATA'),
  ('tzdata\\zoneinfo\\CST6CDT',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\CST6CDT',
   'DATA'),
  ('tzdata\\zoneinfo\\Brazil\\DeNoronha',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Brazil\\DeNoronha',
   'DATA'),
  ('tzdata\\zoneinfo\\Indian\\Comoro',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Indian\\Comoro',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT-5',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT-5',
   'DATA'),
  ('tzdata\\zoneinfo\\zone.tab',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\zone.tab',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Tel_Aviv',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Tel_Aviv',
   'DATA'),
  ('tzdata\\zoneinfo\\HST',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\HST',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Addis_Ababa',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Addis_Ababa',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT+10',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT+10',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\North_Dakota\\Center',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\North_Dakota\\Center',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Cayman',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Cayman',
   'DATA'),
  ('tzdata\\zoneinfo\\GB',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\GB',
   'DATA'),
  ('tzdata\\zoneinfo\\US\\Indiana-Starke',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\US\\Indiana-Starke',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Chatham',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Chatham',
   'DATA'),
  ('tzdata\\zoneinfo\\Universal',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Universal',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Kigali',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Kigali',
   'DATA'),
  ('tzdata\\zoneinfo\\Australia\\Darwin',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Australia\\Darwin',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Atka',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Atka',
   'DATA'),
  ('tzdata\\zoneinfo\\Australia\\South',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Australia\\South',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Guernsey',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Guernsey',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Easter',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Easter',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Bishkek',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Bishkek',
   'DATA'),
  ('tzdata\\zoneinfo\\Australia\\Tasmania',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Australia\\Tasmania',
   'DATA'),
  ('tzdata\\zoneinfo\\Australia\\Broken_Hill',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Australia\\Broken_Hill',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Srednekolymsk',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Srednekolymsk',
   'DATA'),
  ('tzdata\\zoneinfo\\Iran',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Iran',
   'DATA'),
  ('tzdata\\zoneinfo\\Canada\\Central',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Canada\\Central',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Kabul',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Kabul',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Kolkata',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Kolkata',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Athens',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Athens',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Cambridge_Bay',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Cambridge_Bay',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT-10',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT-10',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT+2',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT+2',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Honolulu',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Honolulu',
   'DATA'),
  ('tzdata\\zoneinfo\\Atlantic\\Azores',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Atlantic\\Azores',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Damascus',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Damascus',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\Zulu',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Etc\\Zulu',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Porto-Novo',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Porto-Novo',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Eirunepe',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Eirunepe',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Ouagadougou',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Ouagadougou',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT-13',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT-13',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Chungking',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Chungking',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Juba',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Juba',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Recife',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Recife',
   'DATA'),
  ('tzdata\\zoneinfo\\Indian\\Mahe',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Indian\\Mahe',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Novokuznetsk',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Novokuznetsk',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Dakar',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Dakar',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Dawson_Creek',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Dawson_Creek',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Fiji',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Fiji',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Aqtau',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Aqtau',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Yap',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Yap',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Mendoza',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Mendoza',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Johannesburg',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Johannesburg',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Indiana\\Marengo',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Indiana\\Marengo',
   'DATA'),
  ('tzdata\\zoneinfo\\Brazil\\West',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Brazil\\West',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\St_Vincent',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\St_Vincent',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Miquelon',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Miquelon',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Manaus',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Manaus',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Jamaica',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Jamaica',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Montserrat',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Montserrat',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Barnaul',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Barnaul',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Saigon',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Saigon',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Kentucky\\Louisville',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Kentucky\\Louisville',
   'DATA'),
  ('tzdata\\zoneinfo\\Atlantic\\Stanley',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Atlantic\\Stanley',
   'DATA'),
  ('tzdata\\zoneinfo\\tzdata.zi',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\tzdata.zi',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Argentina\\Buenos_Aires',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Argentina\\Buenos_Aires',
   'DATA'),
  ('tzdata\\zoneinfo\\Mexico\\BajaSur',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Mexico\\BajaSur',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT-3',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT-3',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Tripoli',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Tripoli',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Singapore',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Singapore',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Truk',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Truk',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Monaco',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Monaco',
   'DATA'),
  ('tzdata\\zoneinfo\\Canada\\Eastern',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Canada\\Eastern',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Baghdad',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Baghdad',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Toronto',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Toronto',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Nassau',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Nassau',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Rainy_River',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Rainy_River',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Samarkand',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Samarkand',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Cairo',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Cairo',
   'DATA'),
  ('tzdata\\zoneinfo\\Chile\\Continental',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Chile\\Continental',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Argentina\\Ushuaia',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Argentina\\Ushuaia',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Gibraltar',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Gibraltar',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Saratov',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Saratov',
   'DATA'),
  ('tzdata\\zoneinfo\\Portugal',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Portugal',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Jerusalem',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Jerusalem',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Accra',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Accra',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Budapest',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Budapest',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Coral_Harbour',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Coral_Harbour',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Hermosillo',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Hermosillo',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Ulyanovsk',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Ulyanovsk',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Malta',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Malta',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Malabo',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Malabo',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Belize',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Belize',
   'DATA'),
  ('tzdata\\zoneinfo\\EST5EDT',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\EST5EDT',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Tashkent',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Tashkent',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Bahia_Banderas',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Bahia_Banderas',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Luxembourg',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Luxembourg',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Argentina\\Salta',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Argentina\\Salta',
   'DATA'),
  ('tzdata\\zoneinfo\\Atlantic\\Reykjavik',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Atlantic\\Reykjavik',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Moscow',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Moscow',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Whitehorse',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Whitehorse',
   'DATA'),
  ('tzdata\\zoneinfo\\EET',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\EET',
   'DATA'),
  ('tzdata\\zoneinfo\\US\\Eastern',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\US\\Eastern',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Kaliningrad',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Kaliningrad',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Sao_Paulo',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Sao_Paulo',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Detroit',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Detroit',
   'DATA'),
  ('tzdata\\zoneinfo\\Atlantic\\Bermuda',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Atlantic\\Bermuda',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Palau',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Palau',
   'DATA'),
  ('tzdata\\zoneinfo\\GMT0',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\GMT0',
   'DATA'),
  ('tzdata\\zoneinfo\\US\\Aleutian',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\US\\Aleutian',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Indiana\\Knox',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Indiana\\Knox',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Podgorica',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Podgorica',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Puerto_Rico',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Puerto_Rico',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Copenhagen',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Copenhagen',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT+9',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT+9',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Porto_Velho',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Porto_Velho',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Cordoba',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Cordoba',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Kiev',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Kiev',
   'DATA'),
  ('tzdata\\zoneinfo\\Indian\\Mauritius',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Indian\\Mauritius',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Sarajevo',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Sarajevo',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Lisbon',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Lisbon',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Indiana\\Petersburg',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Indiana\\Petersburg',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Taipei',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Taipei',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Thunder_Bay',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Thunder_Bay',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Tiraspol',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Tiraspol',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Matamoros',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Matamoros',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Chisinau',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Chisinau',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Apia',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Apia',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Warsaw',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Warsaw',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Banjul',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Banjul',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Tarawa',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Tarawa',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Ho_Chi_Minh',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Ho_Chi_Minh',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Bratislava',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Bratislava',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Ojinaga',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Ojinaga',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\St_Thomas',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\St_Thomas',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Gambier',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Gambier',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Brunei',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Brunei',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Lubumbashi',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Lubumbashi',
   'DATA'),
  ('tzdata\\zoneinfo\\Poland',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Poland',
   'DATA'),
  ('tzdata\\zoneinfo\\NZ-CHAT',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\NZ-CHAT',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Montreal',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Montreal',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Belem',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Belem',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Freetown',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Freetown',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Riyadh',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Riyadh',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Santiago',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Santiago',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Kwajalein',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Kwajalein',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT+7',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT+7',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Tijuana',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Tijuana',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Marquesas',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Marquesas',
   'DATA'),
  ('tzdata\\zoneinfo\\Egypt',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Egypt',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Samoa',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Samoa',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Argentina\\San_Luis',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Argentina\\San_Luis',
   'DATA'),
  ('tzdata\\zoneinfo\\Greenwich',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Greenwich',
   'DATA'),
  ('tzdata\\zoneinfo\\Turkey',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Turkey',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Tallinn',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Tallinn',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Asmera',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Asmera',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Lower_Princes',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Lower_Princes',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Brussels',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Brussels',
   'DATA'),
  ('tzdata\\zoneinfo\\Atlantic\\Faeroe',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Atlantic\\Faeroe',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Indiana\\Tell_City',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Indiana\\Tell_City',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Muscat',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Muscat',
   'DATA'),
  ('tzdata\\zoneinfo\\Kwajalein',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Kwajalein',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Santa_Isabel',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Santa_Isabel',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Ndjamena',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Ndjamena',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Louisville',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Louisville',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Vienna',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Vienna',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Blanc-Sablon',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Blanc-Sablon',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\Universal',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Etc\\Universal',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\New_York',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\New_York',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Chuuk',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Chuuk',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Guyana',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Guyana',
   'DATA'),
  ('tzdata\\zoneinfo\\Australia\\Adelaide',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Australia\\Adelaide',
   'DATA'),
  ('tzdata\\zoneinfo\\Indian\\Christmas',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Indian\\Christmas',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Stockholm',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Stockholm',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\UCT',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Etc\\UCT',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Aden',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Aden',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Dushanbe',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Dushanbe',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Tomsk',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Tomsk',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Dacca',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Dacca',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Ulan_Bator',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Ulan_Bator',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT+5',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT+5',
   'DATA'),
  ('tzdata\\zoneinfo\\Canada\\Atlantic',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Canada\\Atlantic',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Rosario',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Rosario',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Riga',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Riga',
   'DATA'),
  ('tzdata\\zoneinfo\\Antarctica\\Vostok',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Antarctica\\Vostok',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Oslo',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Oslo',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\San_Marino',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Europe\\San_Marino',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Argentina\\ComodRivadavia',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Argentina\\ComodRivadavia',
   'DATA'),
  ('tzdata\\zoneinfo\\Canada\\Newfoundland',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Canada\\Newfoundland',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Adak',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Adak',
   'DATA'),
  ('tzdata\\zoneinfo\\Indian\\Maldives',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Indian\\Maldives',
   'DATA'),
  ('tzdata\\zoneinfo\\Australia\\Sydney',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Australia\\Sydney',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Campo_Grande',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Campo_Grande',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Nouakchott',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Nouakchott',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT-2',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT-2',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Marigot',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Marigot',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Araguaina',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Araguaina',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Fakaofo',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Fakaofo',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Port_Moresby',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Port_Moresby',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Grenada',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Grenada',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Samara',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Samara',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Juneau',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Juneau',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Grand_Turk',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Grand_Turk',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Zagreb',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Zagreb',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Yakutat',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Yakutat',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Lome',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Lome',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Ust-Nera',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Ust-Nera',
   'DATA'),
  ('tzdata\\zoneinfo\\US\\Mountain',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\US\\Mountain',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Amman',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Amman',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Novosibirsk',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Novosibirsk',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Anguilla',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Anguilla',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Asuncion',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Asuncion',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Argentina\\Mendoza',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Argentina\\Mendoza',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Khartoum',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Khartoum',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Port-au-Prince',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Port-au-Prince',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Jakarta',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Jakarta',
   'DATA'),
  ('tzdata\\zoneinfo\\UTC',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\UTC',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Asmara',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Asmara',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Sakhalin',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Sakhalin',
   'DATA'),
  ('tzdata\\zoneinfo\\Australia\\Currie',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Australia\\Currie',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Berlin',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Berlin',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Yekaterinburg',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Yekaterinburg',
   'DATA'),
  ('tzdata\\zoneinfo\\US\\East-Indiana',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\US\\East-Indiana',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Tirane',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Tirane',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Makassar',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Makassar',
   'DATA'),
  ('tzdata\\zoneinfo\\Antarctica\\Syowa',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Antarctica\\Syowa',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT-1',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT-1',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Istanbul',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Istanbul',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Calcutta',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Calcutta',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Bangkok',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Bangkok',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Nauru',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Nauru',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Rangoon',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Rangoon',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Atyrau',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Atyrau',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Yerevan',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Yerevan',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Ciudad_Juarez',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Ciudad_Juarez',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Blantyre',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Blantyre',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Knox_IN',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Knox_IN',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Ensenada',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Ensenada',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Edmonton',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Edmonton',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Tortola',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Tortola',
   'DATA'),
  ('tzdata\\zoneinfo\\Cuba',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Cuba',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Pangnirtung',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Pangnirtung',
   'DATA'),
  ('tzdata\\zoneinfo\\Australia\\North',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Australia\\North',
   'DATA'),
  ('tzdata\\zoneinfo\\Antarctica\\DumontDUrville',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Antarctica\\DumontDUrville',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Timbuktu',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Timbuktu',
   'DATA'),
  ('tzdata\\zoneinfo\\MST7MDT',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\MST7MDT',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Volgograd',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Volgograd',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Amsterdam',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Amsterdam',
   'DATA'),
  ('tzdata\\zoneinfo\\Australia\\NSW',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Australia\\NSW',
   'DATA'),
  ('tzdata\\zoneinfo\\Australia\\Lord_Howe',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Australia\\Lord_Howe',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Tehran',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Tehran',
   'DATA'),
  ('tzdata\\zoneinfo\\Atlantic\\Madeira',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Atlantic\\Madeira',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT+11',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT+11',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Bangui',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Bangui',
   'DATA'),
  ('tzdata\\zoneinfo\\Atlantic\\St_Helena',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Atlantic\\St_Helena',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Mogadishu',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Mogadishu',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Dublin',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Dublin',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT-7',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT-7',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Tbilisi',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Tbilisi',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Sofia',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Sofia',
   'DATA'),
  ('tzdata\\zoneinfo\\US\\Pacific',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\US\\Pacific',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Vilnius',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Vilnius',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT+3',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT+3',
   'DATA'),
  ('tzdata\\zoneinfo\\US\\Samoa',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\US\\Samoa',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Mariehamn',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Mariehamn',
   'DATA'),
  ('tzdata\\zoneinfo\\Antarctica\\Mawson',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Antarctica\\Mawson',
   'DATA'),
  ('tzdata\\zoneinfo\\Canada\\Mountain',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Canada\\Mountain',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT+4',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT+4',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT-14',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT-14',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Argentina\\Catamarca',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Argentina\\Catamarca',
   'DATA'),
  ('tzdata\\zoneinfo\\Australia\\ACT',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Australia\\ACT',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Iqaluit',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Iqaluit',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Port_of_Spain',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Port_of_Spain',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Vancouver',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Vancouver',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Djibouti',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Djibouti',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Noumea',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Noumea',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Urumqi',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Urumqi',
   'DATA'),
  ('tzdata\\zoneinfo\\Australia\\Perth',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Australia\\Perth',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Colombo',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Colombo',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Beirut',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Beirut',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Boa_Vista',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Boa_Vista',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Qatar',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Qatar',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Monterrey',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Monterrey',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\UTC',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Etc\\UTC',
   'DATA'),
  ('tzdata\\zoneinfo\\GMT-0',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\GMT-0',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Pago_Pago',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Pago_Pago',
   'DATA'),
  ('tzdata\\zoneinfo\\Atlantic\\Faroe',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Atlantic\\Faroe',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Merida',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Merida',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Dubai',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Dubai',
   'DATA'),
  ('tzdata\\zoneinfo\\Australia\\Lindeman',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Australia\\Lindeman',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Kentucky\\Monticello',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Kentucky\\Monticello',
   'DATA'),
  ('tzdata\\zoneinfo\\Australia\\Melbourne',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Australia\\Melbourne',
   'DATA'),
  ('tzdata\\zoneinfo\\Canada\\Saskatchewan',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Canada\\Saskatchewan',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Midway',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Midway',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Santo_Domingo',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Santo_Domingo',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Ashkhabad',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Ashkhabad',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Kashgar',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Kashgar',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Pohnpei',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Pohnpei',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Thimbu',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Thimbu',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Indiana\\Vevay',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Indiana\\Vevay',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Saipan',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Saipan',
   'DATA'),
  ('tzdata\\zoneinfo\\Atlantic\\Cape_Verde',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Atlantic\\Cape_Verde',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Uzhgorod',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Uzhgorod',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Bamako',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Bamako',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Famagusta',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Famagusta',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Kathmandu',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Kathmandu',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Kuching',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Kuching',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Managua',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Managua',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Nicosia',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Nicosia',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Dar_es_Salaam',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Dar_es_Salaam',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Auckland',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Auckland',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Cancun',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Cancun',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Guadeloupe',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Guadeloupe',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Kyiv',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Kyiv',
   'DATA'),
  ('tzdata\\zoneinfo\\Indian\\Reunion',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Indian\\Reunion',
   'DATA'),
  ('tzdata\\zoneinfo\\EST',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\EST',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Santarem',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Santarem',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Thimphu',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Thimphu',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Gaza',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Gaza',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Chihuahua',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Chihuahua',
   'DATA'),
  ('tzdata\\zoneinfo\\PST8PDT',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\PST8PDT',
   'DATA'),
  ('tzdata\\zoneinfo\\WET',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\WET',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Bougainville',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Bougainville',
   'DATA'),
  ('tzdata\\zoneinfo\\US\\Hawaii',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\US\\Hawaii',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Helsinki',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Helsinki',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Virgin',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Virgin',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Wallis',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Wallis',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Indiana\\Vincennes',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Indiana\\Vincennes',
   'DATA'),
  ('tzdata\\zoneinfo\\GMT',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\GMT',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Fortaleza',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Fortaleza',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\La_Paz',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\La_Paz',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Zaporozhye',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Zaporozhye',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Argentina\\San_Juan',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Argentina\\San_Juan',
   'DATA'),
  ('tzdata\\zoneinfo\\US\\Alaska',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\US\\Alaska',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Casablanca',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Casablanca',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Argentina\\Rio_Gallegos',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Argentina\\Rio_Gallegos',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Rarotonga',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Rarotonga',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Qyzylorda',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Qyzylorda',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Kamchatka',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Kamchatka',
   'DATA'),
  ('tzdata\\zoneinfo\\Arctic\\Longyearbyen',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Arctic\\Longyearbyen',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Bogota',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Bogota',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Pyongyang',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Pyongyang',
   'DATA'),
  ('tzdata\\zoneinfo\\Navajo',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Navajo',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Harbin',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Harbin',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Shanghai',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Shanghai',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT-4',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT-4',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Vatican',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Vatican',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Punta_Arenas',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Punta_Arenas',
   'DATA'),
  ('tzdata\\zoneinfo\\ROC',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\ROC',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Argentina\\Tucuman',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Argentina\\Tucuman',
   'DATA'),
  ('tzdata\\zoneinfo\\Atlantic\\Jan_Mayen',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Atlantic\\Jan_Mayen',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Godthab',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Godthab',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Phnom_Penh',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Phnom_Penh',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Hebron',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Hebron',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT+6',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT+6',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Kralendijk',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Kralendijk',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Brazzaville',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Brazzaville',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Baku',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Baku',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Aruba',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Aruba',
   'DATA'),
  ('tzdata\\zoneinfo\\Mexico\\BajaNorte',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Mexico\\BajaNorte',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Harare',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Harare',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Vladivostok',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Vladivostok',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Antigua',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Antigua',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Andorra',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Andorra',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Ljubljana',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Ljubljana',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Macau',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Macau',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Jujuy',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Jujuy',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Zurich',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Zurich',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Mazatlan',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Mazatlan',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Kuwait',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Kuwait',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Anadyr',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Anadyr',
   'DATA'),
  ('tzdata\\zoneinfo\\Brazil\\East',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Brazil\\East',
   'DATA'),
  ('tzdata\\zoneinfo\\Iceland',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Iceland',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Nuuk',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Nuuk',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Bahia',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Bahia',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Goose_Bay',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Goose_Bay',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\St_Johns',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\St_Johns',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Kampala',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Kampala',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Los_Angeles',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Los_Angeles',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Niue',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Niue',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT-11',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT-11',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Busingen',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Busingen',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Jayapura',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Jayapura',
   'DATA'),
  ('tzdata\\zoneinfo\\Antarctica\\Rothera',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Antarctica\\Rothera',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Rome',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Rome',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Indiana\\Indianapolis',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Indiana\\Indianapolis',
   'DATA'),
  ('tzdata\\zoneinfo\\Antarctica\\McMurdo',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Antarctica\\McMurdo',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Bucharest',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Bucharest',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT+0',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT+0',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Ashgabat',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Ashgabat',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Fort_Nelson',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Fort_Nelson',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Halifax',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Halifax',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Fort_Wayne',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Fort_Wayne',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Algiers',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Algiers',
   'DATA'),
  ('tzdata\\zoneinfo\\Indian\\Cocos',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Indian\\Cocos',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Creston',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Creston',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\London',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Europe\\London',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Jersey',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Jersey',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\North_Dakota\\New_Salem',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\North_Dakota\\New_Salem',
   'DATA'),
  ('tzdata\\zoneinfo\\leapseconds',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\leapseconds',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Kiritimati',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Kiritimati',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Guayaquil',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Guayaquil',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Prague',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Prague',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT-6',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT-6',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Ponape',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Ponape',
   'DATA'),
  ('tzdata\\zoneinfo\\Israel',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Israel',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Moncton',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Moncton',
   'DATA'),
  ('tzdata\\zoneinfo\\Factory',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Factory',
   'DATA'),
  ('tzdata\\zoneinfo\\Canada\\Pacific',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Canada\\Pacific',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Tokyo',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Tokyo',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT+8',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT+8',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Indiana\\Winamac',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Indiana\\Winamac',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Guadalcanal',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Guadalcanal',
   'DATA'),
  ('tzdata\\zoneinfo\\Atlantic\\South_Georgia',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Atlantic\\South_Georgia',
   'DATA'),
  ('tzdata\\zoneinfo\\UCT',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\UCT',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Bujumbura',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Bujumbura',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Lagos',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Lagos',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\Greenwich',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Etc\\Greenwich',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Libreville',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Libreville',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Dhaka',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Dhaka',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Dawson',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Dawson',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Funafuti',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Funafuti',
   'DATA'),
  ('tzdata\\zoneinfo\\Australia\\Canberra',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Australia\\Canberra',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Skopje',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Skopje',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Tahiti',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Tahiti',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Manila',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Manila',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Tongatapu',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Tongatapu',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Almaty',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Almaty',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Abidjan',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Abidjan',
   'DATA'),
  ('tzdata\\zoneinfo\\GB-Eire',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\GB-Eire',
   'DATA'),
  ('tzdata\\zoneinfo\\NZ',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\NZ',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Lusaka',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Lusaka',
   'DATA'),
  ('tzdata\\zoneinfo\\Indian\\Mayotte',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Indian\\Mayotte',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Thule',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Thule',
   'DATA'),
  ('tzdata\\zoneinfo\\US\\Michigan',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\US\\Michigan',
   'DATA'),
  ('tzdata\\zoneinfo\\US\\Central',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\US\\Central',
   'DATA'),
  ('tzdata\\zoneinfo\\PRC',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\PRC',
   'DATA'),
  ('tzdata\\zoneinfo\\Australia\\Victoria',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Australia\\Victoria',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Hong_Kong',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Hong_Kong',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Resolute',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Resolute',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Indianapolis',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Indianapolis',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT-8',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT-8',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Efate',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Efate',
   'DATA'),
  ('tzdata\\zoneinfo\\Atlantic\\Canary',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Atlantic\\Canary',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Nicosia',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Nicosia',
   'DATA'),
  ('tzdata\\zoneinfo\\Jamaica',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Jamaica',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Seoul',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Seoul',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Khandyga',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Khandyga',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Bahrain',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Bahrain',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT+12',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT+12',
   'DATA'),
  ('tzdata\\zoneinfo\\W-SU',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\W-SU',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Johnston',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Johnston',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Argentina\\La_Rioja',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Argentina\\La_Rioja',
   'DATA'),
  ('tzdata\\zoneinfo\\Australia\\LHI',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Australia\\LHI',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Metlakatla',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Metlakatla',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Kosrae',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Kosrae',
   'DATA'),
  ('tzdata\\zoneinfo\\Antarctica\\Troll',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Antarctica\\Troll',
   'DATA'),
  ('tzdata\\zoneinfo\\GMT+0',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\GMT+0',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Panama',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Panama',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Ujung_Pandang',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Ujung_Pandang',
   'DATA'),
  ('tzdata\\zoneinfo\\Singapore',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Singapore',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Hovd',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Hovd',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Martinique',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Martinique',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\El_Aaiun',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Africa\\El_Aaiun',
   'DATA'),
  ('tzdata\\zoneinfo\\Australia\\West',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Australia\\West',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Inuvik',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Inuvik',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Mbabane',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Mbabane',
   'DATA'),
  ('tzdata\\zoneinfo\\Japan',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Japan',
   'DATA'),
  ('tzdata\\zoneinfo\\Australia\\Yancowinna',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Australia\\Yancowinna',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\St_Barthelemy',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\St_Barthelemy',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Simferopol',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Simferopol',
   'DATA'),
  ('tzdata\\zoneinfo\\Antarctica\\Davis',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Antarctica\\Davis',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Majuro',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Majuro',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\El_Salvador',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\El_Salvador',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Nairobi',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Nairobi',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Montevideo',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Montevideo',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Yangon',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Yangon',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Macao',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Macao',
   'DATA'),
  ('tzlocal-5.3.1.dist-info\\INSTALLER',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzlocal-5.3.1.dist-info\\INSTALLER',
   'DATA'),
  ('six-1.17.0.dist-info\\WHEEL',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\six-1.17.0.dist-info\\WHEEL',
   'DATA'),
  ('APScheduler-3.10.4.dist-info\\entry_points.txt',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\APScheduler-3.10.4.dist-info\\entry_points.txt',
   'DATA'),
  ('six-1.17.0.dist-info\\INSTALLER',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\six-1.17.0.dist-info\\INSTALLER',
   'DATA'),
  ('tzlocal-5.3.1.dist-info\\METADATA',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzlocal-5.3.1.dist-info\\METADATA',
   'DATA'),
  ('pytz-2025.2.dist-info\\zip-safe',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz-2025.2.dist-info\\zip-safe',
   'DATA'),
  ('tzdata-2025.2.dist-info\\top_level.txt',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata-2025.2.dist-info\\top_level.txt',
   'DATA'),
  ('pytz-2025.2.dist-info\\LICENSE.txt',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz-2025.2.dist-info\\LICENSE.txt',
   'DATA'),
  ('tzlocal-5.3.1.dist-info\\WHEEL',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzlocal-5.3.1.dist-info\\WHEEL',
   'DATA'),
  ('pytz-2025.2.dist-info\\WHEEL',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz-2025.2.dist-info\\WHEEL',
   'DATA'),
  ('tzdata-2025.2.dist-info\\METADATA',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata-2025.2.dist-info\\METADATA',
   'DATA'),
  ('tzlocal-5.3.1.dist-info\\top_level.txt',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzlocal-5.3.1.dist-info\\top_level.txt',
   'DATA'),
  ('APScheduler-3.10.4.dist-info\\WHEEL',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\APScheduler-3.10.4.dist-info\\WHEEL',
   'DATA'),
  ('APScheduler-3.10.4.dist-info\\top_level.txt',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\APScheduler-3.10.4.dist-info\\top_level.txt',
   'DATA'),
  ('APScheduler-3.10.4.dist-info\\LICENSE.txt',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\APScheduler-3.10.4.dist-info\\LICENSE.txt',
   'DATA'),
  ('tzlocal-5.3.1.dist-info\\RECORD',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzlocal-5.3.1.dist-info\\RECORD',
   'DATA'),
  ('tzlocal-5.3.1.dist-info\\LICENSE.txt',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzlocal-5.3.1.dist-info\\LICENSE.txt',
   'DATA'),
  ('pytz-2025.2.dist-info\\RECORD',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz-2025.2.dist-info\\RECORD',
   'DATA'),
  ('tzdata-2025.2.dist-info\\WHEEL',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata-2025.2.dist-info\\WHEEL',
   'DATA'),
  ('six-1.17.0.dist-info\\LICENSE',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\six-1.17.0.dist-info\\LICENSE',
   'DATA'),
  ('pytz-2025.2.dist-info\\top_level.txt',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz-2025.2.dist-info\\top_level.txt',
   'DATA'),
  ('tzdata-2025.2.dist-info\\licenses\\LICENSE',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata-2025.2.dist-info\\licenses\\LICENSE',
   'DATA'),
  ('pytz-2025.2.dist-info\\METADATA',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz-2025.2.dist-info\\METADATA',
   'DATA'),
  ('six-1.17.0.dist-info\\RECORD',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\six-1.17.0.dist-info\\RECORD',
   'DATA'),
  ('tzdata-2025.2.dist-info\\INSTALLER',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata-2025.2.dist-info\\INSTALLER',
   'DATA'),
  ('tzdata-2025.2.dist-info\\licenses\\licenses\\LICENSE_APACHE',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata-2025.2.dist-info\\licenses\\licenses\\LICENSE_APACHE',
   'DATA'),
  ('APScheduler-3.10.4.dist-info\\RECORD',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\APScheduler-3.10.4.dist-info\\RECORD',
   'DATA'),
  ('six-1.17.0.dist-info\\top_level.txt',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\six-1.17.0.dist-info\\top_level.txt',
   'DATA'),
  ('six-1.17.0.dist-info\\METADATA',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\six-1.17.0.dist-info\\METADATA',
   'DATA'),
  ('APScheduler-3.10.4.dist-info\\REQUESTED',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\APScheduler-3.10.4.dist-info\\REQUESTED',
   'DATA'),
  ('pytz-2025.2.dist-info\\INSTALLER',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz-2025.2.dist-info\\INSTALLER',
   'DATA'),
  ('APScheduler-3.10.4.dist-info\\INSTALLER',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\APScheduler-3.10.4.dist-info\\INSTALLER',
   'DATA'),
  ('tzdata-2025.2.dist-info\\RECORD',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata-2025.2.dist-info\\RECORD',
   'DATA'),
  ('APScheduler-3.10.4.dist-info\\METADATA',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\APScheduler-3.10.4.dist-info\\METADATA',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vienna',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Europe\\Vienna',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Eastern',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Canada\\Eastern',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Eucla',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Australia\\Eucla',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Porto_Acre',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\America\\Porto_Acre',
   'DATA'),
  ('_tcl_data\\tzdata\\Poland',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Poland',
   'DATA'),
  ('_tcl_data\\msgs\\pt_br.msg',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\msgs\\pt_br.msg',
   'DATA'),
  ('_tcl_data\\encoding\\macRoman.enc',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\encoding\\macRoman.enc',
   'DATA'),
  ('_tk_data\\optMenu.tcl',
   'D:/anaconda3/envs/dt/Library/lib\\tk8.6\\optMenu.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Monterrey',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\America\\Monterrey',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Bermuda',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Atlantic\\Bermuda',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Faroe',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Atlantic\\Faroe',
   'DATA'),
  ('_tcl_data\\encoding\\cp855.enc',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\encoding\\cp855.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Pontianak',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Asia\\Pontianak',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Kitts',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\America\\St_Kitts',
   'DATA'),
  ('_tk_data\\msgs\\ru.msg',
   'D:/anaconda3/envs/dt/Library/lib\\tk8.6\\msgs\\ru.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Urumqi',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Asia\\Urumqi',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Guyana',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\America\\Guyana',
   'DATA'),
  ('_tcl_data\\tclIndex',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tclIndex',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Qatar',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Asia\\Qatar',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Novosibirsk',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Asia\\Novosibirsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Perth',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Australia\\Perth',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Matamoros',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\America\\Matamoros',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\St_Helena',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Atlantic\\St_Helena',
   'DATA'),
  ('_tcl_data\\msgs\\en_in.msg',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\msgs\\en_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\Zulu',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Etc\\Zulu',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-0',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Etc\\GMT-0',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Fort_Nelson',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\America\\Fort_Nelson',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Indianapolis',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\America\\Indiana\\Indianapolis',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Atikokan',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\America\\Atikokan',
   'DATA'),
  ('_tcl_data\\encoding\\macCentEuro.enc',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\encoding\\macCentEuro.enc',
   'DATA'),
  ('_tk_data\\scrlbar.tcl',
   'D:/anaconda3/envs/dt/Library/lib\\tk8.6\\scrlbar.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ashkhabad',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Asia\\Ashkhabad',
   'DATA'),
  ('_tcl_data\\encoding\\ksc5601.enc',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\encoding\\ksc5601.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Kyiv',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Europe\\Kyiv',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Vientiane',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Asia\\Vientiane',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Srednekolymsk',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Asia\\Srednekolymsk',
   'DATA'),
  ('_tcl_data\\msgs\\ru.msg',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\msgs\\ru.msg',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo.eps',
   'D:/anaconda3/envs/dt/Library/lib\\tk8.6\\images\\pwrdLogo.eps',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bujumbura',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Africa\\Bujumbura',
   'DATA'),
  ('_tcl_data\\tzdata\\NZ',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\NZ',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Galapagos',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Pacific\\Galapagos',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Martinique',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\America\\Martinique',
   'DATA'),
  ('_tk_data\\ttk\\button.tcl',
   'D:/anaconda3/envs/dt/Library/lib\\tk8.6\\ttk\\button.tcl',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo175.gif',
   'D:/anaconda3/envs/dt/Library/lib\\tk8.6\\images\\pwrdLogo175.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Aleutian',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\US\\Aleutian',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Monaco',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Europe\\Monaco',
   'DATA'),
  ('_tk_data\\comdlg.tcl',
   'D:/anaconda3/envs/dt/Library/lib\\tk8.6\\comdlg.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Mexico_City',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\America\\Mexico_City',
   'DATA'),
  ('_tcl_data\\tzdata\\Israel',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Israel',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Warsaw',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Europe\\Warsaw',
   'DATA'),
  ('_tcl_data\\http1.0\\pkgIndex.tcl',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\http1.0\\pkgIndex.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Hongkong',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Hongkong',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Rothera',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Antarctica\\Rothera',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Chuuk',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Pacific\\Chuuk',
   'DATA'),
  ('_tcl_data\\encoding\\jis0212.enc',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\encoding\\jis0212.enc',
   'DATA'),
  ('_tcl_data\\msgs\\it_ch.msg',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\msgs\\it_ch.msg',
   'DATA'),
  ('_tcl_data\\msgs\\lv.msg',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\msgs\\lv.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Enderbury',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Pacific\\Enderbury',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Sakhalin',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Asia\\Sakhalin',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\North',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Australia\\North',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\London',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Europe\\London',
   'DATA'),
  ('_tcl_data\\msgs\\ja.msg',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\msgs\\ja.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Chita',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Asia\\Chita',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Famagusta',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Asia\\Famagusta',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dubai',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Asia\\Dubai',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\PST8PDT',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\SystemV\\PST8PDT',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Managua',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\America\\Managua',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Edmonton',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\America\\Edmonton',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+10',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Etc\\GMT+10',
   'DATA'),
  ('_tk_data\\xmfbox.tcl',
   'D:/anaconda3/envs/dt/Library/lib\\tk8.6\\xmfbox.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\ascii.enc',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\encoding\\ascii.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Maputo',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Africa\\Maputo',
   'DATA'),
  ('_tcl_data\\msgs\\bg.msg',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\msgs\\bg.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp865.enc',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\encoding\\cp865.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Grenada',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\America\\Grenada',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Manaus',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\America\\Manaus',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Havana',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\America\\Havana',
   'DATA'),
  ('_tcl_data\\msgs\\en_zw.msg',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\msgs\\en_zw.msg',
   'DATA'),
  ('_tcl_data\\msgs\\eo.msg',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\msgs\\eo.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Christmas',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Indian\\Christmas',
   'DATA'),
  ('_tcl_data\\msgs\\mt.msg',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\msgs\\mt.msg',
   'DATA'),
  ('_tcl_data\\msgs\\zh.msg',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\msgs\\zh.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Mountain',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\US\\Mountain',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Iqaluit',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\America\\Iqaluit',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Puerto_Rico',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\America\\Puerto_Rico',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Luxembourg',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Europe\\Luxembourg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Ceuta',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Africa\\Ceuta',
   'DATA'),
  ('_tcl_data\\tzdata\\Mexico\\General',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Mexico\\General',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Harare',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Africa\\Harare',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-13.enc',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\encoding\\iso8859-13.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\UTC',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\UTC',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Dublin',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Europe\\Dublin',
   'DATA'),
  ('_tcl_data\\msgs\\en_be.msg',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\msgs\\en_be.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp1250.enc',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\encoding\\cp1250.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Sitka',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\America\\Sitka',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Istanbul',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Europe\\Istanbul',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Newfoundland',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Canada\\Newfoundland',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Antananarivo',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Indian\\Antananarivo',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Dar_es_Salaam',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Africa\\Dar_es_Salaam',
   'DATA'),
  ('_tcl_data\\msgs\\es_ni.msg',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\msgs\\es_ni.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT0',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Etc\\GMT0',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-4',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Etc\\GMT-4',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Yellowknife',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\America\\Yellowknife',
   'DATA'),
  ('_tcl_data\\encoding\\jis0201.enc',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\encoding\\jis0201.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tbilisi',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Asia\\Tbilisi',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Irkutsk',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Asia\\Irkutsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Bangkok',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Asia\\Bangkok',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Busingen',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Europe\\Busingen',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kuching',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Asia\\Kuching',
   'DATA'),
  ('_tcl_data\\tzdata\\Cuba',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Cuba',
   'DATA'),
  ('_tcl_data\\msgs\\fo_fo.msg',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\msgs\\fo_fo.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\EET',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\EET',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Niue',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Pacific\\Niue',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vaduz',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Europe\\Vaduz',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Melbourne',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Australia\\Melbourne',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lagos',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Africa\\Lagos',
   'DATA'),
  ('_tcl_data\\msgs\\sw.msg',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\msgs\\sw.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp1255.enc',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\encoding\\cp1255.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Shanghai',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Asia\\Shanghai',
   'DATA'),
  ('_tk_data\\ttk\\defaults.tcl',
   'D:/anaconda3/envs/dt/Library/lib\\tk8.6\\ttk\\defaults.tcl',
   'DATA'),
  ('_tk_data\\ttk\\spinbox.tcl',
   'D:/anaconda3/envs/dt/Library/lib\\tk8.6\\ttk\\spinbox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Marquesas',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Pacific\\Marquesas',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cayenne',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\America\\Cayenne',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Gaborone',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Africa\\Gaborone',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-6.enc',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\encoding\\iso8859-6.enc',
   'DATA'),
  ('_tcl_data\\tm.tcl',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tm.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Hermosillo',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\America\\Hermosillo',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\EST5',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\SystemV\\EST5',
   'DATA'),
  ('_tcl_data\\msgs\\eu.msg',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\msgs\\eu.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Jamaica',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Jamaica',
   'DATA'),
  ('_tk_data\\obsolete.tcl',
   'D:/anaconda3/envs/dt/Library/lib\\tk8.6\\obsolete.tcl',
   'DATA'),
  ('_tk_data\\images\\logo100.gif',
   'D:/anaconda3/envs/dt/Library/lib\\tk8.6\\images\\logo100.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-7',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Etc\\GMT-7',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cambridge_Bay',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\America\\Cambridge_Bay',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Caracas',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\America\\Caracas',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Araguaina',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\America\\Araguaina',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Qyzylorda',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Asia\\Qyzylorda',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Adelaide',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Australia\\Adelaide',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Kwajalein',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Pacific\\Kwajalein',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Belfast',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Europe\\Belfast',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Panama',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\America\\Panama',
   'DATA'),
  ('_tk_data\\mkpsenc.tcl',
   'D:/anaconda3/envs/dt/Library/lib\\tk8.6\\mkpsenc.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+1',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Etc\\GMT+1',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Tahiti',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Pacific\\Tahiti',
   'DATA'),
  ('_tk_data\\images\\logo.eps',
   'D:/anaconda3/envs/dt/Library/lib\\tk8.6\\images\\logo.eps',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Halifax',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\America\\Halifax',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Kiritimati',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Pacific\\Kiritimati',
   'DATA'),
  ('_tk_data\\msgs\\fi.msg',
   'D:/anaconda3/envs/dt/Library/lib\\tk8.6\\msgs\\fi.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Phoenix',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\America\\Phoenix',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Guernsey',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Europe\\Guernsey',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\UTC',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Etc\\UTC',
   'DATA'),
  ('_tcl_data\\msgs\\es.msg',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\msgs\\es.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\McMurdo',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Antarctica\\McMurdo',
   'DATA'),
  ('_tcl_data\\msgs\\hr.msg',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\msgs\\hr.msg',
   'DATA'),
  ('_tcl_data\\msgs\\be.msg',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\msgs\\be.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Amman',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Asia\\Amman',
   'DATA'),
  ('_tk_data\\scale.tcl',
   'D:/anaconda3/envs/dt/Library/lib\\tk8.6\\scale.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Rome',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Europe\\Rome',
   'DATA'),
  ('_tcl_data\\msgs\\en_au.msg',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\msgs\\en_au.msg',
   'DATA'),
  ('_tk_data\\msgs\\de.msg',
   'D:/anaconda3/envs/dt/Library/lib\\tk8.6\\msgs\\de.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Boa_Vista',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\America\\Boa_Vista',
   'DATA'),
  ('_tcl_data\\msgs\\mr_in.msg',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\msgs\\mr_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Tucuman',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\America\\Argentina\\Tucuman',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Macao',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Asia\\Macao',
   'DATA'),
  ('_tcl_data\\msgs\\ar_jo.msg',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\msgs\\ar_jo.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Johnston',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Pacific\\Johnston',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Almaty',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Asia\\Almaty',
   'DATA'),
  ('_tcl_data\\msgs\\af_za.msg',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\msgs\\af_za.msg',
   'DATA'),
  ('_tcl_data\\encoding\\euc-kr.enc',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\encoding\\euc-kr.enc',
   'DATA'),
  ('_tcl_data\\msgs\\ar_lb.msg',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\msgs\\ar_lb.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Iceland',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Iceland',
   'DATA'),
  ('_tcl_data\\msgs\\it.msg',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\msgs\\it.msg',
   'DATA'),
  ('_tcl_data\\msgs\\en_hk.msg',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\msgs\\en_hk.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+2',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Etc\\GMT+2',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Zaporozhye',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Europe\\Zaporozhye',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-1.enc',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\encoding\\iso8859-1.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+4',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Etc\\GMT+4',
   'DATA'),
  ('_tcl_data\\msgs\\kl_gl.msg',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\msgs\\kl_gl.msg',
   'DATA'),
  ('_tk_data\\bgerror.tcl',
   'D:/anaconda3/envs/dt/Library/lib\\tk8.6\\bgerror.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Winamac',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\America\\Indiana\\Winamac',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Tasmania',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Australia\\Tasmania',
   'DATA'),
  ('_tcl_data\\encoding\\macTurkish.enc',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\encoding\\macTurkish.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lusaka',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Africa\\Lusaka',
   'DATA'),
  ('_tk_data\\palette.tcl',
   'D:/anaconda3/envs/dt/Library/lib\\tk8.6\\palette.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\cp775.enc',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\encoding\\cp775.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-3',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Etc\\GMT-3',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Accra',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Africa\\Accra',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Tallinn',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Europe\\Tallinn',
   'DATA'),
  ('_tcl_data\\opt0.4\\optparse.tcl',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\opt0.4\\optparse.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Thunder_Bay',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\America\\Thunder_Bay',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Faeroe',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Atlantic\\Faeroe',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Indiana-Starke',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\US\\Indiana-Starke',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Copenhagen',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Europe\\Copenhagen',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-5',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Etc\\GMT-5',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Tunis',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Africa\\Tunis',
   'DATA'),
  ('_tcl_data\\msgs\\zh_tw.msg',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\msgs\\zh_tw.msg',
   'DATA'),
  ('_tcl_data\\encoding\\dingbats.enc',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\encoding\\dingbats.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\La_Paz',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\America\\La_Paz',
   'DATA'),
  ('_tcl_data\\msgs\\fo.msg',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\msgs\\fo.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Malta',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Europe\\Malta',
   'DATA'),
  ('_tcl_data\\tzdata\\Portugal',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Portugal',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Mountain',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Canada\\Mountain',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Lower_Princes',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\America\\Lower_Princes',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Fort_Wayne',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\America\\Fort_Wayne',
   'DATA'),
  ('_tk_data\\tearoff.tcl',
   'D:/anaconda3/envs/dt/Library/lib\\tk8.6\\tearoff.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\es_do.msg',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\msgs\\es_do.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Japan',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Japan',
   'DATA'),
  ('_tcl_data\\encoding\\iso2022-kr.enc',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\encoding\\iso2022-kr.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Pacific',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\US\\Pacific',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Canary',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Atlantic\\Canary',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Pago_Pago',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Pacific\\Pago_Pago',
   'DATA'),
  ('_tcl_data\\encoding\\cp950.enc',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\encoding\\cp950.enc',
   'DATA'),
  ('_tcl_data\\encoding\\macThai.enc',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\encoding\\macThai.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Nipigon',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\America\\Nipigon',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Palmer',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Antarctica\\Palmer',
   'DATA'),
  ('_tcl_data\\msgs\\te_in.msg',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\msgs\\te_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Buenos_Aires',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\America\\Buenos_Aires',
   'DATA'),
  ('_tcl_data\\tzdata\\GMT-0',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\GMT-0',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Bucharest',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Europe\\Bucharest',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Belem',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\America\\Belem',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Kentucky\\Louisville',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\America\\Kentucky\\Louisville',
   'DATA'),
  ('_tcl_data\\msgs\\ga.msg',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\msgs\\ga.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Samarkand',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Asia\\Samarkand',
   'DATA'),
  ('_tk_data\\msgs\\zh_cn.msg',
   'D:/anaconda3/envs/dt/Library/lib\\tk8.6\\msgs\\zh_cn.msg',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo150.gif',
   'D:/anaconda3/envs/dt/Library/lib\\tk8.6\\images\\pwrdLogo150.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Chile\\EasterIsland',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Chile\\EasterIsland',
   'DATA'),
  ('_tk_data\\ttk\\xpTheme.tcl',
   'D:/anaconda3/envs/dt/Library/lib\\tk8.6\\ttk\\xpTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Macquarie',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Antarctica\\Macquarie',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Salta',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\America\\Argentina\\Salta',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Ojinaga',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\America\\Ojinaga',
   'DATA'),
  ('_tcl_data\\msgs\\es_hn.msg',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\msgs\\es_hn.msg',
   'DATA'),
  ('_tk_data\\panedwindow.tcl',
   'D:/anaconda3/envs/dt/Library/lib\\tk8.6\\panedwindow.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Choibalsan',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Asia\\Choibalsan',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Jerusalem',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Asia\\Jerusalem',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Belgrade',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Europe\\Belgrade',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Nuuk',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\America\\Nuuk',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Podgorica',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Europe\\Podgorica',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Santa_Isabel',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\America\\Santa_Isabel',
   'DATA'),
  ('_tcl_data\\clock.tcl',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\clock.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Egypt',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Egypt',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Rosario',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\America\\Rosario',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Metlakatla',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\America\\Metlakatla',
   'DATA'),
  ('_tcl_data\\msgs\\de_at.msg',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\msgs\\de_at.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Detroit',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\America\\Detroit',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Kralendijk',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\America\\Kralendijk',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Hong_Kong',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Asia\\Hong_Kong',
   'DATA'),
  ('_tcl_data\\msgs\\bn.msg',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\msgs\\bn.msg',
   'DATA'),
  ('_tcl_data\\msgs\\es_cl.msg',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\msgs\\es_cl.msg',
   'DATA'),
  ('_tk_data\\menu.tcl',
   'D:/anaconda3/envs/dt/Library/lib\\tk8.6\\menu.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\HST',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\HST',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Singapore',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Asia\\Singapore',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cordoba',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\America\\Cordoba',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Sao_Paulo',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\America\\Sao_Paulo',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-4.enc',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\encoding\\iso8859-4.enc',
   'DATA'),
  ('_tcl_data\\encoding\\macRomania.enc',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\encoding\\macRomania.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Kwajalein',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Kwajalein',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Mendoza',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\America\\Mendoza',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Pacific',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Canada\\Pacific',
   'DATA'),
  ('_tk_data\\ttk\\clamTheme.tcl',
   'D:/anaconda3/envs/dt/Library/lib\\tk8.6\\ttk\\clamTheme.tcl',
   'DATA'),
  ('_tk_data\\images\\logoLarge.gif',
   'D:/anaconda3/envs/dt/Library/lib\\tk8.6\\images\\logoLarge.gif',
   'DATA'),
  ('_tcl_data\\encoding\\cp1258.enc',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\encoding\\cp1258.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Juba',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Africa\\Juba',
   'DATA'),
  ('_tcl_data\\tzdata\\Mexico\\BajaNorte',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Mexico\\BajaNorte',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Gambier',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Pacific\\Gambier',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Noumea',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Pacific\\Noumea',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\ComodRivadavia',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\America\\Argentina\\ComodRivadavia',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Asmara',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Africa\\Asmara',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Noronha',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\America\\Noronha',
   'DATA'),
  ('_tcl_data\\msgs\\ko.msg',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\msgs\\ko.msg',
   'DATA'),
  ('_tk_data\\megawidget.tcl',
   'D:/anaconda3/envs/dt/Library/lib\\tk8.6\\megawidget.tcl',
   'DATA'),
  ('_tk_data\\msgs\\es.msg',
   'D:/anaconda3/envs/dt/Library/lib\\tk8.6\\msgs\\es.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Jujuy',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\America\\Argentina\\Jujuy',
   'DATA'),
  ('_tcl_data\\encoding\\cp1256.enc',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\encoding\\cp1256.enc',
   'DATA'),
  ('_tk_data\\ttk\\altTheme.tcl',
   'D:/anaconda3/envs/dt/Library/lib\\tk8.6\\ttk\\altTheme.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\en_nz.msg',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\msgs\\en_nz.msg',
   'DATA'),
  ('_tcl_data\\msgs\\es_uy.msg',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\msgs\\es_uy.msg',
   'DATA'),
  ('_tcl_data\\msgs\\bn_in.msg',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\msgs\\bn_in.msg',
   'DATA'),
  ('_tk_data\\ttk\\sizegrip.tcl',
   'D:/anaconda3/envs/dt/Library/lib\\tk8.6\\ttk\\sizegrip.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Ponape',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Pacific\\Ponape',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Damascus',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Asia\\Damascus',
   'DATA'),
  ('_tk_data\\msgs\\da.msg',
   'D:/anaconda3/envs/dt/Library/lib\\tk8.6\\msgs\\da.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dacca',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Asia\\Dacca',
   'DATA'),
  ('_tk_data\\ttk\\treeview.tcl',
   'D:/anaconda3/envs/dt/Library/lib\\tk8.6\\ttk\\treeview.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Bogota',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\America\\Bogota',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Stockholm',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Europe\\Stockholm',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Dominica',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\America\\Dominica',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Central',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Canada\\Central',
   'DATA'),
  ('_tk_data\\msgs\\eo.msg',
   'D:/anaconda3/envs/dt/Library/lib\\tk8.6\\msgs\\eo.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Aden',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Asia\\Aden',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\MST7',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\SystemV\\MST7',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Maceio',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\America\\Maceio',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Kinshasa',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Africa\\Kinshasa',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Chatham',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Pacific\\Chatham',
   'DATA'),
  ('_tcl_data\\msgs\\id_id.msg',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\msgs\\id_id.msg',
   'DATA'),
  ('_tcl_data\\history.tcl',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\history.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Creston',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\America\\Creston',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Cape_Verde',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Atlantic\\Cape_Verde',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Douala',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Africa\\Douala',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tomsk',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Asia\\Tomsk',
   'DATA'),
  ('_tcl_data\\encoding\\shiftjis.enc',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\encoding\\shiftjis.enc',
   'DATA'),
  ('_tk_data\\ttk\\notebook.tcl',
   'D:/anaconda3/envs/dt/Library/lib\\tk8.6\\ttk\\notebook.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Santiago',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\America\\Santiago',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\Greenwich',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Etc\\Greenwich',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ho_Chi_Minh',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Asia\\Ho_Chi_Minh',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Lisbon',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Europe\\Lisbon',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Comoro',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Indian\\Comoro',
   'DATA'),
  ('_tcl_data\\msgs\\gv.msg',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\msgs\\gv.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\GB-Eire',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\GB-Eire',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Barnaul',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Asia\\Barnaul',
   'DATA'),
  ('_tcl_data\\tzdata\\EST5EDT',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\EST5EDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Arctic\\Longyearbyen',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Arctic\\Longyearbyen',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Guadalcanal',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Pacific\\Guadalcanal',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Wallis',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Pacific\\Wallis',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cuiaba',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\America\\Cuiaba',
   'DATA'),
  ('_tcl_data\\msgs\\es_py.msg',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\msgs\\es_py.msg',
   'DATA'),
  ('_tcl_data\\msgs\\ro.msg',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\msgs\\ro.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+0',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Etc\\GMT+0',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Reykjavik',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Atlantic\\Reykjavik',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Rarotonga',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Pacific\\Rarotonga',
   'DATA'),
  ('_tcl_data\\tzdata\\UCT',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\UCT',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Alaska',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\US\\Alaska',
   'DATA'),
  ('_tcl_data\\msgs\\es_pr.msg',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\msgs\\es_pr.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp437.enc',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\encoding\\cp437.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Abidjan',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Africa\\Abidjan',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\MST7MDT',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\SystemV\\MST7MDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tashkent',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Asia\\Tashkent',
   'DATA'),
  ('_tcl_data\\msgs\\en_ca.msg',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\msgs\\en_ca.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\South_Pole',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Antarctica\\South_Pole',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Riga',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Europe\\Riga',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Mariehamn',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Europe\\Mariehamn',
   'DATA'),
  ('_tcl_data\\encoding\\tis-620.enc',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\encoding\\tis-620.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Truk',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Pacific\\Truk',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Hebron',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Asia\\Hebron',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Glace_Bay',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\America\\Glace_Bay',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Tongatapu',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Pacific\\Tongatapu',
   'DATA'),
  ('_tk_data\\images\\logoMed.gif',
   'D:/anaconda3/envs/dt/Library/lib\\tk8.6\\images\\logoMed.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Recife',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\America\\Recife',
   'DATA'),
  ('_tcl_data\\tzdata\\PRC',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\PRC',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-9',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Etc\\GMT-9',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Timbuktu',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Africa\\Timbuktu',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Central',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\US\\Central',
   'DATA'),
  ('_tcl_data\\msgs\\kok.msg',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\msgs\\kok.msg',
   'DATA'),
  ('_tcl_data\\encoding\\big5.enc',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\encoding\\big5.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Etc\\GMT',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\YST9YDT',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\SystemV\\YST9YDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Chile\\Continental',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Chile\\Continental',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Volgograd',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Europe\\Volgograd',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Menominee',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\America\\Menominee',
   'DATA'),
  ('_tcl_data\\tzdata\\Greenwich',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Greenwich',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Astrakhan',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Europe\\Astrakhan',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Kigali',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Africa\\Kigali',
   'DATA'),
  ('_tcl_data\\msgs\\sh.msg',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\msgs\\sh.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Beirut',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Asia\\Beirut',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Resolute',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\America\\Resolute',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dili',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Asia\\Dili',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kamchatka',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Asia\\Kamchatka',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Saipan',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Pacific\\Saipan',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-2',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Etc\\GMT-2',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lome',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Africa\\Lome',
   'DATA'),
  ('_tcl_data\\msgs\\es_gt.msg',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\msgs\\es_gt.msg',
   'DATA'),
  ('_tcl_data\\msgs\\eu_es.msg',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\msgs\\eu_es.msg',
   'DATA'),
  ('_tcl_data\\msgs\\te.msg',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\msgs\\te.msg',
   'DATA'),
  ('_tk_data\\tkfbox.tcl',
   'D:/anaconda3/envs/dt/Library/lib\\tk8.6\\tkfbox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\South',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Australia\\South',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Arizona',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\US\\Arizona',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\East-Indiana',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\US\\East-Indiana',
   'DATA'),
  ('_tk_data\\choosedir.tcl',
   'D:/anaconda3/envs/dt/Library/lib\\tk8.6\\choosedir.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Saratov',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Europe\\Saratov',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Ouagadougou',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Africa\\Ouagadougou',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\El_Aaiun',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Africa\\El_Aaiun',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Anchorage',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\America\\Anchorage',
   'DATA'),
  ('_tcl_data\\encoding\\cp850.enc',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\encoding\\cp850.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Kirov',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Europe\\Kirov',
   'DATA'),
  ('_tcl_data\\msgs\\sl.msg',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\msgs\\sl.msg',
   'DATA'),
  ('_tcl_data\\msgs\\nn.msg',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\msgs\\nn.msg',
   'DATA'),
  ('_tcl_data\\msgs\\en_sg.msg',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\msgs\\en_sg.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Danmarkshavn',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\America\\Danmarkshavn',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Winnipeg',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\America\\Winnipeg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Omsk',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Asia\\Omsk',
   'DATA'),
  ('_tcl_data\\msgs\\id.msg',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\msgs\\id.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Novokuznetsk',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Asia\\Novokuznetsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Qostanay',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Asia\\Qostanay',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Apia',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Pacific\\Apia',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Mahe',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Indian\\Mahe',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Krasnoyarsk',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Asia\\Krasnoyarsk',
   'DATA'),
  ('_tcl_data\\msgs\\is.msg',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\msgs\\is.msg',
   'DATA'),
  ('_tk_data\\ttk\\scrollbar.tcl',
   'D:/anaconda3/envs/dt/Library/lib\\tk8.6\\ttk\\scrollbar.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Michigan',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\US\\Michigan',
   'DATA'),
  ('_tcl_data\\msgs\\af.msg',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\msgs\\af.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ust-Nera',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Asia\\Ust-Nera',
   'DATA'),
  ('_tcl_data\\encoding\\jis0208.enc',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\encoding\\jis0208.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\UCT',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Etc\\UCT',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Palau',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Pacific\\Palau',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Madrid',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Europe\\Madrid',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-1',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Etc\\GMT-1',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Mauritius',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Indian\\Mauritius',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Vevay',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\America\\Indiana\\Vevay',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Mogadishu',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Africa\\Mogadishu',
   'DATA'),
  ('_tcl_data\\msgs\\mr.msg',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\msgs\\mr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\EST5EDT',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\SystemV\\EST5EDT',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Thule',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\America\\Thule',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Godthab',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\America\\Godthab',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Marengo',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\America\\Indiana\\Marengo',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Brisbane',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Australia\\Brisbane',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Fiji',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Pacific\\Fiji',
   'DATA'),
  ('_tk_data\\msgs\\fr.msg',
   'D:/anaconda3/envs/dt/Library/lib\\tk8.6\\msgs\\fr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\MST7MDT',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\MST7MDT',
   'DATA'),
  ('_tcl_data\\msgs\\es_sv.msg',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\msgs\\es_sv.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Libya',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Libya',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Honolulu',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Pacific\\Honolulu',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Berlin',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Europe\\Berlin',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Seoul',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Asia\\Seoul',
   'DATA'),
  ('_tk_data\\pkgIndex.tcl',
   'D:/anaconda3/envs/dt/Library/lib\\tk8.6\\pkgIndex.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ulaanbaatar',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Asia\\Ulaanbaatar',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+5',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Etc\\GMT+5',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Manila',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Asia\\Manila',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Zagreb',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Europe\\Zagreb',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Tarawa',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Pacific\\Tarawa',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Asuncion',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\America\\Asuncion',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Lima',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\America\\Lima',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Windhoek',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Africa\\Windhoek',
   'DATA'),
  ('_tcl_data\\msgs\\es_ec.msg',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\msgs\\es_ec.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\ROC',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\ROC',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-10',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Etc\\GMT-10',
   'DATA'),
  ('_tcl_data\\encoding\\cp862.enc',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\encoding\\cp862.enc',
   'DATA'),
  ('_tk_data\\text.tcl',
   'D:/anaconda3/envs/dt/Library/lib\\tk8.6\\text.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Mayotte',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Indian\\Mayotte',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Hawaii',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\US\\Hawaii',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Guam',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Pacific\\Guam',
   'DATA'),
  ('_tcl_data\\tzdata\\MET',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\MET',
   'DATA'),
  ('_tcl_data\\encoding\\cp860.enc',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\encoding\\cp860.enc',
   'DATA'),
  ('_tcl_data\\msgs\\fr_ca.msg',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\msgs\\fr_ca.msg',
   'DATA'),
  ('_tcl_data\\msgs\\en_gb.msg',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\msgs\\en_gb.msg',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo100.gif',
   'D:/anaconda3/envs/dt/Library/lib\\tk8.6\\images\\pwrdLogo100.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Brunei',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Asia\\Brunei',
   'DATA'),
  ('_tcl_data\\encoding\\cp1253.enc',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\encoding\\cp1253.enc',
   'DATA'),
  ('_tk_data\\safetk.tcl',
   'D:/anaconda3/envs/dt/Library/lib\\tk8.6\\safetk.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Universal',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Universal',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Katmandu',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Asia\\Katmandu',
   'DATA'),
  ('_tk_data\\msgs\\pt.msg',
   'D:/anaconda3/envs/dt/Library/lib\\tk8.6\\msgs\\pt.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Eastern',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\US\\Eastern',
   'DATA'),
  ('_tcl_data\\tzdata\\MST',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\MST',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\New_York',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\America\\New_York',
   'DATA'),
  ('_tcl_data\\encoding\\iso2022-jp.enc',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\encoding\\iso2022-jp.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\W-SU',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\W-SU',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-7.enc',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\encoding\\iso8859-7.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Boise',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\America\\Boise',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-3.enc',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\encoding\\iso8859-3.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Nome',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\America\\Nome',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Knox_IN',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\America\\Knox_IN',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Belize',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\America\\Belize',
   'DATA'),
  ('_tk_data\\clrpick.tcl',
   'D:/anaconda3/envs/dt/Library/lib\\tk8.6\\clrpick.tcl',
   'DATA'),
  ('_tk_data\\ttk\\winTheme.tcl',
   'D:/anaconda3/envs/dt/Library/lib\\tk8.6\\ttk\\winTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Navajo',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Navajo',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-12',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Etc\\GMT-12',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lubumbashi',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Africa\\Lubumbashi',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Auckland',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Pacific\\Auckland',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Sofia',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Europe\\Sofia',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Antigua',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\America\\Antigua',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Luanda',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Africa\\Luanda',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Fortaleza',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\America\\Fortaleza',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+8',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Etc\\GMT+8',
   'DATA'),
  ('_tcl_data\\msgs\\fa_in.msg',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\msgs\\fa_in.msg',
   'DATA'),
  ('_tcl_data\\encoding\\macUkraine.enc',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\encoding\\macUkraine.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Macau',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Asia\\Macau',
   'DATA'),
  ('_tcl_data\\tzdata\\Mexico\\BajaSur',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Mexico\\BajaSur',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Cocos',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Indian\\Cocos',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\YST9',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\SystemV\\YST9',
   'DATA'),
  ('_tcl_data\\auto.tcl',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\auto.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tokyo',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Asia\\Tokyo',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\West',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Australia\\West',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Queensland',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Australia\\Queensland',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Calcutta',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Asia\\Calcutta',
   'DATA'),
  ('_tcl_data\\tzdata\\NZ-CHAT',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\NZ-CHAT',
   'DATA'),
  ('_tcl_data\\tzdata\\WET',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\WET',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Miquelon',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\America\\Miquelon',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Oslo',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Europe\\Oslo',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Istanbul',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Asia\\Istanbul',
   'DATA'),
  ('_tk_data\\ttk\\vistaTheme.tcl',
   'D:/anaconda3/envs/dt/Library/lib\\tk8.6\\ttk\\vistaTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Reunion',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Indian\\Reunion',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vilnius',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Europe\\Vilnius',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Adak',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\America\\Adak',
   'DATA'),
  ('_tcl_data\\msgs\\gv_gb.msg',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\msgs\\gv_gb.msg',
   'DATA'),
  ('_tcl_data\\msgs\\hi_in.msg',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\msgs\\hi_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Sao_Tome',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Africa\\Sao_Tome',
   'DATA'),
  ('_tk_data\\images\\tai-ku.gif',
   'D:/anaconda3/envs/dt/Library/lib\\tk8.6\\images\\tai-ku.gif',
   'DATA'),
  ('_tcl_data\\msgs\\uk.msg',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\msgs\\uk.msg',
   'DATA'),
  ('_tk_data\\msgs\\pl.msg',
   'D:/anaconda3/envs/dt/Library/lib\\tk8.6\\msgs\\pl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Barthelemy',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\America\\St_Barthelemy',
   'DATA'),
  ('_tcl_data\\msgs\\ta.msg',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\msgs\\ta.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Mendoza',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\America\\Argentina\\Mendoza',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\DeNoronha',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Brazil\\DeNoronha',
   'DATA'),
  ('_tcl_data\\msgs\\kw_gb.msg',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\msgs\\kw_gb.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ujung_Pandang',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Asia\\Ujung_Pandang',
   'DATA'),
  ('_tcl_data\\msgs\\en_ph.msg',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\msgs\\en_ph.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Goose_Bay',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\America\\Goose_Bay',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-6',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Etc\\GMT-6',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kabul',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Asia\\Kabul',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-8.enc',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\encoding\\iso8859-8.enc',
   'DATA'),
  ('_tk_data\\button.tcl',
   'D:/anaconda3/envs/dt/Library/lib\\tk8.6\\button.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Broken_Hill',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Australia\\Broken_Hill',
   'DATA'),
  ('_tk_data\\iconlist.tcl',
   'D:/anaconda3/envs/dt/Library/lib\\tk8.6\\iconlist.tcl',
   'DATA'),
  ('_tcl_data\\word.tcl',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\word.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\gb2312-raw.enc',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\encoding\\gb2312-raw.enc',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-11.enc',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\encoding\\iso8859-11.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Banjul',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Africa\\Banjul',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Lucia',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\America\\St_Lucia',
   'DATA'),
  ('_tk_data\\spinbox.tcl',
   'D:/anaconda3/envs/dt/Library/lib\\tk8.6\\spinbox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Isle_of_Man',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Europe\\Isle_of_Man',
   'DATA'),
  ('_tcl_data\\msgs\\ar.msg',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\msgs\\ar.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Andorra',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Europe\\Andorra',
   'DATA'),
  ('_tcl_data\\encoding\\macGreek.enc',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\encoding\\macGreek.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Madeira',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Atlantic\\Madeira',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\San_Luis',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\America\\Argentina\\San_Luis',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Chongqing',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Asia\\Chongqing',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indianapolis',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\America\\Indianapolis',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Ciudad_Juarez',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\America\\Ciudad_Juarez',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Pitcairn',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Pacific\\Pitcairn',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Regina',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\America\\Regina',
   'DATA'),
  ('_tcl_data\\encoding\\cp863.enc',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\encoding\\cp863.enc',
   'DATA'),
  ('_tcl_data\\msgs\\zh_sg.msg',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\msgs\\zh_sg.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\ROK',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\ROK',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Prague',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Europe\\Prague',
   'DATA'),
  ('_tcl_data\\init.tcl',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\init.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Aruba',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\America\\Aruba',
   'DATA'),
  ('_tcl_data\\msgs\\fr_ch.msg',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\msgs\\fr_ch.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Eirunepe',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\America\\Eirunepe',
   'DATA'),
  ('_tcl_data\\encoding\\cp936.enc',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\encoding\\cp936.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Nassau',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\America\\Nassau',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Syowa',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Antarctica\\Syowa',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Atka',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\America\\Atka',
   'DATA'),
  ('_tcl_data\\msgs\\fi.msg',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\msgs\\fi.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Louisville',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\America\\Louisville',
   'DATA'),
  ('_tk_data\\msgs\\cs.msg',
   'D:/anaconda3/envs/dt/Library/lib\\tk8.6\\msgs\\cs.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Inuvik',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\America\\Inuvik',
   'DATA'),
  ('_tcl_data\\encoding\\macDingbats.enc',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\encoding\\macDingbats.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Ljubljana',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Europe\\Ljubljana',
   'DATA'),
  ('_tcl_data\\msgs\\ar_in.msg',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\msgs\\ar_in.msg',
   'DATA'),
  ('_tcl_data\\msgs\\ko_kr.msg',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\msgs\\ko_kr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Riyadh',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Asia\\Riyadh',
   'DATA'),
  ('_tcl_data\\encoding\\cp864.enc',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\encoding\\cp864.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Azores',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Atlantic\\Azores',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Midway',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Pacific\\Midway',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Johannesburg',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Africa\\Johannesburg',
   'DATA'),
  ('_tk_data\\listbox.tcl',
   'D:/anaconda3/envs/dt/Library/lib\\tk8.6\\listbox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+12',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Etc\\GMT+12',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Nairobi',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Africa\\Nairobi',
   'DATA'),
  ('_tk_data\\msgs\\hu.msg',
   'D:/anaconda3/envs/dt/Library/lib\\tk8.6\\msgs\\hu.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Currie',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Australia\\Currie',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dushanbe',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Asia\\Dushanbe',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Bahrain',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Asia\\Bahrain',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\Acre',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Brazil\\Acre',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Santarem',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\America\\Santarem',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Bahia_Banderas',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\America\\Bahia_Banderas',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cayman',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\America\\Cayman',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Tirane',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Europe\\Tirane',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Skopje',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Europe\\Skopje',
   'DATA'),
  ('_tcl_data\\msgs\\es_pa.msg',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\msgs\\es_pa.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Magadan',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Asia\\Magadan',
   'DATA'),
  ('_tk_data\\ttk\\cursors.tcl',
   'D:/anaconda3/envs/dt/Library/lib\\tk8.6\\ttk\\cursors.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\HST10',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\SystemV\\HST10',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Jamaica',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\America\\Jamaica',
   'DATA'),
  ('_tcl_data\\tzdata\\Zulu',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Zulu',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Davis',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Antarctica\\Davis',
   'DATA'),
  ('_tcl_data\\tzdata\\GMT+0',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\GMT+0',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ashgabat',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Asia\\Ashgabat',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Baghdad',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Asia\\Baghdad',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Montserrat',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\America\\Montserrat',
   'DATA'),
  ('_tcl_data\\encoding\\euc-cn.enc',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\encoding\\euc-cn.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Blanc-Sablon',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\America\\Blanc-Sablon',
   'DATA'),
  ('_tk_data\\ttk\\combobox.tcl',
   'D:/anaconda3/envs/dt/Library/lib\\tk8.6\\ttk\\combobox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tel_Aviv',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Asia\\Tel_Aviv',
   'DATA'),
  ('_tcl_data\\msgs\\cs.msg',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\msgs\\cs.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Curacao',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\America\\Curacao',
   'DATA'),
  ('_tcl_data\\http1.0\\http.tcl',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\http1.0\\http.tcl',
   'DATA'),
  ('_tk_data\\tk.tcl',
   'D:/anaconda3/envs/dt/Library/lib\\tk8.6\\tk.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Cordoba',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\America\\Argentina\\Cordoba',
   'DATA'),
  ('_tcl_data\\msgs\\es_pe.msg',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\msgs\\es_pe.msg',
   'DATA'),
  ('_tcl_data\\msgs\\ru_ua.msg',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\msgs\\ru_ua.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Conakry',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Africa\\Conakry',
   'DATA'),
  ('_tcl_data\\msgs\\pl.msg',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\msgs\\pl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Porto_Velho',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\America\\Porto_Velho',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Samara',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Europe\\Samara',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Nicosia',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Asia\\Nicosia',
   'DATA'),
  ('_tk_data\\msgs\\en_gb.msg',
   'D:/anaconda3/envs/dt/Library/lib\\tk8.6\\msgs\\en_gb.msg',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo75.gif',
   'D:/anaconda3/envs/dt/Library/lib\\tk8.6\\images\\pwrdLogo75.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Yakutat',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\America\\Yakutat',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Tegucigalpa',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\America\\Tegucigalpa',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Dakar',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Africa\\Dakar',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+7',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Etc\\GMT+7',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+3',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Etc\\GMT+3',
   'DATA'),
  ('_tcl_data\\msgs\\gl_es.msg',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\msgs\\gl_es.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Kaliningrad',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Europe\\Kaliningrad',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Grand_Turk',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\America\\Grand_Turk',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Mazatlan',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\America\\Mazatlan',
   'DATA'),
  ('_tcl_data\\msgs\\es_mx.msg',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\msgs\\es_mx.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Troll',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Antarctica\\Troll',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo200.gif',
   'D:/anaconda3/envs/dt/Library/lib\\tk8.6\\images\\pwrdLogo200.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-11',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Etc\\GMT-11',
   'DATA'),
  ('_tcl_data\\msgs\\th.msg',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\msgs\\th.msg',
   'DATA'),
  ('_tcl_data\\msgs\\kw.msg',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\msgs\\kw.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp737.enc',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\encoding\\cp737.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Efate',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Pacific\\Efate',
   'DATA'),
  ('_tcl_data\\encoding\\gb12345.enc',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\encoding\\gb12345.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Maldives',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Indian\\Maldives',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cancun',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\America\\Cancun',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Muscat',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Asia\\Muscat',
   'DATA'),
  ('_tcl_data\\encoding\\macIceland.enc',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\encoding\\macIceland.enc',
   'DATA'),
  ('_tk_data\\msgs\\el.msg',
   'D:/anaconda3/envs/dt/Library/lib\\tk8.6\\msgs\\el.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp1257.enc',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\encoding\\cp1257.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dhaka',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Asia\\Dhaka',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Malabo',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Africa\\Malabo',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Sarajevo',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Europe\\Sarajevo',
   'DATA'),
  ('_tcl_data\\package.tcl',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\package.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\CST6CDT',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\SystemV\\CST6CDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Athens',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Europe\\Athens',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\LHI',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Australia\\LHI',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Harbin',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Asia\\Harbin',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Anguilla',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\America\\Anguilla',
   'DATA'),
  ('_tcl_data\\msgs\\da.msg',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\msgs\\da.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\EST',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\EST',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+9',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Etc\\GMT+9',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Guadeloupe',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\America\\Guadeloupe',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-10.enc',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\encoding\\iso8859-10.enc',
   'DATA'),
  ('_tcl_data\\msgs\\de_be.msg',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\msgs\\de_be.msg',
   'DATA'),
  ('_tk_data\\msgbox.tcl',
   'D:/anaconda3/envs/dt/Library/lib\\tk8.6\\msgbox.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\cp932.enc',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\encoding\\cp932.enc',
   'DATA'),
  ('_tcl_data\\msgs\\es_cr.msg',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\msgs\\es_cr.msg',
   'DATA'),
  ('_tcl_data\\msgs\\hi.msg',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\msgs\\hi.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\El_Salvador',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\America\\El_Salvador',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\San_Juan',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\America\\Argentina\\San_Juan',
   'DATA'),
  ('_tcl_data\\encoding\\cp857.enc',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\encoding\\cp857.enc',
   'DATA'),
  ('_tcl_data\\msgs\\nl.msg',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\msgs\\nl.msg',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-16.enc',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\encoding\\iso8859-16.enc',
   'DATA'),
  ('_tcl_data\\encoding\\cp852.enc',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\encoding\\cp852.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Knox',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\America\\Indiana\\Knox',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Dawson_Creek',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\America\\Dawson_Creek',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bangui',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Africa\\Bangui',
   'DATA'),
  ('_tk_data\\icons.tcl',
   'D:/anaconda3/envs/dt/Library/lib\\tk8.6\\icons.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Tell_City',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\America\\Indiana\\Tell_City',
   'DATA'),
  ('_tk_data\\ttk\\menubutton.tcl',
   'D:/anaconda3/envs/dt/Library/lib\\tk8.6\\ttk\\menubutton.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Hovd',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Asia\\Hovd',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Ulyanovsk',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Europe\\Ulyanovsk',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Bahia',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\America\\Bahia',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Paris',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Europe\\Paris',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Rangoon',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Asia\\Rangoon',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Port-au-Prince',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\America\\Port-au-Prince',
   'DATA'),
  ('_tcl_data\\msgs\\tr.msg',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\msgs\\tr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Aqtau',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Asia\\Aqtau',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Anadyr',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Asia\\Anadyr',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Santo_Domingo',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\America\\Santo_Domingo',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Tortola',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\America\\Tortola',
   'DATA'),
  ('_tcl_data\\msgs\\kok_in.msg',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\msgs\\kok_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\Universal',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Etc\\Universal',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Lindeman',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Australia\\Lindeman',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Stanley',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Atlantic\\Stanley',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Sydney',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Australia\\Sydney',
   'DATA'),
  ('_tcl_data\\encoding\\gb1988.enc',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\encoding\\gb1988.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Vincent',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\America\\St_Vincent',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Budapest',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Europe\\Budapest',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Cairo',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Africa\\Cairo',
   'DATA'),
  ('_tcl_data\\msgs\\es_co.msg',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\msgs\\es_co.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Toronto',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\America\\Toronto',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\CST6',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\SystemV\\CST6',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Mbabane',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Africa\\Mbabane',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yakutsk',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Asia\\Yakutsk',
   'DATA'),
  ('_tcl_data\\msgs\\en_za.msg',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\msgs\\en_za.msg',
   'DATA'),
  ('_tcl_data\\msgs\\et.msg',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\msgs\\et.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Los_Angeles',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\America\\Los_Angeles',
   'DATA'),
  ('_tcl_data\\msgs\\ca.msg',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\msgs\\ca.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Catamarca',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\America\\Argentina\\Catamarca',
   'DATA'),
  ('_tcl_data\\encoding\\cp1251.enc',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\encoding\\cp1251.enc',
   'DATA'),
  ('_tk_data\\console.tcl',
   'D:/anaconda3/envs/dt/Library/lib\\tk8.6\\console.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Pyongyang',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Asia\\Pyongyang',
   'DATA'),
  ('_tcl_data\\msgs\\gl.msg',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\msgs\\gl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Denver',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\America\\Denver',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Kentucky\\Monticello',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\America\\Kentucky\\Monticello',
   'DATA'),
  ('_tcl_data\\encoding\\koi8-r.enc',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\encoding\\koi8-r.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Kiev',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Europe\\Kiev',
   'DATA'),
  ('_tcl_data\\msgs\\nb.msg',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\msgs\\nb.msg',
   'DATA'),
  ('_tk_data\\license.terms',
   'D:/anaconda3/envs/dt/Library/lib\\tk8.6\\license.terms',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Libreville',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Africa\\Libreville',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Karachi',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Asia\\Karachi',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Bishkek',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Asia\\Bishkek',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Juneau',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\America\\Juneau',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kolkata',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Asia\\Kolkata',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Swift_Current',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\America\\Swift_Current',
   'DATA'),
  ('_tk_data\\ttk\\fonts.tcl',
   'D:/anaconda3/envs/dt/Library/lib\\tk8.6\\ttk\\fonts.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Amsterdam',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Europe\\Amsterdam',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Kampala',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Africa\\Kampala',
   'DATA'),
  ('_tcl_data\\msgs\\el.msg',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\msgs\\el.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Eire',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Eire',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Yukon',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Canada\\Yukon',
   'DATA'),
  ('_tcl_data\\tzdata\\Iran',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Iran',
   'DATA'),
  ('_tcl_data\\encoding\\symbol.enc',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\encoding\\symbol.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Nouakchott',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Africa\\Nouakchott',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Colombo',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Asia\\Colombo',
   'DATA'),
  ('_tcl_data\\parray.tcl',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\parray.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Thimbu',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Asia\\Thimbu',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Virgin',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\America\\Virgin',
   'DATA'),
  ('_tcl_data\\msgs\\ms_my.msg',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\msgs\\ms_my.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Thimphu',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Asia\\Thimphu',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Montreal',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\America\\Montreal',
   'DATA'),
  ('_tcl_data\\encoding\\cp866.enc',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\encoding\\cp866.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Algiers',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Africa\\Algiers',
   'DATA'),
  ('_tcl_data\\msgs\\en_ie.msg',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\msgs\\en_ie.msg',
   'DATA'),
  ('_tk_data\\ttk\\utils.tcl',
   'D:/anaconda3/envs/dt/Library/lib\\tk8.6\\ttk\\utils.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kathmandu',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Asia\\Kathmandu',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\San_Marino',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Europe\\San_Marino',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Bougainville',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Pacific\\Bougainville',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Scoresbysund',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\America\\Scoresbysund',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Fakaofo',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Pacific\\Fakaofo',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Marigot',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\America\\Marigot',
   'DATA'),
  ('_tcl_data\\msgs\\sr.msg',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\msgs\\sr.msg',
   'DATA'),
  ('_tcl_data\\msgs\\mk.msg',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\msgs\\mk.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Petersburg',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\America\\Indiana\\Petersburg',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\ACT',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Australia\\ACT',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Mawson',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Antarctica\\Mawson',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Rankin_Inlet',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\America\\Rankin_Inlet',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Bratislava',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Europe\\Bratislava',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kuwait',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Asia\\Kuwait',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Port_of_Spain',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\America\\Port_of_Spain',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\West',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Brazil\\West',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Makassar',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Asia\\Makassar',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Minsk',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Europe\\Minsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Jakarta',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Asia\\Jakarta',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Norfolk',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Pacific\\Norfolk',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Casey',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Antarctica\\Casey',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\AST4ADT',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\SystemV\\AST4ADT',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Kosrae',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Pacific\\Kosrae',
   'DATA'),
  ('tcl8\\8.5\\msgcat-1.6.1.tm',
   'D:/anaconda3/envs/dt/Library/lib\\tcl8\\8.5\\msgcat-1.6.1.tm',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Tripoli',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Africa\\Tripoli',
   'DATA'),
  ('_tcl_data\\msgs\\pt.msg',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\msgs\\pt.msg',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-5.enc',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\encoding\\iso8859-5.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\PST8',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\SystemV\\PST8',
   'DATA'),
  ('_tcl_data\\encoding\\cp1252.enc',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\encoding\\cp1252.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Ndjamena',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Africa\\Ndjamena',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-2.enc',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\encoding\\iso8859-2.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\NSW',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Australia\\NSW',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-14',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Etc\\GMT-14',
   'DATA'),
  ('_tcl_data\\msgs\\de.msg',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\msgs\\de.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Darwin',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Australia\\Darwin',
   'DATA'),
  ('_tcl_data\\msgs\\en_bw.msg',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\msgs\\en_bw.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Freetown',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Africa\\Freetown',
   'DATA'),
  ('_tcl_data\\encoding\\cp1254.enc',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\encoding\\cp1254.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Uzhgorod',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Europe\\Uzhgorod',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\DumontDUrville',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Antarctica\\DumontDUrville',
   'DATA'),
  ('_tcl_data\\encoding\\macJapan.enc',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\encoding\\macJapan.enc',
   'DATA'),
  ('tcl8\\8.4\\platform\\shell-1.1.4.tm',
   'D:/anaconda3/envs/dt/Library/lib\\tcl8\\8.4\\platform\\shell-1.1.4.tm',
   'DATA'),
  ('_tcl_data\\msgs\\zh_cn.msg',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\msgs\\zh_cn.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Asmera',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Africa\\Asmera',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Tijuana',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\America\\Tijuana',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+11',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Etc\\GMT+11',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-9.enc',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\encoding\\iso8859-9.enc',
   'DATA'),
  ('_tk_data\\images\\README',
   'D:/anaconda3/envs/dt/Library/lib\\tk8.6\\images\\README',
   'DATA'),
  ('_tcl_data\\msgs\\es_ve.msg',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\msgs\\es_ve.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Samoa',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Pacific\\Samoa',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Chungking',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Asia\\Chungking',
   'DATA'),
  ('_tcl_data\\msgs\\kl.msg',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\msgs\\kl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Easter',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Pacific\\Easter',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Vancouver',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\America\\Vancouver',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\La_Rioja',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\America\\Argentina\\La_Rioja',
   'DATA'),
  ('_tk_data\\ttk\\aquaTheme.tcl',
   'D:/anaconda3/envs/dt/Library/lib\\tk8.6\\ttk\\aquaTheme.tcl',
   'DATA'),
  ('_tk_data\\msgs\\it.msg',
   'D:/anaconda3/envs/dt/Library/lib\\tk8.6\\msgs\\it.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Ushuaia',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\America\\Argentina\\Ushuaia',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Chicago',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\America\\Chicago',
   'DATA'),
  ('_tcl_data\\msgs\\fr.msg',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\msgs\\fr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Funafuti',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Pacific\\Funafuti',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Atyrau',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Asia\\Atyrau',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Casablanca',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Africa\\Casablanca',
   'DATA'),
  ('_tcl_data\\msgs\\es_bo.msg',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\msgs\\es_bo.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Rainy_River',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\America\\Rainy_River',
   'DATA'),
  ('_tcl_data\\msgs\\nl_be.msg',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\msgs\\nl_be.msg',
   'DATA'),
  ('_tcl_data\\msgs\\fa.msg',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\msgs\\fa.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Gibraltar',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Europe\\Gibraltar',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\North_Dakota\\Center',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\America\\North_Dakota\\Center',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Khartoum',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Africa\\Khartoum',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Chihuahua',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\America\\Chihuahua',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Niamey',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Africa\\Niamey',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Blantyre',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Africa\\Blantyre',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Kerguelen',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Indian\\Kerguelen',
   'DATA'),
  ('_tcl_data\\tzdata\\GMT0',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\GMT0',
   'DATA'),
  ('_tk_data\\unsupported.tcl',
   'D:/anaconda3/envs/dt/Library/lib\\tk8.6\\unsupported.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Baku',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Asia\\Baku',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tehran',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Asia\\Tehran',
   'DATA'),
  ('_tk_data\\ttk\\classicTheme.tcl',
   'D:/anaconda3/envs/dt/Library/lib\\tk8.6\\ttk\\classicTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Maseru',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Africa\\Maseru',
   'DATA'),
  ('_tcl_data\\encoding\\euc-jp.enc',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\encoding\\euc-jp.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Whitehorse',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\America\\Whitehorse',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Atlantic',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Canada\\Atlantic',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Samoa',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\US\\Samoa',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Rio_Branco',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\America\\Rio_Branco',
   'DATA'),
  ('_tcl_data\\msgs\\es_ar.msg',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\msgs\\es_ar.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\GB',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\GB',
   'DATA'),
  ('_tcl_data\\encoding\\iso2022.enc',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\encoding\\iso2022.enc',
   'DATA'),
  ('_tcl_data\\encoding\\cp861.enc',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\encoding\\cp861.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Addis_Ababa',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Africa\\Addis_Ababa',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Johns',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\America\\St_Johns',
   'DATA'),
  ('tcl8\\8.6\\http-2.9.8.tm',
   'D:/anaconda3/envs/dt/Library/lib\\tcl8\\8.6\\http-2.9.8.tm',
   'DATA'),
  ('_tcl_data\\msgs\\zh_hk.msg',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\msgs\\zh_hk.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yekaterinburg',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Asia\\Yekaterinburg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Brussels',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Europe\\Brussels',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Montevideo',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\America\\Montevideo',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Port_Moresby',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Pacific\\Port_Moresby',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\North_Dakota\\Beulah',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\America\\North_Dakota\\Beulah',
   'DATA'),
  ('_tk_data\\msgs\\nl.msg',
   'D:/anaconda3/envs/dt/Library/lib\\tk8.6\\msgs\\nl.msg',
   'DATA'),
  ('_tcl_data\\msgs\\sq.msg',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\msgs\\sq.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Pangnirtung',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\America\\Pangnirtung',
   'DATA'),
  ('_tcl_data\\tzdata\\CST6CDT',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\CST6CDT',
   'DATA'),
  ('tcl8\\8.4\\platform-1.0.19.tm',
   'D:/anaconda3/envs/dt/Library/lib\\tcl8\\8.4\\platform-1.0.19.tm',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Jersey',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Europe\\Jersey',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Nicosia',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Europe\\Nicosia',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Kanton',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Pacific\\Kanton',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\East',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Brazil\\East',
   'DATA'),
  ('_tcl_data\\encoding\\cp949.enc',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\encoding\\cp949.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Guayaquil',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\America\\Guayaquil',
   'DATA'),
  ('_tcl_data\\opt0.4\\pkgIndex.tcl',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\opt0.4\\pkgIndex.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\fr_be.msg',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\msgs\\fr_be.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Yap',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Pacific\\Yap',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Jujuy',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\America\\Jujuy',
   'DATA'),
  ('_tcl_data\\msgs\\ar_sy.msg',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\msgs\\ar_sy.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Majuro',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Pacific\\Majuro',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Tiraspol',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Europe\\Tiraspol',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Porto-Novo',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Africa\\Porto-Novo',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kashgar',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Asia\\Kashgar',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Merida',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\America\\Merida',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Taipei',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Asia\\Taipei',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Paramaribo',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\America\\Paramaribo',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\North_Dakota\\New_Salem',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\America\\North_Dakota\\New_Salem',
   'DATA'),
  ('_tcl_data\\tzdata\\PST8PDT',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\PST8PDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Aqtobe',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Asia\\Aqtobe',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-14.enc',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\encoding\\iso8859-14.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Chisinau',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Europe\\Chisinau',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Thomas',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\America\\St_Thomas',
   'DATA'),
  ('_tk_data\\ttk\\progress.tcl',
   'D:/anaconda3/envs/dt/Library/lib\\tk8.6\\ttk\\progress.tcl',
   'DATA'),
  ('_tk_data\\ttk\\scale.tcl',
   'D:/anaconda3/envs/dt/Library/lib\\tk8.6\\ttk\\scale.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\cp869.enc',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\encoding\\cp869.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Pohnpei',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Pacific\\Pohnpei',
   'DATA'),
  ('_tcl_data\\encoding\\macCroatian.enc',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\encoding\\macCroatian.enc',
   'DATA'),
  ('_tcl_data\\msgs\\ms.msg',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\msgs\\ms.msg',
   'DATA'),
  ('_tcl_data\\encoding\\macCyrillic.enc',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\encoding\\macCyrillic.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Phnom_Penh',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Asia\\Phnom_Penh',
   'DATA'),
  ('_tk_data\\ttk\\entry.tcl',
   'D:/anaconda3/envs/dt/Library/lib\\tk8.6\\ttk\\entry.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\cp874.enc',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\encoding\\cp874.enc',
   'DATA'),
  ('_tk_data\\images\\logo64.gif',
   'D:/anaconda3/envs/dt/Library/lib\\tk8.6\\images\\logo64.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Yancowinna',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Australia\\Yancowinna',
   'DATA'),
  ('_tcl_data\\safe.tcl',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\safe.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Jan_Mayen',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Atlantic\\Jan_Mayen',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Canberra',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Australia\\Canberra',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Wake',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Pacific\\Wake',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Saigon',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Asia\\Saigon',
   'DATA'),
  ('_tcl_data\\msgs\\vi.msg',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\msgs\\vi.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-13',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Etc\\GMT-13',
   'DATA'),
  ('_tcl_data\\tzdata\\CET',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\CET',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Vostok',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Antarctica\\Vostok',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Simferopol',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Europe\\Simferopol',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Vincennes',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\America\\Indiana\\Vincennes',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Vladivostok',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Asia\\Vladivostok',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Catamarca',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\America\\Catamarca',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Moncton',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\America\\Moncton',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Buenos_Aires',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\America\\Argentina\\Buenos_Aires',
   'DATA'),
  ('_tk_data\\entry.tcl',
   'D:/anaconda3/envs/dt/Library/lib\\tk8.6\\entry.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Oral',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Asia\\Oral',
   'DATA'),
  ('_tcl_data\\encoding\\ebcdic.enc',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\encoding\\ebcdic.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Costa_Rica',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\America\\Costa_Rica',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Helsinki',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Europe\\Helsinki',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Hobart',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Australia\\Hobart',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Brazzaville',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Africa\\Brazzaville',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bamako',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Africa\\Bamako',
   'DATA'),
  ('_tcl_data\\tzdata\\Turkey',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Turkey',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Campo_Grande',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\America\\Campo_Grande',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Saskatchewan',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Canada\\Saskatchewan',
   'DATA'),
  ('_tcl_data\\msgs\\he.msg',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\msgs\\he.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yangon',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Asia\\Yangon',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Coral_Harbour',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\America\\Coral_Harbour',
   'DATA'),
  ('_tcl_data\\encoding\\gb2312.enc',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\encoding\\gb2312.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Singapore',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Singapore',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Barbados',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\America\\Barbados',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Rio_Gallegos',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\America\\Argentina\\Rio_Gallegos',
   'DATA'),
  ('_tcl_data\\msgs\\fa_ir.msg',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\msgs\\fa_ir.msg',
   'DATA'),
  ('_tcl_data\\encoding\\koi8-u.enc',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\encoding\\koi8-u.enc',
   'DATA'),
  ('_tk_data\\tclIndex',
   'D:/anaconda3/envs/dt/Library/lib\\tk8.6\\tclIndex',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Nauru',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Pacific\\Nauru',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Jayapura',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Asia\\Jayapura',
   'DATA'),
  ('_tcl_data\\msgs\\ta_in.msg',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\msgs\\ta_in.msg',
   'DATA'),
  ('_tcl_data\\msgs\\sv.msg',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\msgs\\sv.msg',
   'DATA'),
  ('_tcl_data\\msgs\\lt.msg',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\msgs\\lt.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Zurich',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Europe\\Zurich',
   'DATA'),
  ('_tk_data\\msgs\\sv.msg',
   'D:/anaconda3/envs/dt/Library/lib\\tk8.6\\msgs\\sv.msg',
   'DATA'),
  ('_tk_data\\dialog.tcl',
   'D:/anaconda3/envs/dt/Library/lib\\tk8.6\\dialog.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\sk.msg',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\msgs\\sk.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cns11643.enc',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\encoding\\cns11643.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vatican',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Europe\\Vatican',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ulan_Bator',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Asia\\Ulan_Bator',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Monrovia',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Africa\\Monrovia',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\AST4',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\SystemV\\AST4',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yerevan',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Asia\\Yerevan',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\South_Georgia',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Atlantic\\South_Georgia',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Gaza',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Asia\\Gaza',
   'DATA'),
  ('_tk_data\\fontchooser.tcl',
   'D:/anaconda3/envs/dt/Library/lib\\tk8.6\\fontchooser.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-15.enc',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\encoding\\iso8859-15.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Dawson',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\America\\Dawson',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Lord_Howe',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Australia\\Lord_Howe',
   'DATA'),
  ('_tk_data\\ttk\\ttk.tcl',
   'D:/anaconda3/envs/dt/Library/lib\\tk8.6\\ttk\\ttk.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\GMT',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\GMT',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Guatemala',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\America\\Guatemala',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Chagos',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Indian\\Chagos',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+6',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Etc\\GMT+6',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Moscow',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Europe\\Moscow',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Shiprock',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\America\\Shiprock',
   'DATA'),
  ('tcl8\\8.5\\tcltest-2.5.7.tm',
   'D:/anaconda3/envs/dt/Library/lib\\tcl8\\8.5\\tcltest-2.5.7.tm',
   'DATA'),
  ('_tcl_data\\msgs\\ga_ie.msg',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\msgs\\ga_ie.msg',
   'DATA'),
  ('_tk_data\\ttk\\panedwindow.tcl',
   'D:/anaconda3/envs/dt/Library/lib\\tk8.6\\ttk\\panedwindow.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Punta_Arenas',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\America\\Punta_Arenas',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kuala_Lumpur',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Asia\\Kuala_Lumpur',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Ensenada',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\America\\Ensenada',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Khandyga',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Asia\\Khandyga',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Djibouti',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Africa\\Djibouti',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bissau',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Africa\\Bissau',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-8',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Etc\\GMT-8',
   'DATA'),
  ('_tcl_data\\msgs\\hu.msg',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\msgs\\hu.msg',
   'DATA'),
  ('_tk_data\\focus.tcl',
   'D:/anaconda3/envs/dt/Library/lib\\tk8.6\\focus.tcl',
   'DATA'),
  ('_tk_data\\msgs\\en.msg',
   'D:/anaconda3/envs/dt/Library/lib\\tk8.6\\msgs\\en.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Victoria',
   'D:/anaconda3/envs/dt/Library/lib/tcl8.6\\tzdata\\Australia\\Victoria',
   'DATA'),
  ('cv2\\config-3.py',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\cv2\\config-3.py',
   'DATA'),
  ('cv2\\config.py',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\cv2\\config.py',
   'DATA'),
  ('cv2\\load_config_py3.py',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\cv2\\load_config_py3.py',
   'DATA'),
  ('itsdangerous-2.2.0.dist-info\\RECORD',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\itsdangerous-2.2.0.dist-info\\RECORD',
   'DATA'),
  ('flask-2.3.3.dist-info\\RECORD',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\flask-2.3.3.dist-info\\RECORD',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\METADATA',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\METADATA',
   'DATA'),
  ('itsdangerous-2.2.0.dist-info\\METADATA',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\itsdangerous-2.2.0.dist-info\\METADATA',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\top_level.txt',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\top_level.txt',
   'DATA'),
  ('flask-2.3.3.dist-info\\entry_points.txt',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\flask-2.3.3.dist-info\\entry_points.txt',
   'DATA'),
  ('click-8.2.1.dist-info\\METADATA',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\click-8.2.1.dist-info\\METADATA',
   'DATA'),
  ('flask-2.3.3.dist-info\\WHEEL',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\flask-2.3.3.dist-info\\WHEEL',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\RECORD',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\RECORD',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\WHEEL',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\WHEEL',
   'DATA'),
  ('click-8.2.1.dist-info\\WHEEL',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\click-8.2.1.dist-info\\WHEEL',
   'DATA'),
  ('werkzeug-3.1.3.dist-info\\WHEEL',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\werkzeug-3.1.3.dist-info\\WHEEL',
   'DATA'),
  ('click-8.2.1.dist-info\\RECORD',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\click-8.2.1.dist-info\\RECORD',
   'DATA'),
  ('werkzeug-3.1.3.dist-info\\INSTALLER',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\werkzeug-3.1.3.dist-info\\INSTALLER',
   'DATA'),
  ('itsdangerous-2.2.0.dist-info\\WHEEL',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\itsdangerous-2.2.0.dist-info\\WHEEL',
   'DATA'),
  ('werkzeug-3.1.3.dist-info\\LICENSE.txt',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\werkzeug-3.1.3.dist-info\\LICENSE.txt',
   'DATA'),
  ('click-8.2.1.dist-info\\licenses\\LICENSE.txt',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\click-8.2.1.dist-info\\licenses\\LICENSE.txt',
   'DATA'),
  ('flask-2.3.3.dist-info\\LICENSE.rst',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\flask-2.3.3.dist-info\\LICENSE.rst',
   'DATA'),
  ('click-8.2.1.dist-info\\INSTALLER',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\click-8.2.1.dist-info\\INSTALLER',
   'DATA'),
  ('werkzeug-3.1.3.dist-info\\METADATA',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\werkzeug-3.1.3.dist-info\\METADATA',
   'DATA'),
  ('flask-2.3.3.dist-info\\REQUESTED',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\flask-2.3.3.dist-info\\REQUESTED',
   'DATA'),
  ('itsdangerous-2.2.0.dist-info\\LICENSE.txt',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\itsdangerous-2.2.0.dist-info\\LICENSE.txt',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\LICENSE.txt',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\LICENSE.txt',
   'DATA'),
  ('itsdangerous-2.2.0.dist-info\\INSTALLER',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\itsdangerous-2.2.0.dist-info\\INSTALLER',
   'DATA'),
  ('flask-2.3.3.dist-info\\INSTALLER',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\flask-2.3.3.dist-info\\INSTALLER',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\INSTALLER',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\INSTALLER',
   'DATA'),
  ('flask-2.3.3.dist-info\\METADATA',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\flask-2.3.3.dist-info\\METADATA',
   'DATA'),
  ('werkzeug-3.1.3.dist-info\\RECORD',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\werkzeug-3.1.3.dist-info\\RECORD',
   'DATA'),
  ('cv2\\data\\__init__.py',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\cv2\\data\\__init__.py',
   'DATA'),
  ('cv2\\__init__.py',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\cv2\\__init__.py',
   'DATA'),
  ('cv2\\version.py',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\cv2\\version.py',
   'DATA'),
  ('cv2\\utils\\__init__.py',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\cv2\\utils\\__init__.py',
   'DATA'),
  ('cv2\\typing\\__init__.py',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\cv2\\typing\\__init__.py',
   'DATA'),
  ('cv2\\misc\\version.py',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\cv2\\misc\\version.py',
   'DATA'),
  ('cv2\\misc\\__init__.py',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\cv2\\misc\\__init__.py',
   'DATA'),
  ('cv2\\mat_wrapper\\__init__.py',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\cv2\\mat_wrapper\\__init__.py',
   'DATA'),
  ('cv2\\gapi\\__init__.py',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\cv2\\gapi\\__init__.py',
   'DATA'),
  ('base_library.zip',
   'E:\\01.代码\\嘉兴公安自主申报\\DataTransmission\\build\\DataTransmission_optimized\\base_library.zip',
   'DATA')],
 [],
 False,
 False,
 1751009966,
 [('run.exe',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\PyInstaller\\bootloader\\Windows-64bit-intel\\run.exe',
   'EXECUTABLE')],
 'D:\\anaconda3\\envs\\dt\\python310.dll')
