@echo off
chcp 65001 >nul
title MySQL服务快速诊断

echo ========================================
echo    MySQL服务快速诊断工具
echo ========================================
echo.

echo 正在诊断MySQL服务问题...
echo.

echo [1/6] 检查MySQL服务安装状态
echo ----------------------------------------
sc query mysql >nul 2>&1
if %errorlevel% equ 0 (
    echo ✓ MySQL服务已安装
    sc query mysql
) else (
    echo ✗ MySQL服务未安装
    echo.
    echo 💡 解决方案:
    echo 1. 下载MySQL 8.0安装包
    echo 2. 运行安装程序
    echo 3. 选择"Server only"安装类型
    echo 4. 设置root密码
    echo 5. 确保选择"Configure MySQL Server as a Windows Service"
    echo.
    goto end
)

echo.
echo [2/6] 检查MySQL服务运行状态
echo ----------------------------------------
sc query mysql | find "RUNNING" >nul
if %errorlevel% equ 0 (
    echo ✓ MySQL服务正在运行
) else (
    echo ✗ MySQL服务未运行
    echo.
    echo 尝试启动MySQL服务...
    net start mysql
    if %errorlevel% equ 0 (
        echo ✓ MySQL服务启动成功
    ) else (
        echo ✗ MySQL服务启动失败
        echo.
        echo 💡 可能的原因:
        echo - 需要管理员权限
        echo - MySQL配置文件错误
        echo - 端口3306被占用
        echo - MySQL数据目录权限问题
        echo.
        echo 请以管理员身份运行此脚本
        goto end
    )
)

echo.
echo [3/6] 检查MySQL进程
echo ----------------------------------------
tasklist | findstr /i mysql
if %errorlevel% equ 0 (
    echo ✓ 找到MySQL进程
) else (
    echo ✗ 未找到MySQL进程
    echo 这表明MySQL服务虽然显示运行，但进程可能异常
)

echo.
echo [4/6] 检查端口3306监听状态
echo ----------------------------------------
netstat -ano | findstr :3306
if %errorlevel% equ 0 (
    echo ✓ 端口3306正在监听
) else (
    echo ✗ 端口3306未在监听
    echo.
    echo 💡 可能的原因:
    echo - MySQL配置绑定到其他端口
    echo - MySQL配置绑定到127.0.0.1
    echo - MySQL启动失败但服务状态显示运行
)

echo.
echo [5/6] 测试端口连接
echo ----------------------------------------
echo 测试TCP连接到localhost:3306...
powershell -Command "try { $tcp = New-Object System.Net.Sockets.TcpClient; $tcp.ConnectAsync('localhost', 3306).Wait(3000); if($tcp.Connected) { Write-Host '✓ 端口连接成功'; $tcp.Close() } else { Write-Host '✗ 端口连接失败' } } catch { Write-Host '✗ 端口连接异常:' $_.Exception.Message }"

echo.
echo [6/6] 检查MySQL错误日志
echo ----------------------------------------
echo 查找MySQL错误日志文件...

:: 常见的MySQL错误日志位置
set "log_found=0"

if exist "C:\ProgramData\MySQL\MySQL Server 8.0\Data\*.err" (
    echo ✓ 找到MySQL错误日志: C:\ProgramData\MySQL\MySQL Server 8.0\Data\
    dir "C:\ProgramData\MySQL\MySQL Server 8.0\Data\*.err" /b
    set "log_found=1"
)

if exist "C:\Program Files\MySQL\MySQL Server 8.0\data\*.err" (
    echo ✓ 找到MySQL错误日志: C:\Program Files\MySQL\MySQL Server 8.0\data\
    dir "C:\Program Files\MySQL\MySQL Server 8.0\data\*.err" /b
    set "log_found=1"
)

if "%log_found%"=="0" (
    echo ⚠️ 未找到MySQL错误日志文件
    echo 请检查MySQL安装目录
)

echo.
echo ========================================
echo 诊断完成
echo ========================================
echo.

echo 根据诊断结果，请选择相应的解决方案:
echo.
echo 如果MySQL服务未安装:
echo   → 下载并安装MySQL 8.0
echo.
echo 如果MySQL服务未运行:
echo   → 以管理员身份运行: net start mysql
echo.
echo 如果端口未监听:
echo   → 检查MySQL配置文件 my.ini
echo   → 重启MySQL服务: net stop mysql ^&^& net start mysql
echo.
echo 如果端口连接失败:
echo   → 检查防火墙设置
echo   → 检查MySQL bind-address配置
echo.

:end
echo.
echo 下一步建议:
echo 1. 如果MySQL服务正常 → 运行 test_connection_with_timeout.py
echo 2. 如果MySQL服务异常 → 重新安装MySQL或检查配置
echo 3. 如果需要详细测试 → 运行 fix_database_issues.bat
echo.
pause
