@echo off
chcp 65001 >nul
title DataTransmission 完整分步测试

echo ========================================
echo    DataTransmission 完整分步测试
echo ========================================
echo.

echo 此脚本将按顺序运行所有测试步骤，帮助您精确定位问题所在
echo.

echo 测试步骤:
echo 1. MySQL服务状态测试
echo 2. Python模块导入测试
echo 3. 配置文件验证测试
echo 4. MySQL数据库连接测试
echo 5. 应用程序组件测试
echo.

set /p confirm="是否开始完整测试? (Y/N): "
if /i not "%confirm%"=="Y" goto end

echo.
echo ========================================
echo 步骤1: MySQL服务状态测试
echo ========================================
echo.

call test_step1_mysql_service.bat

echo.
echo 按任意键继续到步骤2...
pause >nul

echo.
echo ========================================
echo 步骤2: Python模块导入测试
echo ========================================
echo.

python test_step2_python_modules.py

echo.
echo 按任意键继续到步骤3...
pause >nul

echo.
echo ========================================
echo 步骤3: 配置文件验证测试
echo ========================================
echo.

python test_step3_config_file.py

echo.
echo 按任意键继续到步骤4...
pause >nul

echo.
echo ========================================
echo 步骤4: MySQL数据库连接测试
echo ========================================
echo.

python test_step4_mysql_connection.py

echo.
echo 按任意键继续到步骤5...
pause >nul

echo.
echo ========================================
echo 步骤5: 应用程序组件测试
echo ========================================
echo.

python test_step5_application_components.py

echo.
echo ========================================
echo 完整测试总结
echo ========================================
echo.

echo 所有测试步骤已完成！
echo.
echo 如果所有步骤都通过，您的DataTransmission应该可以正常运行
echo 如果某个步骤失败，请根据该步骤的错误信息进行修复
echo.

echo 下一步操作建议:
echo - 如果所有测试通过: 运行 DataTransmission.exe
echo - 如果有测试失败: 根据失败步骤的提示进行修复
echo - 需要帮助: 运行 fix_database_issues.bat 选择相应的修复选项
echo.

:end
pause
