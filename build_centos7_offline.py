#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
CentOS 7离线程序构建脚本
基于conda环境创建可在CentOS 7桌面版本直接运行的离线程序包
"""

import os
import sys
import shutil
import subprocess
import time
from pathlib import Path

# 全局变量存储conda命令路径
CONDA_CMD = 'conda'

def find_conda_executable():
    """查找conda可执行文件"""
    import os

    # 常见的conda安装路径
    possible_paths = [
        os.path.expanduser("~/anaconda3/Scripts/conda.exe"),
        os.path.expanduser("~/miniconda3/Scripts/conda.exe"),
        "C:/ProgramData/Anaconda3/Scripts/conda.exe",
        "C:/ProgramData/Miniconda3/Scripts/conda.exe",
        "C:/Users/<USER>/Anaconda3/Scripts/conda.exe".format(os.getenv('USERNAME', '')),
        "C:/Users/<USER>/Miniconda3/Scripts/conda.exe".format(os.getenv('USERNAME', '')),
    ]

    # 首先尝试直接调用conda
    try:
        result = subprocess.run(['conda', '--version'], capture_output=True, text=True, shell=True, timeout=10)
        if result.returncode == 0:
            return 'conda'
    except:
        pass

    # 尝试常见路径
    for path in possible_paths:
        if os.path.exists(path):
            try:
                result = subprocess.run([path, '--version'], capture_output=True, text=True, timeout=10)
                if result.returncode == 0:
                    return path
            except:
                continue

    return None

def check_conda_environment():
    """检查conda环境"""
    print("=" * 60)
    print("检查conda环境")
    print("=" * 60)

    # 查找conda可执行文件
    conda_cmd = find_conda_executable()
    if not conda_cmd:
        print("✗ 未找到conda命令")
        print("请确保:")
        print("1. Anaconda或Miniconda已正确安装")
        print("2. conda已添加到系统PATH环境变量")
        print("3. 或者conda安装在常见位置")

        # 提供手动指定路径的选项
        manual_path = input("\n请输入conda.exe的完整路径（或按Enter跳过）: ").strip()
        if manual_path and os.path.exists(manual_path):
            conda_cmd = manual_path
        else:
            return False

    print(f"✓ 找到conda: {conda_cmd}")

    try:
        # 检查conda版本
        result = subprocess.run([conda_cmd, '--version'], capture_output=True, text=True, shell=True, timeout=30)
        if result.returncode != 0:
            print("✗ conda命令执行失败")
            print(f"错误信息: {result.stderr}")
            return False

        print(f"✓ conda版本: {result.stdout.strip()}")

        # 检查当前Python环境
        python_version = sys.version.split()[0]
        python_path = sys.executable
        print(f"✓ Python版本: {python_version}")
        print(f"✓ Python路径: {python_path}")

        # 检查是否在conda环境中
        in_conda = 'conda' in python_path.lower() or 'anaconda' in python_path.lower() or 'miniconda' in python_path.lower()
        if in_conda:
            print("✓ 当前在conda环境中")
        else:
            print("⚠️  当前不在conda环境中")
            print("建议在conda环境中运行此脚本")

        # 检查conda环境列表
        result = subprocess.run([conda_cmd, 'info', '--envs'], capture_output=True, text=True, shell=True, timeout=30)
        if result.returncode == 0:
            print("✓ 可以获取conda环境列表")

            # 查找当前激活的环境
            current_env = None
            for line in result.stdout.split('\n'):
                if '*' in line:
                    parts = line.split()
                    if len(parts) > 0:
                        current_env = parts[0]
                    break

            if current_env:
                print(f"✓ 当前激活环境: {current_env}")
            else:
                print("⚠️  未检测到激活的环境")
        else:
            print("⚠️  无法获取conda环境信息")

        # 检查关键包
        key_packages = {
            'flask': 'flask',
            'mysql-connector-python': 'mysql.connector',
            'opencv-python': 'cv2',
            'qrcode': 'qrcode',
            'pyzbar': 'pyzbar'
        }

        print("\n检查关键包:")
        missing_packages = []

        for package_name, import_name in key_packages.items():
            try:
                __import__(import_name)
                print(f"✓ {package_name}")
            except ImportError:
                missing_packages.append(package_name)
                print(f"✗ {package_name}")

        if missing_packages:
            print(f"\n⚠️  缺少关键包: {missing_packages}")
            print("建议:")
            print("1. 激活包含这些依赖的conda环境")
            print("2. 或安装缺少的包")

            response = input("\n是否继续构建（将创建基础环境包）? (y/N): ")
            if response.lower() != 'y':
                return False

        # 保存conda命令路径供后续使用
        global CONDA_CMD
        CONDA_CMD = conda_cmd

        return True

    except Exception as e:
        print(f"✗ 检查conda环境时出错: {e}")
        return False

def install_conda_pack():
    """安装conda-pack"""
    print("\n检查conda-pack...")

    global CONDA_CMD

    # 检查conda-pack是否已安装
    result = subprocess.run([CONDA_CMD, 'list', 'conda-pack'], capture_output=True, text=True, shell=True)
    if result.returncode == 0 and 'conda-pack' in result.stdout:
        print("✓ conda-pack已安装")
        return True

    # 尝试通过pip检查
    result = subprocess.run(['pip', 'show', 'conda-pack'], capture_output=True, text=True, shell=True)
    if result.returncode == 0:
        print("✓ conda-pack已安装（通过pip）")
        return True

    print("安装conda-pack...")
    try:
        subprocess.run([CONDA_CMD, 'install', 'conda-pack', '-y'], check=True, shell=True)
        print("✓ conda-pack安装成功")
        return True
    except subprocess.CalledProcessError:
        try:
            subprocess.run(['pip', 'install', 'conda-pack'], check=True, shell=True)
            print("✓ conda-pack安装成功（通过pip）")
            return True
        except subprocess.CalledProcessError as e:
            print(f"✗ conda-pack安装失败: {e}")
            return False

def create_deployment_package():
    """创建部署包"""
    print("\n" + "=" * 60)
    print("创建CentOS 7离线程序包")
    print("=" * 60)
    
    # 创建部署目录
    timestamp = time.strftime("%Y%m%d_%H%M%S")
    deploy_dir = f"DataTransmission_CentOS7_Offline_{timestamp}"
    
    if os.path.exists(deploy_dir):
        shutil.rmtree(deploy_dir)
    
    os.makedirs(deploy_dir)
    os.makedirs(f"{deploy_dir}/src")
    os.makedirs(f"{deploy_dir}/conda_env")
    os.makedirs(f"{deploy_dir}/scripts")
    os.makedirs(f"{deploy_dir}/config")
    
    print(f"✓ 创建部署目录: {deploy_dir}")
    
    # 复制项目文件
    project_files = ['main.py', 'database.py', 'web_server.py', 'qr_generator.py', 
                    'camera_monitor.py', 'config.py', 'README.md']
    
    for file in project_files:
        if os.path.exists(file):
            shutil.copy2(file, f"{deploy_dir}/src/")
            print(f"✓ 复制: {file}")
    
    # 导出conda环境配置
    print("导出conda环境配置...")
    
    global CONDA_CMD

    # 导出跨平台环境配置
    with open(f"{deploy_dir}/conda_env/environment.yml", 'w') as f:
        subprocess.run([CONDA_CMD, 'env', 'export', '--no-builds', '--from-history'],
                      stdout=f, shell=True)
    print("✓ 导出environment.yml")

    # 导出详细包列表
    with open(f"{deploy_dir}/conda_env/conda_packages.txt", 'w') as f:
        subprocess.run([CONDA_CMD, 'list', '--export'], stdout=f, shell=True)

    with open(f"{deploy_dir}/conda_env/pip_packages.txt", 'w') as f:
        subprocess.run(['pip', 'freeze'], stdout=f, shell=True)

    print("✓ 导出包列表")
    
    # 获取当前环境名
    result = subprocess.run([CONDA_CMD, 'info', '--envs'], capture_output=True, text=True, shell=True)
    current_env = None
    if result.returncode == 0:
        for line in result.stdout.split('\n'):
            if '*' in line:
                current_env = line.split()[0]
                break

    # 打包conda环境
    if current_env and current_env != 'base':
        print(f"打包conda环境: {current_env}")
        try:
            subprocess.run([CONDA_CMD, 'pack', '-n', current_env, '-o',
                          f"{deploy_dir}/conda_env/datatransmission_env.tar.gz"],
                          check=True, shell=True)
            print("✓ conda环境打包成功")
        except subprocess.CalledProcessError as e:
            print(f"⚠️  conda环境打包失败: {e}")
            print("将使用环境配置文件进行部署")
    else:
        print("⚠️  当前在base环境或无法获取环境名，跳过环境打包")
        print("将使用环境配置文件进行部署")
    
    return deploy_dir

def create_centos7_scripts(deploy_dir):
    """创建CentOS 7专用脚本"""
    print("\n创建CentOS 7专用脚本...")
    
    # 创建安装脚本
    install_script = f"""#!/bin/bash
# DataTransmission CentOS 7 离线安装脚本

set -e

echo "=== DataTransmission CentOS 7 离线安装 ==="

# 检查系统
if ! grep -q "CentOS Linux release 7" /etc/redhat-release 2>/dev/null; then
    echo "警告: 此脚本专为CentOS 7设计"
    read -p "是否继续? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        exit 1
    fi
fi

# 检查桌面环境
if [ -z "$DISPLAY" ]; then
    echo "警告: 未检测到图形桌面环境"
    echo "请确保在CentOS 7桌面版本中运行"
fi

# 设置变量
CONDA_DIR="/opt/miniconda3"
INSTALL_DIR="/opt/DataTransmission"
ENV_NAME="datatransmission"
CURRENT_DIR="$(pwd)"

# 检查权限
if [ "$EUID" -ne 0 ]; then
    echo "此脚本需要root权限，请使用sudo运行"
    exit 1
fi

# 安装系统依赖
echo "安装系统依赖..."
yum update -y
yum install -y epel-release
yum install -y mariadb-server mariadb mariadb-devel
yum install -y zbar zbar-devel
yum install -y mesa-libGL mesa-libGL-devel
yum install -y python3-tkinter
yum install -y libX11-devel libXext-devel
yum install -y wget curl

# 启动数据库
systemctl start mariadb
systemctl enable mariadb

# 安装Miniconda
if [ ! -d "$CONDA_DIR" ]; then
    echo "安装Miniconda..."
    cd /tmp
    
    if [ ! -f "Miniconda3-latest-Linux-x86_64.sh" ]; then
        wget -O Miniconda3-latest-Linux-x86_64.sh \\
             "https://repo.anaconda.com/miniconda/Miniconda3-latest-Linux-x86_64.sh" || {{
            echo "下载失败，请手动下载Miniconda到/tmp目录"
            exit 1
        }}
    fi
    
    bash Miniconda3-latest-Linux-x86_64.sh -b -p $CONDA_DIR
    chown -R $(logname):$(logname) $CONDA_DIR 2>/dev/null || chown -R $SUDO_USER:$SUDO_USER $CONDA_DIR
    
    echo 'export PATH="'$CONDA_DIR'/bin:$PATH"' >> /etc/profile
    export PATH="$CONDA_DIR/bin:$PATH"
    
    cd $CURRENT_DIR
fi

# 设置conda环境
echo "设置conda环境..."
source $CONDA_DIR/etc/profile.d/conda.sh

# 安装环境
if [ -f "conda_env/datatransmission_env.tar.gz" ]; then
    echo "使用打包的conda环境..."
    mkdir -p $CONDA_DIR/envs/$ENV_NAME
    tar -xzf conda_env/datatransmission_env.tar.gz -C $CONDA_DIR/envs/$ENV_NAME
    conda-unpack -n $ENV_NAME 2>/dev/null || echo "环境已准备就绪"
else
    echo "从配置文件创建环境..."
    conda env create -f conda_env/environment.yml -n $ENV_NAME
fi

# 安装程序
echo "安装程序..."
mkdir -p $INSTALL_DIR
cp -r src/* $INSTALL_DIR/
chown -R $(logname):$(logname) $INSTALL_DIR 2>/dev/null || chown -R $SUDO_USER:$SUDO_USER $INSTALL_DIR

# 创建启动脚本
cat > $INSTALL_DIR/start_datatransmission.sh << 'STARTEOF'
#!/bin/bash
# DataTransmission CentOS 7 启动脚本

echo "=== DataTransmission 启动中 ==="

# 设置变量
CONDA_DIR="/opt/miniconda3"
ENV_NAME="datatransmission"
INSTALL_DIR="/opt/DataTransmission"

# 检查桌面环境
if [ -z "$DISPLAY" ]; then
    echo "错误: 未检测到图形桌面环境"
    exit 1
fi

# 激活conda环境
export PATH="$CONDA_DIR/bin:$PATH"
source $CONDA_DIR/etc/profile.d/conda.sh
conda activate $ENV_NAME || {{
    echo "错误: 无法激活环境 $ENV_NAME"
    exit 1
}}

# 设置环境变量
export DISPLAY=${{DISPLAY:-:0.0}}
export LD_LIBRARY_PATH=/usr/lib64:/usr/local/lib:$LD_LIBRARY_PATH

# 切换到项目目录
cd $INSTALL_DIR || {{
    echo "错误: 无法进入项目目录"
    exit 1
}}

echo "✓ 环境已激活: $(conda info --envs | grep '*')"
echo "✓ 启动DataTransmission..."

# 启动程序
python main.py
STARTEOF

chmod +x $INSTALL_DIR/start_datatransmission.sh

# 创建桌面快捷方式
REAL_USER=$(logname 2>/dev/null || echo $SUDO_USER)
if [ -n "$REAL_USER" ] && [ -d "/home/<USER>/Desktop" ]; then
    cat > /home/<USER>/Desktop/DataTransmission.desktop << 'DESKTOPEOF'
[Desktop Entry]
Version=1.0
Type=Application
Name=DataTransmission
Comment=数据传输客户端
Exec=/opt/DataTransmission/start_datatransmission.sh
Terminal=true
StartupNotify=true
Categories=Application;Network;
DESKTOPEOF
    
    chmod +x /home/<USER>/Desktop/DataTransmission.desktop
    chown $REAL_USER:$REAL_USER /home/<USER>/Desktop/DataTransmission.desktop
    echo "✓ 桌面快捷方式已创建"
fi

echo ""
echo "=== 安装完成 ==="
echo "下一步操作："
echo "1. 配置数据库: mysql_secure_installation"
echo "2. 创建数据库和用户"
echo "3. 配置应用: vi $INSTALL_DIR/config.py"
echo "4. 启动程序: $INSTALL_DIR/start_datatransmission.sh"
echo "5. 或双击桌面快捷方式启动"
"""
    
    with open(f"{deploy_dir}/scripts/install_centos7.sh", 'w') as f:
        f.write(install_script)
    
    os.chmod(f"{deploy_dir}/scripts/install_centos7.sh", 0o755)
    print("✓ 创建安装脚本")
    
    # 创建数据库初始化脚本
    db_script = """-- DataTransmission 数据库初始化脚本
-- 在MariaDB中执行

CREATE DATABASE IF NOT EXISTS DataTransmission CHARACTER SET utf8 COLLATE utf8_general_ci;

USE DataTransmission;

-- 创建用户
CREATE USER IF NOT EXISTS 'datatrans'@'localhost' IDENTIFIED BY 'JKga#123';
GRANT ALL PRIVILEGES ON DataTransmission.* TO 'datatrans'@'localhost';
FLUSH PRIVILEGES;

-- 表结构将由程序自动创建

SELECT 'DataTransmission数据库初始化完成' AS message;
"""
    
    with open(f"{deploy_dir}/scripts/init_database.sql", 'w') as f:
        f.write(db_script)
    
    print("✓ 创建数据库脚本")

def create_readme(deploy_dir):
    """创建README文件"""
    readme_content = f"""# DataTransmission CentOS 7 离线程序包

## 系统要求
- CentOS 7 桌面版本
- 图形界面环境 (GNOME/KDE)
- 至少2GB内存
- 至少2GB磁盘空间

## 安装步骤

### 1. 传输文件
将整个程序包传输到CentOS 7机器

### 2. 运行安装脚本
```bash
cd {os.path.basename(deploy_dir)}
sudo ./scripts/install_centos7.sh
```

### 3. 配置数据库
```bash
# 配置MariaDB
sudo mysql_secure_installation

# 初始化数据库
mysql -u root -p < scripts/init_database.sql
```

### 4. 配置应用程序
```bash
sudo vi /opt/DataTransmission/config.py
```

### 5. 启动程序
```bash
# 方法1: 命令行启动
/opt/DataTransmission/start_datatransmission.sh

# 方法2: 双击桌面快捷方式
```

## 功能特性
- HTTP API接口 (端口5000)
- 二维码生成和显示 (900x900像素，居中)
- 摄像头监控和二维码识别
- 实时摄像头预览窗口 (320x240像素，左上角)
- 数据库存储和管理

## 故障排除
1. 确保在图形桌面环境中运行
2. 检查摄像头权限和驱动
3. 确认数据库配置正确
4. 查看程序日志获取详细错误信息

## 技术支持
如遇问题，请提供：
- CentOS版本信息
- 错误日志内容
- 程序运行环境描述
"""
    
    with open(f"{deploy_dir}/README.md", 'w', encoding='utf-8') as f:
        f.write(readme_content)
    
    print("✓ 创建README文件")

def main():
    """主函数"""
    print("DataTransmission CentOS 7 离线程序构建工具")
    print("基于conda环境创建可在CentOS 7桌面版本直接运行的程序包")
    
    # 检查conda环境
    if not check_conda_environment():
        print("conda环境检查失败，请解决问题后重试")
        return False
    
    # 安装conda-pack
    if not install_conda_pack():
        print("conda-pack安装失败，将使用环境配置文件")
    
    # 创建部署包
    deploy_dir = create_deployment_package()
    
    # 创建CentOS 7脚本
    create_centos7_scripts(deploy_dir)
    
    # 创建README
    create_readme(deploy_dir)
    
    print("\n" + "=" * 60)
    print("CentOS 7离线程序包创建完成")
    print("=" * 60)
    print(f"部署包位置: {deploy_dir}")
    print("\n包含文件:")
    print("- src/                     (项目源代码)")
    print("- conda_env/               (conda环境文件)")
    print("- scripts/install_centos7.sh  (安装脚本)")
    print("- scripts/init_database.sql   (数据库脚本)")
    print("- README.md                (使用说明)")
    print("\n部署步骤:")
    print("1. 将整个文件夹传输到CentOS 7机器")
    print("2. 运行: sudo ./scripts/install_centos7.sh")
    print("3. 配置数据库和应用程序")
    print("4. 启动程序")
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
