#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Windows EXE打包脚本
使用PyInstaller将DataTransmission项目打包成独立的Windows可执行文件
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path

def check_dependencies():
    """检查必要的依赖"""
    print("检查依赖包...")

    try:
        import PyInstaller
        print(f"✓ PyInstaller版本: {PyInstaller.__version__}")
    except ImportError:
        print("✗ PyInstaller未安装，正在安装...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
        print("✓ PyInstaller安装完成")

    # 检查其他关键依赖 (包名 -> 导入名映射)
    required_packages = {
        'flask': 'flask',
        'mysql-connector-python': 'mysql.connector',
        'opencv-python': 'cv2',
        'qrcode': 'qrcode',
        'pyzbar': 'pyzbar',
        'pillow': 'PIL',
        'apscheduler': 'apscheduler',
        'numpy': 'numpy',
        'requests': 'requests'
    }

    missing_packages = []
    for package_name, import_name in required_packages.items():
        try:
            module = __import__(import_name)
            # 尝试获取版本信息
            version = getattr(module, '__version__', 'unknown')
            print(f"✓ {package_name} (导入为: {import_name}, 版本: {version})")
        except ImportError as e:
            missing_packages.append(package_name)
            print(f"✗ {package_name} (尝试导入: {import_name}) - 错误: {e}")

    # 特殊检查：mysql.connector的具体模块
    try:
        import mysql.connector
        from mysql.connector import Error  # noqa: F401
        print("✓ MySQL Connector 核心模块检查通过")
    except ImportError as e:
        print(f"⚠️  MySQL Connector 模块问题: {e}")
        print("尝试重新安装: pip uninstall mysql-connector-python && pip install mysql-connector-python")

    # 特殊检查：PIL/Pillow
    try:
        from PIL import Image  # noqa: F401
        print("✓ PIL/Pillow Image模块检查通过")
    except ImportError as e:
        print(f"⚠️  PIL/Pillow 模块问题: {e}")
        print("尝试重新安装: pip uninstall pillow && pip install pillow")

    if missing_packages:
        print(f"\n缺少依赖包: {missing_packages}")
        print("解决方法:")
        print("1. pip install -r requirements.txt")
        print("2. 或者逐个安装: pip install " + " ".join(missing_packages))
        return False

    print("\n✓ 所有依赖包检查通过")
    return True

def create_spec_file():
    """创建PyInstaller规格文件"""
    print("创建优化的PyInstaller规格文件...")

    # 首先尝试使用优化脚本
    try:
        import optimize_pyinstaller_spec
        if optimize_pyinstaller_spec.create_optimized_spec():
            print("✓ 使用优化的规格文件")
            return True
    except:
        print("使用默认规格文件...")

    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

# 数据文件
datas = [
    ('config.py', '.'),
    ('README.md', '.'),
]

# 隐藏导入（解决动态导入问题）
hiddenimports = [
    # MySQL Connector
    'mysql.connector',
    'mysql.connector.locales.eng',
    'mysql.connector.locales.eng.client_error',
    'mysql.connector.connection',
    'mysql.connector.cursor',
    'mysql.connector.pooling',
    'mysql.connector.errors',

    # PyZBar
    'pyzbar',
    'pyzbar.pyzbar',
    'pyzbar.wrapper',

    # OpenCV
    'cv2',
    'cv2.data',

    # PIL/Pillow
    'PIL',
    'PIL.Image',
    'PIL.ImageTk',
    'PIL._tkinter_finder',

    # Tkinter
    'tkinter',
    'tkinter.ttk',
    'tkinter.messagebox',
    'tkinter.filedialog',

    # APScheduler
    'apscheduler',
    'apscheduler.schedulers.background',
    'apscheduler.triggers.interval',
    'apscheduler.executors.pool',
    'apscheduler.jobstores.memory',

    # Flask
    'flask',
    'flask.json.tag',
    'flask.helpers',
    'flask.wrappers',

    # NumPy
    'numpy',
    'numpy.core',
    'numpy.core._multiarray_umath',

    # QRCode
    'qrcode',
    'qrcode.image.pil',
    'qrcode.image.svg',

    # 其他可能需要的模块
    'json',
    'logging',
    'threading',
    'time',
    'datetime',

    # 修复PyInstaller运行时错误的模块
    'reprlib',
    'collections',
    'collections.abc',
    'pkgutil',
    'importlib',
    'importlib.util',
    'importlib.machinery',
    'importlib.abc',
    'sys',
    'os',
    'pathlib',
    'types',
    'weakref',
    'gc',
    'builtins',
    'warnings',
    'traceback',
    'linecache',
    'tokenize',
    'keyword',
    'operator',
    'functools',
    'itertools',
    'copy',
    'copyreg',
    'pickle',
    'struct',
    'array',
    'io',
    'codecs',
    'locale',
    'encodings',
    'encodings.utf_8',
    'encodings.latin_1',
    'encodings.cp1252',
    'encodings.ascii',
]

# 排除的模块（减少文件大小，但保留必要模块）
excludes = [
    'matplotlib',
    'scipy',
    'pandas',
    'jupyter',
    'notebook',
    'IPython',
    'pytest',
    'setuptools',
    'distutils',
    'test',
    'tests',
    'unittest',
    'doctest',
    'pydoc',
    'xmlrpc',
    'email',
    'html',
    'xml',
    'multiprocessing',
    'concurrent',
    'asyncio',
    'queue',
    'dbm',
    'shelve',
    'marshal',
    'xdrlib',
    'heapq',
    'bisect',
    'pprint',
    'difflib',
    'stringprep',
    'readline',
    'rlcompleter',
    'cmd',
    'shlex',
    'tkinter.tix',
    'tkinter.turtle',
    'turtle',
    'turtledemo',
]

a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=[],
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=excludes,
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='DataTransmission',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='app_icon.ico' if os.path.exists('app_icon.ico') else None,
)
'''
    
    with open('DataTransmission.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("✓ 创建PyInstaller规格文件")

def create_windows_config():
    """创建Windows专用配置文件"""
    config_content = '''# Windows服务器配置文件
# 数据库配置
DATABASE_CONFIG = {
    'host': 'localhost',
    'port': 3306,
    'database': 'DataTransmission',
    'user': 'root',  # 请根据实际情况修改
    'password': '',  # 请根据实际情况修改
    'charset': 'utf8'
}

# Flask配置
FLASK_CONFIG = {
    'host': '0.0.0.0',
    'port': 5000,
    'debug': False
}

# 二维码显示配置
QR_DISPLAY_TIME = 2  # 二维码显示时间（秒）
QR_DISPLAY_SIZE = 900  # 二维码显示尺寸（像素）

# 摄像头配置
CAMERA_INDEX = 0  # 摄像头索引
CAPTURE_INTERVAL = 1  # 截图间隔（秒）

# Windows特定配置
WINDOWS_SERVICE_MODE = True  # Windows服务模式
LOG_TO_FILE = True  # 记录日志到文件
'''
    
    with open('config_windows.py', 'w', encoding='utf-8') as f:
        f.write(config_content)
    
    print("✓ 创建Windows配置文件")

def build_exe():
    """构建EXE文件"""
    print("开始构建EXE文件...")
    
    # 清理之前的构建
    if os.path.exists('dist'):
        shutil.rmtree('dist')
    if os.path.exists('build'):
        shutil.rmtree('build')
    
    # 运行PyInstaller - 优先使用优化的规格文件
    spec_file = 'DataTransmission_optimized.spec' if os.path.exists('DataTransmission_optimized.spec') else 'DataTransmission.spec'

    cmd = [
        'pyinstaller',
        '--clean',
        '--noconfirm',
        spec_file
    ]

    print(f"使用规格文件: {spec_file}")
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("✓ EXE文件构建成功")
        if result.stdout:
            print("构建输出:", result.stdout[-500:])  # 显示最后500字符
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ 构建失败: {e}")
        print(f"错误输出: {e.stderr}")
        if e.stdout:
            print(f"标准输出: {e.stdout}")
        return False

def create_deployment_package():
    """创建部署包"""
    print("创建Windows部署包...")
    
    # 创建部署目录
    deploy_dir = Path('DataTransmission_Windows_Deploy')
    if deploy_dir.exists():
        shutil.rmtree(deploy_dir)
    
    deploy_dir.mkdir()
    
    # 复制EXE文件
    exe_path = Path('dist/DataTransmission.exe')
    if exe_path.exists():
        shutil.copy2(exe_path, deploy_dir / 'DataTransmission.exe')
        print("✓ 复制EXE文件")
    else:
        print("✗ 未找到EXE文件")
        return False
    
    # 复制配置文件
    shutil.copy2('config_windows.py', deploy_dir / 'config.py')
    
    # 复制其他必要文件
    if os.path.exists('README.md'):
        shutil.copy2('README.md', deploy_dir)
    
    # 创建启动脚本
    start_script = '''@echo off
echo 启动DataTransmission服务...
echo 请确保已安装MySQL数据库并配置好连接信息
echo.
DataTransmission.exe
pause
'''
    
    with open(deploy_dir / 'start.bat', 'w', encoding='gbk') as f:
        f.write(start_script)
    
    # 创建安装说明
    install_guide = '''# DataTransmission Windows部署说明

## 文件说明
- DataTransmission.exe: 主程序
- config.py: 配置文件
- start.bat: 启动脚本
- README.md: 项目说明

## 部署步骤

### 1. 安装MySQL数据库
下载并安装MySQL 8.0或MariaDB 10.x

### 2. 创建数据库
```sql
CREATE DATABASE DataTransmission CHARACTER SET utf8 COLLATE utf8_general_ci;
CREATE USER 'datatrans'@'localhost' IDENTIFIED BY 'your_password';
GRANT ALL PRIVILEGES ON DataTransmission.* TO 'datatrans'@'localhost';
FLUSH PRIVILEGES;
```

### 3. 配置应用程序
编辑 config.py 文件，修改数据库连接信息：
```python
DATABASE_CONFIG = {
    'host': 'localhost',
    'port': 3306,
    'database': 'DataTransmission',
    'user': 'datatrans',
    'password': 'your_password',
    'charset': 'utf8'
}
```

### 4. 运行程序
双击 start.bat 或直接运行 DataTransmission.exe

### 5. 测试功能
打开浏览器访问: http://localhost:5000/health

## 注意事项
1. 确保Windows防火墙允许程序访问网络
2. 如果使用摄像头功能，确保摄像头驱动正常
3. 程序需要管理员权限来访问摄像头和网络端口
4. 建议在Windows Server 2016+或Windows 10+上运行

## 故障排除
- 如果程序无法启动，检查config.py中的数据库配置
- 如果摄像头无法使用，检查设备管理器中的摄像头状态
- 如果二维码无法显示，确保系统支持图形界面
- 查看程序目录下的data_transmission.log文件获取详细错误信息
'''
    
    with open(deploy_dir / 'INSTALL_GUIDE.md', 'w', encoding='utf-8') as f:
        f.write(install_guide)
    
    print(f"✓ 部署包创建完成: {deploy_dir}")
    return True

def main():
    """主函数"""
    print("=" * 50)
    print("DataTransmission Windows EXE打包工具")
    print("=" * 50)
    
    # 检查依赖
    if not check_dependencies():
        return False
    
    # 创建规格文件
    create_spec_file()
    
    # 创建Windows配置
    create_windows_config()
    
    # 构建EXE
    if not build_exe():
        return False
    
    # 创建部署包
    if not create_deployment_package():
        return False
    
    print("\n" + "=" * 50)
    print("打包完成！")
    print("=" * 50)
    print("生成的文件:")
    print("- DataTransmission_Windows_Deploy/DataTransmission.exe")
    print("- DataTransmission_Windows_Deploy/config.py")
    print("- DataTransmission_Windows_Deploy/start.bat")
    print("- DataTransmission_Windows_Deploy/INSTALL_GUIDE.md")
    print("\n部署步骤:")
    print("1. 将 DataTransmission_Windows_Deploy 文件夹复制到Windows服务器")
    print("2. 安装MySQL数据库")
    print("3. 配置 config.py 中的数据库连接")
    print("4. 运行 start.bat 启动程序")
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
