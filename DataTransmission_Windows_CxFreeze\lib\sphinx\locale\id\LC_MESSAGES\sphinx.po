# Translations template for Sphinx.
# Copyright (C) 2024 ORGANIZATION
# This file is distributed under the same license as the Sphinx project.
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2016-2017
# <AUTHOR> <EMAIL>, 2009
# <AUTHOR> <EMAIL>, 2019-2020
# <AUTHOR> <EMAIL>, 2023
# Sakti <PERSON>wi <PERSON>ahyono <<EMAIL>>, 2013,2015
# Tumpal Gemelli, 2018
msgid ""
msgstr ""
"Project-Id-Version: Sphinx\n"
"Report-Msgid-Bugs-To: EMAIL@ADDRESS\n"
"POT-Creation-Date: 2024-04-14 23:27+0000\n"
"PO-Revision-Date: 2013-04-02 08:44+0000\n"
"Last-Translator: oon arfiandwi (OonID) <<EMAIL>>, 2023\n"
"Language-Team: Indonesian (http://app.transifex.com/sphinx-doc/sphinx-1/language/id/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.14.0\n"
"Language: id\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#: sphinx/application.py:157
#, python-format
msgid "Cannot find source directory (%s)"
msgstr "Tidak dapat menemukan direktori sumber (%s)"

#: sphinx/application.py:161
#, python-format
msgid "Output directory (%s) is not a directory"
msgstr "Direktori keluaran (%s) bukan direktori"

#: sphinx/application.py:165
msgid "Source directory and destination directory cannot be identical"
msgstr "Direktori sumber dan direktori tujuan tidak boleh sama"

#: sphinx/application.py:197
#, python-format
msgid "Running Sphinx v%s"
msgstr "Menjalankan Sphinx v%s"

#: sphinx/application.py:219
#, python-format
msgid ""
"This project needs at least Sphinx v%s and therefore cannot be built with "
"this version."
msgstr "Proyek ini memerlukan sedikitnya Sphinx v%s dan maka itu tidak bisa dibangun dengan versi ini."

#: sphinx/application.py:235
msgid "making output directory"
msgstr "membuat direktori keluaran"

#: sphinx/application.py:240 sphinx/registry.py:450
#, python-format
msgid "while setting up extension %s:"
msgstr "saat menyiapkan ekstensi %s:"

#: sphinx/application.py:246
msgid ""
"'setup' as currently defined in conf.py isn't a Python callable. Please "
"modify its definition to make it a callable function. This is needed for "
"conf.py to behave as a Sphinx extension."
msgstr "'setup' yang saat ini didefinisikan pada conf.py bukanlah sebuah Python callable. Silakan modifikasi definisinya untuk membuatnya menjadi fungsi callable. Hal ini diperlukan guna conf.py berjalan sebagai ekstensi Sphinx."

#: sphinx/application.py:277
#, python-format
msgid "loading translations [%s]... "
msgstr "memuat terjemahan [%s]... "

#: sphinx/application.py:294 sphinx/util/display.py:85
msgid "done"
msgstr "selesai"

#: sphinx/application.py:296
msgid "not available for built-in messages"
msgstr "tidak tersedia untuk built-in messages"

#: sphinx/application.py:310
msgid "loading pickled environment"
msgstr "memuat lingkungan yang diawetkan"

#: sphinx/application.py:318
#, python-format
msgid "failed: %s"
msgstr "gagal: %s"

#: sphinx/application.py:332
msgid "No builder selected, using default: html"
msgstr "Tidak ada builder yang dipilih, menggunakan default: html"

#: sphinx/application.py:365
msgid "succeeded"
msgstr "berhasil"

#: sphinx/application.py:366
msgid "finished with problems"
msgstr "selesai with masalah"

#: sphinx/application.py:370
#, python-format
msgid "build %s, %s warning (with warnings treated as errors)."
msgstr "bangun %s, %s peringatan (dengan peringatan dianggap sebagai kesalahan)."

#: sphinx/application.py:372
#, python-format
msgid "build %s, %s warnings (with warnings treated as errors)."
msgstr ""

#: sphinx/application.py:375
#, python-format
msgid "build %s, %s warning."
msgstr "build %s, %s peringatan."

#: sphinx/application.py:377
#, python-format
msgid "build %s, %s warnings."
msgstr ""

#: sphinx/application.py:381
#, python-format
msgid "build %s."
msgstr "build %s."

#: sphinx/application.py:610
#, python-format
msgid "node class %r is already registered, its visitors will be overridden"
msgstr "kelas simpul %r sudah terdaftar, pengunjungnya akan diganti"

#: sphinx/application.py:689
#, python-format
msgid "directive %r is already registered, it will be overridden"
msgstr "pengarahan %r sudah terdaftar, itu akan diganti"

#: sphinx/application.py:711 sphinx/application.py:733
#, python-format
msgid "role %r is already registered, it will be overridden"
msgstr "peran %r sudah terdaftar, itu akan diganti"

#: sphinx/application.py:1282
#, python-format
msgid ""
"the %s extension does not declare if it is safe for parallel reading, "
"assuming it isn't - please ask the extension author to check and make it "
"explicit"
msgstr "ekstensi %s tidak akan dinyatakan jika itu aman untuk pembacaan paralel, dengan anggapan itu tidak aman - silakan tanya pembuat ekstensi untuk memeriksa dan membuatnya eksplisit"

#: sphinx/application.py:1286
#, python-format
msgid "the %s extension is not safe for parallel reading"
msgstr "ekstensi %s tidak aman untuk pembacaan paralel"

#: sphinx/application.py:1289
#, python-format
msgid ""
"the %s extension does not declare if it is safe for parallel writing, "
"assuming it isn't - please ask the extension author to check and make it "
"explicit"
msgstr " \nekstensi %s tidak akan dinyatakan jika itu aman untuk penulisan paralel, dengan anggapan itu tidak aman - silakan tanya pembuat ekstensi untuk memeriksa dan membuatnya eksplisit"

#: sphinx/application.py:1293
#, python-format
msgid "the %s extension is not safe for parallel writing"
msgstr "ekstensi %s tidak aman untuk penulisan paralel"

#: sphinx/application.py:1301 sphinx/application.py:1305
#, python-format
msgid "doing serial %s"
msgstr "mengerjakan serial %s"

#: sphinx/config.py:309
#, python-format
msgid "config directory doesn't contain a conf.py file (%s)"
msgstr "direktori konfigurasi tidak berisi berkas conf.py (%s)"

#: sphinx/config.py:318
msgid ""
"Invalid configuration value found: 'language = None'. Update your "
"configuration to a valid language code. Falling back to 'en' (English)."
msgstr ""

#: sphinx/config.py:341
#, python-format
msgid ""
"cannot override dictionary config setting %r, ignoring (use %r to set "
"individual elements)"
msgstr "tidak dapat menulis ulang pengaturan direktori konfigurasi %r, mengabaikan (gunakan %r untuk mengatur elemen-elemen satuan)"

#: sphinx/config.py:350
#, python-format
msgid "invalid number %r for config value %r, ignoring"
msgstr "nomor %r yang salah untuk konfigurasi nilai %r, mengabaikan"

#: sphinx/config.py:355
#, python-format
msgid "cannot override config setting %r with unsupported type, ignoring"
msgstr "tidak dapat menulis ulang pengaturan konfigurasi %r dengan tipe yang tidak didukung, mengabaikan"

#: sphinx/config.py:378
#, python-format
msgid "unknown config value %r in override, ignoring"
msgstr "nilai konfigurasi %r yang tidak dikenal pada penulisan ulang, mengabaikan"

#: sphinx/config.py:418
#, python-format
msgid "No such config value: %r"
msgstr ""

#: sphinx/config.py:440
#, python-format
msgid "Config value %r already present"
msgstr "Nilai konfigurasi %r sudah ada"

#: sphinx/config.py:473
#, python-format
msgid "cannot cache unpickable configuration value: %r"
msgstr ""

#: sphinx/config.py:509
#, python-format
msgid "There is a syntax error in your configuration file: %s\n"
msgstr "Ada kesalahan sintaksis dalam file konfigurasi Anda: %s\n"

#: sphinx/config.py:512
msgid ""
"The configuration file (or one of the modules it imports) called sys.exit()"
msgstr "Berkas konfigurasi (atau salah satu dari modul terimpor) disebut sys.exit()"

#: sphinx/config.py:519
#, python-format
msgid ""
"There is a programmable error in your configuration file:\n"
"\n"
"%s"
msgstr "Terdapat kesalahan programmable dalam berkas konfigurasi anda:\n\n%s"

#: sphinx/config.py:540
#, python-format
msgid "Failed to convert %r to a set or tuple"
msgstr ""

#: sphinx/config.py:565
#, python-format
msgid ""
"The config value `source_suffix' expects a string, list of strings, or "
"dictionary. But `%r' is given."
msgstr "Nilai konfigurasi `source_suffix 'mengharapkan sebuah string, daftar string, atau kamus. Tetapi `%r' diberikan."

#: sphinx/config.py:585
#, python-format
msgid "Section %s"
msgstr "Bab %s"

#: sphinx/config.py:586
#, python-format
msgid "Fig. %s"
msgstr "Gambar. %s"

#: sphinx/config.py:587
#, python-format
msgid "Table %s"
msgstr "Tabel %s"

#: sphinx/config.py:588
#, python-format
msgid "Listing %s"
msgstr "Daftar %s"

#: sphinx/config.py:663
msgid ""
"The config value `{name}` has to be a one of {candidates}, but `{current}` "
"is given."
msgstr "Nilai konfigurasi `{name}` harus salah satu dari {candidates}, tapi `{current}` diberikan."

#: sphinx/config.py:687
msgid ""
"The config value `{name}' has type `{current.__name__}'; expected "
"{permitted}."
msgstr "Nilai konfigurasi `{name}' memiliki tipe `{current.__name__}'; diharapkan {permitted}."

#: sphinx/config.py:700
msgid ""
"The config value `{name}' has type `{current.__name__}', defaults to "
"`{default.__name__}'."
msgstr "Nilai konfigurasi `{name}` bertipe `{current.__name__}', default menjadi `{default.__name__}'."

#: sphinx/config.py:711
#, python-format
msgid "primary_domain %r not found, ignored."
msgstr "primary_domain %r tidak ditemukan, diabaikan."

#: sphinx/config.py:723
msgid ""
"Since v2.0, Sphinx uses \"index\" as root_doc by default. Please add "
"\"root_doc = 'contents'\" to your conf.py."
msgstr ""

#: sphinx/events.py:64
#, python-format
msgid "Event %r already present"
msgstr "Event %r sudah ada"

#: sphinx/events.py:70
#, python-format
msgid "Unknown event name: %s"
msgstr "Nama event tidak dikenal: %s"

#: sphinx/events.py:109
#, python-format
msgid "Handler %r for event %r threw an exception"
msgstr ""

#: sphinx/extension.py:55
#, python-format
msgid ""
"The %s extension is required by needs_extensions settings, but it is not "
"loaded."
msgstr "Ekstensi %s diperlukan oleh pengaturan needs_extensions, tapi itu tidak dimuat."

#: sphinx/extension.py:76
#, python-format
msgid ""
"This project needs the extension %s at least in version %s and therefore "
"cannot be built with the loaded version (%s)."
msgstr "Proyek ini memerlukan ekstensi %s sedikitnya pada versi %s dan maka itu tidak bisa dibangun dengan versi yang dimuat (%s)."

#: sphinx/highlighting.py:155
#, python-format
msgid "Pygments lexer name %r is not known"
msgstr "Nama Pygments lexer %r tidak diketahui"

#: sphinx/highlighting.py:189
#, python-format
msgid ""
"Lexing literal_block %r as \"%s\" resulted in an error at token: %r. "
"Retrying in relaxed mode."
msgstr ""

#: sphinx/project.py:66
#, python-format
msgid ""
"multiple files found for the document \"%s\": %r\n"
"Use %r for the build."
msgstr ""

#: sphinx/project.py:81
#, python-format
msgid "Ignored unreadable document %r."
msgstr ""

#: sphinx/registry.py:142
#, python-format
msgid "Builder class %s has no \"name\" attribute"
msgstr "Class Builder %s tidak punya atribut \"name\""

#: sphinx/registry.py:144
#, python-format
msgid "Builder %r already exists (in module %s)"
msgstr "Builder %r sudah ada (di modul %s)"

#: sphinx/registry.py:157
#, python-format
msgid "Builder name %s not registered or available through entry point"
msgstr "Nama Builder %s todal terdaftar atau tersedia melalui entry point"

#: sphinx/registry.py:164
#, python-format
msgid "Builder name %s not registered"
msgstr "Nama Builder %s tidak terdaftar"

#: sphinx/registry.py:171
#, python-format
msgid "domain %s already registered"
msgstr "domain %s telah terdaftar"

#: sphinx/registry.py:194 sphinx/registry.py:207 sphinx/registry.py:218
#, python-format
msgid "domain %s not yet registered"
msgstr "domain %s belum didaftarkan"

#: sphinx/registry.py:198
#, python-format
msgid "The %r directive is already registered to domain %s"
msgstr "Pengarahan %r sudah terdaftar di domain %s"

#: sphinx/registry.py:210
#, python-format
msgid "The %r role is already registered to domain %s"
msgstr "Peran %r sudah terdaftar di domain %s"

#: sphinx/registry.py:221
#, python-format
msgid "The %r index is already registered to domain %s"
msgstr "Indeks %r sudah terdaftar ke domain %s"

#: sphinx/registry.py:252
#, python-format
msgid "The %r object_type is already registered"
msgstr "object_type %r telah didaftarkan"

#: sphinx/registry.py:278
#, python-format
msgid "The %r crossref_type is already registered"
msgstr "crossref_type %r telah didaftarkan"

#: sphinx/registry.py:285
#, python-format
msgid "source_suffix %r is already registered"
msgstr "source_suffix %r telah didaftarkan"

#: sphinx/registry.py:294
#, python-format
msgid "source_parser for %r is already registered"
msgstr "source_parser untuk %r telah didaftarkan"

#: sphinx/registry.py:302
#, python-format
msgid "Source parser for %s not registered"
msgstr "Parser sumber untuk %s tidak terdaftar"

#: sphinx/registry.py:318
#, python-format
msgid "Translator for %r already exists"
msgstr "Penerjemah untuk %r sudah ada"

#: sphinx/registry.py:334
#, python-format
msgid "kwargs for add_node() must be a (visit, depart) function tuple: %r=%r"
msgstr "kwargs untuk add_node() harus berupa (visit, depart) function tuple: %r=%r"

#: sphinx/registry.py:417
#, python-format
msgid "enumerable_node %r already registered"
msgstr "enumerable_node %r telah terdaftar"

#: sphinx/registry.py:429
#, python-format
msgid "math renderer %s is already registered"
msgstr ""

#: sphinx/registry.py:444
#, python-format
msgid ""
"the extension %r was already merged with Sphinx since version %s; this "
"extension is ignored."
msgstr "ekstensi %r telah digabungkan dengan Sphinx sejak versi %s; ekstensi diabaikan."

#: sphinx/registry.py:455
msgid "Original exception:\n"
msgstr "Eksepsi orisinal:\n"

#: sphinx/registry.py:456
#, python-format
msgid "Could not import extension %s"
msgstr "Tidak dapat mengimpor ekstensi %s"

#: sphinx/registry.py:461
#, python-format
msgid ""
"extension %r has no setup() function; is it really a Sphinx extension "
"module?"
msgstr "ekstensi %r tidak memiliki fungsi setup(); apa itu benar-benar sebuah modul ekstensi Sphinx?"

#: sphinx/registry.py:470
#, python-format
msgid ""
"The %s extension used by this project needs at least Sphinx v%s; it "
"therefore cannot be built with this version."
msgstr "Ekstensi %s yang digunakan proyek ini memerlukan sedikitnya Sphinx v%s; maka itu tidak bisa dibangun dengan versi ini."

#: sphinx/registry.py:478
#, python-format
msgid ""
"extension %r returned an unsupported object from its setup() function; it "
"should return None or a metadata dictionary"
msgstr "ekstensi %r mengembalikan objek yang tidak didukung dari fungsi setup() nya; seharusnya mengembalikan None atau dictionary metadata"

#: sphinx/roles.py:201
#, python-format
msgid "Python Enhancement Proposals; PEP %s"
msgstr "Python Enhancement Proposals; PEP %s"

#: sphinx/roles.py:222
#, python-format
msgid "invalid PEP number %s"
msgstr "nomor PEP %s tidak valid"

#: sphinx/roles.py:257
#, python-format
msgid "invalid RFC number %s"
msgstr "nomor RFC tidak valid %s"

#: sphinx/theming.py:125
#, python-format
msgid "setting %s.%s occurs in none of the searched theme configs"
msgstr "pengaturan %s.%s terjadi pada tak satupun konfigurasi tema yang dicari"

#: sphinx/theming.py:140
#, python-format
msgid "unsupported theme option %r given"
msgstr "opsi tema yang tidak didukung %r diberikan"

#: sphinx/theming.py:206
#, python-format
msgid "file %r on theme path is not a valid zipfile or contains no theme"
msgstr "berkas %r pada path tema merupakan berkas zip yang tidak valid atau tidak berisi tema"

#: sphinx/theming.py:226
#, python-format
msgid "no theme named %r found (missing theme.toml?)"
msgstr ""

#: sphinx/theming.py:259
#, python-format
msgid "The %r theme has circular inheritance"
msgstr ""

#: sphinx/theming.py:262
#, python-format
msgid ""
"The %r theme inherits from %r, which is not a loaded theme. Loaded themes "
"are: %s"
msgstr ""

#: sphinx/theming.py:269
#, python-format
msgid "The %r theme has too many ancestors"
msgstr ""

#: sphinx/theming.py:295
#, python-format
msgid "no theme configuration file found in %r"
msgstr ""

#: sphinx/theming.py:323 sphinx/theming.py:374
#, python-format
msgid "theme %r doesn't have the \"theme\" table"
msgstr ""

#: sphinx/theming.py:327
#, python-format
msgid "The %r theme \"[theme]\" table is not a table"
msgstr ""

#: sphinx/theming.py:331 sphinx/theming.py:377
#, python-format
msgid "The %r theme must define the \"theme.inherit\" setting"
msgstr ""

#: sphinx/theming.py:335
#, python-format
msgid "The %r theme \"[options]\" table is not a table"
msgstr ""

#: sphinx/theming.py:353
#, python-format
msgid "The \"theme.pygments_style\" setting must be a table. Hint: \"%s\""
msgstr ""

#: sphinx/builders/__init__.py:183
#, python-format
msgid "a suitable image for %s builder not found: %s (%s)"
msgstr "gambar yang sesuai untuk builder %s tidak ditemukan: %s (%s)"

#: sphinx/builders/__init__.py:187
#, python-format
msgid "a suitable image for %s builder not found: %s"
msgstr "gambar yang sesuai untuk builder %s tidak ditemukan: %s"

#: sphinx/builders/__init__.py:207
msgid "building [mo]: "
msgstr "membangun [mo]: "

#: sphinx/builders/__init__.py:208 sphinx/builders/__init__.py:574
#: sphinx/builders/__init__.py:601
msgid "writing output... "
msgstr "menulis keluaran... "

#: sphinx/builders/__init__.py:217
#, python-format
msgid "all of %d po files"
msgstr "semua dari %d berkas po"

#: sphinx/builders/__init__.py:235
#, python-format
msgid "targets for %d po files that are specified"
msgstr "target untuk %d berkas po yang telah ditetapkan"

#: sphinx/builders/__init__.py:243
#, python-format
msgid "targets for %d po files that are out of date"
msgstr "target untuk %d berkas po telah usang"

#: sphinx/builders/__init__.py:252
msgid "all source files"
msgstr "semua berkas sumber"

#: sphinx/builders/__init__.py:262
#, python-format
msgid "file %r given on command line does not exist, "
msgstr ""

#: sphinx/builders/__init__.py:267
#, python-format
msgid ""
"file %r given on command line is not under the source directory, ignoring"
msgstr "berkas %r yang diberikan di command line tidak berada dalam direktori sumber, mengabaikan"

#: sphinx/builders/__init__.py:273
#, python-format
msgid "file %r given on command line is not a valid document, ignoring"
msgstr ""

#: sphinx/builders/__init__.py:282
#, python-format
msgid "%d source files given on command line"
msgstr "%d berkas sumber diberikan di command line"

#: sphinx/builders/__init__.py:294
#, python-format
msgid "targets for %d source files that are out of date"
msgstr "target untuk %d berkas sumber yang telah usang"

#: sphinx/builders/__init__.py:309 sphinx/builders/gettext.py:243
#, python-format
msgid "building [%s]: "
msgstr "membangun [%s]: "

#: sphinx/builders/__init__.py:316
msgid "looking for now-outdated files... "
msgstr "mencari berkas yang kini-usang... "

#: sphinx/builders/__init__.py:320
#, python-format
msgid "%d found"
msgstr "%d ditemukan"

#: sphinx/builders/__init__.py:322
msgid "none found"
msgstr "tidak ditemukan apapun"

#: sphinx/builders/__init__.py:327
msgid "pickling environment"
msgstr "lingkungan pengawetan"

#: sphinx/builders/__init__.py:333
msgid "checking consistency"
msgstr "memeriksa konsistensi"

#: sphinx/builders/__init__.py:337
msgid "no targets are out of date."
msgstr "tidak ada target yang usang."

#: sphinx/builders/__init__.py:376
msgid "updating environment: "
msgstr "memperbarui lingkungan:"

#: sphinx/builders/__init__.py:397
#, python-format
msgid "%s added, %s changed, %s removed"
msgstr "%s ditambahkan, %s diubah, %s dihapus"

#: sphinx/builders/__init__.py:435 sphinx/builders/__init__.py:447
msgid "reading sources... "
msgstr "membaca sumber... "

#: sphinx/builders/__init__.py:549
#, python-format
msgid "docnames to write: %s"
msgstr "docnames yang akan ditulis: %s"

#: sphinx/builders/__init__.py:558 sphinx/builders/singlehtml.py:157
msgid "preparing documents"
msgstr "menyiapkan dokumen"

#: sphinx/builders/__init__.py:561
msgid "copying assets"
msgstr ""

#: sphinx/builders/_epub_base.py:215
#, python-format
msgid "duplicated ToC entry found: %s"
msgstr "entri ToC ganda ditemukan: %s"

#: sphinx/builders/_epub_base.py:404 sphinx/builders/html/__init__.py:758
#: sphinx/builders/latex/__init__.py:432 sphinx/builders/texinfo.py:187
msgid "copying images... "
msgstr "menyalin gambar... "

#: sphinx/builders/_epub_base.py:411
#, python-format
msgid "cannot read image file %r: copying it instead"
msgstr "tidak dapat membaca berkas gambar %r: menyalin gambar sebagai gantinya"

#: sphinx/builders/_epub_base.py:417 sphinx/builders/html/__init__.py:766
#: sphinx/builders/latex/__init__.py:440 sphinx/builders/texinfo.py:197
#, python-format
msgid "cannot copy image file %r: %s"
msgstr "tidak dapat menyalin berkas gambar %r: %s"

#: sphinx/builders/_epub_base.py:434
#, python-format
msgid "cannot write image file %r: %s"
msgstr "tidak dapat menulis berkas gambar %r: %s"

#: sphinx/builders/_epub_base.py:444
msgid "Pillow not found - copying image files"
msgstr "Pillow tidak ditemukan - menyalin berkas gambar"

#: sphinx/builders/_epub_base.py:470
msgid "writing mimetype file..."
msgstr ""

#: sphinx/builders/_epub_base.py:475
msgid "writing META-INF/container.xml file..."
msgstr ""

#: sphinx/builders/_epub_base.py:508
msgid "writing content.opf file..."
msgstr ""

#: sphinx/builders/_epub_base.py:539
#, python-format
msgid "unknown mimetype for %s, ignoring"
msgstr "mimetype yang tidak dikenal untuk %s, mengabaikan"

#: sphinx/builders/_epub_base.py:686
msgid "writing toc.ncx file..."
msgstr ""

#: sphinx/builders/_epub_base.py:711
#, python-format
msgid "writing %s file..."
msgstr "menulis %s berkas..."

#: sphinx/builders/changes.py:32
#, python-format
msgid "The overview file is in %(outdir)s."
msgstr "Berkas tinjauan berada di %(outdir)s."

#: sphinx/builders/changes.py:59
#, python-format
msgid "no changes in version %s."
msgstr "tidak ada pengubahan dalam versi %s."

#: sphinx/builders/changes.py:61
msgid "writing summary file..."
msgstr "menulis berkas ringkasan..."

#: sphinx/builders/changes.py:76
msgid "Builtins"
msgstr "Modul Internal"

#: sphinx/builders/changes.py:78
msgid "Module level"
msgstr "Level Modul"

#: sphinx/builders/changes.py:123
msgid "copying source files..."
msgstr "menyalin berkas sumber..."

#: sphinx/builders/changes.py:130
#, python-format
msgid "could not read %r for changelog creation"
msgstr "tidak dapat membaca %r untuk pembuatan changelog"

#: sphinx/builders/dummy.py:19
msgid "The dummy builder generates no files."
msgstr "Builder contoh tidak menghasilkan berkas apapun."

#: sphinx/builders/epub3.py:81
#, python-format
msgid "The ePub file is in %(outdir)s."
msgstr "Berkas ePub berada di %(outdir)s."

#: sphinx/builders/epub3.py:185
msgid "writing nav.xhtml file..."
msgstr ""

#: sphinx/builders/epub3.py:211
msgid "conf value \"epub_language\" (or \"language\") should not be empty for EPUB3"
msgstr "nilai conf \"epub_language\" (atau \"language\") tidak seharsunya kosong untuk EPUB3"

#: sphinx/builders/epub3.py:215
msgid "conf value \"epub_uid\" should be XML NAME for EPUB3"
msgstr "nilai conf \"epub_uid\" harus berupa XML NAME untuk EPUB3"

#: sphinx/builders/epub3.py:218
msgid "conf value \"epub_title\" (or \"html_title\") should not be empty for EPUB3"
msgstr "nilai conf \"epub_title\" (atau \"html_title\") tidak seharusnya kosong untuk EPUB3"

#: sphinx/builders/epub3.py:222
msgid "conf value \"epub_author\" should not be empty for EPUB3"
msgstr "nilai conf \"epub_author\" tidak seharusnya kosong untuk EPUB3"

#: sphinx/builders/epub3.py:225
msgid "conf value \"epub_contributor\" should not be empty for EPUB3"
msgstr "nilai conf \"epub_contributor\" tidak seharusnya kosong untuk EPUB3"

#: sphinx/builders/epub3.py:228
msgid "conf value \"epub_description\" should not be empty for EPUB3"
msgstr "nilai conf \"epub_description\" tidak seharusnya kosong untuk EPUB3"

#: sphinx/builders/epub3.py:231
msgid "conf value \"epub_publisher\" should not be empty for EPUB3"
msgstr "nilai conf \"epub_publisher\" tidak seharusnya kosong untuk EPUB3"

#: sphinx/builders/epub3.py:234
msgid "conf value \"epub_copyright\" (or \"copyright\")should not be empty for EPUB3"
msgstr "nilai conf \"epub_copyright\" (atau \"copyright\") tidak seharusnya kosong untuk EPUB3"

#: sphinx/builders/epub3.py:238
msgid "conf value \"epub_identifier\" should not be empty for EPUB3"
msgstr "nilai conf \"epub_identifier\" tidak seharusnya kosong untuk EPUB3"

#: sphinx/builders/epub3.py:241
msgid "conf value \"version\" should not be empty for EPUB3"
msgstr "bilai conf \"version\" tidak seharusnya kosong untuk EPUB3"

#: sphinx/builders/epub3.py:255 sphinx/builders/html/__init__.py:1187
#, python-format
msgid "invalid css_file: %r, ignored"
msgstr "css_file yang salah: %r, mengabaikan"

#: sphinx/builders/gettext.py:222
#, python-format
msgid "The message catalogs are in %(outdir)s."
msgstr "Katalog pesan berada di %(outdir)s."

#: sphinx/builders/gettext.py:244
#, python-format
msgid "targets for %d template files"
msgstr "target untuk %d berkas templat"

#: sphinx/builders/gettext.py:248
msgid "reading templates... "
msgstr "membaca templat... "

#: sphinx/builders/gettext.py:282
msgid "writing message catalogs... "
msgstr "menulis katalog pesan... "

#: sphinx/builders/linkcheck.py:59
#, python-format
msgid "Look for any errors in the above output or in %(outdir)s/output.txt"
msgstr "Mencari kesalahan sembarang dalam keluaran di atas atau di %(outdir)s/output.txt"

#: sphinx/builders/linkcheck.py:137
#, python-format
msgid "broken link: %s (%s)"
msgstr "tautan rusak: %s (%s)"

#: sphinx/builders/linkcheck.py:660
#, python-format
msgid "Failed to compile regex in linkcheck_allowed_redirects: %r %s"
msgstr ""

#: sphinx/builders/manpage.py:37
#, python-format
msgid "The manual pages are in %(outdir)s."
msgstr "Halaman manual berada di %(outdir)s."

#: sphinx/builders/manpage.py:44
msgid "no \"man_pages\" config value found; no manual pages will be written"
msgstr "tidak ditemukan nilai konfigurasi \"man_pages\"; halaman manual tidak akan ditulis"

#: sphinx/builders/latex/__init__.py:314 sphinx/builders/manpage.py:53
#: sphinx/builders/singlehtml.py:165 sphinx/builders/texinfo.py:112
msgid "writing"
msgstr "penulisan"

#: sphinx/builders/manpage.py:68
#, python-format
msgid "\"man_pages\" config value references unknown document %s"
msgstr "\"man_pages\" nilai konfigurasi mengacu pada dokumen tidak diketahui %s"

#: sphinx/builders/singlehtml.py:34
#, python-format
msgid "The HTML page is in %(outdir)s."
msgstr "Halaman HTML berada di %(outdir)s."

#: sphinx/builders/singlehtml.py:160
msgid "assembling single document"
msgstr "merakit dokumen tunggal"

#: sphinx/builders/singlehtml.py:178
msgid "writing additional files"
msgstr "menulis file tambahan"

#: sphinx/builders/texinfo.py:48
#, python-format
msgid "The Texinfo files are in %(outdir)s."
msgstr "Berkas Texinfo berada di %(outdir)s."

#: sphinx/builders/texinfo.py:50
msgid ""
"\n"
"Run 'make' in that directory to run these through makeinfo\n"
"(use 'make info' here to do that automatically)."
msgstr "\nJalankan 'make' di direktori tersebut untuk menjalankannya melalui makeinfo\n(gunakan 'make info' di sini untuk melakukannya secara otomatis)."

#: sphinx/builders/texinfo.py:77
msgid "no \"texinfo_documents\" config value found; no documents will be written"
msgstr "tidak ditemukan nilai konfigurasi \"texinfo_documents\"; dokumen tidak akan ditulis"

#: sphinx/builders/texinfo.py:85
#, python-format
msgid "\"texinfo_documents\" config value references unknown document %s"
msgstr "nilai konfigurasi \"texinfo_documents\" mereferensikan dokumen yang tidak dikenal %s"

#: sphinx/builders/latex/__init__.py:296 sphinx/builders/texinfo.py:108
#, python-format
msgid "processing %s"
msgstr "memroses %s"

#: sphinx/builders/latex/__init__.py:369 sphinx/builders/texinfo.py:161
msgid "resolving references..."
msgstr "memecahkan referensi..."

#: sphinx/builders/latex/__init__.py:380 sphinx/builders/texinfo.py:171
msgid " (in "
msgstr " (dalam "

#: sphinx/builders/texinfo.py:202
msgid "copying Texinfo support files"
msgstr "menyalin berkas pendukung Texinfo"

#: sphinx/builders/texinfo.py:206
#, python-format
msgid "error writing file Makefile: %s"
msgstr "kesalahan menulis berkas Makefile: %s"

#: sphinx/builders/text.py:30
#, python-format
msgid "The text files are in %(outdir)s."
msgstr "Berkas teks berada di %(outdir)s."

#: sphinx/builders/html/__init__.py:1138 sphinx/builders/text.py:77
#: sphinx/builders/xml.py:96
#, python-format
msgid "error writing file %s: %s"
msgstr "kesalahan menulis berkas %s: %s"

#: sphinx/builders/xml.py:36
#, python-format
msgid "The XML files are in %(outdir)s."
msgstr "Berkas XML berada di %(outdir)s."

#: sphinx/builders/xml.py:109
#, python-format
msgid "The pseudo-XML files are in %(outdir)s."
msgstr "Berkas pseudo-XML berada di %(outdir)s."

#: sphinx/builders/html/__init__.py:130
#, python-format
msgid "build info file is broken: %r"
msgstr "berkas info build rusak: %r"

#: sphinx/builders/html/__init__.py:168
#, python-format
msgid "The HTML pages are in %(outdir)s."
msgstr "Halaman HTML berada di %(outdir)s."

#: sphinx/builders/html/__init__.py:394
#, python-format
msgid "Failed to read build info file: %r"
msgstr "Gagal membaca berkas info build: %r"

#: sphinx/builders/html/__init__.py:487 sphinx/builders/latex/__init__.py:189
#: sphinx/transforms/__init__.py:119 sphinx/writers/manpage.py:101
#: sphinx/writers/texinfo.py:227
#, python-format
msgid "%b %d, %Y"
msgstr "%d %b, %Y"

#: sphinx/builders/html/__init__.py:506 sphinx/themes/basic/defindex.html:30
msgid "General Index"
msgstr "Indeks Umum"

#: sphinx/builders/html/__init__.py:506
msgid "index"
msgstr "index"

#: sphinx/builders/html/__init__.py:579
msgid "next"
msgstr "berikut"

#: sphinx/builders/html/__init__.py:588
msgid "previous"
msgstr "sebelum"

#: sphinx/builders/html/__init__.py:684
msgid "generating indices"
msgstr "menghasilkan indeks"

#: sphinx/builders/html/__init__.py:699
msgid "writing additional pages"
msgstr "menulis halaman tambahan"

#: sphinx/builders/html/__init__.py:776
msgid "copying downloadable files... "
msgstr "menyalin berkas yang dapat diunduh... "

#: sphinx/builders/html/__init__.py:784
#, python-format
msgid "cannot copy downloadable file %r: %s"
msgstr "tidak dapat menyalin berkas yang dapat diunduh %r: %s"

#: sphinx/builders/html/__init__.py:817 sphinx/builders/html/__init__.py:829
#, python-format
msgid "Failed to copy a file in html_static_file: %s: %r"
msgstr ""

#: sphinx/builders/html/__init__.py:850
msgid "copying static files"
msgstr "menyalin file statis"

#: sphinx/builders/html/__init__.py:866
#, python-format
msgid "cannot copy static file %r"
msgstr "tidak dapat menyalin berkas statik %r"

#: sphinx/builders/html/__init__.py:871
msgid "copying extra files"
msgstr "menyalin berkas tambahan"

#: sphinx/builders/html/__init__.py:877
#, python-format
msgid "cannot copy extra file %r"
msgstr "tidak dapat menyalin berkas ekstra %r"

#: sphinx/builders/html/__init__.py:884
#, python-format
msgid "Failed to write build info file: %r"
msgstr "Gagal menulis berkas info build: %r"

#: sphinx/builders/html/__init__.py:933
msgid ""
"search index couldn't be loaded, but not all documents will be built: the "
"index will be incomplete."
msgstr "indeks pencarian tidak dapat dimuat, tapi tidak semua dokumen akan dibangun: indeks akan jadi tidak lengkap."

#: sphinx/builders/html/__init__.py:978
#, python-format
msgid "page %s matches two patterns in html_sidebars: %r and %r"
msgstr "halaman %s sebanding dengan dua pola dalam html_sidebars: %r dan %r"

#: sphinx/builders/html/__init__.py:1121
#, python-format
msgid ""
"a Unicode error occurred when rendering the page %s. Please make sure all "
"config values that contain non-ASCII content are Unicode strings."
msgstr "kesalahan Unicode terjadi saat render halaman %s. Silakan pastikan semua nilai konfigurasi yang berisi konten non-ASCII adalah string Unicode."

#: sphinx/builders/html/__init__.py:1126
#, python-format
msgid ""
"An error happened in rendering the page %s.\n"
"Reason: %r"
msgstr "Kesalahan terjadi saat render halaman %s.\nAlasan: %r"

#: sphinx/builders/html/__init__.py:1154
msgid "dumping object inventory"
msgstr "menyisihkan persediaan obyek"

#: sphinx/builders/html/__init__.py:1162
#, python-format
msgid "dumping search index in %s"
msgstr ""

#: sphinx/builders/html/__init__.py:1210
#, python-format
msgid "invalid js_file: %r, ignored"
msgstr "js_file yang salah: %r, mengabaikan"

#: sphinx/builders/html/__init__.py:1238
msgid "Many math_renderers are registered. But no math_renderer is selected."
msgstr "Banyak math_renderers teregistrasi. Namun tidak satu pun math_renderer yang dipilih."

#: sphinx/builders/html/__init__.py:1241
#, python-format
msgid "Unknown math_renderer %r is given."
msgstr "math_renderer %r yang tidak diketahui diberikan."

#: sphinx/builders/html/__init__.py:1249
#, python-format
msgid "html_extra_path entry %r does not exist"
msgstr "entri html_extra_path %r tidak ada"

#: sphinx/builders/html/__init__.py:1253
#, python-format
msgid "html_extra_path entry %r is placed inside outdir"
msgstr ""

#: sphinx/builders/html/__init__.py:1262
#, python-format
msgid "html_static_path entry %r does not exist"
msgstr "entri html_static_path %r tidak ada"

#: sphinx/builders/html/__init__.py:1266
#, python-format
msgid "html_static_path entry %r is placed inside outdir"
msgstr ""

#: sphinx/builders/html/__init__.py:1275 sphinx/builders/latex/__init__.py:444
#, python-format
msgid "logo file %r does not exist"
msgstr "berkas logo %r tidak ada"

#: sphinx/builders/html/__init__.py:1284
#, python-format
msgid "favicon file %r does not exist"
msgstr "berkas favicon %r tidak ada"

#: sphinx/builders/html/__init__.py:1291
msgid ""
"HTML 4 is no longer supported by Sphinx. (\"html4_writer=True\" detected in "
"configuration options)"
msgstr ""

#: sphinx/builders/html/__init__.py:1306
#, python-format
msgid "%s %s documentation"
msgstr "Dokumentasi %s %s"

#: sphinx/builders/latex/__init__.py:115
#, python-format
msgid "The LaTeX files are in %(outdir)s."
msgstr "Berkas LaTeX berada di %(outdir)s."

#: sphinx/builders/latex/__init__.py:117
msgid ""
"\n"
"Run 'make' in that directory to run these through (pdf)latex\n"
"(use `make latexpdf' here to do that automatically)."
msgstr "\nJalankan 'make' di direktori tersebut untuk menjalankannya melalui (pdf)latex\n(gunakan 'make latexpdf' di sini untuk melakukannya secara otomatis)."

#: sphinx/builders/latex/__init__.py:152
msgid "no \"latex_documents\" config value found; no documents will be written"
msgstr "tidak ditemukan nilai konfigurasi \"latex_documents\"; dokumen tidak akan ditulis"

#: sphinx/builders/latex/__init__.py:160
#, python-format
msgid "\"latex_documents\" config value references unknown document %s"
msgstr "nilai konfigurasi \"latex_documents\" mereferensikan dokumen yang tidak dikenal %s"

#: sphinx/builders/latex/__init__.py:196 sphinx/domains/std/__init__.py:559
#: sphinx/domains/std/__init__.py:571 sphinx/templates/latex/latex.tex_t:106
#: sphinx/themes/basic/genindex-single.html:30
#: sphinx/themes/basic/genindex-single.html:55
#: sphinx/themes/basic/genindex-split.html:11
#: sphinx/themes/basic/genindex-split.html:14
#: sphinx/themes/basic/genindex.html:11 sphinx/themes/basic/genindex.html:34
#: sphinx/themes/basic/genindex.html:67 sphinx/themes/basic/layout.html:138
#: sphinx/writers/texinfo.py:497
msgid "Index"
msgstr "Indeks"

#: sphinx/builders/latex/__init__.py:199 sphinx/templates/latex/latex.tex_t:91
msgid "Release"
msgstr "Rilis"

#: sphinx/builders/latex/__init__.py:213 sphinx/writers/latex.py:370
#, python-format
msgid "no Babel option known for language %r"
msgstr "tidak ada opsi Babel yang dikenal untuk bahasa %r"

#: sphinx/builders/latex/__init__.py:394
msgid "copying TeX support files"
msgstr "menyalin berkas pendukung TeX"

#: sphinx/builders/latex/__init__.py:410
msgid "copying TeX support files..."
msgstr "menyalin berkas pendukung TeX... "

#: sphinx/builders/latex/__init__.py:423
msgid "copying additional files"
msgstr "menyalin berkas tambahan"

#: sphinx/builders/latex/__init__.py:466
#, python-format
msgid "Unknown configure key: latex_elements[%r], ignored."
msgstr ""

#: sphinx/builders/latex/__init__.py:474
#, python-format
msgid "Unknown theme option: latex_theme_options[%r], ignored."
msgstr ""

#: sphinx/builders/latex/theming.py:87
#, python-format
msgid "%r doesn't have \"theme\" setting"
msgstr ""

#: sphinx/builders/latex/theming.py:90
#, python-format
msgid "%r doesn't have \"%s\" setting"
msgstr ""

#: sphinx/builders/latex/transforms.py:120
msgid "Failed to get a docname!"
msgstr "Gagal mendapatkan docname!"

#: sphinx/builders/latex/transforms.py:121
msgid "Failed to get a docname for source {source!r}!"
msgstr ""

#: sphinx/builders/latex/transforms.py:482
#, python-format
msgid "No footnote was found for given reference node %r"
msgstr ""

#: sphinx/cmd/build.py:46
msgid "Exception occurred while building, starting debugger:"
msgstr "Eksepsi terjadi saat membangun, memulai debugger:"

#: sphinx/cmd/build.py:61
msgid "Interrupted!"
msgstr "Diinterupsi"

#: sphinx/cmd/build.py:63
msgid "reST markup error:"
msgstr "markup reST salah:"

#: sphinx/cmd/build.py:69
msgid "Encoding error:"
msgstr "Kesalahan encoding:"

#: sphinx/cmd/build.py:72 sphinx/cmd/build.py:87
#, python-format
msgid ""
"The full traceback has been saved in %s, if you want to report the issue to "
"the developers."
msgstr "Traceback lengkap telah disimpan di %s, bila ingin melaporkan masalah ini kepada developer."

#: sphinx/cmd/build.py:76
msgid "Recursion error:"
msgstr "Kesalahan rekursi:"

#: sphinx/cmd/build.py:79
msgid ""
"This can happen with very large or deeply nested source files. You can "
"carefully increase the default Python recursion limit of 1000 in conf.py "
"with e.g.:"
msgstr ""

#: sphinx/cmd/build.py:84
msgid "Exception occurred:"
msgstr "Terjadi eksepsi:"

#: sphinx/cmd/build.py:90
msgid ""
"Please also report this if it was a user error, so that a better error "
"message can be provided next time."
msgstr "Mohon juga melaporkan hal ini jika sebuah kesalahan pengguna sehingga lain kali perintah salah yang lebih baik dapat disediakan."

#: sphinx/cmd/build.py:93
msgid ""
"A bug report can be filed in the tracker at <https://github.com/sphinx-"
"doc/sphinx/issues>. Thanks!"
msgstr "Laporan bug dapat diisi pada tracker di <https://github.com/sphinx-doc/sphinx/issues>. Terima kasih!"

#: sphinx/cmd/build.py:109
msgid "job number should be a positive number"
msgstr "job number seharusnya sebuah bilangan positif"

#: sphinx/cmd/build.py:117 sphinx/cmd/quickstart.py:474
#: sphinx/ext/apidoc.py:317 sphinx/ext/autosummary/generate.py:689
msgid "For more information, visit <https://www.sphinx-doc.org/>."
msgstr ""

#: sphinx/cmd/build.py:118
msgid ""
"\n"
"Generate documentation from source files.\n"
"\n"
"sphinx-build generates documentation from the files in SOURCEDIR and places it\n"
"in OUTPUTDIR. It looks for 'conf.py' in SOURCEDIR for the configuration\n"
"settings. The 'sphinx-quickstart' tool may be used to generate template files,\n"
"including 'conf.py'\n"
"\n"
"sphinx-build can create documentation in different formats. A format is\n"
"selected by specifying the builder name on the command line; it defaults to\n"
"HTML. Builders can also perform other tasks related to documentation\n"
"processing.\n"
"\n"
"By default, everything that is outdated is built. Output only for selected\n"
"files can be built by specifying individual filenames.\n"
msgstr ""

#: sphinx/cmd/build.py:139
msgid "path to documentation source files"
msgstr "path ke berkas sumber"

#: sphinx/cmd/build.py:141
msgid "path to output directory"
msgstr "path ke direktori output"

#: sphinx/cmd/build.py:143
msgid ""
"(optional) a list of specific files to rebuild. Ignored if --write-all is "
"specified"
msgstr ""

#: sphinx/cmd/build.py:146
msgid "general options"
msgstr "opsi umum"

#: sphinx/cmd/build.py:149
msgid "builder to use (default: 'html')"
msgstr ""

#: sphinx/cmd/build.py:152
msgid ""
"run in parallel with N processes, when possible. 'auto' uses the number of "
"CPU cores"
msgstr ""

#: sphinx/cmd/build.py:155
msgid "write all files (default: only write new and changed files)"
msgstr "tulis semua berkas (default: hanya tulis berkas yang baru dan diubah)"

#: sphinx/cmd/build.py:158
msgid "don't use a saved environment, always read all files"
msgstr "jangan pakai saved environment, selalu baca semua berkas"

#: sphinx/cmd/build.py:161
msgid "path options"
msgstr ""

#: sphinx/cmd/build.py:163
msgid ""
"directory for doctree and environment files (default: OUTPUT_DIR/.doctrees)"
msgstr ""

#: sphinx/cmd/build.py:166
msgid "directory for the configuration file (conf.py) (default: SOURCE_DIR)"
msgstr ""

#: sphinx/cmd/build.py:171
msgid "use no configuration file, only use settings from -D options"
msgstr ""

#: sphinx/cmd/build.py:174
msgid "override a setting in configuration file"
msgstr "override sebuah aturan di berkas konfigurasi"

#: sphinx/cmd/build.py:177
msgid "pass a value into HTML templates"
msgstr "masukkan sebuah nilai ke templat HTML"

#: sphinx/cmd/build.py:180
msgid "define tag: include \"only\" blocks with TAG"
msgstr "define tag: masukkan blok \"only\" dengan TAG"

#: sphinx/cmd/build.py:182
msgid "nit-picky mode: warn about all missing references"
msgstr ""

#: sphinx/cmd/build.py:184
msgid "console output options"
msgstr "opsi output konsol"

#: sphinx/cmd/build.py:187
msgid "increase verbosity (can be repeated)"
msgstr "tingkatkan verbosity (dapat diulang)"

#: sphinx/cmd/build.py:189 sphinx/ext/apidoc.py:340
msgid "no output on stdout, just warnings on stderr"
msgstr "tanpa output pada stdout, hanya peringatan pada stderr"

#: sphinx/cmd/build.py:191
msgid "no output at all, not even warnings"
msgstr "tanpa output sama sekali, peringatan sekalipun"

#: sphinx/cmd/build.py:194
msgid "do emit colored output (default: auto-detect)"
msgstr "siarkan output berwarna (default: auto-detect)"

#: sphinx/cmd/build.py:197
msgid "do not emit colored output (default: auto-detect)"
msgstr "jangan siarkan output berwarna (default: auto-detect)"

#: sphinx/cmd/build.py:199
msgid "warning control options"
msgstr ""

#: sphinx/cmd/build.py:201
msgid "write warnings (and errors) to given file"
msgstr "tulis peringatan (dan galat) pada berkas terpilih"

#: sphinx/cmd/build.py:203
msgid "turn warnings into errors"
msgstr "ubah peringatan menjadi galat"

#: sphinx/cmd/build.py:205
msgid "with --fail-on-warning, keep going when getting warnings"
msgstr ""

#: sphinx/cmd/build.py:207
msgid "show full traceback on exception"
msgstr "tampilkan traceback penuh pada eksepsi"

#: sphinx/cmd/build.py:209
msgid "run Pdb on exception"
msgstr "jalankan Pdb pada eksepsi"

#: sphinx/cmd/build.py:244
msgid "cannot combine -a option and filenames"
msgstr "tidak dapat menggabungkan opsi -a dan nama berkas"

#: sphinx/cmd/build.py:276
#, python-format
msgid "cannot open warning file %r: %s"
msgstr "tidak dapat membuka berkas peringatan %r: %s"

#: sphinx/cmd/build.py:296
msgid "-D option argument must be in the form name=value"
msgstr "argumen opsi -D harus dalam bentuk name=value"

#: sphinx/cmd/build.py:303
msgid "-A option argument must be in the form name=value"
msgstr "argumen opsi -A harus dalam bentuk name=value"

#: sphinx/cmd/quickstart.py:42
msgid "automatically insert docstrings from modules"
msgstr "masukkan docstrings secara otomatis dari modules"

#: sphinx/cmd/quickstart.py:43
msgid "automatically test code snippets in doctest blocks"
msgstr "uji snippet kode secara otomatis pada blok doctest"

#: sphinx/cmd/quickstart.py:44
msgid "link between Sphinx documentation of different projects"
msgstr "tautkan antara dokumentasi Sphinx dari berbagai proyek"

#: sphinx/cmd/quickstart.py:45
msgid "write \"todo\" entries that can be shown or hidden on build"
msgstr "tulis entri \"todo\" yang dapat ditampilan atau disembunyikan dalam build"

#: sphinx/cmd/quickstart.py:46
msgid "checks for documentation coverage"
msgstr "periksa coverage dokumentasi"

#: sphinx/cmd/quickstart.py:47
msgid "include math, rendered as PNG or SVG images"
msgstr "masukkan math, yang dirender sebagai gambar PNG atau SVG"

#: sphinx/cmd/quickstart.py:48
msgid "include math, rendered in the browser by MathJax"
msgstr "masukkan math, yang dirender di perambah sebagai gambar PNG atau SVG"

#: sphinx/cmd/quickstart.py:49
msgid "conditional inclusion of content based on config values"
msgstr "inklusi bersyarat untuk isi berdasarkan nilai konfig"

#: sphinx/cmd/quickstart.py:50
msgid "include links to the source code of documented Python objects"
msgstr "masukkan tautan ke sumber kode untuk objek Python yang terdokumentasi"

#: sphinx/cmd/quickstart.py:51
msgid "create .nojekyll file to publish the document on GitHub pages"
msgstr "buat berkas .nojekyll untuk menerbitkannya di halaman GitHub"

#: sphinx/cmd/quickstart.py:93
msgid "Please enter a valid path name."
msgstr "Mohon masukkan nama path yang sah."

#: sphinx/cmd/quickstart.py:109
msgid "Please enter some text."
msgstr "Mohon masukan teks."

#: sphinx/cmd/quickstart.py:116
#, python-format
msgid "Please enter one of %s."
msgstr "Mohon masukkan satu dari %s."

#: sphinx/cmd/quickstart.py:123
msgid "Please enter either 'y' or 'n'."
msgstr "Mohon ketik salah satu dari 'y' atau 'n'."

#: sphinx/cmd/quickstart.py:129
msgid "Please enter a file suffix, e.g. '.rst' or '.txt'."
msgstr "Mohon masukkan satu suffiks berkas, contohnya '.rst' atau '.txt'."

#: sphinx/cmd/quickstart.py:208
#, python-format
msgid "Welcome to the Sphinx %s quickstart utility."
msgstr "Selamat datang ke alat quickstart Sphinx %s."

#: sphinx/cmd/quickstart.py:210
msgid ""
"Please enter values for the following settings (just press Enter to\n"
"accept a default value, if one is given in brackets)."
msgstr "Silakan masukkan nilai untuk pengaturan berikut (cukup tekan Enter to\nmenerima nilai bawaan, jika diberikan dalam tanda kurung)."

#: sphinx/cmd/quickstart.py:215
#, python-format
msgid "Selected root path: %s"
msgstr ""

#: sphinx/cmd/quickstart.py:218
msgid "Enter the root path for documentation."
msgstr "Masukkan jalur root untuk dokumentasi."

#: sphinx/cmd/quickstart.py:219
msgid "Root path for the documentation"
msgstr "Root path dokumentasi"

#: sphinx/cmd/quickstart.py:224
msgid "Error: an existing conf.py has been found in the selected root path."
msgstr "Galat: berkas conf.py telah ditemukan dalam root path terpilih."

#: sphinx/cmd/quickstart.py:226
msgid "sphinx-quickstart will not overwrite existing Sphinx projects."
msgstr "sphinx-quickstart tidak akan menulis ulang proyek Sphinx yang ada."

#: sphinx/cmd/quickstart.py:228
msgid "Please enter a new root path (or just Enter to exit)"
msgstr "Silakan masukkan root path baru (atau tekan Enter untuk keluar)"

#: sphinx/cmd/quickstart.py:235
msgid ""
"You have two options for placing the build directory for Sphinx output.\n"
"Either, you use a directory \"_build\" within the root path, or you separate\n"
"\"source\" and \"build\" directories within the root path."
msgstr ""

#: sphinx/cmd/quickstart.py:238
msgid "Separate source and build directories (y/n)"
msgstr "Pisahkan direktori source dan build (y/n)"

#: sphinx/cmd/quickstart.py:242
msgid ""
"Inside the root directory, two more directories will be created; \"_templates\"\n"
"for custom HTML templates and \"_static\" for custom stylesheets and other static\n"
"files. You can enter another prefix (such as \".\") to replace the underscore."
msgstr ""

#: sphinx/cmd/quickstart.py:245
msgid "Name prefix for templates and static dir"
msgstr "Nama prefiks untuk dir templat dan static"

#: sphinx/cmd/quickstart.py:249
msgid ""
"The project name will occur in several places in the built documentation."
msgstr "Nama proyek akan muncul di beberapa tempat dalam dokumentasi yang dibuat."

#: sphinx/cmd/quickstart.py:250
msgid "Project name"
msgstr "Nama proyek"

#: sphinx/cmd/quickstart.py:252
msgid "Author name(s)"
msgstr "Nama(-nama) pembuat"

#: sphinx/cmd/quickstart.py:256
msgid ""
"Sphinx has the notion of a \"version\" and a \"release\" for the\n"
"software. Each version can have multiple releases. For example, for\n"
"Python the version is something like 2.5 or 3.0, while the release is\n"
"something like 2.5.1 or 3.0a1. If you don't need this dual structure,\n"
"just set both to the same value."
msgstr ""

#: sphinx/cmd/quickstart.py:261
msgid "Project version"
msgstr "Versi proyek"

#: sphinx/cmd/quickstart.py:263
msgid "Project release"
msgstr "Rilis proyek"

#: sphinx/cmd/quickstart.py:267
msgid ""
"If the documents are to be written in a language other than English,\n"
"you can select a language here by its language code. Sphinx will then\n"
"translate text that it generates into that language.\n"
"\n"
"For a list of supported codes, see\n"
"https://www.sphinx-doc.org/en/master/usage/configuration.html#confval-language."
msgstr ""

#: sphinx/cmd/quickstart.py:275
msgid "Project language"
msgstr "Bahasa proyek"

#: sphinx/cmd/quickstart.py:281
msgid ""
"The file name suffix for source files. Commonly, this is either \".txt\"\n"
"or \".rst\". Only files with this suffix are considered documents."
msgstr ""

#: sphinx/cmd/quickstart.py:283
msgid "Source file suffix"
msgstr "Akhiran berkas sumber"

#: sphinx/cmd/quickstart.py:287
msgid ""
"One document is special in that it is considered the top node of the\n"
"\"contents tree\", that is, it is the root of the hierarchical structure\n"
"of the documents. Normally, this is \"index\", but if your \"index\"\n"
"document is a custom template, you can also set this to another filename."
msgstr ""

#: sphinx/cmd/quickstart.py:291
msgid "Name of your master document (without suffix)"
msgstr "Nama dokumen master Anda (tanpa akhiran)"

#: sphinx/cmd/quickstart.py:296
#, python-format
msgid ""
"Error: the master file %s has already been found in the selected root path."
msgstr "Kesalahan: file master %s telah ditemukan di jalur utama yang dipilih."

#: sphinx/cmd/quickstart.py:298
msgid "sphinx-quickstart will not overwrite the existing file."
msgstr "sphinx-quickstart tidak akan menimpa berkas yang sudah ada."

#: sphinx/cmd/quickstart.py:300
msgid ""
"Please enter a new file name, or rename the existing file and press Enter"
msgstr "Silakan masukkan nama file baru, atau ganti nama file yang ada dan tekan Enter"

#: sphinx/cmd/quickstart.py:304
msgid "Indicate which of the following Sphinx extensions should be enabled:"
msgstr "Tunjukkan ekstensi Sphinx berikut mana yang harus diaktifkan:"

#: sphinx/cmd/quickstart.py:312
msgid ""
"Note: imgmath and mathjax cannot be enabled at the same time. imgmath has "
"been deselected."
msgstr "Catatan: imgmath dan mathjax tidak dapat diaktifkan secara bersamaan. imgmath telah diubah tidak pilih."

#: sphinx/cmd/quickstart.py:318
msgid ""
"A Makefile and a Windows command file can be generated for you so that you\n"
"only have to run e.g. `make html' instead of invoking sphinx-build\n"
"directly."
msgstr ""

#: sphinx/cmd/quickstart.py:321
msgid "Create Makefile? (y/n)"
msgstr "Buat Makefile? (y/n)"

#: sphinx/cmd/quickstart.py:324
msgid "Create Windows command file? (y/n)"
msgstr "Buat berkas perintah Windows? (y/n)"

#: sphinx/cmd/quickstart.py:368 sphinx/ext/apidoc.py:93
#, python-format
msgid "Creating file %s."
msgstr "Membuat file %s."

#: sphinx/cmd/quickstart.py:373 sphinx/ext/apidoc.py:90
#, python-format
msgid "File %s already exists, skipping."
msgstr "File %s sudah ada, lewati."

#: sphinx/cmd/quickstart.py:418
msgid "Finished: An initial directory structure has been created."
msgstr "Selesai: Struktur direktori awal telah dibuat."

#: sphinx/cmd/quickstart.py:420
#, python-format
msgid ""
"You should now populate your master file %s and create other documentation\n"
"source files. "
msgstr ""

#: sphinx/cmd/quickstart.py:423
msgid ""
"Use the Makefile to build the docs, like so:\n"
"   make builder"
msgstr ""

#: sphinx/cmd/quickstart.py:426
#, python-format
msgid ""
"Use the sphinx-build command to build the docs, like so:\n"
"   sphinx-build -b builder %s %s"
msgstr ""

#: sphinx/cmd/quickstart.py:428
msgid ""
"where \"builder\" is one of the supported builders, e.g. html, latex or "
"linkcheck."
msgstr "di mana \"pembangun\" adalah salah satu pembangun yang didukung, mis. html, lateks, atau periksa tautan."

#: sphinx/cmd/quickstart.py:464
msgid ""
"\n"
"Generate required files for a Sphinx project.\n"
"\n"
"sphinx-quickstart is an interactive tool that asks some questions about your\n"
"project and then generates a complete documentation directory and sample\n"
"Makefile to be used with sphinx-build.\n"
msgstr "\nHasilkan file yang diperlukan untuk proyek Sphinx. \n\nsphinx-quickstart adalah alat interaktif yang menanyakan beberapa pertanyaan tentang proyek Anda \ndan kemudian menghasilkan direktori dokumentasi lengkap dan contoh \nMakefile untuk digunakan dengan sphinx-build.\n"

#: sphinx/cmd/quickstart.py:479
msgid "quiet mode"
msgstr "mode diam"

#: sphinx/cmd/quickstart.py:484
msgid "project root"
msgstr "root proyek"

#: sphinx/cmd/quickstart.py:486
msgid "Structure options"
msgstr "Opsi struktur"

#: sphinx/cmd/quickstart.py:488
msgid "if specified, separate source and build dirs"
msgstr "jika ditentukan, pisahkan direktori sumber dan pembangunan"

#: sphinx/cmd/quickstart.py:490
msgid "if specified, create build dir under source dir"
msgstr ""

#: sphinx/cmd/quickstart.py:492
msgid "replacement for dot in _templates etc."
msgstr "pengganti dot di _templates dll."

#: sphinx/cmd/quickstart.py:494
msgid "Project basic options"
msgstr "Opsi dasar proyek"

#: sphinx/cmd/quickstart.py:496
msgid "project name"
msgstr "nama proyek"

#: sphinx/cmd/quickstart.py:498
msgid "author names"
msgstr "nama penulis"

#: sphinx/cmd/quickstart.py:500
msgid "version of project"
msgstr "versi proyek"

#: sphinx/cmd/quickstart.py:502
msgid "release of project"
msgstr "rilis proyek"

#: sphinx/cmd/quickstart.py:504
msgid "document language"
msgstr "bahasa dokumen"

#: sphinx/cmd/quickstart.py:506
msgid "source file suffix"
msgstr "akhiran berkas sumber"

#: sphinx/cmd/quickstart.py:508
msgid "master document name"
msgstr "nama dokumen utama"

#: sphinx/cmd/quickstart.py:510
msgid "use epub"
msgstr "gunakan epub"

#: sphinx/cmd/quickstart.py:512
msgid "Extension options"
msgstr "Opsi ekstensi"

#: sphinx/cmd/quickstart.py:516 sphinx/ext/apidoc.py:400
#, python-format
msgid "enable %s extension"
msgstr "aktifkan ekstensi %s"

#: sphinx/cmd/quickstart.py:518 sphinx/ext/apidoc.py:396
msgid "enable arbitrary extensions"
msgstr "aktifkan ekstensi berubah-ubah"

#: sphinx/cmd/quickstart.py:520
msgid "Makefile and Batchfile creation"
msgstr "Pembuatan Makefile dan Batchfile"

#: sphinx/cmd/quickstart.py:522
msgid "create makefile"
msgstr "buat makefile"

#: sphinx/cmd/quickstart.py:524
msgid "do not create makefile"
msgstr "jangan membuat makefile"

#: sphinx/cmd/quickstart.py:526
msgid "create batchfile"
msgstr "buat batchfile"

#: sphinx/cmd/quickstart.py:529
msgid "do not create batchfile"
msgstr "jangan membuat batchfile"

#: sphinx/cmd/quickstart.py:532
msgid "use make-mode for Makefile/make.bat"
msgstr "gunakan mode-make untuk Makefile/make.bat"

#: sphinx/cmd/quickstart.py:535
msgid "do not use make-mode for Makefile/make.bat"
msgstr "jangan gunakan make-mode untuk Makefile/make.bat"

#: sphinx/cmd/quickstart.py:537 sphinx/ext/apidoc.py:402
msgid "Project templating"
msgstr "Membuat templat proyek"

#: sphinx/cmd/quickstart.py:540 sphinx/ext/apidoc.py:405
msgid "template directory for template files"
msgstr "direktori templat untuk berkas templat"

#: sphinx/cmd/quickstart.py:543
msgid "define a template variable"
msgstr "mendefinisikan variabel templat"

#: sphinx/cmd/quickstart.py:576
msgid "\"quiet\" is specified, but any of \"project\" or \"author\" is not specified."
msgstr "\"quiet\" ditentukan, tetapi tidak ada \"project\" atau \"author\" yang ditentukan."

#: sphinx/cmd/quickstart.py:590
msgid ""
"Error: specified path is not a directory, or sphinx files already exist."
msgstr "Kesalahan: jalur yang ditentukan bukan direktori, atau file sphinx sudah ada."

#: sphinx/cmd/quickstart.py:592
msgid ""
"sphinx-quickstart only generate into a empty directory. Please specify a new"
" root path."
msgstr "sphinx-quickstart hanya menghasilkan direktori kosong. Silakan tentukan jalur utama baru."

#: sphinx/cmd/quickstart.py:607
#, python-format
msgid "Invalid template variable: %s"
msgstr "Variabel templat tidak valid: %s"

#: sphinx/directives/code.py:61
msgid "non-whitespace stripped by dedent"
msgstr ""

#: sphinx/directives/code.py:82
#, python-format
msgid "Invalid caption: %s"
msgstr "Keterangan tidak valid: %s"

#: sphinx/directives/code.py:127 sphinx/directives/code.py:277
#: sphinx/directives/code.py:453
#, python-format
msgid "line number spec is out of range(1-%d): %r"
msgstr "spesifikasi nomor baris di luar kisaran (1-%d): %r"

#: sphinx/directives/code.py:206
#, python-format
msgid "Cannot use both \"%s\" and \"%s\" options"
msgstr "Tidak dapat menggunakan kedua opsi \"%s\" dan \"%s\""

#: sphinx/directives/code.py:220
#, python-format
msgid "Include file %r not found or reading it failed"
msgstr "Berkas yang disertakan %r tidak ditemukan atau gagal membacanya"

#: sphinx/directives/code.py:223
#, python-format
msgid ""
"Encoding %r used for reading included file %r seems to be wrong, try giving "
"an :encoding: option"
msgstr "Pengkodean %r yang digunakan untuk membaca file yang disertakan %r tampaknya salah, mencoba berikan opsi :encoding:"

#: sphinx/directives/code.py:260
#, python-format
msgid "Object named %r not found in include file %r"
msgstr "Objek bernama %r tidak ditemukan disertakan di berkas %r"

#: sphinx/directives/code.py:286
msgid "Cannot use \"lineno-match\" with a disjoint set of \"lines\""
msgstr "Tidak dapat menggunakan \"lineno-match\" dengan rangkaian \"baris\" yang terpisah"

#: sphinx/directives/code.py:291
#, python-format
msgid "Line spec %r: no lines pulled from include file %r"
msgstr "Spesifikasi baris %r: tidak ada baris yang ditarik dari berkas %r"

#: sphinx/directives/other.py:120
#, python-format
msgid "toctree glob pattern %r didn't match any documents"
msgstr ""

#: sphinx/directives/other.py:146 sphinx/environment/adapters/toctree.py:324
#, python-format
msgid "toctree contains reference to excluded document %r"
msgstr "toctree berisi referensi ke dokumen yang dikecualikan %r"

#: sphinx/directives/other.py:149 sphinx/environment/adapters/toctree.py:328
#, python-format
msgid "toctree contains reference to nonexisting document %r"
msgstr "toctree berisi referensi ke dokumen yang tidak ada %r"

#: sphinx/directives/other.py:160
#, python-format
msgid "duplicated entry found in toctree: %s"
msgstr ""

#: sphinx/directives/other.py:193
msgid "Section author: "
msgstr "Penyusun bagian:"

#: sphinx/directives/other.py:195
msgid "Module author: "
msgstr "Penyusun modul: "

#: sphinx/directives/other.py:197
msgid "Code author: "
msgstr "Penulis kode:"

#: sphinx/directives/other.py:199
msgid "Author: "
msgstr "Penyusun: "

#: sphinx/directives/other.py:275
msgid ".. acks content is not a list"
msgstr ""

#: sphinx/directives/other.py:301
msgid ".. hlist content is not a list"
msgstr ""

#: sphinx/directives/patches.py:66
msgid ""
"\":file:\" option for csv-table directive now recognizes an absolute path as"
" a relative path from source directory. Please update your document."
msgstr ""

#: sphinx/domains/__init__.py:397
#, python-format
msgid "%s %s"
msgstr "%s %s"

#: sphinx/domains/changeset.py:23
#, python-format
msgid "Added in version %s"
msgstr ""

#: sphinx/domains/changeset.py:24
#, python-format
msgid "Changed in version %s"
msgstr "Berubah pada versi %s"

#: sphinx/domains/changeset.py:25
#, python-format
msgid "Deprecated since version %s"
msgstr "Ditinggalkan sejak versi %s"

#: sphinx/domains/changeset.py:26
#, python-format
msgid "Removed in version %s"
msgstr ""

#: sphinx/domains/citation.py:71
#, python-format
msgid "duplicate citation %s, other instance in %s"
msgstr "kutipan rangkap %s, contoh lain dalam %s"

#: sphinx/domains/citation.py:82
#, python-format
msgid "Citation [%s] is not referenced."
msgstr "Kutipan [%s] tidak dirujuk."

#: sphinx/domains/javascript.py:165
#, python-format
msgid "%s() (built-in function)"
msgstr "%s() (fungsi built-in)"

#: sphinx/domains/javascript.py:166 sphinx/domains/python/__init__.py:240
#, python-format
msgid "%s() (%s method)"
msgstr "%s() (method %s)"

#: sphinx/domains/javascript.py:168
#, python-format
msgid "%s() (class)"
msgstr "%s() (class)"

#: sphinx/domains/javascript.py:170
#, python-format
msgid "%s (global variable or constant)"
msgstr "%s (variabel global atau konstan)"

#: sphinx/domains/javascript.py:172 sphinx/domains/python/__init__.py:325
#, python-format
msgid "%s (%s attribute)"
msgstr "%s (atribut %s)"

#: sphinx/domains/javascript.py:255
msgid "Arguments"
msgstr "Argumen"

#: sphinx/domains/cpp/__init__.py:350 sphinx/domains/javascript.py:258
msgid "Throws"
msgstr "Throws"

#: sphinx/domains/c/__init__.py:251 sphinx/domains/cpp/__init__.py:361
#: sphinx/domains/javascript.py:261 sphinx/domains/python/_object.py:175
msgid "Returns"
msgstr "Kembali"

#: sphinx/domains/c/__init__.py:253 sphinx/domains/javascript.py:263
#: sphinx/domains/python/_object.py:177
msgid "Return type"
msgstr "Return type"

#: sphinx/domains/javascript.py:331
#, python-format
msgid "%s (module)"
msgstr "%s (module)"

#: sphinx/domains/c/__init__.py:622 sphinx/domains/cpp/__init__.py:764
#: sphinx/domains/javascript.py:368 sphinx/domains/python/__init__.py:574
msgid "function"
msgstr "fungsi"

#: sphinx/domains/javascript.py:369 sphinx/domains/python/__init__.py:578
msgid "method"
msgstr "method"

#: sphinx/domains/cpp/__init__.py:762 sphinx/domains/javascript.py:370
#: sphinx/domains/python/__init__.py:576
msgid "class"
msgstr "class"

#: sphinx/domains/javascript.py:371 sphinx/domains/python/__init__.py:575
msgid "data"
msgstr "data"

#: sphinx/domains/javascript.py:372 sphinx/domains/python/__init__.py:581
msgid "attribute"
msgstr "atribut"

#: sphinx/domains/javascript.py:373 sphinx/domains/python/__init__.py:583
msgid "module"
msgstr "modul"

#: sphinx/domains/javascript.py:404
#, python-format
msgid "duplicate %s description of %s, other %s in %s"
msgstr ""

#: sphinx/domains/math.py:63
#, python-format
msgid "duplicate label of equation %s, other instance in %s"
msgstr "duplikasi label persamaan %s, misalnya di %s"

#: sphinx/domains/math.py:118 sphinx/writers/latex.py:2252
#, python-format
msgid "Invalid math_eqref_format: %r"
msgstr "Math_eqref_format tidak valid: %r"

#: sphinx/domains/rst.py:127 sphinx/domains/rst.py:184
#, python-format
msgid "%s (directive)"
msgstr "%s (direktif)"

#: sphinx/domains/rst.py:185 sphinx/domains/rst.py:189
#, python-format
msgid ":%s: (directive option)"
msgstr ""

#: sphinx/domains/rst.py:213
#, python-format
msgid "%s (role)"
msgstr "%s (role)"

#: sphinx/domains/rst.py:223
msgid "directive"
msgstr "direktif"

#: sphinx/domains/rst.py:224
msgid "directive-option"
msgstr "opsi-direktif"

#: sphinx/domains/rst.py:225
msgid "role"
msgstr "role"

#: sphinx/domains/rst.py:247
#, python-format
msgid "duplicate description of %s %s, other instance in %s"
msgstr ""

#: sphinx/domains/c/__init__.py:146
#, python-format
msgid "%s (C %s)"
msgstr ""

#: sphinx/domains/c/__init__.py:207 sphinx/domains/c/_symbol.py:552
#, python-format
msgid ""
"Duplicate C declaration, also defined at %s:%s.\n"
"Declaration is '.. c:%s:: %s'."
msgstr ""

#: sphinx/domains/c/__init__.py:245 sphinx/domains/cpp/__init__.py:344
#: sphinx/domains/python/_object.py:163 sphinx/ext/napoleon/docstring.py:762
msgid "Parameters"
msgstr "Parameter"

#: sphinx/domains/c/__init__.py:248 sphinx/domains/cpp/__init__.py:357
msgid "Return values"
msgstr "Nilai kembalian"

#: sphinx/domains/c/__init__.py:620 sphinx/domains/cpp/__init__.py:765
msgid "member"
msgstr "anggota"

#: sphinx/domains/c/__init__.py:621
msgid "variable"
msgstr "variabel"

#: sphinx/domains/c/__init__.py:623
msgid "macro"
msgstr "macro"

#: sphinx/domains/c/__init__.py:624
msgid "struct"
msgstr "struct"

#: sphinx/domains/c/__init__.py:625 sphinx/domains/cpp/__init__.py:763
msgid "union"
msgstr "union"

#: sphinx/domains/c/__init__.py:626 sphinx/domains/cpp/__init__.py:768
msgid "enum"
msgstr "enum"

#: sphinx/domains/c/__init__.py:627 sphinx/domains/cpp/__init__.py:769
msgid "enumerator"
msgstr "enumerator"

#: sphinx/domains/c/__init__.py:628 sphinx/domains/cpp/__init__.py:766
msgid "type"
msgstr "tipe"

#: sphinx/domains/c/__init__.py:630 sphinx/domains/cpp/__init__.py:771
msgid "function parameter"
msgstr ""

#: sphinx/domains/cpp/__init__.py:63
msgid "Template Parameters"
msgstr "Parameter Templat"

#: sphinx/domains/cpp/__init__.py:185
#, python-format
msgid "%s (C++ %s)"
msgstr "%s (C++ %s)"

#: sphinx/domains/cpp/__init__.py:268 sphinx/domains/cpp/_symbol.py:790
#, python-format
msgid ""
"Duplicate C++ declaration, also defined at %s:%s.\n"
"Declaration is '.. cpp:%s:: %s'."
msgstr ""

#: sphinx/domains/cpp/__init__.py:767
msgid "concept"
msgstr "konsep"

#: sphinx/domains/cpp/__init__.py:772
msgid "template parameter"
msgstr ""

#: sphinx/domains/python/__init__.py:94 sphinx/domains/python/__init__.py:231
#, python-format
msgid "%s() (in module %s)"
msgstr "%s() (di modul %s)"

#: sphinx/domains/python/__init__.py:154 sphinx/domains/python/__init__.py:321
#: sphinx/domains/python/__init__.py:372
#, python-format
msgid "%s (in module %s)"
msgstr "%s (di modul %s)"

#: sphinx/domains/python/__init__.py:156
#, python-format
msgid "%s (built-in variable)"
msgstr "%s (variabel built-in)"

#: sphinx/domains/python/__init__.py:181
#, python-format
msgid "%s (built-in class)"
msgstr "%s (class built-in)"

#: sphinx/domains/python/__init__.py:182
#, python-format
msgid "%s (class in %s)"
msgstr "%s (class di %s)"

#: sphinx/domains/python/__init__.py:236
#, python-format
msgid "%s() (%s class method)"
msgstr "%s() (method class %s)"

#: sphinx/domains/python/__init__.py:238
#, python-format
msgid "%s() (%s static method)"
msgstr "%s() (method static %s)"

#: sphinx/domains/python/__init__.py:376
#, python-format
msgid "%s (%s property)"
msgstr ""

#: sphinx/domains/python/__init__.py:502
msgid "Python Module Index"
msgstr "Indeks Modul Python"

#: sphinx/domains/python/__init__.py:503
msgid "modules"
msgstr "modul"

#: sphinx/domains/python/__init__.py:552
msgid "Deprecated"
msgstr "Akan ditinggalkan"

#: sphinx/domains/python/__init__.py:577
msgid "exception"
msgstr "eksepsi"

#: sphinx/domains/python/__init__.py:579
msgid "class method"
msgstr "method class"

#: sphinx/domains/python/__init__.py:580
msgid "static method"
msgstr "method static"

#: sphinx/domains/python/__init__.py:582
msgid "property"
msgstr "property"

#: sphinx/domains/python/__init__.py:640
#, python-format
msgid ""
"duplicate object description of %s, other instance in %s, use :no-index: for"
" one of them"
msgstr ""

#: sphinx/domains/python/__init__.py:760
#, python-format
msgid "more than one target found for cross-reference %r: %s"
msgstr "lebih dari satu target ditemukan untuk referensi silang %r: %s"

#: sphinx/domains/python/__init__.py:821
msgid " (deprecated)"
msgstr " (obsolet)"

#: sphinx/domains/python/_object.py:168
msgid "Variables"
msgstr "Variabel"

#: sphinx/domains/python/_object.py:172
msgid "Raises"
msgstr "Raises"

#: sphinx/domains/std/__init__.py:80 sphinx/domains/std/__init__.py:97
#, python-format
msgid "environment variable; %s"
msgstr "variabel environment; %s"

#: sphinx/domains/std/__init__.py:157
#, python-format
msgid ""
"Malformed option description %r, should look like \"opt\", \"-opt args\", \""
"--opt args\", \"/opt args\" or \"+opt args\""
msgstr "Deskripsi opsi salah bentuk %r, seharusnya terlihat seperti \"opt\", \"-opt args\", \"--opt args\", \"/opt args\" atau \"+opt args\""

#: sphinx/domains/std/__init__.py:228
#, python-format
msgid "%s command line option"
msgstr ""

#: sphinx/domains/std/__init__.py:230
msgid "command line option"
msgstr "opsi baris perintah"

#: sphinx/domains/std/__init__.py:348
msgid "glossary term must be preceded by empty line"
msgstr "Daftar Istilah kata sulit harus didahului dengan baris kosong"

#: sphinx/domains/std/__init__.py:356
msgid "glossary terms must not be separated by empty lines"
msgstr "Daftar istilah kata sulit tidak boleh dipisahkan oleh garis kosong"

#: sphinx/domains/std/__init__.py:362 sphinx/domains/std/__init__.py:375
msgid "glossary seems to be misformatted, check indentation"
msgstr "Daftar istilah kata sulit tampaknya salah format, periksa indentasi"

#: sphinx/domains/std/__init__.py:518
msgid "glossary term"
msgstr "daftar istilah"

#: sphinx/domains/std/__init__.py:519
msgid "grammar token"
msgstr "token grammar"

#: sphinx/domains/std/__init__.py:520
msgid "reference label"
msgstr "label referensi"

#: sphinx/domains/std/__init__.py:522
msgid "environment variable"
msgstr "variabel environment"

#: sphinx/domains/std/__init__.py:523
msgid "program option"
msgstr "opsi program"

#: sphinx/domains/std/__init__.py:524
msgid "document"
msgstr "dokumen"

#: sphinx/domains/std/__init__.py:560 sphinx/domains/std/__init__.py:572
msgid "Module Index"
msgstr "Indeks Modul"

#: sphinx/domains/std/__init__.py:561 sphinx/domains/std/__init__.py:573
#: sphinx/themes/basic/defindex.html:25
msgid "Search Page"
msgstr "Pencarian Halaman"

#: sphinx/domains/std/__init__.py:616 sphinx/domains/std/__init__.py:722
#: sphinx/ext/autosectionlabel.py:53
#, python-format
msgid "duplicate label %s, other instance in %s"
msgstr "label rangkap %s, contoh lain dalam %s"

#: sphinx/domains/std/__init__.py:635
#, python-format
msgid "duplicate %s description of %s, other instance in %s"
msgstr ""

#: sphinx/domains/std/__init__.py:841
msgid "numfig is disabled. :numref: is ignored."
msgstr "numfig dinonaktifkan. :numref: diabaikan."

#: sphinx/domains/std/__init__.py:849
#, python-format
msgid "Failed to create a cross reference. Any number is not assigned: %s"
msgstr ""

#: sphinx/domains/std/__init__.py:861
#, python-format
msgid "the link has no caption: %s"
msgstr "tautan tidak memiliki teks: %s"

#: sphinx/domains/std/__init__.py:875
#, python-format
msgid "invalid numfig_format: %s (%r)"
msgstr "numfig_format tidak valid: %s (%r)"

#: sphinx/domains/std/__init__.py:878
#, python-format
msgid "invalid numfig_format: %s"
msgstr "numfig_format tidak valid: %s"

#: sphinx/domains/std/__init__.py:1109
#, python-format
msgid "undefined label: %r"
msgstr "label yang tidak ditentukan: %r"

#: sphinx/domains/std/__init__.py:1111
#, python-format
msgid "Failed to create a cross reference. A title or caption not found: %r"
msgstr ""

#: sphinx/environment/__init__.py:71
msgid "new config"
msgstr "konfigurasi baru"

#: sphinx/environment/__init__.py:72
msgid "config changed"
msgstr "konfigurasi berubah"

#: sphinx/environment/__init__.py:73
msgid "extensions changed"
msgstr "ekstensi berubah"

#: sphinx/environment/__init__.py:279
msgid "build environment version not current"
msgstr "membangun lingkungan bukan versi saat ini"

#: sphinx/environment/__init__.py:281
msgid "source directory has changed"
msgstr "direktori sumber telah berubah"

#: sphinx/environment/__init__.py:360
msgid ""
"This environment is incompatible with the selected builder, please choose "
"another doctree directory."
msgstr "Lingkungan ini tidak kompatibel dengan pembangun yang dipilih, silakan pilih direktori doctree lain."

#: sphinx/environment/__init__.py:459
#, python-format
msgid "Failed to scan documents in %s: %r"
msgstr "Gagal memindai dokumen dalam %s: %r"

#: sphinx/environment/__init__.py:596
#, python-format
msgid "Domain %r is not registered"
msgstr "Domain %r tidak terdaftar"

#: sphinx/environment/__init__.py:730
msgid "document isn't included in any toctree"
msgstr "dokumen tidak termasuk dalam toctree"

#: sphinx/environment/__init__.py:766
msgid "self referenced toctree found. Ignored."
msgstr "totree referensikan sendiri ditemukan. Diabaikan"

#: sphinx/environment/adapters/indexentries.py:69
#, python-format
msgid "see %s"
msgstr "lihat %s"

#: sphinx/environment/adapters/indexentries.py:73
#, python-format
msgid "see also %s"
msgstr "lihat juga %s"

#: sphinx/environment/adapters/indexentries.py:76
#, python-format
msgid "unknown index entry type %r"
msgstr "tipe entri indeks tidak dikenal %r"

#: sphinx/environment/adapters/indexentries.py:187
#: sphinx/templates/latex/sphinxmessages.sty_t:11
msgid "Symbols"
msgstr "Simbol"

#: sphinx/environment/adapters/toctree.py:297
#, python-format
msgid "circular toctree references detected, ignoring: %s <- %s"
msgstr "referensi toctree melingkar terdeteksi, mengabaikan: %s <- %s"

#: sphinx/environment/adapters/toctree.py:317
#, python-format
msgid ""
"toctree contains reference to document %r that doesn't have a title: no link"
" will be generated"
msgstr "toctree berisi referensi ke dokumen %r yang tidak memiliki judul: tidak ada tautan yang akan dihasilkan"

#: sphinx/environment/adapters/toctree.py:326
#, python-format
msgid "toctree contains reference to non-included document %r"
msgstr ""

#: sphinx/environment/collectors/asset.py:89
#, python-format
msgid "image file not readable: %s"
msgstr "berkas gambar tidak dapat dibaca: %s"

#: sphinx/environment/collectors/asset.py:108
#, python-format
msgid "image file %s not readable: %s"
msgstr "berkas gambar %s tidak dapat dibaca: %s"

#: sphinx/environment/collectors/asset.py:134
#, python-format
msgid "download file not readable: %s"
msgstr "berkas unduhan tidak dapat dibaca: %s"

#: sphinx/environment/collectors/toctree.py:225
#, python-format
msgid "%s is already assigned section numbers (nested numbered toctree?)"
msgstr "%s sudah diberi nomor bagian (penomoran bersarang toctree?)"

#: sphinx/ext/apidoc.py:86
#, python-format
msgid "Would create file %s."
msgstr "Akan membuat berkas %s."

#: sphinx/ext/apidoc.py:318
msgid ""
"\n"
"Look recursively in <MODULE_PATH> for Python modules and packages and create\n"
"one reST file with automodule directives per package in the <OUTPUT_PATH>.\n"
"\n"
"The <EXCLUDE_PATTERN>s can be file and/or directory patterns that will be\n"
"excluded from generation.\n"
"\n"
"Note: By default this script will not overwrite already created files."
msgstr "\nLihat secara rekursif dalam <MODULE_PATH> untuk modul dan paket Python dan buat \nsatu berkas reST dengan arahan automodule per paket di <OUTPUT_PATH>. \n\n<EXCLUDE_PATTERN> dapat berupa pola berkas dan/atau direktori yang akan \ndikecualikan dari pembuatan. \n\nCatatan: Secara bawaan skrip ini tidak akan menimpa berkas yang sudah dibuat."

#: sphinx/ext/apidoc.py:331
msgid "path to module to document"
msgstr "jalur ke modul ke dokumen"

#: sphinx/ext/apidoc.py:333
msgid ""
"fnmatch-style file and/or directory patterns to exclude from generation"
msgstr "Berkas gaya-fnmatch dan/atau pola direktori untuk dikecualikan dari pembuatan"

#: sphinx/ext/apidoc.py:338
msgid "directory to place all output"
msgstr "direktori untuk menempatkan semua keluaran"

#: sphinx/ext/apidoc.py:343
msgid "maximum depth of submodules to show in the TOC (default: 4)"
msgstr "kedalaman maksimum submodul untuk ditampilkan di TOC (bawaan: 4)"

#: sphinx/ext/apidoc.py:346
msgid "overwrite existing files"
msgstr "menimpa file yang ada"

#: sphinx/ext/apidoc.py:349
msgid ""
"follow symbolic links. Powerful when combined with "
"collective.recipe.omelette."
msgstr "ikuti tautan simbolik. Berdaya bila digabungkan dengan collective.recipe.omelette."

#: sphinx/ext/apidoc.py:352
msgid "run the script without creating files"
msgstr "operasikan skrip tanpa membuat file"

#: sphinx/ext/apidoc.py:355
msgid "put documentation for each module on its own page"
msgstr "letakkan dokumentasi untuk setiap modul di halamannya sendiri"

#: sphinx/ext/apidoc.py:358
msgid "include \"_private\" modules"
msgstr "termasuk modul \"_private\""

#: sphinx/ext/apidoc.py:360
msgid "filename of table of contents (default: modules)"
msgstr "nama file daftar isi (bawaan: modul)"

#: sphinx/ext/apidoc.py:362
msgid "don't create a table of contents file"
msgstr "jangan membuat berkas daftar isi"

#: sphinx/ext/apidoc.py:365
msgid ""
"don't create headings for the module/package packages (e.g. when the "
"docstrings already contain them)"
msgstr "jangan membuat judul untuk paket modul/paket (mis. ketika docstrings sudah berisi hal tersebut)"

#: sphinx/ext/apidoc.py:370
msgid "put module documentation before submodule documentation"
msgstr "letakkan dokumentasi modul sebelum dokumentasi submodul"

#: sphinx/ext/apidoc.py:374
msgid ""
"interpret module paths according to PEP-0420 implicit namespaces "
"specification"
msgstr "menafsirkan jalur modul sesuai dengan spesifikasi namespaces implisit PEP-0420"

#: sphinx/ext/apidoc.py:378
msgid "file suffix (default: rst)"
msgstr "akhiran berkas (bawaan: rst)"

#: sphinx/ext/apidoc.py:380
msgid "generate a full project with sphinx-quickstart"
msgstr "menghasilkan proyek penuh dengan sphinx-quickstart"

#: sphinx/ext/apidoc.py:383
msgid "append module_path to sys.path, used when --full is given"
msgstr "menambahkan module_path ke sys.path, digunakan ketika --full diberikan"

#: sphinx/ext/apidoc.py:385
msgid "project name (default: root module name)"
msgstr "nama proyek (bawaan: nama modul utama)"

#: sphinx/ext/apidoc.py:387
msgid "project author(s), used when --full is given"
msgstr "penulis-(penulis) proyek, digunakan ketika --full diberikan"

#: sphinx/ext/apidoc.py:389
msgid "project version, used when --full is given"
msgstr "versi proyek, digunakan ketika --full diberikan"

#: sphinx/ext/apidoc.py:391
msgid "project release, used when --full is given, defaults to --doc-version"
msgstr "rilis proyek, digunakan ketika --full diberikan, bawaan ke --doc-version"

#: sphinx/ext/apidoc.py:394
msgid "extension options"
msgstr "opsi ekstensi"

#: sphinx/ext/apidoc.py:427
#, python-format
msgid "%s is not a directory."
msgstr "%s bukan direktori."

#: sphinx/ext/autosectionlabel.py:49
#, python-format
msgid "section \"%s\" gets labeled as \"%s\""
msgstr ""

#: sphinx/ext/coverage.py:46
#, python-format
msgid "invalid regex %r in %s"
msgstr "regex tidak valid %r dalam %s"

#: sphinx/ext/coverage.py:75
#, python-format
msgid ""
"Testing of coverage in the sources finished, look at the results in "
"%(outdir)spython.txt."
msgstr "Pengujian cakupan di sumber selesai, lihat hasilnya dalam %(outdir)spython.txt."

#: sphinx/ext/coverage.py:89
#, python-format
msgid "invalid regex %r in coverage_c_regexes"
msgstr "%r regex tidak valid di coverage_c_regexes"

#: sphinx/ext/coverage.py:157
#, python-format
msgid "undocumented c api: %s [%s] in file %s"
msgstr ""

#: sphinx/ext/coverage.py:189
#, python-format
msgid "module %s could not be imported: %s"
msgstr "modul %s tidak dapat diimpor: %s"

#: sphinx/ext/coverage.py:340
#, python-format
msgid "undocumented python function: %s :: %s"
msgstr ""

#: sphinx/ext/coverage.py:356
#, python-format
msgid "undocumented python class: %s :: %s"
msgstr ""

#: sphinx/ext/coverage.py:369
#, python-format
msgid "undocumented python method: %s :: %s :: %s"
msgstr ""

#: sphinx/ext/doctest.py:115
#, python-format
msgid "missing '+' or '-' in '%s' option."
msgstr "tidak ada '+' atau '-' dalam opsi '%s'."

#: sphinx/ext/doctest.py:120
#, python-format
msgid "'%s' is not a valid option."
msgstr "'%s' bukan opsi yang valid."

#: sphinx/ext/doctest.py:134
#, python-format
msgid "'%s' is not a valid pyversion option"
msgstr "'%s' bukan opsi pyversion yang valid"

#: sphinx/ext/doctest.py:220
msgid "invalid TestCode type"
msgstr "tipe TestCode tidak valid"

#: sphinx/ext/doctest.py:281
#, python-format
msgid ""
"Testing of doctests in the sources finished, look at the results in "
"%(outdir)s/output.txt."
msgstr "Pengujian dokumen di sumber selesai, lihat hasil dalam %(outdir)s/output.txt."

#: sphinx/ext/doctest.py:438
#, python-format
msgid "no code/output in %s block at %s:%s"
msgstr "tidak ada kode/keluaran dalam blok %s pada %s:%s"

#: sphinx/ext/doctest.py:526
#, python-format
msgid "ignoring invalid doctest code: %r"
msgstr "mengabaikan kode dokumen yang tidak valid: %r"

#: sphinx/ext/duration.py:77
msgid ""
"====================== slowest reading durations ======================="
msgstr "====================== durasi membaca paling lambat ======================="

#: sphinx/ext/extlinks.py:82
#, python-format
msgid ""
"hardcoded link %r could be replaced by an extlink (try using %r instead)"
msgstr ""

#: sphinx/ext/graphviz.py:135
msgid "Graphviz directive cannot have both content and a filename argument"
msgstr "Pengarahan Graphviz tidak dapat memiliki konten dan argumen nama berkas sekaligus"

#: sphinx/ext/graphviz.py:145
#, python-format
msgid "External Graphviz file %r not found or reading it failed"
msgstr "Berkas Graphviz eksternal %r tidak ditemukan atau gagal dibaca"

#: sphinx/ext/graphviz.py:152
msgid "Ignoring \"graphviz\" directive without content."
msgstr "Mengabaikan pengarahan \"graphviz\" tanpa konten."

#: sphinx/ext/graphviz.py:268
#, python-format
msgid "graphviz_dot executable path must be set! %r"
msgstr ""

#: sphinx/ext/graphviz.py:303
#, python-format
msgid ""
"dot command %r cannot be run (needed for graphviz output), check the "
"graphviz_dot setting"
msgstr "perintah dot %r tidak dapat dioperasikan (diperlukan untuk keluaran graphviz), periksa pengaturan graphviz_dot"

#: sphinx/ext/graphviz.py:310
#, python-format
msgid ""
"dot exited with error:\n"
"[stderr]\n"
"%r\n"
"[stdout]\n"
"%r"
msgstr "dot keluar dengan kesalahan: \n[stderr]\n%r\n[stdout]\n%r"

#: sphinx/ext/graphviz.py:313
#, python-format
msgid ""
"dot did not produce an output file:\n"
"[stderr]\n"
"%r\n"
"[stdout]\n"
"%r"
msgstr "dot tidak menghasilkan berkas output: \n[stderr]\n%r\n[stdout]\n%r"

#: sphinx/ext/graphviz.py:329
#, python-format
msgid "graphviz_output_format must be one of 'png', 'svg', but is %r"
msgstr "graphviz_output_format harus salah satu dari 'png', 'svg', tetapi %r"

#: sphinx/ext/graphviz.py:333 sphinx/ext/graphviz.py:386
#: sphinx/ext/graphviz.py:423
#, python-format
msgid "dot code %r: %s"
msgstr "kode dot %r: %s"

#: sphinx/ext/graphviz.py:436 sphinx/ext/graphviz.py:444
#, python-format
msgid "[graph: %s]"
msgstr "[graph: %s]"

#: sphinx/ext/graphviz.py:438 sphinx/ext/graphviz.py:446
msgid "[graph]"
msgstr "[graph]"

#: sphinx/ext/imgconverter.py:39
#, python-format
msgid ""
"Unable to run the image conversion command %r. 'sphinx.ext.imgconverter' requires ImageMagick by default. Ensure it is installed, or set the 'image_converter' option to a custom conversion command.\n"
"\n"
"Traceback: %s"
msgstr ""

#: sphinx/ext/imgconverter.py:48 sphinx/ext/imgconverter.py:72
#, python-format
msgid ""
"convert exited with error:\n"
"[stderr]\n"
"%r\n"
"[stdout]\n"
"%r"
msgstr "convert keluar dengan kesalahan: \n[stderr]\n%r\n[stdout]\n%r"

#: sphinx/ext/imgconverter.py:67
#, python-format
msgid "convert command %r cannot be run, check the image_converter setting"
msgstr ""

#: sphinx/ext/imgmath.py:158
#, python-format
msgid ""
"LaTeX command %r cannot be run (needed for math display), check the "
"imgmath_latex setting"
msgstr "Perintah LaTeX %r tidak dapat dioperasikan (diperlukan untuk tampilan matematika), periksa pengaturan imgmath_latex"

#: sphinx/ext/imgmath.py:173
#, python-format
msgid ""
"%s command %r cannot be run (needed for math display), check the imgmath_%s "
"setting"
msgstr "%s perintah %r tidak dapat dioperasikan (diperlukan untuk tampilan matematika), periksa pengaturan imgmath_%s"

#: sphinx/ext/imgmath.py:327
#, python-format
msgid "display latex %r: %s"
msgstr "tampilkan latex %r: %s"

#: sphinx/ext/imgmath.py:361
#, python-format
msgid "inline latex %r: %s"
msgstr "inline latex %r: %s"

#: sphinx/ext/imgmath.py:368 sphinx/ext/mathjax.py:53
msgid "Link to this equation"
msgstr ""

#: sphinx/ext/intersphinx.py:195
#, python-format
msgid "intersphinx inventory has moved: %s -> %s"
msgstr "pengimpanan intersphinx telah dipindahkan: %s -> %s"

#: sphinx/ext/intersphinx.py:230
#, python-format
msgid "loading intersphinx inventory from %s..."
msgstr "memuat penyimpanan intersphinx dari %s..."

#: sphinx/ext/intersphinx.py:244
msgid ""
"encountered some issues with some of the inventories, but they had working "
"alternatives:"
msgstr "mengalami beberapa masalah dengan beberapa inventaris, tetapi mereka memiliki alternatif berfungsi:"

#: sphinx/ext/intersphinx.py:250
msgid "failed to reach any of the inventories with the following issues:"
msgstr "gagal mencapai salah satu inventaris dengan masalah berikut:"

#: sphinx/ext/intersphinx.py:303
#, python-format
msgid "(in %s v%s)"
msgstr "(di %s v%s)"

#: sphinx/ext/intersphinx.py:305
#, python-format
msgid "(in %s)"
msgstr "(dalam %s)"

#: sphinx/ext/intersphinx.py:538
#, python-format
msgid "inventory for external cross-reference not found: %r"
msgstr ""

#: sphinx/ext/intersphinx.py:546
#, python-format
msgid "invalid external cross-reference suffix: %r"
msgstr ""

#: sphinx/ext/intersphinx.py:557
#, python-format
msgid "domain for external cross-reference not found: %r"
msgstr ""

#: sphinx/ext/intersphinx.py:750
#, python-format
msgid "external %s:%s reference target not found: %s"
msgstr ""

#: sphinx/ext/intersphinx.py:775
#, python-format
msgid "intersphinx identifier %r is not string. Ignored"
msgstr "pengenal intersphinx %r bukan string. Diabaikan"

#: sphinx/ext/intersphinx.py:797
#, python-format
msgid "Failed to read intersphinx_mapping[%s], ignored: %r"
msgstr ""

#: sphinx/ext/linkcode.py:69 sphinx/ext/viewcode.py:199
msgid "[source]"
msgstr "[sumber]"

#: sphinx/ext/todo.py:69
msgid "Todo"
msgstr "Todo"

#: sphinx/ext/todo.py:102
#, python-format
msgid "TODO entry found: %s"
msgstr "Entri TODO ditemukan: %s"

#: sphinx/ext/todo.py:161
msgid "<<original entry>>"
msgstr "<<original entry>>"

#: sphinx/ext/todo.py:163
#, python-format
msgid "(The <<original entry>> is located in %s, line %d.)"
msgstr "(<<original entry>> terletak di %s, baris %d.)"

#: sphinx/ext/todo.py:173
msgid "original entry"
msgstr "entri asli"

#: sphinx/ext/viewcode.py:256
msgid "highlighting module code... "
msgstr "menyoroti kode modul..."

#: sphinx/ext/viewcode.py:284
msgid "[docs]"
msgstr "[docs]"

#: sphinx/ext/viewcode.py:304
msgid "Module code"
msgstr "Kode modul"

#: sphinx/ext/viewcode.py:310
#, python-format
msgid "<h1>Source code for %s</h1>"
msgstr "<h1>Kode sumber untuk %s</h1>"

#: sphinx/ext/viewcode.py:336
msgid "Overview: module code"
msgstr "Tinjauan: kode modul"

#: sphinx/ext/viewcode.py:337
msgid "<h1>All modules for which code is available</h1>"
msgstr "<h1>Semua modul dimana kode tersedia</h1>"

#: sphinx/ext/autodoc/__init__.py:135
#, python-format
msgid "invalid value for member-order option: %s"
msgstr ""

#: sphinx/ext/autodoc/__init__.py:143
#, python-format
msgid "invalid value for class-doc-from option: %s"
msgstr ""

#: sphinx/ext/autodoc/__init__.py:399
#, python-format
msgid "invalid signature for auto%s (%r)"
msgstr "tanda tangan tidak valid untuk outo %s (%r)"

#: sphinx/ext/autodoc/__init__.py:515
#, python-format
msgid "error while formatting arguments for %s: %s"
msgstr "kesalahan saat memformat argumen untuk %s: %s"

#: sphinx/ext/autodoc/__init__.py:798
#, python-format
msgid ""
"autodoc: failed to determine %s.%s (%r) to be documented, the following exception was raised:\n"
"%s"
msgstr ""

#: sphinx/ext/autodoc/__init__.py:893
#, python-format
msgid ""
"don't know which module to import for autodocumenting %r (try placing a "
"\"module\" or \"currentmodule\" directive in the document, or giving an "
"explicit module name)"
msgstr "tidak tahu modul mana yang akan diimpor untuk autodocumenting %r (coba letakkan pengarahan \"module\" atau \"currentmodule\" dalam dokumen, atau berikan nama modul yang eksplisit)"

#: sphinx/ext/autodoc/__init__.py:937
#, python-format
msgid "A mocked object is detected: %r"
msgstr ""

#: sphinx/ext/autodoc/__init__.py:956
#, python-format
msgid "error while formatting signature for %s: %s"
msgstr ""

#: sphinx/ext/autodoc/__init__.py:1019
msgid "\"::\" in automodule name doesn't make sense"
msgstr "\"::\" dalam nama automodule tidak masuk akal"

#: sphinx/ext/autodoc/__init__.py:1026
#, python-format
msgid "signature arguments or return annotation given for automodule %s"
msgstr "argumen tanda tangan atau anotasi kembalian diberikan untuk automodule %s"

#: sphinx/ext/autodoc/__init__.py:1039
#, python-format
msgid ""
"__all__ should be a list of strings, not %r (in module %s) -- ignoring "
"__all__"
msgstr "__all__ harus berupa daftar string, bukan %r (dalam modul %s) -- mengabaikan __all__"

#: sphinx/ext/autodoc/__init__.py:1105
#, python-format
msgid ""
"missing attribute mentioned in :members: option: module %s, attribute %s"
msgstr ""

#: sphinx/ext/autodoc/__init__.py:1327 sphinx/ext/autodoc/__init__.py:1404
#: sphinx/ext/autodoc/__init__.py:2824
#, python-format
msgid "Failed to get a function signature for %s: %s"
msgstr ""

#: sphinx/ext/autodoc/__init__.py:1618
#, python-format
msgid "Failed to get a constructor signature for %s: %s"
msgstr ""

#: sphinx/ext/autodoc/__init__.py:1745
#, python-format
msgid "Bases: %s"
msgstr "Basis: %s"

#: sphinx/ext/autodoc/__init__.py:1759
#, python-format
msgid "missing attribute %s in object %s"
msgstr "atribut hilang %s dalam objek %s"

#: sphinx/ext/autodoc/__init__.py:1858 sphinx/ext/autodoc/__init__.py:1895
#: sphinx/ext/autodoc/__init__.py:1990
#, python-format
msgid "alias of %s"
msgstr ""

#: sphinx/ext/autodoc/__init__.py:1878
#, python-format
msgid "alias of TypeVar(%s)"
msgstr ""

#: sphinx/ext/autodoc/__init__.py:2216 sphinx/ext/autodoc/__init__.py:2316
#, python-format
msgid "Failed to get a method signature for %s: %s"
msgstr ""

#: sphinx/ext/autodoc/__init__.py:2447
#, python-format
msgid "Invalid __slots__ found on %s. Ignored."
msgstr ""

#: sphinx/ext/autodoc/preserve_defaults.py:190
#, python-format
msgid "Failed to parse a default argument value for %r: %s"
msgstr ""

#: sphinx/ext/autodoc/type_comment.py:132
#, python-format
msgid "Failed to update signature for %r: parameter not found: %s"
msgstr ""

#: sphinx/ext/autodoc/type_comment.py:135
#, python-format
msgid "Failed to parse type_comment for %r: %s"
msgstr ""

#: sphinx/ext/autosummary/__init__.py:251
#, python-format
msgid "autosummary references excluded document %r. Ignored."
msgstr ""

#: sphinx/ext/autosummary/__init__.py:253
#, python-format
msgid ""
"autosummary: stub file not found %r. Check your autosummary_generate "
"setting."
msgstr ""

#: sphinx/ext/autosummary/__init__.py:272
msgid "A captioned autosummary requires :toctree: option. ignored."
msgstr ""

#: sphinx/ext/autosummary/__init__.py:325
#, python-format
msgid ""
"autosummary: failed to import %s.\n"
"Possible hints:\n"
"%s"
msgstr ""

#: sphinx/ext/autosummary/__init__.py:339
#, python-format
msgid "failed to parse name %s"
msgstr "gagal mengurai nama %s"

#: sphinx/ext/autosummary/__init__.py:344
#, python-format
msgid "failed to import object %s"
msgstr "gagal mengimpor objek %s"

#: sphinx/ext/autosummary/__init__.py:802
#, python-format
msgid "autosummary_generate: file not found: %s"
msgstr ""

#: sphinx/ext/autosummary/__init__.py:810
msgid ""
"autosummary generates .rst files internally. But your source_suffix does not"
" contain .rst. Skipped."
msgstr ""

#: sphinx/ext/autosummary/generate.py:200
#: sphinx/ext/autosummary/generate.py:358
#, python-format
msgid ""
"autosummary: failed to determine %r to be documented, the following exception was raised:\n"
"%s"
msgstr ""

#: sphinx/ext/autosummary/generate.py:470
#, python-format
msgid "[autosummary] generating autosummary for: %s"
msgstr "[autosummary] menghasilkan autosummary untuk: %s"

#: sphinx/ext/autosummary/generate.py:474
#, python-format
msgid "[autosummary] writing to %s"
msgstr "[autosummary] menulis ke %s"

#: sphinx/ext/autosummary/generate.py:517
#, python-format
msgid ""
"[autosummary] failed to import %s.\n"
"Possible hints:\n"
"%s"
msgstr ""

#: sphinx/ext/autosummary/generate.py:690
msgid ""
"\n"
"Generate ReStructuredText using autosummary directives.\n"
"\n"
"sphinx-autogen is a frontend to sphinx.ext.autosummary.generate. It generates\n"
"the reStructuredText files from the autosummary directives contained in the\n"
"given input files.\n"
"\n"
"The format of the autosummary directive is documented in the\n"
"``sphinx.ext.autosummary`` Python module and can be read using::\n"
"\n"
"  pydoc sphinx.ext.autosummary\n"
msgstr "\nHasilkan ReStructuredText menggunakan pengarahan autosummary.\n\nsphinx-autogen adalah tampilan depan ke sphinx.ext.autosummary.generate. Ini menghasilkan \nfile reStructuredText dari pengarahan autosummary yang terkandung dalam \nfile input yang diberikan.\n\nFormat pengarahan autosummary didokumentasikan dalam \nmodul ``sphinx.ext.autosummary`` dan dapat dibaca menggunakan::\n\n  pydoc sphinx.ext.autosummary\n"

#: sphinx/ext/autosummary/generate.py:707
msgid "source files to generate rST files for"
msgstr "berkas sumber untuk menghasilkan file rST untuk"

#: sphinx/ext/autosummary/generate.py:711
msgid "directory to place all output in"
msgstr "direktori untuk menempatkan semua keluaran dalam"

#: sphinx/ext/autosummary/generate.py:714
#, python-format
msgid "default suffix for files (default: %(default)s)"
msgstr "akhiran bawaan untuk berkas (bawaan: %(default)s)"

#: sphinx/ext/autosummary/generate.py:718
#, python-format
msgid "custom template directory (default: %(default)s)"
msgstr "direktori templat ubahsuai (bawaan: %(default)s)"

#: sphinx/ext/autosummary/generate.py:722
#, python-format
msgid "document imported members (default: %(default)s)"
msgstr "mendokumentasikan anggota yang diimpor (bawaan: %(default)s)"

#: sphinx/ext/autosummary/generate.py:726
#, python-format
msgid ""
"document exactly the members in module __all__ attribute. (default: "
"%(default)s)"
msgstr ""

#: sphinx/ext/napoleon/__init__.py:341 sphinx/ext/napoleon/docstring.py:728
msgid "Keyword Arguments"
msgstr "Argumen Kata Kunci"

#: sphinx/ext/napoleon/docstring.py:682
msgid "Example"
msgstr "Contoh"

#: sphinx/ext/napoleon/docstring.py:683
msgid "Examples"
msgstr "Contoh-contoh"

#: sphinx/ext/napoleon/docstring.py:744
msgid "Notes"
msgstr "Catatan"

#: sphinx/ext/napoleon/docstring.py:753
msgid "Other Parameters"
msgstr "Parameter lainnya"

#: sphinx/ext/napoleon/docstring.py:789
msgid "Receives"
msgstr ""

#: sphinx/ext/napoleon/docstring.py:793
msgid "References"
msgstr "Referensi"

#: sphinx/ext/napoleon/docstring.py:825
msgid "Warns"
msgstr "Peringatkan"

#: sphinx/ext/napoleon/docstring.py:829
msgid "Yields"
msgstr "Hasil"

#: sphinx/ext/napoleon/docstring.py:987
#, python-format
msgid "invalid value set (missing closing brace): %s"
msgstr ""

#: sphinx/ext/napoleon/docstring.py:994
#, python-format
msgid "invalid value set (missing opening brace): %s"
msgstr ""

#: sphinx/ext/napoleon/docstring.py:1001
#, python-format
msgid "malformed string literal (missing closing quote): %s"
msgstr ""

#: sphinx/ext/napoleon/docstring.py:1008
#, python-format
msgid "malformed string literal (missing opening quote): %s"
msgstr ""

#: sphinx/locale/__init__.py:228
msgid "Attention"
msgstr "Pehatian"

#: sphinx/locale/__init__.py:229
msgid "Caution"
msgstr "Hati-hati"

#: sphinx/locale/__init__.py:230
msgid "Danger"
msgstr "Bahaya"

#: sphinx/locale/__init__.py:231
msgid "Error"
msgstr "Kesalahan"

#: sphinx/locale/__init__.py:232
msgid "Hint"
msgstr "Petunjuk"

#: sphinx/locale/__init__.py:233
msgid "Important"
msgstr "Penting"

#: sphinx/locale/__init__.py:234
msgid "Note"
msgstr "Catatan"

#: sphinx/locale/__init__.py:235
msgid "See also"
msgstr "Lihat juga"

#: sphinx/locale/__init__.py:236
msgid "Tip"
msgstr "Tip"

#: sphinx/locale/__init__.py:237
msgid "Warning"
msgstr "Peringatan"

#: sphinx/templates/latex/longtable.tex_t:52
#: sphinx/templates/latex/sphinxmessages.sty_t:8
msgid "continued from previous page"
msgstr "lanjutan dari halaman sebelumnya"

#: sphinx/templates/latex/longtable.tex_t:63
#: sphinx/templates/latex/sphinxmessages.sty_t:9
msgid "continues on next page"
msgstr "berlanjut ke halaman berikutnya"

#: sphinx/templates/latex/sphinxmessages.sty_t:10
msgid "Non-alphabetical"
msgstr "Non-abjad"

#: sphinx/templates/latex/sphinxmessages.sty_t:12
msgid "Numbers"
msgstr "Angka"

#: sphinx/templates/latex/sphinxmessages.sty_t:13
msgid "page"
msgstr "laman"

#: sphinx/themes/agogo/layout.html:38 sphinx/themes/basic/globaltoc.html:10
#: sphinx/themes/basic/localtoc.html:12 sphinx/themes/scrolls/layout.html:41
msgid "Table of Contents"
msgstr "Daftar Isi"

#: sphinx/themes/agogo/layout.html:43 sphinx/themes/basic/layout.html:141
#: sphinx/themes/basic/search.html:11 sphinx/themes/basic/search.html:23
msgid "Search"
msgstr "Pencarian"

#: sphinx/themes/agogo/layout.html:46 sphinx/themes/basic/searchbox.html:16
#: sphinx/themes/basic/searchfield.html:18
msgid "Go"
msgstr "Go"

#: sphinx/themes/agogo/layout.html:90 sphinx/themes/basic/sourcelink.html:15
msgid "Show Source"
msgstr "Lihat Sumber"

#: sphinx/themes/basic/defindex.html:11
msgid "Overview"
msgstr "Tinjauan"

#: sphinx/themes/basic/defindex.html:15
msgid "Welcome! This is"
msgstr "Selamat Datang! Ini adalah"

#: sphinx/themes/basic/defindex.html:16
msgid "the documentation for"
msgstr "dokumentasi untuk"

#: sphinx/themes/basic/defindex.html:17
msgid "last updated"
msgstr "terakhir diperbarui"

#: sphinx/themes/basic/defindex.html:20
msgid "Indices and tables:"
msgstr "Indeks dan tabel:"

#: sphinx/themes/basic/defindex.html:23
msgid "Complete Table of Contents"
msgstr "Daftar Isi Lengkap"

#: sphinx/themes/basic/defindex.html:24
msgid "lists all sections and subsections"
msgstr "daftar semua seksi dan subseksi"

#: sphinx/themes/basic/defindex.html:26
msgid "search this documentation"
msgstr "pencarian pada dokumentasi ini"

#: sphinx/themes/basic/defindex.html:28
msgid "Global Module Index"
msgstr "Index Modul Global"

#: sphinx/themes/basic/defindex.html:29
msgid "quick access to all modules"
msgstr "akses cepat semua modul"

#: sphinx/themes/basic/defindex.html:31
msgid "all functions, classes, terms"
msgstr "semua fungsi, class, term"

#: sphinx/themes/basic/genindex-single.html:33
#, python-format
msgid "Index &ndash; %(key)s"
msgstr "Index &ndash; %(key)s"

#: sphinx/themes/basic/genindex-single.html:61
#: sphinx/themes/basic/genindex-split.html:24
#: sphinx/themes/basic/genindex-split.html:38
#: sphinx/themes/basic/genindex.html:73
msgid "Full index on one page"
msgstr "Index penuh dalam satu halaman"

#: sphinx/themes/basic/genindex-split.html:16
msgid "Index pages by letter"
msgstr "Index halaman berdasarkan huruf"

#: sphinx/themes/basic/genindex-split.html:25
msgid "can be huge"
msgstr "dapat menjadi besar"

#: sphinx/themes/basic/layout.html:26
msgid "Navigation"
msgstr "Navigasi"

#: sphinx/themes/basic/layout.html:126
#, python-format
msgid "Search within %(docstitle)s"
msgstr "Pencarian dalam %(docstitle)s"

#: sphinx/themes/basic/layout.html:135
msgid "About these documents"
msgstr "Tentang dokumen ini"

#: sphinx/themes/basic/layout.html:144 sphinx/themes/basic/layout.html:188
#: sphinx/themes/basic/layout.html:190
msgid "Copyright"
msgstr "Hak Cipta"

#: sphinx/themes/basic/layout.html:194 sphinx/themes/basic/layout.html:200
#, python-format
msgid "&#169; %(copyright_prefix)s %(copyright)s."
msgstr ""

#: sphinx/themes/basic/layout.html:212
#, python-format
msgid "Last updated on %(last_updated)s."
msgstr "Terakhir diperbarui pada %(last_updated)s."

#: sphinx/themes/basic/layout.html:215
#, python-format
msgid ""
"Created using <a href=\"https://www.sphinx-doc.org/\">Sphinx</a> "
"%(sphinx_version)s."
msgstr ""

#: sphinx/themes/basic/opensearch.xml:4
#, python-format
msgid "Search %(docstitle)s"
msgstr "Pencarian %(docstitle)s"

#: sphinx/themes/basic/relations.html:12
msgid "Previous topic"
msgstr "Topik sebelumnya"

#: sphinx/themes/basic/relations.html:14
msgid "previous chapter"
msgstr "bab sebelum"

#: sphinx/themes/basic/relations.html:19
msgid "Next topic"
msgstr "Topik berikutnya"

#: sphinx/themes/basic/relations.html:21
msgid "next chapter"
msgstr "bab berikutnya"

#: sphinx/themes/basic/search.html:28
msgid ""
"Please activate JavaScript to enable the search\n"
"    functionality."
msgstr "Tolong aktifkan JavaScript untuk melakukan pencarian.\n "

#: sphinx/themes/basic/search.html:36
msgid ""
"Searching for multiple words only shows matches that contain\n"
"    all words."
msgstr "Mencari beberapa kata hanya menunjukkan kecocokan yang mengandung\n    semua kata."

#: sphinx/themes/basic/search.html:43
msgid "search"
msgstr "pencarian"

#: sphinx/themes/basic/searchbox.html:12
msgid "Quick search"
msgstr "Pencarian cepat"

#: sphinx/themes/basic/sourcelink.html:12
msgid "This Page"
msgstr "Halaman Ini"

#: sphinx/themes/basic/changes/frameset.html:5
#: sphinx/themes/basic/changes/versionchanges.html:12
#, python-format
msgid "Changes in Version %(version)s &#8212; %(docstitle)s"
msgstr "Perubahan pada Versi %(version)s &#8212; %(docstitle)s"

#: sphinx/themes/basic/changes/rstsource.html:5
#, python-format
msgid "%(filename)s &#8212; %(docstitle)s"
msgstr "%(filename)s &#8212; %(docstitle)s"

#: sphinx/themes/basic/changes/versionchanges.html:17
#, python-format
msgid "Automatically generated list of changes in version %(version)s"
msgstr "Daftar perubahan dibuat otomatis untuk versi %(version)s"

#: sphinx/themes/basic/changes/versionchanges.html:18
msgid "Library changes"
msgstr "Perubahan library"

#: sphinx/themes/basic/changes/versionchanges.html:23
msgid "C API changes"
msgstr "Perubahan API C"

#: sphinx/themes/basic/changes/versionchanges.html:25
msgid "Other changes"
msgstr "Perubahan lain"

#: sphinx/themes/basic/static/searchtools.js:112
msgid "Search Results"
msgstr "Hasil Pencarian"

#: sphinx/themes/basic/static/searchtools.js:114
msgid ""
"Your search did not match any documents. Please make sure that all words are"
" spelled correctly and that you've selected enough categories."
msgstr "Tidak ada dokumen yang cocok dengan pencarian anda. Pastikan semua kata ditulis dengan benar dan sudah memilih cukup kategori."

#: sphinx/themes/basic/static/searchtools.js:118
msgid ""
"Search finished, found ${resultCount} page(s) matching the search query."
msgstr ""

#: sphinx/themes/basic/static/searchtools.js:246
msgid "Searching"
msgstr "Pencarian"

#: sphinx/themes/basic/static/searchtools.js:262
msgid "Preparing search..."
msgstr "Penyiapkan pencarian..."

#: sphinx/themes/basic/static/searchtools.js:463
msgid ", in "
msgstr ", di"

#: sphinx/themes/basic/static/sphinx_highlight.js:112
msgid "Hide Search Matches"
msgstr "Sembunyikan Hasil Pencarian"

#: sphinx/themes/classic/layout.html:20
#: sphinx/themes/classic/static/sidebar.js_t:57
msgid "Collapse sidebar"
msgstr "Tutup sidebar"

#: sphinx/themes/classic/static/sidebar.js_t:48
msgid "Expand sidebar"
msgstr "Buka sidebar"

#: sphinx/themes/haiku/layout.html:24
msgid "Contents"
msgstr "Konten"

#: sphinx/transforms/__init__.py:128
msgid "could not calculate translation progress!"
msgstr ""

#: sphinx/transforms/__init__.py:133
msgid "no translated elements!"
msgstr ""

#: sphinx/transforms/__init__.py:250
#, python-format
msgid ""
"4 column based index found. It might be a bug of extensions you use: %r"
msgstr "4 kolom berdasarkan indeks ditemukan. Ini mungkin bug ekstensi yang Anda gunakan: %r"

#: sphinx/transforms/__init__.py:291
#, python-format
msgid "Footnote [%s] is not referenced."
msgstr "Catatan kaki [%s] tidak dirujuk."

#: sphinx/transforms/__init__.py:297
msgid "Footnote [#] is not referenced."
msgstr "Catatan kaki [#] tidak dirujuk."

#: sphinx/transforms/i18n.py:205 sphinx/transforms/i18n.py:270
msgid ""
"inconsistent footnote references in translated message. original: {0}, "
"translated: {1}"
msgstr "referensi catatan kaki yang tidak konsisten dalam pesan yang diterjemahkan. asli: {0}, diterjemahkan: {1}"

#: sphinx/transforms/i18n.py:245
msgid ""
"inconsistent references in translated message. original: {0}, translated: "
"{1}"
msgstr "referensi yang tidak konsisten dalam pesan yang diterjemahkan. asli: {0}, diterjemahkan: {1}"

#: sphinx/transforms/i18n.py:285
msgid ""
"inconsistent citation references in translated message. original: {0}, "
"translated: {1}"
msgstr "referensi kutipan tidak konsisten dalam pesan yang diterjemahkan. asli: {0}, diterjemahkan: {1}"

#: sphinx/transforms/i18n.py:302
msgid ""
"inconsistent term references in translated message. original: {0}, "
"translated: {1}"
msgstr "referensi istilah yang tidak konsisten dalam pesan yang diterjemahkan. asli: {0}, diterjemahkan: {1}"

#: sphinx/transforms/post_transforms/__init__.py:116
msgid ""
"Could not determine the fallback text for the cross-reference. Might be a "
"bug."
msgstr ""

#: sphinx/transforms/post_transforms/__init__.py:158
#, python-format
msgid "more than one target found for 'any' cross-reference %r: could be %s"
msgstr "lebih dari satu target ditemukan untuk referensi silang 'any' %r: bisa %s"

#: sphinx/transforms/post_transforms/__init__.py:209
#, python-format
msgid "%s:%s reference target not found: %s"
msgstr ""

#: sphinx/transforms/post_transforms/__init__.py:212
#, python-format
msgid "%r reference target not found: %s"
msgstr ""

#: sphinx/transforms/post_transforms/images.py:89
#, python-format
msgid "Could not fetch remote image: %s [%d]"
msgstr "Tidak dapat mengambil gambar jarak jauh: %s [%d]"

#: sphinx/transforms/post_transforms/images.py:117
#, python-format
msgid "Could not fetch remote image: %s [%s]"
msgstr "Tidak dapat mengambil gambar jarak jauh: %s [%s]"

#: sphinx/transforms/post_transforms/images.py:135
#, python-format
msgid "Unknown image format: %s..."
msgstr "Format gambar tidak dikenal: %s..."

#: sphinx/util/__init__.py:168
#, python-format
msgid "undecodable source characters, replacing with \"?\": %r"
msgstr "karakter sumber undecodable, menggantinya dengan \"?\": %r"

#: sphinx/util/display.py:78
msgid "skipped"
msgstr "dilewati"

#: sphinx/util/display.py:83
msgid "failed"
msgstr "gagal"

#: sphinx/util/docfields.py:87
#, python-format
msgid ""
"Problem in %s domain: field is supposed to use role '%s', but that role is "
"not in the domain."
msgstr ""

#: sphinx/util/docutils.py:295
#, python-format
msgid "unknown directive or role name: %s:%s"
msgstr ""

#: sphinx/util/docutils.py:591
#, python-format
msgid "unknown node type: %r"
msgstr "tipe simpul tidak dikenal: %r"

#: sphinx/util/i18n.py:94
#, python-format
msgid "reading error: %s, %s"
msgstr "kesalahan membaca: %s, %s"

#: sphinx/util/i18n.py:101
#, python-format
msgid "writing error: %s, %s"
msgstr "kesalahan menulis: %s, %s"

#: sphinx/util/i18n.py:125
#, python-format
msgid "locale_dir %s does not exist"
msgstr ""

#: sphinx/util/i18n.py:215
#, python-format
msgid ""
"Invalid date format. Quote the string by single quote if you want to output "
"it directly: %s"
msgstr "Format tanggal tidak valid. Kutip string dengan kutipan tunggal jika Anda ingin menampilkannya secara langsung: %s"

#: sphinx/util/nodes.py:386
#, python-format
msgid ""
"%r is deprecated for index entries (from entry %r). Use 'pair: %s' instead."
msgstr ""

#: sphinx/util/nodes.py:439
#, python-format
msgid "toctree contains ref to nonexisting file %r"
msgstr "toctree berisi ref ke berkas yang tidak ada %r"

#: sphinx/util/nodes.py:637
#, python-format
msgid "exception while evaluating only directive expression: %s"
msgstr "pengecualian saat mengevaluasi hanya ekspresi pengarahan: %s"

#: sphinx/util/rst.py:71
#, python-format
msgid "default role %s not found"
msgstr "peran bawaan %s tidak ditemukan"

#: sphinx/writers/html5.py:100 sphinx/writers/html5.py:109
msgid "Link to this definition"
msgstr ""

#: sphinx/writers/html5.py:397
#, python-format
msgid "numfig_format is not defined for %s"
msgstr "numfig_format tidak didefinisikan untuk %s"

#: sphinx/writers/html5.py:407
#, python-format
msgid "Any IDs not assigned for %s node"
msgstr "Tidak ada ID apa pun yang ditugaskan untuk simpul %s"

#: sphinx/writers/html5.py:462
msgid "Link to this term"
msgstr ""

#: sphinx/writers/html5.py:496 sphinx/writers/html5.py:501
msgid "Link to this heading"
msgstr ""

#: sphinx/writers/html5.py:505
msgid "Link to this table"
msgstr ""

#: sphinx/writers/html5.py:548
msgid "Link to this code"
msgstr ""

#: sphinx/writers/html5.py:550
msgid "Link to this image"
msgstr ""

#: sphinx/writers/html5.py:552
msgid "Link to this toctree"
msgstr ""

#: sphinx/writers/html5.py:688
msgid "Could not obtain image size. :scale: option is ignored."
msgstr "Tidak dapat memperoleh ukuran gambar. :scale: option diabaikan."

#: sphinx/writers/latex.py:335
#, python-format
msgid "unknown %r toplevel_sectioning for class %r"
msgstr "%r toplevel_sectioning tidak diketahui untuk kelas %r"

#: sphinx/writers/latex.py:386
msgid "too large :maxdepth:, ignored."
msgstr ":maxdepth: terlalu besar, diabaikan."

#: sphinx/writers/latex.py:625
msgid "document title is not a single Text node"
msgstr "judul dokumen bukan simpul Text tunggal"

#: sphinx/writers/latex.py:656 sphinx/writers/texinfo.py:626
msgid ""
"encountered title node not in section, topic, table, admonition or sidebar"
msgstr "simpul judul tidak ditemui dalam bagian, topik, tabel, peringatan atau sisi bilah"

#: sphinx/writers/latex.py:959 sphinx/writers/manpage.py:259
#: sphinx/writers/texinfo.py:641
msgid "Footnotes"
msgstr "Catatan kaki"

#: sphinx/writers/latex.py:1028
msgid ""
"both tabularcolumns and :widths: option are given. :widths: is ignored."
msgstr "opsi tabularcolumns dan :widths: opsi diberikan bersamaan. :widths: diabaikan."

#: sphinx/writers/latex.py:1388
#, python-format
msgid "dimension unit %s is invalid. Ignored."
msgstr "unit dimensi %s tidak valid. Diabaikan"

#: sphinx/writers/latex.py:1722
#, python-format
msgid "unknown index entry type %s found"
msgstr "entri indeks tidak diketahui ditemukan tipe %s"

#: sphinx/writers/manpage.py:305 sphinx/writers/text.py:907
#, python-format
msgid "[image: %s]"
msgstr "[gambar: %s]"

#: sphinx/writers/manpage.py:306 sphinx/writers/text.py:908
msgid "[image]"
msgstr "[gambar]"

#: sphinx/writers/texinfo.py:1197
msgid "caption not inside a figure."
msgstr "keterangan tidak di dalam gambar."

#: sphinx/writers/texinfo.py:1284
#, python-format
msgid "unimplemented node type: %r"
msgstr "tipe simpul tidak diterapkan: %r"
