@echo off
chcp 65001 >nul
title DataTransmission 数据传输工具

echo ========================================
echo    DataTransmission 数据传输工具
echo ========================================
echo.

echo 正在检查系统环境...

:: 检查MySQL服务
echo 检查MySQL服务状态...
sc query mysql >nul 2>&1
if %errorlevel% equ 0 (
    echo ✓ MySQL服务已安装
    sc query mysql | find "RUNNING" >nul
    if %errorlevel% equ 0 (
        echo ✓ MySQL服务正在运行
    ) else (
        echo ⚠ MySQL服务未运行，正在尝试启动...
        net start mysql >nul 2>&1
        if %errorlevel% equ 0 (
            echo ✓ MySQL服务启动成功
        ) else (
            echo ✗ MySQL服务启动失败，请手动启动MySQL服务
            echo   或检查MySQL是否正确安装
        )
    )
) else (
    echo ⚠ 未检测到MySQL服务
    echo   请确保已安装MySQL数据库
)

echo.
echo 检查配置文件...
if exist "config.py" (
    echo ✓ 配置文件存在
) else (
    echo ✗ 配置文件不存在
    echo   请确保config.py文件在当前目录
    pause
    exit /b 1
)

echo.
echo 启动DataTransmission服务...
echo 请确保已配置好数据库连接信息
echo.
echo Web界面地址: http://localhost:5000
echo 按 Ctrl+C 停止程序
echo.

DataTransmission.exe

echo.
echo 程序已退出
pause
