#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
二维码显示功能修复测试脚本
专门测试修复后的二维码显示功能
"""

import time
import logging
import json
from database import DatabaseManager
from qr_generator import QRGenerator

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def test_qr_display_fix():
    """测试修复后的二维码显示功能"""
    print("=" * 60)
    print("二维码显示功能修复测试")
    print("=" * 60)
    print("修复内容:")
    print("✓ 增强错误处理和日志记录")
    print("✓ 改进窗口创建和定位逻辑")
    print("✓ 添加显示状态检查")
    print("✓ 优化窗口生命周期管理")
    print("✓ 只有成功显示后才更新数据库状态")
    print("=" * 60)
    
    try:
        # 初始化组件
        print("初始化数据库连接...")
        db = DatabaseManager()
        print("✓ 数据库连接成功")
        
        print("初始化二维码生成器...")
        qr_gen = QRGenerator(db)
        print("✓ 二维码生成器初始化成功")
        
        # 创建测试数据
        test_data = [
            {
                "id": str(int(time.time() * 1000)),
                "type": 1,
                "data": "测试二维码显示修复 - 数据1"
            },
            {
                "id": str(int(time.time() * 1000) + 1),
                "type": 2,
                "data": "测试二维码显示修复 - 数据2"
            },
            {
                "id": str(int(time.time() * 1000) + 2),
                "type": 3,
                "data": "测试二维码显示修复 - 数据3"
            }
        ]
        
        print(f"\n准备插入 {len(test_data)} 条测试数据...")
        
        # 插入测试数据到数据库
        for i, data in enumerate(test_data, 1):
            success = db.insert_transmission_data(data["id"], data["type"], data["data"])
            if success:
                print(f"✓ 测试数据 {i} 插入成功: {data}")
            else:
                print(f"✗ 测试数据 {i} 插入失败: {data}")
        
        print("\n开始二维码显示测试...")
        print("观察要点:")
        print("1. 二维码是否在屏幕中央显示")
        print("2. 窗口尺寸是否为900x900像素")
        print("3. 每个二维码是否显示2秒")
        print("4. 显示完成后是否正确关闭窗口")
        print("5. 数据库状态是否正确更新")
        print()
        
        # 手动处理测试数据
        processed_count = 0
        max_attempts = len(test_data) + 2  # 多给几次机会
        
        for attempt in range(max_attempts):
            print(f"--- 第 {attempt + 1} 次处理尝试 ---")
            
            # 检查是否还有待处理数据
            pending_data = db.get_next_transmission_data()
            if pending_data is None:
                print("✓ 所有数据已处理完成")
                break
            
            print(f"发现待处理数据: {pending_data}")
            
            # 手动处理一条数据
            qr_gen.manual_process()
            processed_count += 1
            
            print(f"已处理 {processed_count} 条数据")
            
            # 等待一段时间再处理下一条
            if attempt < max_attempts - 1:
                print("等待3秒后处理下一条...")
                time.sleep(3)
        
        print(f"\n测试完成，共处理 {processed_count} 条数据")
        
        # 验证数据库状态
        print("\n验证数据库状态...")
        remaining_data = db.get_next_transmission_data()
        if remaining_data is None:
            print("✓ 所有测试数据已正确处理")
        else:
            print(f"⚠️  仍有未处理数据: {remaining_data}")
        
        db.close()
        print("✓ 数据库连接已关闭")
        
    except Exception as e:
        print(f"✗ 测试过程中出错: {e}")
        logging.error(f"测试二维码显示修复时出错: {e}")
        import traceback
        traceback.print_exc()

def test_single_qr_display():
    """测试单个二维码显示"""
    print("\n" + "=" * 60)
    print("单个二维码显示测试")
    print("=" * 60)
    
    try:
        db = DatabaseManager()
        qr_gen = QRGenerator(db)
        
        # 创建单个测试数据
        test_data = {
            "id": str(int(time.time() * 1000)),
            "type": 99,
            "data": "单个二维码显示测试 - 修复验证"
        }
        
        print(f"测试数据: {test_data}")
        
        # 直接生成和显示二维码
        print("生成二维码...")
        qr_image = qr_gen.generate_qr_code(test_data)
        
        if qr_image is not None:
            print("✓ 二维码生成成功")
            print("开始显示二维码...")
            print("请观察:")
            print("- 二维码是否在屏幕中央显示")
            print("- 窗口是否为900x900像素")
            print("- 是否显示2秒后自动关闭")
            
            qr_gen.display_qr_code(qr_image)
            print("✓ 二维码显示完成")
        else:
            print("✗ 二维码生成失败")
        
        db.close()
        
    except Exception as e:
        print(f"✗ 单个二维码测试出错: {e}")
        import traceback
        traceback.print_exc()

def check_display_config():
    """检查显示配置"""
    print("\n显示配置检查:")
    print("-" * 30)
    
    try:
        from config import QR_DISPLAY_TIME, QR_DISPLAY_SIZE
        print(f"二维码显示时间: {QR_DISPLAY_TIME} 秒")
        print(f"二维码显示尺寸: {QR_DISPLAY_SIZE} 像素")
        
        # 检查图形环境
        try:
            import tkinter as tk
            root = tk.Tk()
            screen_width = root.winfo_screenwidth()
            screen_height = root.winfo_screenheight()
            root.destroy()
            print(f"屏幕尺寸: {screen_width}x{screen_height}")
            print(f"居中位置: ({(screen_width-QR_DISPLAY_SIZE)//2}, {(screen_height-QR_DISPLAY_SIZE)//2})")
        except Exception as e:
            print(f"⚠️  无法获取屏幕信息: {e}")
        
        # 检查OpenCV
        try:
            import cv2
            print(f"OpenCV版本: {cv2.__version__}")
        except Exception as e:
            print(f"⚠️  OpenCV问题: {e}")
            
    except Exception as e:
        print(f"配置检查出错: {e}")
    
    print("-" * 30)

def main():
    """主测试函数"""
    print("DataTransmission 二维码显示功能修复测试")
    print("测试修复后的二维码显示功能是否正常工作")
    
    # 检查配置
    check_display_config()
    
    # 询问测试类型
    print("\n请选择测试类型:")
    print("1. 完整测试（插入数据并自动处理）")
    print("2. 单个二维码显示测试")
    print("3. 两个测试都运行")
    
    try:
        choice = input("请输入选择 (1/2/3): ").strip()
        
        if choice == "1":
            test_qr_display_fix()
        elif choice == "2":
            test_single_qr_display()
        elif choice == "3":
            test_single_qr_display()
            test_qr_display_fix()
        else:
            print("无效选择，运行完整测试")
            test_qr_display_fix()
            
    except KeyboardInterrupt:
        print("\n测试被用户中断")
    except Exception as e:
        print(f"测试选择出错: {e}")
    
    print("\n" + "=" * 60)
    print("测试完成")
    print("=" * 60)
    print("如果二维码显示正常:")
    print("✓ 应该看到二维码在屏幕中央显示")
    print("✓ 窗口尺寸应该是900x900像素")
    print("✓ 每个二维码显示2秒后自动关闭")
    print("✓ 控制台应该显示详细的处理日志")
    print("✓ 数据库状态应该正确更新")
    print()
    print("如果仍有问题:")
    print("- 检查图形环境是否正常")
    print("- 确认OpenCV安装是否正确")
    print("- 查看控制台的详细错误信息")

if __name__ == "__main__":
    main()
