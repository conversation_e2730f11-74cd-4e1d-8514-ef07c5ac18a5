#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
带连接监控的DataTransmission启动脚本
解决MySQL连接丢失问题
"""

import sys
import time
import logging
import signal
import threading
from connection_monitor import start_connection_monitoring, stop_connection_monitoring

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('datatransmission.log'),
            logging.StreamHandler()
        ]
    )

def signal_handler(signum, frame):
    """信号处理器"""
    logging.info("收到退出信号，正在安全关闭...")
    
    # 停止连接监控
    stop_connection_monitoring()
    
    # 这里可以添加其他清理代码
    # 比如停止QR生成器、Web服务器等
    
    sys.exit(0)

def main():
    """主函数"""
    # 设置日志
    setup_logging()
    
    # 注册信号处理器
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    print("DataTransmission 启动中...")
    print("=" * 50)
    
    try:
        # 1. 启动连接监控
        print("启动MySQL连接监控...")
        start_connection_monitoring()
        time.sleep(2)  # 等待监控启动
        
        # 2. 导入并启动主程序
        print("启动DataTransmission主程序...")
        from main import DataTransmissionClient
        
        # 创建客户端实例
        client = DataTransmissionClient()
        
        # 启动客户端
        client.start()
        
        print("=" * 50)
        print("DataTransmission 已启动")
        print("MySQL连接监控: 已启用")
        print("Web界面: http://localhost:5000")
        print("按 Ctrl+C 安全退出")
        print("=" * 50)
        
        # 主循环 - 保持程序运行
        while True:
            time.sleep(60)
            
    except KeyboardInterrupt:
        print("\n用户请求退出")
    except ImportError as e:
        print(f"导入主程序失败: {e}")
        print("请确保main.py文件存在且可正常导入")
    except Exception as e:
        logging.error(f"程序运行异常: {e}")
        print(f"程序异常: {e}")
    finally:
        print("正在安全关闭...")
        stop_connection_monitoring()
        print("程序已退出")

if __name__ == "__main__":
    main()
