@echo off
chcp 65001 >nul
title DataTransmission 数据库一键设置

echo ========================================
echo    DataTransmission 数据库一键设置
echo ========================================
echo.

echo 此脚本将帮助您快速设置DataTransmission所需的数据库
echo.

echo 步骤1: 检查MySQL服务
echo ----------------------------------------
sc query mysql >nul 2>&1
if %errorlevel% neq 0 (
    echo ✗ 未检测到MySQL服务
    echo.
    echo 请先安装MySQL数据库:
    echo 1. 下载MySQL 8.0安装包
    echo 2. 运行安装程序
    echo 3. 设置root用户密码
    echo 4. 确保选择"Install as Windows Service"
    echo.
    pause
    exit /b 1
)

echo ✓ MySQL服务已安装

sc query mysql | find "RUNNING" >nul
if %errorlevel% neq 0 (
    echo ⚠ MySQL服务未运行，正在启动...
    net start mysql >nul 2>&1
    if %errorlevel% neq 0 (
        echo ✗ MySQL服务启动失败
        echo 请以管理员身份运行此脚本
        pause
        exit /b 1
    )
)

echo ✓ MySQL服务正在运行

echo.
echo 步骤2: 创建数据库
echo ----------------------------------------
echo 请输入MySQL root用户的密码:
set /p mysql_password="密码: "

echo.
echo 正在创建DataTransmission数据库...
mysql -u root -p%mysql_password% < create_database.sql >nul 2>&1
if %errorlevel% equ 0 (
    echo ✓ 数据库创建成功
) else (
    echo ✗ 数据库创建失败
    echo.
    echo 可能的原因:
    echo 1. 密码错误
    echo 2. MySQL服务未正常运行
    echo 3. 权限不足
    echo.
    echo 请手动执行以下命令:
    echo mysql -u root -p
    echo CREATE DATABASE DataTransmission CHARACTER SET utf8 COLLATE utf8_general_ci;
    echo.
    pause
    exit /b 1
)

echo.
echo 步骤3: 更新配置文件
echo ----------------------------------------
echo 正在更新config.py中的数据库密码...

:: 创建临时的Python脚本来更新配置
echo import re > update_config.py
echo. >> update_config.py
echo # 读取配置文件 >> update_config.py
echo with open('config.py', 'r', encoding='utf-8') as f: >> update_config.py
echo     content = f.read() >> update_config.py
echo. >> update_config.py
echo # 更新密码 >> update_config.py
echo password = '%mysql_password%' >> update_config.py
echo content = re.sub(r"'password': '[^']*'", f"'password': '{password}'", content) >> update_config.py
echo. >> update_config.py
echo # 写回文件 >> update_config.py
echo with open('config.py', 'w', encoding='utf-8') as f: >> update_config.py
echo     f.write(content) >> update_config.py
echo. >> update_config.py
echo print('配置文件更新成功') >> update_config.py

python update_config.py
del update_config.py

echo ✓ 配置文件更新完成

echo.
echo 步骤4: 测试数据库连接
echo ----------------------------------------
echo 正在测试数据库连接...
python test_database_connection.py

echo.
echo ========================================
echo 数据库设置完成！
echo ========================================
echo.
echo 现在您可以启动DataTransmission程序:
echo 1. 双击 DataTransmission.exe
echo 2. 或运行 debug_start.bat 查看详细信息
echo.
echo 程序启动后，访问: http://localhost:5000
echo.
pause
