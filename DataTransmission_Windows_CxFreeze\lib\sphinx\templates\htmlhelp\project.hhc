{%- macro sitemap(name, docname) -%}
<OBJECT type="text/sitemap">
  <PARAM name="Name" value="{{ name|e }}" />
  <PARAM name="Local" value="{{ docname|e }}{{ suffix }}" />
</OBJECT>
{%- endmacro -%}

<!DOCTYPE HTML PUBLIC "-//IETF//DTD HTML//EN">
<HTML>
  <HEAD>
    <META name="GENERATOR" content="Microsoft&reg; HTML Help Workshop 4.1" />
    <!-- Sitemap 1.0 -->
  </HEAD>
  <BODY>
    <OBJECT type="text/site properties">
      <PARAM name="Window Styles" value="0x801227" />
      <PARAM name="ImageType" value="Folder" />
    </OBJECT>
    <UL>
      <LI>
        {{ sitemap(short_title, root_doc)|indent(8) }}
      </LI>
      {%- for indexname, indexcls, content, collapse in domain_indices %}
      <LI>
        {{ sitemap(indexcls.localname, indexname)|indent(8) }}
      </LI>
      {%- endfor %}
      {{ body|indent(6) }}
    </UL>
  </BODY>
</HTML>
