#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
CentOS 7离线程序简化构建脚本
不依赖conda环境，直接创建基于当前Python环境的部署包
"""

import os
import sys
import shutil
import subprocess
import time
from pathlib import Path

def create_simple_deployment_package():
    """创建简化的部署包"""
    print("=" * 60)
    print("创建CentOS 7离线程序包（简化版本）")
    print("=" * 60)
    
    # 创建部署目录
    timestamp = time.strftime("%Y%m%d_%H%M%S")
    deploy_dir = f"DataTransmission_CentOS7_Simple_{timestamp}"
    
    if os.path.exists(deploy_dir):
        shutil.rmtree(deploy_dir)
    
    os.makedirs(deploy_dir)
    os.makedirs(f"{deploy_dir}/src")
    os.makedirs(f"{deploy_dir}/scripts")
    os.makedirs(f"{deploy_dir}/requirements")
    
    print(f"✓ 创建部署目录: {deploy_dir}")
    
    # 复制项目文件
    project_files = ['main.py', 'database.py', 'web_server.py', 'qr_generator.py', 
                    'camera_monitor.py', 'config.py', 'README.md']
    
    for file in project_files:
        if os.path.exists(file):
            shutil.copy2(file, f"{deploy_dir}/src/")
            print(f"✓ 复制: {file}")
    
    # 导出当前环境的包列表
    print("导出Python包列表...")

    try:
        with open(f"{deploy_dir}/requirements/requirements.txt", 'w') as f:
            subprocess.run(['pip', 'freeze'], stdout=f, shell=True)
        print("✓ 导出当前环境requirements.txt")
    except Exception as e:
        print(f"⚠️  导出当前环境requirements.txt失败: {e}")

    # 创建基础依赖列表
    basic_requirements = """# DataTransmission 基础依赖包
# 如果当前环境的requirements.txt为空，请使用此列表

Flask==2.3.3
mysql-connector-python==8.1.0
opencv-python==********
qrcode[pil]==7.4.2
pyzbar==0.1.9
Pillow==10.0.1
APScheduler==3.10.4
numpy==1.24.3
requests==2.31.0

# 可选依赖
# tkinter (通常系统自带)
# zbar (系统包管理器安装)
"""

    with open(f"{deploy_dir}/requirements/requirements_basic.txt", 'w', encoding='utf-8') as f:
        f.write(basic_requirements)

    print("✓ 创建基础依赖列表")
    
    # 创建Python环境信息
    python_info = f"""# Python环境信息
Python版本: {sys.version}
Python路径: {sys.executable}
平台: {sys.platform}
"""
    
    with open(f"{deploy_dir}/requirements/python_info.txt", 'w') as f:
        f.write(python_info)
    
    print("✓ 创建Python环境信息")
    
    return deploy_dir

def create_centos7_install_script(deploy_dir):
    """创建CentOS 7安装脚本"""
    print("创建CentOS 7安装脚本...")
    
    install_script = """#!/bin/bash
# DataTransmission CentOS 7 简化安装脚本

set -e

echo "=== DataTransmission CentOS 7 简化安装 ==="

# 检查系统
if ! grep -q "CentOS Linux release 7" /etc/redhat-release 2>/dev/null; then
    echo "警告: 此脚本专为CentOS 7设计"
    read -p "是否继续? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        exit 1
    fi
fi

# 检查权限
if [ "$EUID" -ne 0 ]; then
    echo "此脚本需要root权限，请使用sudo运行"
    exit 1
fi

# 设置变量
INSTALL_DIR="/opt/DataTransmission"
CURRENT_DIR="$(pwd)"

# 安装系统依赖
echo "安装系统依赖..."
yum update -y
yum install -y epel-release
yum install -y python3 python3-pip python3-devel
yum install -y mariadb-server mariadb mariadb-devel
yum install -y zbar zbar-devel
yum install -y mesa-libGL mesa-libGL-devel
yum install -y python3-tkinter
yum install -y libX11-devel libXext-devel
yum install -y gcc gcc-c++ make
yum install -y wget curl

# 启动数据库
systemctl start mariadb
systemctl enable mariadb

# 安装Python包
echo "安装Python依赖..."
pip3 install --upgrade pip

# 尝试从requirements.txt安装
if [ -s "requirements/requirements.txt" ]; then
    echo "使用当前环境的requirements.txt..."
    pip3 install -r requirements/requirements.txt || {
        echo "从requirements.txt安装失败，使用基础依赖..."
        pip3 install -r requirements/requirements_basic.txt
    }
else
    echo "使用基础依赖列表..."
    pip3 install -r requirements/requirements_basic.txt
fi

# 安装程序
echo "安装程序..."
mkdir -p $INSTALL_DIR
cp -r src/* $INSTALL_DIR/
chown -R $(logname):$(logname) $INSTALL_DIR 2>/dev/null || chown -R $SUDO_USER:$SUDO_USER $INSTALL_DIR

# 创建启动脚本
cat > $INSTALL_DIR/start_datatransmission.sh << 'STARTEOF'
#!/bin/bash
# DataTransmission CentOS 7 启动脚本

echo "=== DataTransmission 启动中 ==="

# 设置变量
INSTALL_DIR="/opt/DataTransmission"

# 检查桌面环境
if [ -z "$DISPLAY" ]; then
    echo "错误: 未检测到图形桌面环境"
    exit 1
fi

# 设置环境变量
export DISPLAY=${DISPLAY:-:0.0}
export LD_LIBRARY_PATH=/usr/lib64:/usr/local/lib:$LD_LIBRARY_PATH

# 切换到项目目录
cd $INSTALL_DIR || {
    echo "错误: 无法进入项目目录"
    exit 1
}

echo "✓ 工作目录: $(pwd)"
echo "✓ 显示环境: $DISPLAY"
echo "✓ Python版本: $(python3 --version)"

# 检查主程序文件
if [ ! -f "main.py" ]; then
    echo "错误: 未找到主程序文件 main.py"
    exit 1
fi

echo "启动DataTransmission程序..."
echo "注意: 程序将在桌面环境中显示二维码和摄像头预览"
echo "按Ctrl+C停止程序"
echo ""

# 启动程序
python3 main.py
STARTEOF

chmod +x $INSTALL_DIR/start_datatransmission.sh

# 创建桌面快捷方式
REAL_USER=$(logname 2>/dev/null || echo $SUDO_USER)
if [ -n "$REAL_USER" ] && [ -d "/home/<USER>/Desktop" ]; then
    cat > /home/<USER>/Desktop/DataTransmission.desktop << 'DESKTOPEOF'
[Desktop Entry]
Version=1.0
Type=Application
Name=DataTransmission
Comment=数据传输客户端
Exec=/opt/DataTransmission/start_datatransmission.sh
Terminal=true
StartupNotify=true
Categories=Application;Network;
DESKTOPEOF
    
    chmod +x /home/<USER>/Desktop/DataTransmission.desktop
    chown $REAL_USER:$REAL_USER /home/<USER>/Desktop/DataTransmission.desktop
    echo "✓ 桌面快捷方式已创建"
fi

echo ""
echo "=== 安装完成 ==="
echo "下一步操作："
echo "1. 配置数据库: mysql_secure_installation"
echo "2. 创建数据库和用户"
echo "3. 配置应用: vi $INSTALL_DIR/config.py"
echo "4. 启动程序: $INSTALL_DIR/start_datatransmission.sh"
echo "5. 或双击桌面快捷方式启动"
"""
    
    with open(f"{deploy_dir}/scripts/install_centos7.sh", 'w', encoding='utf-8') as f:
        f.write(install_script)

    os.chmod(f"{deploy_dir}/scripts/install_centos7.sh", 0o755)
    print("✓ 创建安装脚本")

    # 创建数据库初始化脚本
    db_script = """-- DataTransmission 数据库初始化脚本
-- 在MariaDB中执行

CREATE DATABASE IF NOT EXISTS DataTransmission CHARACTER SET utf8 COLLATE utf8_general_ci;

USE DataTransmission;

-- 创建用户
CREATE USER IF NOT EXISTS 'datatrans'@'localhost' IDENTIFIED BY 'JKga#123';
GRANT ALL PRIVILEGES ON DataTransmission.* TO 'datatrans'@'localhost';
FLUSH PRIVILEGES;

-- 表结构将由程序自动创建

SELECT 'DataTransmission数据库初始化完成' AS message;
"""

    with open(f"{deploy_dir}/scripts/init_database.sql", 'w', encoding='utf-8') as f:
        f.write(db_script)
    
    print("✓ 创建数据库脚本")

def create_readme(deploy_dir):
    """创建README文件"""
    readme_content = f"""# DataTransmission CentOS 7 简化离线程序包

## 系统要求
- CentOS 7 桌面版本
- 图形界面环境 (GNOME/KDE)
- 至少2GB内存
- 至少1GB磁盘空间

## 安装步骤

### 1. 传输文件
将整个程序包传输到CentOS 7机器

### 2. 运行安装脚本
```bash
cd {os.path.basename(deploy_dir)}
sudo ./scripts/install_centos7.sh
```

### 3. 配置数据库
```bash
# 配置MariaDB
sudo mysql_secure_installation

# 初始化数据库
mysql -u root -p < scripts/init_database.sql
```

### 4. 配置应用程序
```bash
sudo vi /opt/DataTransmission/config.py
```

### 5. 启动程序
```bash
# 方法1: 命令行启动
/opt/DataTransmission/start_datatransmission.sh

# 方法2: 双击桌面快捷方式
```

## 功能特性
- HTTP API接口 (端口5000)
- 二维码生成和显示 (900x900像素，居中)
- 摄像头监控和二维码识别
- 实时摄像头预览窗口 (320x240像素，左上角)
- 数据库存储和管理

## 注意事项
- 此版本使用系统Python3和pip安装依赖
- 不依赖conda环境
- 适合简单部署场景

## 故障排除
1. 确保在图形桌面环境中运行
2. 检查摄像头权限和驱动
3. 确认数据库配置正确
4. 查看程序日志获取详细错误信息
"""
    
    with open(f"{deploy_dir}/README.md", 'w', encoding='utf-8') as f:
        f.write(readme_content)
    
    print("✓ 创建README文件")

def main():
    """主函数"""
    print("DataTransmission CentOS 7 简化离线程序构建工具")
    print("基于当前Python环境创建可在CentOS 7桌面版本直接运行的程序包")
    print("不依赖conda环境，使用系统Python3")
    
    # 检查Python环境
    print(f"\n当前Python版本: {sys.version}")
    print(f"Python路径: {sys.executable}")
    
    # 创建部署包
    deploy_dir = create_simple_deployment_package()
    
    # 创建CentOS 7脚本
    create_centos7_install_script(deploy_dir)
    
    # 创建README
    create_readme(deploy_dir)
    
    print("\n" + "=" * 60)
    print("CentOS 7简化离线程序包创建完成")
    print("=" * 60)
    print(f"部署包位置: {deploy_dir}")
    print("\n包含文件:")
    print("- src/                     (项目源代码)")
    print("- requirements/            (Python包依赖)")
    print("- scripts/install_centos7.sh  (安装脚本)")
    print("- scripts/init_database.sql   (数据库脚本)")
    print("- README.md                (使用说明)")
    print("\n特点:")
    print("- 不依赖conda环境")
    print("- 使用系统Python3和pip")
    print("- 简化的安装过程")
    print("- 完整的功能保留")
    print("\n部署步骤:")
    print("1. 将整个文件夹传输到CentOS 7机器")
    print("2. 运行: sudo ./scripts/install_centos7.sh")
    print("3. 配置数据库和应用程序")
    print("4. 启动程序")
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
