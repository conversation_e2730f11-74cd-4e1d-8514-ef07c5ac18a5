#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
二维码显示功能测试脚本
测试二维码是否能正确显示在屏幕中央，尺寸为900x900像素
"""

import json
import time
import logging
from database import DatabaseManager
from qr_generator import QRGenerator

# 配置日志
logging.basicConfig(level=logging.INFO)

def test_qr_display():
    """测试二维码显示功能"""
    print("开始测试二维码显示功能...")
    
    try:
        # 初始化数据库和二维码生成器
        db = DatabaseManager()
        qr_gen = QRGenerator(db)
        
        # 创建测试数据
        test_data = {
            "id": str(int(time.time() * 1000)),
            "type": 1,
            "data": "这是一个测试二维码，用于验证显示效果和位置"
        }
        
        print(f"测试数据: {test_data}")
        
        # 生成二维码图像
        qr_image = qr_gen.generate_qr_code(test_data)
        
        if qr_image is not None:
            print("二维码生成成功，即将显示...")
            print("二维码将显示在屏幕中央，尺寸为900x900像素")
            print("显示时间为2秒")
            
            # 显示二维码
            qr_gen.display_qr_code(qr_image)
            
            print("二维码显示完成")
        else:
            print("二维码生成失败")
        
        db.close()
        
    except Exception as e:
        print(f"测试过程中出错: {e}")
        logging.error(f"测试二维码显示时出错: {e}")

def test_multiple_qr_display():
    """测试连续显示多个二维码"""
    print("\n开始测试连续显示多个二维码...")
    
    try:
        db = DatabaseManager()
        qr_gen = QRGenerator(db)
        
        # 创建多个测试数据
        test_cases = [
            {"id": str(int(time.time() * 1000)), "type": 1, "data": "第一个测试二维码"},
            {"id": str(int(time.time() * 1000) + 1), "type": 2, "data": "第二个测试二维码"},
            {"id": str(int(time.time() * 1000) + 2), "type": 3, "data": "第三个测试二维码"}
        ]
        
        for i, test_data in enumerate(test_cases, 1):
            print(f"\n显示第 {i} 个二维码: {test_data}")
            
            qr_image = qr_gen.generate_qr_code(test_data)
            if qr_image is not None:
                qr_gen.display_qr_code(qr_image)
                print(f"第 {i} 个二维码显示完成")
            else:
                print(f"第 {i} 个二维码生成失败")
            
            # 间隔1秒
            time.sleep(1)
        
        db.close()
        print("\n连续显示测试完成")
        
    except Exception as e:
        print(f"连续显示测试出错: {e}")
        logging.error(f"测试连续显示时出错: {e}")

def main():
    """主测试函数"""
    print("=" * 60)
    print("二维码显示功能测试")
    print("=" * 60)
    print("测试要求:")
    print("1. 二维码应显示在屏幕中央")
    print("2. 二维码尺寸应为900x900像素")
    print("3. 显示时间为2秒")
    print("=" * 60)
    
    try:
        # 单个二维码显示测试
        test_qr_display()
        
        # 等待用户确认
        input("\n按回车键继续测试连续显示功能...")
        
        # 连续显示测试
        test_multiple_qr_display()
        
    except KeyboardInterrupt:
        print("\n测试被用户中断")
    except Exception as e:
        print(f"\n测试过程中出现异常: {e}")
    
    print("\n" + "=" * 60)
    print("测试完成")
    print("=" * 60)

if __name__ == "__main__":
    main()
