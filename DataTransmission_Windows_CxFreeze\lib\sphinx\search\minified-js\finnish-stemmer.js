FinnishStemmer=function(){var r=new BaseStemmer;var i=[["pa",-1,1],["sti",-1,2],["kaan",-1,1],["han",-1,1],["kin",-1,1],["hän",-1,1],["kään",-1,1],["ko",-1,1],["pä",-1,1],["kö",-1,1]];var e=[["lla",-1,-1],["na",-1,-1],["ssa",-1,-1],["ta",-1,-1],["lta",3,-1],["sta",3,-1]];var a=[["llä",-1,-1],["nä",-1,-1],["ssä",-1,-1],["tä",-1,-1],["ltä",3,-1],["stä",3,-1]];var s=[["lle",-1,-1],["ine",-1,-1]];var t=[["nsa",-1,3],["mme",-1,3],["nne",-1,3],["ni",-1,2],["si",-1,1],["an",-1,4],["en",-1,6],["än",-1,5],["nsä",-1,3]];var u=[["aa",-1,-1],["ee",-1,-1],["ii",-1,-1],["oo",-1,-1],["uu",-1,-1],["ää",-1,-1],["öö",-1,-1]];var l=[["a",-1,8],["lla",0,-1],["na",0,-1],["ssa",0,-1],["ta",0,-1],["lta",4,-1],["sta",4,-1],["tta",4,2],["lle",-1,-1],["ine",-1,-1],["ksi",-1,-1],["n",-1,7],["han",11,1],["den",11,-1,S],["seen",11,-1,C],["hen",11,2],["tten",11,-1,S],["hin",11,3],["siin",11,-1,S],["hon",11,4],["hän",11,5],["hön",11,6],["ä",-1,8],["llä",22,-1],["nä",22,-1],["ssä",22,-1],["tä",22,-1],["ltä",26,-1],["stä",26,-1],["ttä",26,2]];var c=[["eja",-1,-1],["mma",-1,1],["imma",1,-1],["mpa",-1,1],["impa",3,-1],["mmi",-1,1],["immi",5,-1],["mpi",-1,1],["impi",7,-1],["ejä",-1,-1],["mmä",-1,1],["immä",10,-1],["mpä",-1,1],["impä",12,-1]];var n=[["i",-1,-1],["j",-1,-1]];var f=[["mma",-1,1],["imma",0,-1]];var o=[17,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,8];var b=[119,223,119,1];var _=[17,65,16,1,0,0,0,0,0,0,0,0,0,0,0,0,8,0,32];var m=[17,65,16,0,0,0,0,0,0,0,0,0,0,0,0,0,8,0,32];var k=[17,97,24,1,0,0,0,0,0,0,0,0,0,0,0,0,8,0,32];var d=false;var v="";var w=0;var g=0;function p(){g=r.limit;w=r.limit;r:while(true){var i=r.cursor;i:{if(!r.in_grouping(_,97,246)){break i}r.cursor=i;break r}r.cursor=i;if(r.cursor>=r.limit){return false}r.cursor++}r:while(true){i:{if(!r.out_grouping(_,97,246)){break i}break r}if(r.cursor>=r.limit){return false}r.cursor++}g=r.cursor;r:while(true){var e=r.cursor;i:{if(!r.in_grouping(_,97,246)){break i}r.cursor=e;break r}r.cursor=e;if(r.cursor>=r.limit){return false}r.cursor++}r:while(true){i:{if(!r.out_grouping(_,97,246)){break i}break r}if(r.cursor>=r.limit){return false}r.cursor++}w=r.cursor;return true}function h(){if(!(w<=r.cursor)){return false}return true}function q(){var e;if(r.cursor<g){return false}var a=r.limit_backward;r.limit_backward=g;r.ket=r.cursor;e=r.find_among_b(i);if(e==0){r.limit_backward=a;return false}r.bra=r.cursor;r.limit_backward=a;switch(e){case 1:if(!r.in_grouping_b(k,97,246)){return false}break;case 2:if(!h()){return false}break}if(!r.slice_del()){return false}return true}function j(){var i;if(r.cursor<g){return false}var u=r.limit_backward;r.limit_backward=g;r.ket=r.cursor;i=r.find_among_b(t);if(i==0){r.limit_backward=u;return false}r.bra=r.cursor;r.limit_backward=u;switch(i){case 1:{var l=r.limit-r.cursor;r:{if(!r.eq_s_b("k")){break r}return false}r.cursor=r.limit-l}if(!r.slice_del()){return false}break;case 2:if(!r.slice_del()){return false}r.ket=r.cursor;if(!r.eq_s_b("kse")){return false}r.bra=r.cursor;if(!r.slice_from("ksi")){return false}break;case 3:if(!r.slice_del()){return false}break;case 4:if(r.find_among_b(e)==0){return false}if(!r.slice_del()){return false}break;case 5:if(r.find_among_b(a)==0){return false}if(!r.slice_del()){return false}break;case 6:if(r.find_among_b(s)==0){return false}if(!r.slice_del()){return false}break}return true}function C(){if(r.find_among_b(u)==0){return false}return true}function S(){if(!r.eq_s_b("i")){return false}if(!r.in_grouping_b(m,97,246)){return false}return true}function B(){var i;if(r.cursor<g){return false}var e=r.limit_backward;r.limit_backward=g;r.ket=r.cursor;i=r.find_among_b(l);if(i==0){r.limit_backward=e;return false}r.bra=r.cursor;r.limit_backward=e;switch(i){case 1:if(!r.eq_s_b("a")){return false}break;case 2:if(!r.eq_s_b("e")){return false}break;case 3:if(!r.eq_s_b("i")){return false}break;case 4:if(!r.eq_s_b("o")){return false}break;case 5:if(!r.eq_s_b("ä")){return false}break;case 6:if(!r.eq_s_b("ö")){return false}break;case 7:var a=r.limit-r.cursor;r:{var s=r.limit-r.cursor;i:{var t=r.limit-r.cursor;e:{if(!C()){break e}break i}r.cursor=r.limit-t;if(!r.eq_s_b("ie")){r.cursor=r.limit-a;break r}}r.cursor=r.limit-s;if(r.cursor<=r.limit_backward){r.cursor=r.limit-a;break r}r.cursor--;r.bra=r.cursor}break;case 8:if(!r.in_grouping_b(_,97,246)){return false}if(!r.in_grouping_b(b,98,122)){return false}break}if(!r.slice_del()){return false}d=true;return true}function F(){var i;if(r.cursor<w){return false}var e=r.limit_backward;r.limit_backward=w;r.ket=r.cursor;i=r.find_among_b(c);if(i==0){r.limit_backward=e;return false}r.bra=r.cursor;r.limit_backward=e;switch(i){case 1:{var a=r.limit-r.cursor;r:{if(!r.eq_s_b("po")){break r}return false}r.cursor=r.limit-a}break}if(!r.slice_del()){return false}return true}function W(){if(r.cursor<g){return false}var i=r.limit_backward;r.limit_backward=g;r.ket=r.cursor;if(r.find_among_b(n)==0){r.limit_backward=i;return false}r.bra=r.cursor;r.limit_backward=i;if(!r.slice_del()){return false}return true}function x(){var i;if(r.cursor<g){return false}var e=r.limit_backward;r.limit_backward=g;r.ket=r.cursor;if(!r.eq_s_b("t")){r.limit_backward=e;return false}r.bra=r.cursor;var a=r.limit-r.cursor;if(!r.in_grouping_b(_,97,246)){r.limit_backward=e;return false}r.cursor=r.limit-a;if(!r.slice_del()){return false}r.limit_backward=e;if(r.cursor<w){return false}var s=r.limit_backward;r.limit_backward=w;r.ket=r.cursor;i=r.find_among_b(f);if(i==0){r.limit_backward=s;return false}r.bra=r.cursor;r.limit_backward=s;switch(i){case 1:{var t=r.limit-r.cursor;r:{if(!r.eq_s_b("po")){break r}return false}r.cursor=r.limit-t}break}if(!r.slice_del()){return false}return true}function y(){if(r.cursor<g){return false}var i=r.limit_backward;r.limit_backward=g;var e=r.limit-r.cursor;r:{var a=r.limit-r.cursor;if(!C()){break r}r.cursor=r.limit-a;r.ket=r.cursor;if(r.cursor<=r.limit_backward){break r}r.cursor--;r.bra=r.cursor;if(!r.slice_del()){return false}}r.cursor=r.limit-e;var s=r.limit-r.cursor;r:{r.ket=r.cursor;if(!r.in_grouping_b(o,97,228)){break r}r.bra=r.cursor;if(!r.in_grouping_b(b,98,122)){break r}if(!r.slice_del()){return false}}r.cursor=r.limit-s;var t=r.limit-r.cursor;r:{r.ket=r.cursor;if(!r.eq_s_b("j")){break r}r.bra=r.cursor;i:{var u=r.limit-r.cursor;e:{if(!r.eq_s_b("o")){break e}break i}r.cursor=r.limit-u;if(!r.eq_s_b("u")){break r}}if(!r.slice_del()){return false}}r.cursor=r.limit-t;var l=r.limit-r.cursor;r:{r.ket=r.cursor;if(!r.eq_s_b("o")){break r}r.bra=r.cursor;if(!r.eq_s_b("j")){break r}if(!r.slice_del()){return false}}r.cursor=r.limit-l;r.limit_backward=i;r:while(true){var c=r.limit-r.cursor;i:{if(!r.out_grouping_b(_,97,246)){break i}r.cursor=r.limit-c;break r}r.cursor=r.limit-c;if(r.cursor<=r.limit_backward){return false}r.cursor--}r.ket=r.cursor;if(!r.in_grouping_b(b,98,122)){return false}r.bra=r.cursor;v=r.slice_to();if(v==""){return false}if(!r.eq_s_b(v)){return false}if(!r.slice_del()){return false}return true}this.stem=function(){var i=r.cursor;p();r.cursor=i;d=false;r.limit_backward=r.cursor;r.cursor=r.limit;var e=r.limit-r.cursor;q();r.cursor=r.limit-e;var a=r.limit-r.cursor;j();r.cursor=r.limit-a;var s=r.limit-r.cursor;B();r.cursor=r.limit-s;var t=r.limit-r.cursor;F();r.cursor=r.limit-t;r:{i:{if(!d){break i}var u=r.limit-r.cursor;W();r.cursor=r.limit-u;break r}var l=r.limit-r.cursor;x();r.cursor=r.limit-l}var c=r.limit-r.cursor;y();r.cursor=r.limit-c;r.cursor=r.limit_backward;return true};this["stemWord"]=function(i){r.setCurrent(i);this.stem();return r.getCurrent()}};