import qrcode
import json
import cv2
import numpy as np
import logging
import time
from database import DatabaseManager
from config import QR_DISPLAY_TIME, QR_DISPLAY_SIZE
from apscheduler.schedulers.background import BackgroundScheduler
import threading

class QRGenerator:
    def __init__(self, db_manager):
        self.db_manager = db_manager
        self.scheduler = BackgroundScheduler()
        self.is_running = False
        self.display_lock = threading.Lock()
    
    def generate_qr_code(self, data_dict):
        """生成二维码图像"""
        try:
            # 将字典转换为JSON字符串
            json_string = json.dumps(data_dict, ensure_ascii=False)
            
            # 创建二维码对象
            qr = qrcode.QRCode(
                version=1,
                error_correction=qrcode.constants.ERROR_CORRECT_L,
                box_size=10,
                border=4,
            )
            
            # 添加数据
            qr.add_data(json_string)
            qr.make(fit=True)
            
            # 创建二维码图像
            qr_img = qr.make_image(fill_color="black", back_color="white")
            
            # 转换为OpenCV格式
            qr_array = np.array(qr_img)
            if len(qr_array.shape) == 2:  # 灰度图
                qr_cv = cv2.cvtColor(qr_array.astype(np.uint8) * 255, cv2.COLOR_GRAY2BGR)
            else:
                qr_cv = cv2.cvtColor(qr_array.astype(np.uint8), cv2.COLOR_RGB2BGR)
            
            return qr_cv
            
        except Exception as e:
            logging.error(f"生成二维码时出错: {e}")
            return None
    
    def display_qr_code(self, qr_image, display_time=QR_DISPLAY_TIME):
        """显示二维码"""
        window_name = 'QR Code Display'
        try:
            with self.display_lock:
                logging.info(f"开始显示二维码，显示时间: {display_time}秒")

                # 调整图像大小为配置的尺寸
                target_size = QR_DISPLAY_SIZE
                resized_qr = cv2.resize(qr_image, (target_size, target_size), interpolation=cv2.INTER_NEAREST)

                # 创建窗口
                cv2.namedWindow(window_name, cv2.WINDOW_NORMAL)

                # 设置窗口大小
                cv2.resizeWindow(window_name, target_size, target_size)

                # 尝试获取屏幕尺寸并居中显示
                try:
                    import tkinter as tk
                    root = tk.Tk()
                    root.withdraw()  # 隐藏tkinter窗口
                    screen_width = root.winfo_screenwidth()
                    screen_height = root.winfo_screenheight()
                    root.destroy()

                    # 计算居中位置
                    x = (screen_width - target_size) // 2
                    y = (screen_height - target_size) // 2

                    logging.info(f"屏幕尺寸: {screen_width}x{screen_height}, 窗口位置: ({x}, {y})")

                except Exception as e:
                    logging.warning(f"无法获取屏幕尺寸，使用默认位置: {e}")
                    x, y = 100, 100  # 默认位置

                # 移动窗口到计算位置
                cv2.moveWindow(window_name, x, y)

                # 设置窗口属性
                try:
                    cv2.setWindowProperty(window_name, cv2.WND_PROP_TOPMOST, 1)
                except:
                    pass  # 某些系统可能不支持置顶

                # 显示二维码
                cv2.imshow(window_name, resized_qr)
                logging.info("二维码窗口已显示")

                # 等待指定时间（使用循环确保窗口保持显示）
                start_time = time.time()
                while time.time() - start_time < display_time:
                    key = cv2.waitKey(100)  # 每100ms检查一次
                    if key == 27:  # ESC键
                        logging.info("用户按ESC键关闭二维码")
                        break
                    # 检查窗口是否被关闭
                    if cv2.getWindowProperty(window_name, cv2.WND_PROP_VISIBLE) < 1:
                        logging.info("二维码窗口被用户关闭")
                        break

                logging.info(f"二维码显示完成，实际显示时间: {time.time() - start_time:.1f}秒")

        except Exception as e:
            logging.error(f"显示二维码时出错: {e}")
            import traceback
            logging.error(f"错误详情: {traceback.format_exc()}")
        finally:
            # 确保窗口被关闭
            try:
                cv2.destroyWindow(window_name)
                cv2.waitKey(1)  # 确保窗口销毁
            except:
                pass
    
    def process_transmission_data(self):
        """处理传输数据的定时任务"""
        try:
            # 获取下一条待传输的数据
            data = self.db_manager.get_next_transmission_data()
            
            if data is None:
                logging.debug("没有待传输的数据")
                return
            
            id_val, type_val, data_val = data
            
            # 构建JSON对象
            json_data = {
                "id": id_val,
                "type": type_val,
                "data": data_val
            }
            
            logging.info(f"处理传输数据: {json_data}")
            
            # 生成二维码
            qr_image = self.generate_qr_code(json_data)
            
            if qr_image is not None:
                try:
                    # 显示二维码
                    logging.info(f"准备显示二维码: id={id_val}, type={type_val}")
                    self.display_qr_code(qr_image)

                    # 只有在二维码显示完成后才更新数据库状态
                    success = self.db_manager.update_transmission_status(id_val, type_val)
                    if success:
                        logging.info(f"数据处理完成: id={id_val}, type={type_val}")
                    else:
                        logging.error(f"更新状态失败: id={id_val}, type={type_val}")

                except Exception as e:
                    logging.error(f"显示二维码过程中出错: {e}")
                    # 如果显示失败，不更新状态，下次继续尝试

            else:
                logging.error(f"生成二维码失败: id={id_val}, type={type_val}")
                
        except Exception as e:
            logging.error(f"处理传输数据时出错: {e}")
    
    def start_scheduler(self):
        """启动定时任务调度器"""
        if not self.is_running:
            # 添加定时任务，每3秒执行一次（考虑到显示时间2秒）
            self.scheduler.add_job(
                func=self.process_transmission_data,
                trigger="interval",
                seconds=3,
                id='qr_generation_job'
            )
            
            self.scheduler.start()
            self.is_running = True
            logging.info("二维码生成定时任务已启动")
    
    def stop_scheduler(self):
        """停止定时任务调度器"""
        if self.is_running:
            self.scheduler.shutdown()
            self.is_running = False
            logging.info("二维码生成定时任务已停止")
    
    def manual_process(self):
        """手动处理一次传输数据（用于测试）"""
        self.process_transmission_data()
