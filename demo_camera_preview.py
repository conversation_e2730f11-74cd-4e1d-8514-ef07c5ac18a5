#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
摄像头预览功能演示脚本
展示新增的实时预览窗口功能和特性
"""

import time
import json
import logging
import threading
from database import DatabaseManager
from camera_monitor import CameraMonitor

# 配置日志
logging.basicConfig(level=logging.INFO)

def create_demo_data():
    """创建演示用的二维码数据"""
    demo_data = [
        {"id": str(int(time.time() * 1000)), "type": 1, "data": "演示数据1 - 用户登录"},
        {"id": str(int(time.time() * 1000) + 1), "type": 2, "data": "演示数据2 - 设备注册"},
        {"id": str(int(time.time() * 1000) + 2), "type": 3, "data": "演示数据3 - 数据传输"},
    ]
    return demo_data

def demo_preview_features():
    """演示预览窗口功能"""
    print("=" * 60)
    print("DataTransmission 摄像头预览功能演示")
    print("=" * 60)
    print()
    print("新增功能特性:")
    print("✓ 左上角实时摄像头预览窗口")
    print("✓ 预览窗口尺寸: 320x240 像素")
    print("✓ 实时显示摄像头画面")
    print("✓ 二维码检测时显示绿色边框")
    print("✓ 显示摄像头状态和检测统计")
    print("✓ 显示实时时间戳")
    print("✓ 可手动关闭预览窗口")
    print("✓ 关闭后需重启服务才能重新开启")
    print()
    
    try:
        # 初始化组件
        db = DatabaseManager()
        camera = CameraMonitor(db)
        
        print("正在启动摄像头监控...")
        if camera.start_monitoring():
            print("✓ 摄像头监控已启动")
            print("✓ 预览窗口应该已在左上角显示")
            print()
            
            # 演示说明
            print("演示说明:")
            print("1. 预览窗口位置: 屏幕左上角 (10, 10)")
            print("2. 窗口内容:")
            print("   - 实时摄像头画面")
            print("   - 顶部: 摄像头状态 (Camera: 0 | QR: X detected)")
            print("   - 底部: 当前时间戳")
            print("   - 二维码检测框: 绿色边框 + 类型标签")
            print()
            print("3. 交互功能:")
            print("   - 点击窗口X按钮可关闭预览")
            print("   - 关闭后程序继续运行，但预览禁用")
            print("   - 需要重启程序才能重新启用预览")
            print()
            
            # 创建演示数据
            demo_data = create_demo_data()
            print("演示数据已准备:")
            for i, data in enumerate(demo_data, 1):
                print(f"  {i}. {data}")
            print()
            
            print("请使用手机或其他设备生成包含以上JSON数据的二维码进行测试")
            print("或者使用任何二维码进行测试")
            print()
            print("观察要点:")
            print("- 预览窗口是否正常显示")
            print("- 二维码检测时是否显示绿色边框")
            print("- 状态信息是否正确更新")
            print("- 时间戳是否实时更新")
            print()
            
            # 运行演示
            start_time = time.time()
            qr_detected_count = 0
            
            try:
                print("演示运行中... (按 Ctrl+C 停止)")
                print("=" * 60)
                
                while True:
                    time.sleep(2)
                    
                    # 检查运行时间
                    elapsed = time.time() - start_time
                    
                    # 检查预览窗口状态
                    if camera.preview_closed:
                        print(f"\n[{time.strftime('%H:%M:%S')}] 检测到预览窗口已被用户关闭")
                        print("摄像头监控继续运行，但预览已禁用")
                        print("要重新启用预览，请重启程序")
                        break
                    
                    # 显示运行状态
                    if int(elapsed) % 10 == 0:  # 每10秒显示一次状态
                        print(f"[{time.strftime('%H:%M:%S')}] 运行时间: {int(elapsed)}秒 | 预览状态: {'正常' if camera.preview_enabled else '已关闭'}")
                    
                    # 模拟检测统计（实际统计由摄像头监控提供）
                    if hasattr(camera, 'qr_detected_count'):
                        if camera.qr_detected_count != qr_detected_count:
                            qr_detected_count = camera.qr_detected_count
                            print(f"[{time.strftime('%H:%M:%S')}] 检测到二维码! 总计: {qr_detected_count}")
                
            except KeyboardInterrupt:
                print(f"\n[{time.strftime('%H:%M:%S')}] 用户停止演示")
            
            # 停止监控
            print("\n正在停止摄像头监控...")
            camera.stop_monitoring()
            print("✓ 摄像头监控已停止")
            
        else:
            print("✗ 摄像头监控启动失败")
            print("\n可能的原因:")
            print("- 摄像头未连接或被其他程序占用")
            print("- 摄像头驱动问题")
            print("- config.py中的CAMERA_INDEX设置错误")
            print("\n解决方法:")
            print("- 检查摄像头连接")
            print("- 关闭其他使用摄像头的程序")
            print("- 修改config.py中的CAMERA_INDEX值")
        
        db.close()
        
    except Exception as e:
        print(f"✗ 演示过程中出错: {e}")
        logging.error(f"演示摄像头预览时出错: {e}")

def show_config_info():
    """显示配置信息"""
    print("当前配置信息:")
    print("-" * 30)
    
    try:
        from config import (CAMERA_PREVIEW_ENABLED, CAMERA_PREVIEW_SIZE, 
                           CAMERA_PREVIEW_POSITION, CAMERA_INDEX, CAPTURE_INTERVAL)
        
        print(f"摄像头索引: {CAMERA_INDEX}")
        print(f"截图间隔: {CAPTURE_INTERVAL}秒")
        print(f"预览启用: {CAMERA_PREVIEW_ENABLED}")
        print(f"预览尺寸: {CAMERA_PREVIEW_SIZE[0]}x{CAMERA_PREVIEW_SIZE[1]} 像素")
        print(f"预览位置: ({CAMERA_PREVIEW_POSITION[0]}, {CAMERA_PREVIEW_POSITION[1]})")
        
        if not CAMERA_PREVIEW_ENABLED:
            print("\n⚠️  预览功能已在配置中禁用")
            print("要启用预览，请在config.py中设置:")
            print("CAMERA_PREVIEW_ENABLED = True")
        
    except ImportError as e:
        print(f"无法读取配置: {e}")
    
    print("-" * 30)
    print()

def main():
    """主演示函数"""
    print("DataTransmission 摄像头预览功能演示")
    print("展示新增的实时预览窗口功能")
    print()
    
    # 显示配置信息
    show_config_info()
    
    # 询问是否开始演示
    try:
        response = input("是否开始摄像头预览功能演示? (y/N): ")
        if response.lower() != 'y':
            print("演示已取消")
            return
    except KeyboardInterrupt:
        print("\n演示已取消")
        return
    
    # 运行演示
    demo_preview_features()
    
    print("\n" + "=" * 60)
    print("演示完成")
    print("=" * 60)
    print("功能总结:")
    print("✓ 实时摄像头预览窗口 (左上角)")
    print("✓ 二维码检测可视化 (绿色边框)")
    print("✓ 状态信息显示 (摄像头状态、检测统计)")
    print("✓ 实时时间戳显示")
    print("✓ 手动关闭功能 (点击X按钮)")
    print("✓ 关闭后需重启程序重新启用")
    print()
    print("这个预览功能大大提升了调试和监控的便利性!")

if __name__ == "__main__":
    main()
