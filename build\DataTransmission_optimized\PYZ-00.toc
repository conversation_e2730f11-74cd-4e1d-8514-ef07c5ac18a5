('E:\\01.CODE\\01.JK<PERSON>\\DataTransmission\\build\\DataTransmission_optimized\\PYZ-00.pyz',
 [('PIL',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\PIL\\__init__.py',
   'PYMODULE'),
  ('PIL.BlpImagePlugin',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\PIL\\BlpImagePlugin.py',
   'PYMODULE'),
  ('PIL.BmpImagePlugin',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\PIL\\BmpImagePlugin.py',
   'PYMODULE'),
  ('PIL.BufrStubImagePlugin',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\PIL\\BufrStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.CurImagePlugin',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\PIL\\CurImagePlugin.py',
   'PYMODULE'),
  ('PIL.DcxImagePlugin',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\PIL\\DcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.DdsImagePlugin',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\PIL\\DdsImagePlugin.py',
   'PYMODULE'),
  ('PIL.EpsImagePlugin',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\PIL\\EpsImagePlugin.py',
   'PYMODULE'),
  ('PIL.ExifTags',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\PIL\\ExifTags.py',
   'PYMODULE'),
  ('PIL.FitsImagePlugin',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\PIL\\FitsImagePlugin.py',
   'PYMODULE'),
  ('PIL.FliImagePlugin',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\PIL\\FliImagePlugin.py',
   'PYMODULE'),
  ('PIL.FpxImagePlugin',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\PIL\\FpxImagePlugin.py',
   'PYMODULE'),
  ('PIL.FtexImagePlugin',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\PIL\\FtexImagePlugin.py',
   'PYMODULE'),
  ('PIL.GbrImagePlugin',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\PIL\\GbrImagePlugin.py',
   'PYMODULE'),
  ('PIL.GifImagePlugin',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\PIL\\GifImagePlugin.py',
   'PYMODULE'),
  ('PIL.GimpGradientFile',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\PIL\\GimpGradientFile.py',
   'PYMODULE'),
  ('PIL.GimpPaletteFile',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\PIL\\GimpPaletteFile.py',
   'PYMODULE'),
  ('PIL.GribStubImagePlugin',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\PIL\\GribStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.Hdf5StubImagePlugin',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\PIL\\Hdf5StubImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcnsImagePlugin',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\PIL\\IcnsImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcoImagePlugin',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\PIL\\IcoImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImImagePlugin',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\PIL\\ImImagePlugin.py',
   'PYMODULE'),
  ('PIL.Image',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\PIL\\Image.py',
   'PYMODULE'),
  ('PIL.ImageChops',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\PIL\\ImageChops.py',
   'PYMODULE'),
  ('PIL.ImageCms',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\PIL\\ImageCms.py',
   'PYMODULE'),
  ('PIL.ImageColor',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\PIL\\ImageColor.py',
   'PYMODULE'),
  ('PIL.ImageDraw',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\PIL\\ImageDraw.py',
   'PYMODULE'),
  ('PIL.ImageDraw2',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\PIL\\ImageDraw2.py',
   'PYMODULE'),
  ('PIL.ImageFile',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\PIL\\ImageFile.py',
   'PYMODULE'),
  ('PIL.ImageFilter',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\PIL\\ImageFilter.py',
   'PYMODULE'),
  ('PIL.ImageFont',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\PIL\\ImageFont.py',
   'PYMODULE'),
  ('PIL.ImageMode',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\PIL\\ImageMode.py',
   'PYMODULE'),
  ('PIL.ImageOps',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\PIL\\ImageOps.py',
   'PYMODULE'),
  ('PIL.ImagePalette',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\PIL\\ImagePalette.py',
   'PYMODULE'),
  ('PIL.ImagePath',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\PIL\\ImagePath.py',
   'PYMODULE'),
  ('PIL.ImageQt',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\PIL\\ImageQt.py',
   'PYMODULE'),
  ('PIL.ImageSequence',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\PIL\\ImageSequence.py',
   'PYMODULE'),
  ('PIL.ImageShow',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\PIL\\ImageShow.py',
   'PYMODULE'),
  ('PIL.ImageTk',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\PIL\\ImageTk.py',
   'PYMODULE'),
  ('PIL.ImageWin',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\PIL\\ImageWin.py',
   'PYMODULE'),
  ('PIL.ImtImagePlugin',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\PIL\\ImtImagePlugin.py',
   'PYMODULE'),
  ('PIL.IptcImagePlugin',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\PIL\\IptcImagePlugin.py',
   'PYMODULE'),
  ('PIL.Jpeg2KImagePlugin',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\PIL\\Jpeg2KImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegImagePlugin',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\PIL\\JpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegPresets',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\PIL\\JpegPresets.py',
   'PYMODULE'),
  ('PIL.McIdasImagePlugin',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\PIL\\McIdasImagePlugin.py',
   'PYMODULE'),
  ('PIL.MicImagePlugin',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\PIL\\MicImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpegImagePlugin',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\PIL\\MpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpoImagePlugin',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\PIL\\MpoImagePlugin.py',
   'PYMODULE'),
  ('PIL.MspImagePlugin',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\PIL\\MspImagePlugin.py',
   'PYMODULE'),
  ('PIL.PaletteFile',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\PIL\\PaletteFile.py',
   'PYMODULE'),
  ('PIL.PalmImagePlugin',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\PIL\\PalmImagePlugin.py',
   'PYMODULE'),
  ('PIL.PcdImagePlugin',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\PIL\\PcdImagePlugin.py',
   'PYMODULE'),
  ('PIL.PcxImagePlugin',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\PIL\\PcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.PdfImagePlugin',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\PIL\\PdfImagePlugin.py',
   'PYMODULE'),
  ('PIL.PdfParser',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\PIL\\PdfParser.py',
   'PYMODULE'),
  ('PIL.PixarImagePlugin',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\PIL\\PixarImagePlugin.py',
   'PYMODULE'),
  ('PIL.PngImagePlugin',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\PIL\\PngImagePlugin.py',
   'PYMODULE'),
  ('PIL.PpmImagePlugin',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\PIL\\PpmImagePlugin.py',
   'PYMODULE'),
  ('PIL.PsdImagePlugin',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\PIL\\PsdImagePlugin.py',
   'PYMODULE'),
  ('PIL.PyAccess',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\PIL\\PyAccess.py',
   'PYMODULE'),
  ('PIL.QoiImagePlugin',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\PIL\\QoiImagePlugin.py',
   'PYMODULE'),
  ('PIL.SgiImagePlugin',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\PIL\\SgiImagePlugin.py',
   'PYMODULE'),
  ('PIL.SpiderImagePlugin',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\PIL\\SpiderImagePlugin.py',
   'PYMODULE'),
  ('PIL.SunImagePlugin',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\PIL\\SunImagePlugin.py',
   'PYMODULE'),
  ('PIL.TgaImagePlugin',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\PIL\\TgaImagePlugin.py',
   'PYMODULE'),
  ('PIL.TiffImagePlugin',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\PIL\\TiffImagePlugin.py',
   'PYMODULE'),
  ('PIL.TiffTags',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\PIL\\TiffTags.py',
   'PYMODULE'),
  ('PIL.WebPImagePlugin',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\PIL\\WebPImagePlugin.py',
   'PYMODULE'),
  ('PIL.WmfImagePlugin',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\PIL\\WmfImagePlugin.py',
   'PYMODULE'),
  ('PIL.XVThumbImagePlugin',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\PIL\\XVThumbImagePlugin.py',
   'PYMODULE'),
  ('PIL.XbmImagePlugin',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\PIL\\XbmImagePlugin.py',
   'PYMODULE'),
  ('PIL.XpmImagePlugin',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\PIL\\XpmImagePlugin.py',
   'PYMODULE'),
  ('PIL._binary',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\PIL\\_binary.py',
   'PYMODULE'),
  ('PIL._deprecate',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\PIL\\_deprecate.py',
   'PYMODULE'),
  ('PIL._tkinter_finder',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\PIL\\_tkinter_finder.py',
   'PYMODULE'),
  ('PIL._util',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\PIL\\_util.py',
   'PYMODULE'),
  ('PIL._version',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\PIL\\_version.py',
   'PYMODULE'),
  ('PIL.features',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\PIL\\features.py',
   'PYMODULE'),
  ('__future__', 'D:\\anaconda3\\envs\\dt\\lib\\__future__.py', 'PYMODULE'),
  ('_aix_support', 'D:\\anaconda3\\envs\\dt\\lib\\_aix_support.py', 'PYMODULE'),
  ('_bootsubprocess',
   'D:\\anaconda3\\envs\\dt\\lib\\_bootsubprocess.py',
   'PYMODULE'),
  ('_compat_pickle',
   'D:\\anaconda3\\envs\\dt\\lib\\_compat_pickle.py',
   'PYMODULE'),
  ('_compression', 'D:\\anaconda3\\envs\\dt\\lib\\_compression.py', 'PYMODULE'),
  ('_py_abc', 'D:\\anaconda3\\envs\\dt\\lib\\_py_abc.py', 'PYMODULE'),
  ('_pydecimal', 'D:\\anaconda3\\envs\\dt\\lib\\_pydecimal.py', 'PYMODULE'),
  ('_strptime', 'D:\\anaconda3\\envs\\dt\\lib\\_strptime.py', 'PYMODULE'),
  ('_threading_local',
   'D:\\anaconda3\\envs\\dt\\lib\\_threading_local.py',
   'PYMODULE'),
  ('apscheduler',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\apscheduler\\__init__.py',
   'PYMODULE'),
  ('apscheduler.events',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\apscheduler\\events.py',
   'PYMODULE'),
  ('apscheduler.executors',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\apscheduler\\executors\\__init__.py',
   'PYMODULE'),
  ('apscheduler.executors.asyncio',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\apscheduler\\executors\\asyncio.py',
   'PYMODULE'),
  ('apscheduler.executors.base',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\apscheduler\\executors\\base.py',
   'PYMODULE'),
  ('apscheduler.executors.base_py3',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\apscheduler\\executors\\base_py3.py',
   'PYMODULE'),
  ('apscheduler.executors.debug',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\apscheduler\\executors\\debug.py',
   'PYMODULE'),
  ('apscheduler.executors.gevent',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\apscheduler\\executors\\gevent.py',
   'PYMODULE'),
  ('apscheduler.executors.pool',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\apscheduler\\executors\\pool.py',
   'PYMODULE'),
  ('apscheduler.executors.tornado',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\apscheduler\\executors\\tornado.py',
   'PYMODULE'),
  ('apscheduler.executors.twisted',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\apscheduler\\executors\\twisted.py',
   'PYMODULE'),
  ('apscheduler.job',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\apscheduler\\job.py',
   'PYMODULE'),
  ('apscheduler.jobstores',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\apscheduler\\jobstores\\__init__.py',
   'PYMODULE'),
  ('apscheduler.jobstores.base',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\apscheduler\\jobstores\\base.py',
   'PYMODULE'),
  ('apscheduler.jobstores.memory',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\apscheduler\\jobstores\\memory.py',
   'PYMODULE'),
  ('apscheduler.jobstores.mongodb',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\apscheduler\\jobstores\\mongodb.py',
   'PYMODULE'),
  ('apscheduler.jobstores.redis',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\apscheduler\\jobstores\\redis.py',
   'PYMODULE'),
  ('apscheduler.jobstores.rethinkdb',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\apscheduler\\jobstores\\rethinkdb.py',
   'PYMODULE'),
  ('apscheduler.jobstores.sqlalchemy',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\apscheduler\\jobstores\\sqlalchemy.py',
   'PYMODULE'),
  ('apscheduler.jobstores.zookeeper',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\apscheduler\\jobstores\\zookeeper.py',
   'PYMODULE'),
  ('apscheduler.schedulers',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\apscheduler\\schedulers\\__init__.py',
   'PYMODULE'),
  ('apscheduler.schedulers.asyncio',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\apscheduler\\schedulers\\asyncio.py',
   'PYMODULE'),
  ('apscheduler.schedulers.background',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\apscheduler\\schedulers\\background.py',
   'PYMODULE'),
  ('apscheduler.schedulers.base',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\apscheduler\\schedulers\\base.py',
   'PYMODULE'),
  ('apscheduler.schedulers.blocking',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\apscheduler\\schedulers\\blocking.py',
   'PYMODULE'),
  ('apscheduler.schedulers.gevent',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\apscheduler\\schedulers\\gevent.py',
   'PYMODULE'),
  ('apscheduler.schedulers.qt',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\apscheduler\\schedulers\\qt.py',
   'PYMODULE'),
  ('apscheduler.schedulers.tornado',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\apscheduler\\schedulers\\tornado.py',
   'PYMODULE'),
  ('apscheduler.schedulers.twisted',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\apscheduler\\schedulers\\twisted.py',
   'PYMODULE'),
  ('apscheduler.triggers',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\apscheduler\\triggers\\__init__.py',
   'PYMODULE'),
  ('apscheduler.triggers.base',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\apscheduler\\triggers\\base.py',
   'PYMODULE'),
  ('apscheduler.triggers.combining',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\apscheduler\\triggers\\combining.py',
   'PYMODULE'),
  ('apscheduler.triggers.cron',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\apscheduler\\triggers\\cron\\__init__.py',
   'PYMODULE'),
  ('apscheduler.triggers.cron.expressions',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\apscheduler\\triggers\\cron\\expressions.py',
   'PYMODULE'),
  ('apscheduler.triggers.cron.fields',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\apscheduler\\triggers\\cron\\fields.py',
   'PYMODULE'),
  ('apscheduler.triggers.date',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\apscheduler\\triggers\\date.py',
   'PYMODULE'),
  ('apscheduler.triggers.interval',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\apscheduler\\triggers\\interval.py',
   'PYMODULE'),
  ('apscheduler.util',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\apscheduler\\util.py',
   'PYMODULE'),
  ('argparse', 'D:\\anaconda3\\envs\\dt\\lib\\argparse.py', 'PYMODULE'),
  ('ast', 'D:\\anaconda3\\envs\\dt\\lib\\ast.py', 'PYMODULE'),
  ('base64', 'D:\\anaconda3\\envs\\dt\\lib\\base64.py', 'PYMODULE'),
  ('bdb', 'D:\\anaconda3\\envs\\dt\\lib\\bdb.py', 'PYMODULE'),
  ('blinker',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\blinker\\__init__.py',
   'PYMODULE'),
  ('blinker._utilities',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\blinker\\_utilities.py',
   'PYMODULE'),
  ('blinker.base',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\blinker\\base.py',
   'PYMODULE'),
  ('bz2', 'D:\\anaconda3\\envs\\dt\\lib\\bz2.py', 'PYMODULE'),
  ('calendar', 'D:\\anaconda3\\envs\\dt\\lib\\calendar.py', 'PYMODULE'),
  ('camera_monitor',
   'E:\\01.CODE\\01.JKGA\\DataTransmission\\camera_monitor.py',
   'PYMODULE'),
  ('certifi',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\certifi\\__init__.py',
   'PYMODULE'),
  ('certifi.core',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\certifi\\core.py',
   'PYMODULE'),
  ('charset_normalizer',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\charset_normalizer\\__init__.py',
   'PYMODULE'),
  ('charset_normalizer.api',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\charset_normalizer\\api.py',
   'PYMODULE'),
  ('charset_normalizer.cd',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\charset_normalizer\\cd.py',
   'PYMODULE'),
  ('charset_normalizer.constant',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\charset_normalizer\\constant.py',
   'PYMODULE'),
  ('charset_normalizer.legacy',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\charset_normalizer\\legacy.py',
   'PYMODULE'),
  ('charset_normalizer.models',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\charset_normalizer\\models.py',
   'PYMODULE'),
  ('charset_normalizer.utils',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\charset_normalizer\\utils.py',
   'PYMODULE'),
  ('charset_normalizer.version',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\charset_normalizer\\version.py',
   'PYMODULE'),
  ('click',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\click\\__init__.py',
   'PYMODULE'),
  ('click._compat',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\click\\_compat.py',
   'PYMODULE'),
  ('click._termui_impl',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\click\\_termui_impl.py',
   'PYMODULE'),
  ('click._textwrap',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\click\\_textwrap.py',
   'PYMODULE'),
  ('click._winconsole',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\click\\_winconsole.py',
   'PYMODULE'),
  ('click.core',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\click\\core.py',
   'PYMODULE'),
  ('click.decorators',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\click\\decorators.py',
   'PYMODULE'),
  ('click.exceptions',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\click\\exceptions.py',
   'PYMODULE'),
  ('click.formatting',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\click\\formatting.py',
   'PYMODULE'),
  ('click.globals',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\click\\globals.py',
   'PYMODULE'),
  ('click.parser',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\click\\parser.py',
   'PYMODULE'),
  ('click.shell_completion',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\click\\shell_completion.py',
   'PYMODULE'),
  ('click.termui',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\click\\termui.py',
   'PYMODULE'),
  ('click.testing',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\click\\testing.py',
   'PYMODULE'),
  ('click.types',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\click\\types.py',
   'PYMODULE'),
  ('click.utils',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\click\\utils.py',
   'PYMODULE'),
  ('code', 'D:\\anaconda3\\envs\\dt\\lib\\code.py', 'PYMODULE'),
  ('codeop', 'D:\\anaconda3\\envs\\dt\\lib\\codeop.py', 'PYMODULE'),
  ('colorama',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\colorama\\__init__.py',
   'PYMODULE'),
  ('colorama.ansi',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\colorama\\ansi.py',
   'PYMODULE'),
  ('colorama.ansitowin32',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\colorama\\ansitowin32.py',
   'PYMODULE'),
  ('colorama.initialise',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\colorama\\initialise.py',
   'PYMODULE'),
  ('colorama.win32',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\colorama\\win32.py',
   'PYMODULE'),
  ('colorama.winterm',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\colorama\\winterm.py',
   'PYMODULE'),
  ('colorsys', 'D:\\anaconda3\\envs\\dt\\lib\\colorsys.py', 'PYMODULE'),
  ('config', 'E:\\01.CODE\\01.JKGA\\DataTransmission\\config.py', 'PYMODULE'),
  ('configparser', 'D:\\anaconda3\\envs\\dt\\lib\\configparser.py', 'PYMODULE'),
  ('contextlib', 'D:\\anaconda3\\envs\\dt\\lib\\contextlib.py', 'PYMODULE'),
  ('contextvars', 'D:\\anaconda3\\envs\\dt\\lib\\contextvars.py', 'PYMODULE'),
  ('copy', 'D:\\anaconda3\\envs\\dt\\lib\\copy.py', 'PYMODULE'),
  ('csv', 'D:\\anaconda3\\envs\\dt\\lib\\csv.py', 'PYMODULE'),
  ('ctypes', 'D:\\anaconda3\\envs\\dt\\lib\\ctypes\\__init__.py', 'PYMODULE'),
  ('ctypes._aix', 'D:\\anaconda3\\envs\\dt\\lib\\ctypes\\_aix.py', 'PYMODULE'),
  ('ctypes._endian',
   'D:\\anaconda3\\envs\\dt\\lib\\ctypes\\_endian.py',
   'PYMODULE'),
  ('ctypes.macholib',
   'D:\\anaconda3\\envs\\dt\\lib\\ctypes\\macholib\\__init__.py',
   'PYMODULE'),
  ('ctypes.macholib.dyld',
   'D:\\anaconda3\\envs\\dt\\lib\\ctypes\\macholib\\dyld.py',
   'PYMODULE'),
  ('ctypes.macholib.dylib',
   'D:\\anaconda3\\envs\\dt\\lib\\ctypes\\macholib\\dylib.py',
   'PYMODULE'),
  ('ctypes.macholib.framework',
   'D:\\anaconda3\\envs\\dt\\lib\\ctypes\\macholib\\framework.py',
   'PYMODULE'),
  ('ctypes.util', 'D:\\anaconda3\\envs\\dt\\lib\\ctypes\\util.py', 'PYMODULE'),
  ('ctypes.wintypes',
   'D:\\anaconda3\\envs\\dt\\lib\\ctypes\\wintypes.py',
   'PYMODULE'),
  ('database',
   'E:\\01.CODE\\01.JKGA\\DataTransmission\\database.py',
   'PYMODULE'),
  ('dataclasses', 'D:\\anaconda3\\envs\\dt\\lib\\dataclasses.py', 'PYMODULE'),
  ('datetime', 'D:\\anaconda3\\envs\\dt\\lib\\datetime.py', 'PYMODULE'),
  ('decimal', 'D:\\anaconda3\\envs\\dt\\lib\\decimal.py', 'PYMODULE'),
  ('dis', 'D:\\anaconda3\\envs\\dt\\lib\\dis.py', 'PYMODULE'),
  ('fileinput', 'D:\\anaconda3\\envs\\dt\\lib\\fileinput.py', 'PYMODULE'),
  ('flask',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\flask\\__init__.py',
   'PYMODULE'),
  ('flask.app',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\flask\\app.py',
   'PYMODULE'),
  ('flask.blueprints',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\flask\\blueprints.py',
   'PYMODULE'),
  ('flask.cli',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\flask\\cli.py',
   'PYMODULE'),
  ('flask.config',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\flask\\config.py',
   'PYMODULE'),
  ('flask.ctx',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\flask\\ctx.py',
   'PYMODULE'),
  ('flask.debughelpers',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\flask\\debughelpers.py',
   'PYMODULE'),
  ('flask.globals',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\flask\\globals.py',
   'PYMODULE'),
  ('flask.helpers',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\flask\\helpers.py',
   'PYMODULE'),
  ('flask.json',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\flask\\json\\__init__.py',
   'PYMODULE'),
  ('flask.json.provider',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\flask\\json\\provider.py',
   'PYMODULE'),
  ('flask.json.tag',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\flask\\json\\tag.py',
   'PYMODULE'),
  ('flask.logging',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\flask\\logging.py',
   'PYMODULE'),
  ('flask.scaffold',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\flask\\scaffold.py',
   'PYMODULE'),
  ('flask.sessions',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\flask\\sessions.py',
   'PYMODULE'),
  ('flask.signals',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\flask\\signals.py',
   'PYMODULE'),
  ('flask.templating',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\flask\\templating.py',
   'PYMODULE'),
  ('flask.testing',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\flask\\testing.py',
   'PYMODULE'),
  ('flask.typing',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\flask\\typing.py',
   'PYMODULE'),
  ('flask.wrappers',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\flask\\wrappers.py',
   'PYMODULE'),
  ('fnmatch', 'D:\\anaconda3\\envs\\dt\\lib\\fnmatch.py', 'PYMODULE'),
  ('fractions', 'D:\\anaconda3\\envs\\dt\\lib\\fractions.py', 'PYMODULE'),
  ('ftplib', 'D:\\anaconda3\\envs\\dt\\lib\\ftplib.py', 'PYMODULE'),
  ('getopt', 'D:\\anaconda3\\envs\\dt\\lib\\getopt.py', 'PYMODULE'),
  ('getpass', 'D:\\anaconda3\\envs\\dt\\lib\\getpass.py', 'PYMODULE'),
  ('gettext', 'D:\\anaconda3\\envs\\dt\\lib\\gettext.py', 'PYMODULE'),
  ('glob', 'D:\\anaconda3\\envs\\dt\\lib\\glob.py', 'PYMODULE'),
  ('gzip', 'D:\\anaconda3\\envs\\dt\\lib\\gzip.py', 'PYMODULE'),
  ('hashlib', 'D:\\anaconda3\\envs\\dt\\lib\\hashlib.py', 'PYMODULE'),
  ('hmac', 'D:\\anaconda3\\envs\\dt\\lib\\hmac.py', 'PYMODULE'),
  ('http', 'D:\\anaconda3\\envs\\dt\\lib\\http\\__init__.py', 'PYMODULE'),
  ('http.client', 'D:\\anaconda3\\envs\\dt\\lib\\http\\client.py', 'PYMODULE'),
  ('http.cookiejar',
   'D:\\anaconda3\\envs\\dt\\lib\\http\\cookiejar.py',
   'PYMODULE'),
  ('http.cookies',
   'D:\\anaconda3\\envs\\dt\\lib\\http\\cookies.py',
   'PYMODULE'),
  ('http.server', 'D:\\anaconda3\\envs\\dt\\lib\\http\\server.py', 'PYMODULE'),
  ('idna',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\idna\\__init__.py',
   'PYMODULE'),
  ('idna.core',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\idna\\core.py',
   'PYMODULE'),
  ('idna.idnadata',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\idna\\idnadata.py',
   'PYMODULE'),
  ('idna.intranges',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\idna\\intranges.py',
   'PYMODULE'),
  ('idna.package_data',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\idna\\package_data.py',
   'PYMODULE'),
  ('idna.uts46data',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\idna\\uts46data.py',
   'PYMODULE'),
  ('importlib',
   'D:\\anaconda3\\envs\\dt\\lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('importlib._abc',
   'D:\\anaconda3\\envs\\dt\\lib\\importlib\\_abc.py',
   'PYMODULE'),
  ('importlib._adapters',
   'D:\\anaconda3\\envs\\dt\\lib\\importlib\\_adapters.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   'D:\\anaconda3\\envs\\dt\\lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'D:\\anaconda3\\envs\\dt\\lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib._common',
   'D:\\anaconda3\\envs\\dt\\lib\\importlib\\_common.py',
   'PYMODULE'),
  ('importlib.abc',
   'D:\\anaconda3\\envs\\dt\\lib\\importlib\\abc.py',
   'PYMODULE'),
  ('importlib.machinery',
   'D:\\anaconda3\\envs\\dt\\lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib.metadata',
   'D:\\anaconda3\\envs\\dt\\lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   'D:\\anaconda3\\envs\\dt\\lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'D:\\anaconda3\\envs\\dt\\lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'D:\\anaconda3\\envs\\dt\\lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   'D:\\anaconda3\\envs\\dt\\lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'D:\\anaconda3\\envs\\dt\\lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'D:\\anaconda3\\envs\\dt\\lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('importlib.readers',
   'D:\\anaconda3\\envs\\dt\\lib\\importlib\\readers.py',
   'PYMODULE'),
  ('importlib.resources',
   'D:\\anaconda3\\envs\\dt\\lib\\importlib\\resources.py',
   'PYMODULE'),
  ('importlib.util',
   'D:\\anaconda3\\envs\\dt\\lib\\importlib\\util.py',
   'PYMODULE'),
  ('inspect', 'D:\\anaconda3\\envs\\dt\\lib\\inspect.py', 'PYMODULE'),
  ('ipaddress', 'D:\\anaconda3\\envs\\dt\\lib\\ipaddress.py', 'PYMODULE'),
  ('itsdangerous',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\itsdangerous\\__init__.py',
   'PYMODULE'),
  ('itsdangerous._json',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\itsdangerous\\_json.py',
   'PYMODULE'),
  ('itsdangerous.encoding',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\itsdangerous\\encoding.py',
   'PYMODULE'),
  ('itsdangerous.exc',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\itsdangerous\\exc.py',
   'PYMODULE'),
  ('itsdangerous.serializer',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\itsdangerous\\serializer.py',
   'PYMODULE'),
  ('itsdangerous.signer',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\itsdangerous\\signer.py',
   'PYMODULE'),
  ('itsdangerous.timed',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\itsdangerous\\timed.py',
   'PYMODULE'),
  ('itsdangerous.url_safe',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\itsdangerous\\url_safe.py',
   'PYMODULE'),
  ('jinja2',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\jinja2\\__init__.py',
   'PYMODULE'),
  ('jinja2._identifier',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\jinja2\\_identifier.py',
   'PYMODULE'),
  ('jinja2.async_utils',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\jinja2\\async_utils.py',
   'PYMODULE'),
  ('jinja2.bccache',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\jinja2\\bccache.py',
   'PYMODULE'),
  ('jinja2.compiler',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\jinja2\\compiler.py',
   'PYMODULE'),
  ('jinja2.constants',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\jinja2\\constants.py',
   'PYMODULE'),
  ('jinja2.debug',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\jinja2\\debug.py',
   'PYMODULE'),
  ('jinja2.defaults',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\jinja2\\defaults.py',
   'PYMODULE'),
  ('jinja2.environment',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\jinja2\\environment.py',
   'PYMODULE'),
  ('jinja2.exceptions',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\jinja2\\exceptions.py',
   'PYMODULE'),
  ('jinja2.ext',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\jinja2\\ext.py',
   'PYMODULE'),
  ('jinja2.filters',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\jinja2\\filters.py',
   'PYMODULE'),
  ('jinja2.idtracking',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\jinja2\\idtracking.py',
   'PYMODULE'),
  ('jinja2.lexer',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\jinja2\\lexer.py',
   'PYMODULE'),
  ('jinja2.loaders',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\jinja2\\loaders.py',
   'PYMODULE'),
  ('jinja2.nodes',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\jinja2\\nodes.py',
   'PYMODULE'),
  ('jinja2.optimizer',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\jinja2\\optimizer.py',
   'PYMODULE'),
  ('jinja2.parser',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\jinja2\\parser.py',
   'PYMODULE'),
  ('jinja2.runtime',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\jinja2\\runtime.py',
   'PYMODULE'),
  ('jinja2.sandbox',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\jinja2\\sandbox.py',
   'PYMODULE'),
  ('jinja2.tests',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\jinja2\\tests.py',
   'PYMODULE'),
  ('jinja2.utils',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\jinja2\\utils.py',
   'PYMODULE'),
  ('jinja2.visitor',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\jinja2\\visitor.py',
   'PYMODULE'),
  ('json', 'D:\\anaconda3\\envs\\dt\\lib\\json\\__init__.py', 'PYMODULE'),
  ('json.decoder',
   'D:\\anaconda3\\envs\\dt\\lib\\json\\decoder.py',
   'PYMODULE'),
  ('json.encoder',
   'D:\\anaconda3\\envs\\dt\\lib\\json\\encoder.py',
   'PYMODULE'),
  ('json.scanner',
   'D:\\anaconda3\\envs\\dt\\lib\\json\\scanner.py',
   'PYMODULE'),
  ('logging', 'D:\\anaconda3\\envs\\dt\\lib\\logging\\__init__.py', 'PYMODULE'),
  ('logging.handlers',
   'D:\\anaconda3\\envs\\dt\\lib\\logging\\handlers.py',
   'PYMODULE'),
  ('lzma', 'D:\\anaconda3\\envs\\dt\\lib\\lzma.py', 'PYMODULE'),
  ('markupsafe',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\markupsafe\\__init__.py',
   'PYMODULE'),
  ('markupsafe._native',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\markupsafe\\_native.py',
   'PYMODULE'),
  ('mimetypes', 'D:\\anaconda3\\envs\\dt\\lib\\mimetypes.py', 'PYMODULE'),
  ('modulefinder', 'D:\\anaconda3\\envs\\dt\\lib\\modulefinder.py', 'PYMODULE'),
  ('mysql',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\mysql\\__init__.py',
   'PYMODULE'),
  ('mysql.connector',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\mysql\\connector\\__init__.py',
   'PYMODULE'),
  ('mysql.connector.abstracts',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\mysql\\connector\\abstracts.py',
   'PYMODULE'),
  ('mysql.connector.authentication',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\mysql\\connector\\authentication.py',
   'PYMODULE'),
  ('mysql.connector.charsets',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\mysql\\connector\\charsets.py',
   'PYMODULE'),
  ('mysql.connector.connection',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\mysql\\connector\\connection.py',
   'PYMODULE'),
  ('mysql.connector.connection_cext',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\mysql\\connector\\connection_cext.py',
   'PYMODULE'),
  ('mysql.connector.constants',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\mysql\\connector\\constants.py',
   'PYMODULE'),
  ('mysql.connector.conversion',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\mysql\\connector\\conversion.py',
   'PYMODULE'),
  ('mysql.connector.cursor',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\mysql\\connector\\cursor.py',
   'PYMODULE'),
  ('mysql.connector.cursor_cext',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\mysql\\connector\\cursor_cext.py',
   'PYMODULE'),
  ('mysql.connector.custom_types',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\mysql\\connector\\custom_types.py',
   'PYMODULE'),
  ('mysql.connector.dbapi',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\mysql\\connector\\dbapi.py',
   'PYMODULE'),
  ('mysql.connector.errorcode',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\mysql\\connector\\errorcode.py',
   'PYMODULE'),
  ('mysql.connector.errors',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\mysql\\connector\\errors.py',
   'PYMODULE'),
  ('mysql.connector.locales',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\mysql\\connector\\locales\\__init__.py',
   'PYMODULE'),
  ('mysql.connector.locales.eng',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\mysql\\connector\\locales\\eng\\__init__.py',
   'PYMODULE'),
  ('mysql.connector.locales.eng.client_error',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\mysql\\connector\\locales\\eng\\client_error.py',
   'PYMODULE'),
  ('mysql.connector.logger',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\mysql\\connector\\logger.py',
   'PYMODULE'),
  ('mysql.connector.network',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\mysql\\connector\\network.py',
   'PYMODULE'),
  ('mysql.connector.opentelemetry',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\mysql\\connector\\opentelemetry\\__init__.py',
   'PYMODULE'),
  ('mysql.connector.opentelemetry.constants',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\mysql\\connector\\opentelemetry\\constants.py',
   'PYMODULE'),
  ('mysql.connector.opentelemetry.context_propagation',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\mysql\\connector\\opentelemetry\\context_propagation.py',
   'PYMODULE'),
  ('mysql.connector.opentelemetry.instrumentation',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\mysql\\connector\\opentelemetry\\instrumentation.py',
   'PYMODULE'),
  ('mysql.connector.optionfiles',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\mysql\\connector\\optionfiles.py',
   'PYMODULE'),
  ('mysql.connector.plugins',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\mysql\\connector\\plugins\\__init__.py',
   'PYMODULE'),
  ('mysql.connector.pooling',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\mysql\\connector\\pooling.py',
   'PYMODULE'),
  ('mysql.connector.protocol',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\mysql\\connector\\protocol.py',
   'PYMODULE'),
  ('mysql.connector.types',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\mysql\\connector\\types.py',
   'PYMODULE'),
  ('mysql.connector.utils',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\mysql\\connector\\utils.py',
   'PYMODULE'),
  ('mysql.connector.version',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\mysql\\connector\\version.py',
   'PYMODULE'),
  ('mysql.opentelemetry',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\mysql\\opentelemetry\\__init__.py',
   'PYMODULE'),
  ('mysql.opentelemetry.attributes',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\mysql\\opentelemetry\\attributes\\__init__.py',
   'PYMODULE'),
  ('mysql.opentelemetry.context',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\mysql\\opentelemetry\\context\\__init__.py',
   'PYMODULE'),
  ('mysql.opentelemetry.context.context',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\mysql\\opentelemetry\\context\\context.py',
   'PYMODULE'),
  ('mysql.opentelemetry.environment_variables',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\mysql\\opentelemetry\\environment_variables.py',
   'PYMODULE'),
  ('mysql.opentelemetry.importlib_metadata',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\mysql\\opentelemetry\\importlib_metadata\\__init__.py',
   'PYMODULE'),
  ('mysql.opentelemetry.importlib_metadata._adapters',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\mysql\\opentelemetry\\importlib_metadata\\_adapters.py',
   'PYMODULE'),
  ('mysql.opentelemetry.importlib_metadata._collections',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\mysql\\opentelemetry\\importlib_metadata\\_collections.py',
   'PYMODULE'),
  ('mysql.opentelemetry.importlib_metadata._compat',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\mysql\\opentelemetry\\importlib_metadata\\_compat.py',
   'PYMODULE'),
  ('mysql.opentelemetry.importlib_metadata._functools',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\mysql\\opentelemetry\\importlib_metadata\\_functools.py',
   'PYMODULE'),
  ('mysql.opentelemetry.importlib_metadata._itertools',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\mysql\\opentelemetry\\importlib_metadata\\_itertools.py',
   'PYMODULE'),
  ('mysql.opentelemetry.importlib_metadata._meta',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\mysql\\opentelemetry\\importlib_metadata\\_meta.py',
   'PYMODULE'),
  ('mysql.opentelemetry.importlib_metadata._py39compat',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\mysql\\opentelemetry\\importlib_metadata\\_py39compat.py',
   'PYMODULE'),
  ('mysql.opentelemetry.importlib_metadata._text',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\mysql\\opentelemetry\\importlib_metadata\\_text.py',
   'PYMODULE'),
  ('mysql.opentelemetry.metrics',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\mysql\\opentelemetry\\metrics\\__init__.py',
   'PYMODULE'),
  ('mysql.opentelemetry.metrics._internal',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\mysql\\opentelemetry\\metrics\\_internal\\__init__.py',
   'PYMODULE'),
  ('mysql.opentelemetry.metrics._internal.instrument',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\mysql\\opentelemetry\\metrics\\_internal\\instrument.py',
   'PYMODULE'),
  ('mysql.opentelemetry.metrics._internal.observation',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\mysql\\opentelemetry\\metrics\\_internal\\observation.py',
   'PYMODULE'),
  ('mysql.opentelemetry.sdk',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\mysql\\opentelemetry\\sdk\\__init__.py',
   'PYMODULE'),
  ('mysql.opentelemetry.sdk.environment_variables',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\mysql\\opentelemetry\\sdk\\environment_variables.py',
   'PYMODULE'),
  ('mysql.opentelemetry.sdk.resources',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\mysql\\opentelemetry\\sdk\\resources\\__init__.py',
   'PYMODULE'),
  ('mysql.opentelemetry.sdk.trace',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\mysql\\opentelemetry\\sdk\\trace\\__init__.py',
   'PYMODULE'),
  ('mysql.opentelemetry.sdk.trace.id_generator',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\mysql\\opentelemetry\\sdk\\trace\\id_generator.py',
   'PYMODULE'),
  ('mysql.opentelemetry.sdk.trace.sampling',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\mysql\\opentelemetry\\sdk\\trace\\sampling.py',
   'PYMODULE'),
  ('mysql.opentelemetry.sdk.util',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\mysql\\opentelemetry\\sdk\\util\\__init__.py',
   'PYMODULE'),
  ('mysql.opentelemetry.sdk.util.instrumentation',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\mysql\\opentelemetry\\sdk\\util\\instrumentation.py',
   'PYMODULE'),
  ('mysql.opentelemetry.semconv',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\mysql\\opentelemetry\\semconv\\__init__.py',
   'PYMODULE'),
  ('mysql.opentelemetry.semconv.resource',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\mysql\\opentelemetry\\semconv\\resource\\__init__.py',
   'PYMODULE'),
  ('mysql.opentelemetry.semconv.trace',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\mysql\\opentelemetry\\semconv\\trace\\__init__.py',
   'PYMODULE'),
  ('mysql.opentelemetry.trace',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\mysql\\opentelemetry\\trace\\__init__.py',
   'PYMODULE'),
  ('mysql.opentelemetry.trace.propagation',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\mysql\\opentelemetry\\trace\\propagation\\__init__.py',
   'PYMODULE'),
  ('mysql.opentelemetry.trace.span',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\mysql\\opentelemetry\\trace\\span.py',
   'PYMODULE'),
  ('mysql.opentelemetry.trace.status',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\mysql\\opentelemetry\\trace\\status.py',
   'PYMODULE'),
  ('mysql.opentelemetry.util',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\mysql\\opentelemetry\\util\\__init__.py',
   'PYMODULE'),
  ('mysql.opentelemetry.util._importlib_metadata',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\mysql\\opentelemetry\\util\\_importlib_metadata.py',
   'PYMODULE'),
  ('mysql.opentelemetry.util._once',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\mysql\\opentelemetry\\util\\_once.py',
   'PYMODULE'),
  ('mysql.opentelemetry.util._providers',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\mysql\\opentelemetry\\util\\_providers.py',
   'PYMODULE'),
  ('mysql.opentelemetry.util.types',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\mysql\\opentelemetry\\util\\types.py',
   'PYMODULE'),
  ('netrc', 'D:\\anaconda3\\envs\\dt\\lib\\netrc.py', 'PYMODULE'),
  ('nturl2path', 'D:\\anaconda3\\envs\\dt\\lib\\nturl2path.py', 'PYMODULE'),
  ('numbers', 'D:\\anaconda3\\envs\\dt\\lib\\numbers.py', 'PYMODULE'),
  ('numpy',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\numpy\\__init__.py',
   'PYMODULE'),
  ('numpy.__config__',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\numpy\\__config__.py',
   'PYMODULE'),
  ('numpy._distributor_init',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\numpy\\_distributor_init.py',
   'PYMODULE'),
  ('numpy._globals',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\numpy\\_globals.py',
   'PYMODULE'),
  ('numpy._pytesttester',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\numpy\\_pytesttester.py',
   'PYMODULE'),
  ('numpy._typing',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\numpy\\_typing\\__init__.py',
   'PYMODULE'),
  ('numpy._typing._add_docstring',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\numpy\\_typing\\_add_docstring.py',
   'PYMODULE'),
  ('numpy._typing._array_like',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\numpy\\_typing\\_array_like.py',
   'PYMODULE'),
  ('numpy._typing._char_codes',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\numpy\\_typing\\_char_codes.py',
   'PYMODULE'),
  ('numpy._typing._dtype_like',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\numpy\\_typing\\_dtype_like.py',
   'PYMODULE'),
  ('numpy._typing._generic_alias',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\numpy\\_typing\\_generic_alias.py',
   'PYMODULE'),
  ('numpy._typing._nbit',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\numpy\\_typing\\_nbit.py',
   'PYMODULE'),
  ('numpy._typing._nested_sequence',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\numpy\\_typing\\_nested_sequence.py',
   'PYMODULE'),
  ('numpy._typing._scalars',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\numpy\\_typing\\_scalars.py',
   'PYMODULE'),
  ('numpy._typing._shape',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\numpy\\_typing\\_shape.py',
   'PYMODULE'),
  ('numpy._version',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\numpy\\_version.py',
   'PYMODULE'),
  ('numpy.array_api',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\numpy\\array_api\\__init__.py',
   'PYMODULE'),
  ('numpy.array_api._array_object',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\numpy\\array_api\\_array_object.py',
   'PYMODULE'),
  ('numpy.array_api._constants',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\numpy\\array_api\\_constants.py',
   'PYMODULE'),
  ('numpy.array_api._creation_functions',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\numpy\\array_api\\_creation_functions.py',
   'PYMODULE'),
  ('numpy.array_api._data_type_functions',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\numpy\\array_api\\_data_type_functions.py',
   'PYMODULE'),
  ('numpy.array_api._dtypes',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\numpy\\array_api\\_dtypes.py',
   'PYMODULE'),
  ('numpy.array_api._elementwise_functions',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\numpy\\array_api\\_elementwise_functions.py',
   'PYMODULE'),
  ('numpy.array_api._manipulation_functions',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\numpy\\array_api\\_manipulation_functions.py',
   'PYMODULE'),
  ('numpy.array_api._searching_functions',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\numpy\\array_api\\_searching_functions.py',
   'PYMODULE'),
  ('numpy.array_api._set_functions',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\numpy\\array_api\\_set_functions.py',
   'PYMODULE'),
  ('numpy.array_api._sorting_functions',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\numpy\\array_api\\_sorting_functions.py',
   'PYMODULE'),
  ('numpy.array_api._statistical_functions',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\numpy\\array_api\\_statistical_functions.py',
   'PYMODULE'),
  ('numpy.array_api._typing',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\numpy\\array_api\\_typing.py',
   'PYMODULE'),
  ('numpy.array_api._utility_functions',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\numpy\\array_api\\_utility_functions.py',
   'PYMODULE'),
  ('numpy.array_api.linalg',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\numpy\\array_api\\linalg.py',
   'PYMODULE'),
  ('numpy.compat',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\numpy\\compat\\__init__.py',
   'PYMODULE'),
  ('numpy.compat._inspect',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\numpy\\compat\\_inspect.py',
   'PYMODULE'),
  ('numpy.compat.py3k',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\numpy\\compat\\py3k.py',
   'PYMODULE'),
  ('numpy.core',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\numpy\\core\\__init__.py',
   'PYMODULE'),
  ('numpy.core._add_newdocs',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\numpy\\core\\_add_newdocs.py',
   'PYMODULE'),
  ('numpy.core._add_newdocs_scalars',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\numpy\\core\\_add_newdocs_scalars.py',
   'PYMODULE'),
  ('numpy.core._asarray',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\numpy\\core\\_asarray.py',
   'PYMODULE'),
  ('numpy.core._dtype',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\numpy\\core\\_dtype.py',
   'PYMODULE'),
  ('numpy.core._dtype_ctypes',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\numpy\\core\\_dtype_ctypes.py',
   'PYMODULE'),
  ('numpy.core._exceptions',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\numpy\\core\\_exceptions.py',
   'PYMODULE'),
  ('numpy.core._internal',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\numpy\\core\\_internal.py',
   'PYMODULE'),
  ('numpy.core._machar',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\numpy\\core\\_machar.py',
   'PYMODULE'),
  ('numpy.core._methods',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\numpy\\core\\_methods.py',
   'PYMODULE'),
  ('numpy.core._string_helpers',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\numpy\\core\\_string_helpers.py',
   'PYMODULE'),
  ('numpy.core._type_aliases',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\numpy\\core\\_type_aliases.py',
   'PYMODULE'),
  ('numpy.core._ufunc_config',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\numpy\\core\\_ufunc_config.py',
   'PYMODULE'),
  ('numpy.core.arrayprint',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\numpy\\core\\arrayprint.py',
   'PYMODULE'),
  ('numpy.core.defchararray',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\numpy\\core\\defchararray.py',
   'PYMODULE'),
  ('numpy.core.einsumfunc',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\numpy\\core\\einsumfunc.py',
   'PYMODULE'),
  ('numpy.core.fromnumeric',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\numpy\\core\\fromnumeric.py',
   'PYMODULE'),
  ('numpy.core.function_base',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\numpy\\core\\function_base.py',
   'PYMODULE'),
  ('numpy.core.getlimits',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\numpy\\core\\getlimits.py',
   'PYMODULE'),
  ('numpy.core.memmap',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\numpy\\core\\memmap.py',
   'PYMODULE'),
  ('numpy.core.multiarray',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\numpy\\core\\multiarray.py',
   'PYMODULE'),
  ('numpy.core.numeric',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\numpy\\core\\numeric.py',
   'PYMODULE'),
  ('numpy.core.numerictypes',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\numpy\\core\\numerictypes.py',
   'PYMODULE'),
  ('numpy.core.overrides',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\numpy\\core\\overrides.py',
   'PYMODULE'),
  ('numpy.core.records',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\numpy\\core\\records.py',
   'PYMODULE'),
  ('numpy.core.shape_base',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\numpy\\core\\shape_base.py',
   'PYMODULE'),
  ('numpy.core.umath',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\numpy\\core\\umath.py',
   'PYMODULE'),
  ('numpy.ctypeslib',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\numpy\\ctypeslib.py',
   'PYMODULE'),
  ('numpy.distutils',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\numpy\\distutils\\__init__.py',
   'PYMODULE'),
  ('numpy.distutils.cpuinfo',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\numpy\\distutils\\cpuinfo.py',
   'PYMODULE'),
  ('numpy.fft',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\numpy\\fft\\__init__.py',
   'PYMODULE'),
  ('numpy.fft._pocketfft',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\numpy\\fft\\_pocketfft.py',
   'PYMODULE'),
  ('numpy.fft.helper',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\numpy\\fft\\helper.py',
   'PYMODULE'),
  ('numpy.lib',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\numpy\\lib\\__init__.py',
   'PYMODULE'),
  ('numpy.lib._datasource',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\numpy\\lib\\_datasource.py',
   'PYMODULE'),
  ('numpy.lib._iotools',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\numpy\\lib\\_iotools.py',
   'PYMODULE'),
  ('numpy.lib._version',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\numpy\\lib\\_version.py',
   'PYMODULE'),
  ('numpy.lib.arraypad',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\numpy\\lib\\arraypad.py',
   'PYMODULE'),
  ('numpy.lib.arraysetops',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\numpy\\lib\\arraysetops.py',
   'PYMODULE'),
  ('numpy.lib.arrayterator',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\numpy\\lib\\arrayterator.py',
   'PYMODULE'),
  ('numpy.lib.format',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\numpy\\lib\\format.py',
   'PYMODULE'),
  ('numpy.lib.function_base',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\numpy\\lib\\function_base.py',
   'PYMODULE'),
  ('numpy.lib.histograms',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\numpy\\lib\\histograms.py',
   'PYMODULE'),
  ('numpy.lib.index_tricks',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\numpy\\lib\\index_tricks.py',
   'PYMODULE'),
  ('numpy.lib.mixins',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\numpy\\lib\\mixins.py',
   'PYMODULE'),
  ('numpy.lib.nanfunctions',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\numpy\\lib\\nanfunctions.py',
   'PYMODULE'),
  ('numpy.lib.npyio',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\numpy\\lib\\npyio.py',
   'PYMODULE'),
  ('numpy.lib.polynomial',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\numpy\\lib\\polynomial.py',
   'PYMODULE'),
  ('numpy.lib.scimath',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\numpy\\lib\\scimath.py',
   'PYMODULE'),
  ('numpy.lib.shape_base',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\numpy\\lib\\shape_base.py',
   'PYMODULE'),
  ('numpy.lib.stride_tricks',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\numpy\\lib\\stride_tricks.py',
   'PYMODULE'),
  ('numpy.lib.twodim_base',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\numpy\\lib\\twodim_base.py',
   'PYMODULE'),
  ('numpy.lib.type_check',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\numpy\\lib\\type_check.py',
   'PYMODULE'),
  ('numpy.lib.ufunclike',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\numpy\\lib\\ufunclike.py',
   'PYMODULE'),
  ('numpy.lib.utils',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\numpy\\lib\\utils.py',
   'PYMODULE'),
  ('numpy.linalg',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\numpy\\linalg\\__init__.py',
   'PYMODULE'),
  ('numpy.linalg.linalg',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\numpy\\linalg\\linalg.py',
   'PYMODULE'),
  ('numpy.ma',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\numpy\\ma\\__init__.py',
   'PYMODULE'),
  ('numpy.ma.core',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\numpy\\ma\\core.py',
   'PYMODULE'),
  ('numpy.ma.extras',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\numpy\\ma\\extras.py',
   'PYMODULE'),
  ('numpy.ma.mrecords',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\numpy\\ma\\mrecords.py',
   'PYMODULE'),
  ('numpy.matrixlib',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\numpy\\matrixlib\\__init__.py',
   'PYMODULE'),
  ('numpy.matrixlib.defmatrix',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\numpy\\matrixlib\\defmatrix.py',
   'PYMODULE'),
  ('numpy.polynomial',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\numpy\\polynomial\\__init__.py',
   'PYMODULE'),
  ('numpy.polynomial._polybase',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\numpy\\polynomial\\_polybase.py',
   'PYMODULE'),
  ('numpy.polynomial.chebyshev',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\numpy\\polynomial\\chebyshev.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\numpy\\polynomial\\hermite.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite_e',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\numpy\\polynomial\\hermite_e.py',
   'PYMODULE'),
  ('numpy.polynomial.laguerre',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\numpy\\polynomial\\laguerre.py',
   'PYMODULE'),
  ('numpy.polynomial.legendre',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\numpy\\polynomial\\legendre.py',
   'PYMODULE'),
  ('numpy.polynomial.polynomial',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\numpy\\polynomial\\polynomial.py',
   'PYMODULE'),
  ('numpy.polynomial.polyutils',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\numpy\\polynomial\\polyutils.py',
   'PYMODULE'),
  ('numpy.random',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\numpy\\random\\__init__.py',
   'PYMODULE'),
  ('numpy.random._pickle',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\numpy\\random\\_pickle.py',
   'PYMODULE'),
  ('numpy.testing',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\numpy\\testing\\__init__.py',
   'PYMODULE'),
  ('numpy.testing._private',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\numpy\\testing\\_private\\__init__.py',
   'PYMODULE'),
  ('numpy.testing._private.decorators',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\numpy\\testing\\_private\\decorators.py',
   'PYMODULE'),
  ('numpy.testing._private.extbuild',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\numpy\\testing\\_private\\extbuild.py',
   'PYMODULE'),
  ('numpy.testing._private.noseclasses',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\numpy\\testing\\_private\\noseclasses.py',
   'PYMODULE'),
  ('numpy.testing._private.nosetester',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\numpy\\testing\\_private\\nosetester.py',
   'PYMODULE'),
  ('numpy.testing._private.parameterized',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\numpy\\testing\\_private\\parameterized.py',
   'PYMODULE'),
  ('numpy.testing._private.utils',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\numpy\\testing\\_private\\utils.py',
   'PYMODULE'),
  ('numpy.typing',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\numpy\\typing\\__init__.py',
   'PYMODULE'),
  ('numpy.version',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\numpy\\version.py',
   'PYMODULE'),
  ('opcode', 'D:\\anaconda3\\envs\\dt\\lib\\opcode.py', 'PYMODULE'),
  ('optparse', 'D:\\anaconda3\\envs\\dt\\lib\\optparse.py', 'PYMODULE'),
  ('packaging',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\packaging\\__init__.py',
   'PYMODULE'),
  ('packaging._structures',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\packaging\\_structures.py',
   'PYMODULE'),
  ('packaging.version',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\packaging\\version.py',
   'PYMODULE'),
  ('pathlib', 'D:\\anaconda3\\envs\\dt\\lib\\pathlib.py', 'PYMODULE'),
  ('pdb', 'D:\\anaconda3\\envs\\dt\\lib\\pdb.py', 'PYMODULE'),
  ('pickle', 'D:\\anaconda3\\envs\\dt\\lib\\pickle.py', 'PYMODULE'),
  ('pkgutil', 'D:\\anaconda3\\envs\\dt\\lib\\pkgutil.py', 'PYMODULE'),
  ('platform', 'D:\\anaconda3\\envs\\dt\\lib\\platform.py', 'PYMODULE'),
  ('png', 'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\png.py', 'PYMODULE'),
  ('profile', 'D:\\anaconda3\\envs\\dt\\lib\\profile.py', 'PYMODULE'),
  ('pstats', 'D:\\anaconda3\\envs\\dt\\lib\\pstats.py', 'PYMODULE'),
  ('py_compile', 'D:\\anaconda3\\envs\\dt\\lib\\py_compile.py', 'PYMODULE'),
  ('pytz',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\__init__.py',
   'PYMODULE'),
  ('pytz.exceptions',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\exceptions.py',
   'PYMODULE'),
  ('pytz.lazy',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\lazy.py',
   'PYMODULE'),
  ('pytz.tzfile',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\tzfile.py',
   'PYMODULE'),
  ('pytz.tzinfo',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pytz\\tzinfo.py',
   'PYMODULE'),
  ('pyzbar',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pyzbar\\__init__.py',
   'PYMODULE'),
  ('pyzbar.locations',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pyzbar\\locations.py',
   'PYMODULE'),
  ('pyzbar.pyzbar',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pyzbar\\pyzbar.py',
   'PYMODULE'),
  ('pyzbar.pyzbar_error',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pyzbar\\pyzbar_error.py',
   'PYMODULE'),
  ('pyzbar.wrapper',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pyzbar\\wrapper.py',
   'PYMODULE'),
  ('pyzbar.zbar_library',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\pyzbar\\zbar_library.py',
   'PYMODULE'),
  ('qr_generator',
   'E:\\01.CODE\\01.JKGA\\DataTransmission\\qr_generator.py',
   'PYMODULE'),
  ('qrcode',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\qrcode\\__init__.py',
   'PYMODULE'),
  ('qrcode.LUT',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\qrcode\\LUT.py',
   'PYMODULE'),
  ('qrcode.base',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\qrcode\\base.py',
   'PYMODULE'),
  ('qrcode.compat',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\qrcode\\compat\\__init__.py',
   'PYMODULE'),
  ('qrcode.compat.etree',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\qrcode\\compat\\etree.py',
   'PYMODULE'),
  ('qrcode.compat.pil',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\qrcode\\compat\\pil.py',
   'PYMODULE'),
  ('qrcode.constants',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\qrcode\\constants.py',
   'PYMODULE'),
  ('qrcode.exceptions',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\qrcode\\exceptions.py',
   'PYMODULE'),
  ('qrcode.image',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\qrcode\\image\\__init__.py',
   'PYMODULE'),
  ('qrcode.image.base',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\qrcode\\image\\base.py',
   'PYMODULE'),
  ('qrcode.image.pil',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\qrcode\\image\\pil.py',
   'PYMODULE'),
  ('qrcode.image.pure',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\qrcode\\image\\pure.py',
   'PYMODULE'),
  ('qrcode.image.styledpil',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\qrcode\\image\\styledpil.py',
   'PYMODULE'),
  ('qrcode.image.styles',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\qrcode\\image\\styles\\__init__.py',
   'PYMODULE'),
  ('qrcode.image.styles.colormasks',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\qrcode\\image\\styles\\colormasks.py',
   'PYMODULE'),
  ('qrcode.image.styles.moduledrawers',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\qrcode\\image\\styles\\moduledrawers\\__init__.py',
   'PYMODULE'),
  ('qrcode.image.styles.moduledrawers.base',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\qrcode\\image\\styles\\moduledrawers\\base.py',
   'PYMODULE'),
  ('qrcode.image.styles.moduledrawers.pil',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\qrcode\\image\\styles\\moduledrawers\\pil.py',
   'PYMODULE'),
  ('qrcode.image.styles.moduledrawers.svg',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\qrcode\\image\\styles\\moduledrawers\\svg.py',
   'PYMODULE'),
  ('qrcode.image.svg',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\qrcode\\image\\svg.py',
   'PYMODULE'),
  ('qrcode.main',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\qrcode\\main.py',
   'PYMODULE'),
  ('qrcode.util',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\qrcode\\util.py',
   'PYMODULE'),
  ('quopri', 'D:\\anaconda3\\envs\\dt\\lib\\quopri.py', 'PYMODULE'),
  ('random', 'D:\\anaconda3\\envs\\dt\\lib\\random.py', 'PYMODULE'),
  ('requests',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\requests\\__init__.py',
   'PYMODULE'),
  ('requests.__version__',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\requests\\__version__.py',
   'PYMODULE'),
  ('requests._internal_utils',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\requests\\_internal_utils.py',
   'PYMODULE'),
  ('requests.adapters',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\requests\\adapters.py',
   'PYMODULE'),
  ('requests.api',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\requests\\api.py',
   'PYMODULE'),
  ('requests.auth',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\requests\\auth.py',
   'PYMODULE'),
  ('requests.certs',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\requests\\certs.py',
   'PYMODULE'),
  ('requests.compat',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\requests\\compat.py',
   'PYMODULE'),
  ('requests.cookies',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\requests\\cookies.py',
   'PYMODULE'),
  ('requests.exceptions',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\requests\\exceptions.py',
   'PYMODULE'),
  ('requests.hooks',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\requests\\hooks.py',
   'PYMODULE'),
  ('requests.models',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\requests\\models.py',
   'PYMODULE'),
  ('requests.packages',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\requests\\packages.py',
   'PYMODULE'),
  ('requests.sessions',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\requests\\sessions.py',
   'PYMODULE'),
  ('requests.status_codes',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\requests\\status_codes.py',
   'PYMODULE'),
  ('requests.structures',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\requests\\structures.py',
   'PYMODULE'),
  ('requests.utils',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\requests\\utils.py',
   'PYMODULE'),
  ('runpy', 'D:\\anaconda3\\envs\\dt\\lib\\runpy.py', 'PYMODULE'),
  ('secrets', 'D:\\anaconda3\\envs\\dt\\lib\\secrets.py', 'PYMODULE'),
  ('selectors', 'D:\\anaconda3\\envs\\dt\\lib\\selectors.py', 'PYMODULE'),
  ('shutil', 'D:\\anaconda3\\envs\\dt\\lib\\shutil.py', 'PYMODULE'),
  ('signal', 'D:\\anaconda3\\envs\\dt\\lib\\signal.py', 'PYMODULE'),
  ('six', 'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\six.py', 'PYMODULE'),
  ('smtplib', 'D:\\anaconda3\\envs\\dt\\lib\\smtplib.py', 'PYMODULE'),
  ('socket', 'D:\\anaconda3\\envs\\dt\\lib\\socket.py', 'PYMODULE'),
  ('socketserver', 'D:\\anaconda3\\envs\\dt\\lib\\socketserver.py', 'PYMODULE'),
  ('sqlite3', 'D:\\anaconda3\\envs\\dt\\lib\\sqlite3\\__init__.py', 'PYMODULE'),
  ('sqlite3.dbapi2',
   'D:\\anaconda3\\envs\\dt\\lib\\sqlite3\\dbapi2.py',
   'PYMODULE'),
  ('sqlite3.dump',
   'D:\\anaconda3\\envs\\dt\\lib\\sqlite3\\dump.py',
   'PYMODULE'),
  ('ssl', 'D:\\anaconda3\\envs\\dt\\lib\\ssl.py', 'PYMODULE'),
  ('statistics', 'D:\\anaconda3\\envs\\dt\\lib\\statistics.py', 'PYMODULE'),
  ('string', 'D:\\anaconda3\\envs\\dt\\lib\\string.py', 'PYMODULE'),
  ('subprocess', 'D:\\anaconda3\\envs\\dt\\lib\\subprocess.py', 'PYMODULE'),
  ('sysconfig', 'D:\\anaconda3\\envs\\dt\\lib\\sysconfig.py', 'PYMODULE'),
  ('tarfile', 'D:\\anaconda3\\envs\\dt\\lib\\tarfile.py', 'PYMODULE'),
  ('tempfile', 'D:\\anaconda3\\envs\\dt\\lib\\tempfile.py', 'PYMODULE'),
  ('textwrap', 'D:\\anaconda3\\envs\\dt\\lib\\textwrap.py', 'PYMODULE'),
  ('threading', 'D:\\anaconda3\\envs\\dt\\lib\\threading.py', 'PYMODULE'),
  ('timeit', 'D:\\anaconda3\\envs\\dt\\lib\\timeit.py', 'PYMODULE'),
  ('tkinter', 'D:\\anaconda3\\envs\\dt\\lib\\tkinter\\__init__.py', 'PYMODULE'),
  ('tkinter.colorchooser',
   'D:\\anaconda3\\envs\\dt\\lib\\tkinter\\colorchooser.py',
   'PYMODULE'),
  ('tkinter.commondialog',
   'D:\\anaconda3\\envs\\dt\\lib\\tkinter\\commondialog.py',
   'PYMODULE'),
  ('tkinter.constants',
   'D:\\anaconda3\\envs\\dt\\lib\\tkinter\\constants.py',
   'PYMODULE'),
  ('tkinter.dialog',
   'D:\\anaconda3\\envs\\dt\\lib\\tkinter\\dialog.py',
   'PYMODULE'),
  ('tkinter.dnd', 'D:\\anaconda3\\envs\\dt\\lib\\tkinter\\dnd.py', 'PYMODULE'),
  ('tkinter.filedialog',
   'D:\\anaconda3\\envs\\dt\\lib\\tkinter\\filedialog.py',
   'PYMODULE'),
  ('tkinter.font',
   'D:\\anaconda3\\envs\\dt\\lib\\tkinter\\font.py',
   'PYMODULE'),
  ('tkinter.messagebox',
   'D:\\anaconda3\\envs\\dt\\lib\\tkinter\\messagebox.py',
   'PYMODULE'),
  ('tkinter.scrolledtext',
   'D:\\anaconda3\\envs\\dt\\lib\\tkinter\\scrolledtext.py',
   'PYMODULE'),
  ('tkinter.simpledialog',
   'D:\\anaconda3\\envs\\dt\\lib\\tkinter\\simpledialog.py',
   'PYMODULE'),
  ('tkinter.ttk', 'D:\\anaconda3\\envs\\dt\\lib\\tkinter\\ttk.py', 'PYMODULE'),
  ('token', 'D:\\anaconda3\\envs\\dt\\lib\\token.py', 'PYMODULE'),
  ('tokenize', 'D:\\anaconda3\\envs\\dt\\lib\\tokenize.py', 'PYMODULE'),
  ('trace', 'D:\\anaconda3\\envs\\dt\\lib\\trace.py', 'PYMODULE'),
  ('tracemalloc', 'D:\\anaconda3\\envs\\dt\\lib\\tracemalloc.py', 'PYMODULE'),
  ('tty', 'D:\\anaconda3\\envs\\dt\\lib\\tty.py', 'PYMODULE'),
  ('typing', 'D:\\anaconda3\\envs\\dt\\lib\\typing.py', 'PYMODULE'),
  ('typing_extensions',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\typing_extensions.py',
   'PYMODULE'),
  ('tzdata',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Africa',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Africa\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.America',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.America.Argentina',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Argentina\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.America.Indiana',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Indiana\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.America.Kentucky',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Kentucky\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.America.North_Dakota',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\America\\North_Dakota\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Antarctica',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Antarctica\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Arctic',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Arctic\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Asia',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Atlantic',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Atlantic\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Australia',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Australia\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Brazil',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Brazil\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Canada',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Canada\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Chile',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Chile\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Etc',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Etc\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Europe',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Europe\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Indian',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Indian\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Mexico',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Mexico\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Pacific',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.US',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzdata\\zoneinfo\\US\\__init__.py',
   'PYMODULE'),
  ('tzlocal',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzlocal\\__init__.py',
   'PYMODULE'),
  ('tzlocal.unix',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzlocal\\unix.py',
   'PYMODULE'),
  ('tzlocal.utils',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzlocal\\utils.py',
   'PYMODULE'),
  ('tzlocal.win32',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzlocal\\win32.py',
   'PYMODULE'),
  ('tzlocal.windows_tz',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\tzlocal\\windows_tz.py',
   'PYMODULE'),
  ('urllib', 'D:\\anaconda3\\envs\\dt\\lib\\urllib\\__init__.py', 'PYMODULE'),
  ('urllib.error',
   'D:\\anaconda3\\envs\\dt\\lib\\urllib\\error.py',
   'PYMODULE'),
  ('urllib.parse',
   'D:\\anaconda3\\envs\\dt\\lib\\urllib\\parse.py',
   'PYMODULE'),
  ('urllib.request',
   'D:\\anaconda3\\envs\\dt\\lib\\urllib\\request.py',
   'PYMODULE'),
  ('urllib.response',
   'D:\\anaconda3\\envs\\dt\\lib\\urllib\\response.py',
   'PYMODULE'),
  ('urllib3',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\urllib3\\__init__.py',
   'PYMODULE'),
  ('urllib3._base_connection',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\urllib3\\_base_connection.py',
   'PYMODULE'),
  ('urllib3._collections',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\urllib3\\_collections.py',
   'PYMODULE'),
  ('urllib3._request_methods',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\urllib3\\_request_methods.py',
   'PYMODULE'),
  ('urllib3._version',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\urllib3\\_version.py',
   'PYMODULE'),
  ('urllib3.connection',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\urllib3\\connection.py',
   'PYMODULE'),
  ('urllib3.connectionpool',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\urllib3\\connectionpool.py',
   'PYMODULE'),
  ('urllib3.contrib',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\urllib3\\contrib\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\urllib3\\contrib\\emscripten\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.connection',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\urllib3\\contrib\\emscripten\\connection.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.fetch',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\urllib3\\contrib\\emscripten\\fetch.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.request',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\urllib3\\contrib\\emscripten\\request.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.response',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\urllib3\\contrib\\emscripten\\response.py',
   'PYMODULE'),
  ('urllib3.contrib.pyopenssl',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\urllib3\\contrib\\pyopenssl.py',
   'PYMODULE'),
  ('urllib3.contrib.socks',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\urllib3\\contrib\\socks.py',
   'PYMODULE'),
  ('urllib3.exceptions',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\urllib3\\exceptions.py',
   'PYMODULE'),
  ('urllib3.fields',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\urllib3\\fields.py',
   'PYMODULE'),
  ('urllib3.filepost',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\urllib3\\filepost.py',
   'PYMODULE'),
  ('urllib3.http2',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\urllib3\\http2\\__init__.py',
   'PYMODULE'),
  ('urllib3.http2.connection',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\urllib3\\http2\\connection.py',
   'PYMODULE'),
  ('urllib3.http2.probe',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\urllib3\\http2\\probe.py',
   'PYMODULE'),
  ('urllib3.poolmanager',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\urllib3\\poolmanager.py',
   'PYMODULE'),
  ('urllib3.response',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\urllib3\\response.py',
   'PYMODULE'),
  ('urllib3.util',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\urllib3\\util\\__init__.py',
   'PYMODULE'),
  ('urllib3.util.connection',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\urllib3\\util\\connection.py',
   'PYMODULE'),
  ('urllib3.util.proxy',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\urllib3\\util\\proxy.py',
   'PYMODULE'),
  ('urllib3.util.request',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\urllib3\\util\\request.py',
   'PYMODULE'),
  ('urllib3.util.response',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\urllib3\\util\\response.py',
   'PYMODULE'),
  ('urllib3.util.retry',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\urllib3\\util\\retry.py',
   'PYMODULE'),
  ('urllib3.util.ssl_',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\urllib3\\util\\ssl_.py',
   'PYMODULE'),
  ('urllib3.util.ssl_match_hostname',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\urllib3\\util\\ssl_match_hostname.py',
   'PYMODULE'),
  ('urllib3.util.ssltransport',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\urllib3\\util\\ssltransport.py',
   'PYMODULE'),
  ('urllib3.util.timeout',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\urllib3\\util\\timeout.py',
   'PYMODULE'),
  ('urllib3.util.url',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\urllib3\\util\\url.py',
   'PYMODULE'),
  ('urllib3.util.util',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\urllib3\\util\\util.py',
   'PYMODULE'),
  ('urllib3.util.wait',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\urllib3\\util\\wait.py',
   'PYMODULE'),
  ('uuid', 'D:\\anaconda3\\envs\\dt\\lib\\uuid.py', 'PYMODULE'),
  ('web_server',
   'E:\\01.CODE\\01.JKGA\\DataTransmission\\web_server.py',
   'PYMODULE'),
  ('webbrowser', 'D:\\anaconda3\\envs\\dt\\lib\\webbrowser.py', 'PYMODULE'),
  ('werkzeug',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\werkzeug\\__init__.py',
   'PYMODULE'),
  ('werkzeug._internal',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\werkzeug\\_internal.py',
   'PYMODULE'),
  ('werkzeug._reloader',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\werkzeug\\_reloader.py',
   'PYMODULE'),
  ('werkzeug.datastructures',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\werkzeug\\datastructures\\__init__.py',
   'PYMODULE'),
  ('werkzeug.datastructures.accept',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\werkzeug\\datastructures\\accept.py',
   'PYMODULE'),
  ('werkzeug.datastructures.auth',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\werkzeug\\datastructures\\auth.py',
   'PYMODULE'),
  ('werkzeug.datastructures.cache_control',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\werkzeug\\datastructures\\cache_control.py',
   'PYMODULE'),
  ('werkzeug.datastructures.csp',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\werkzeug\\datastructures\\csp.py',
   'PYMODULE'),
  ('werkzeug.datastructures.etag',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\werkzeug\\datastructures\\etag.py',
   'PYMODULE'),
  ('werkzeug.datastructures.file_storage',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\werkzeug\\datastructures\\file_storage.py',
   'PYMODULE'),
  ('werkzeug.datastructures.headers',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\werkzeug\\datastructures\\headers.py',
   'PYMODULE'),
  ('werkzeug.datastructures.mixins',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\werkzeug\\datastructures\\mixins.py',
   'PYMODULE'),
  ('werkzeug.datastructures.range',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\werkzeug\\datastructures\\range.py',
   'PYMODULE'),
  ('werkzeug.datastructures.structures',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\werkzeug\\datastructures\\structures.py',
   'PYMODULE'),
  ('werkzeug.debug',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\werkzeug\\debug\\__init__.py',
   'PYMODULE'),
  ('werkzeug.debug.console',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\werkzeug\\debug\\console.py',
   'PYMODULE'),
  ('werkzeug.debug.repr',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\werkzeug\\debug\\repr.py',
   'PYMODULE'),
  ('werkzeug.debug.tbtools',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\werkzeug\\debug\\tbtools.py',
   'PYMODULE'),
  ('werkzeug.exceptions',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\werkzeug\\exceptions.py',
   'PYMODULE'),
  ('werkzeug.formparser',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\werkzeug\\formparser.py',
   'PYMODULE'),
  ('werkzeug.http',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\werkzeug\\http.py',
   'PYMODULE'),
  ('werkzeug.local',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\werkzeug\\local.py',
   'PYMODULE'),
  ('werkzeug.middleware',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\werkzeug\\middleware\\__init__.py',
   'PYMODULE'),
  ('werkzeug.middleware.shared_data',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\werkzeug\\middleware\\shared_data.py',
   'PYMODULE'),
  ('werkzeug.routing',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\werkzeug\\routing\\__init__.py',
   'PYMODULE'),
  ('werkzeug.routing.converters',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\werkzeug\\routing\\converters.py',
   'PYMODULE'),
  ('werkzeug.routing.exceptions',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\werkzeug\\routing\\exceptions.py',
   'PYMODULE'),
  ('werkzeug.routing.map',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\werkzeug\\routing\\map.py',
   'PYMODULE'),
  ('werkzeug.routing.matcher',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\werkzeug\\routing\\matcher.py',
   'PYMODULE'),
  ('werkzeug.routing.rules',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\werkzeug\\routing\\rules.py',
   'PYMODULE'),
  ('werkzeug.sansio',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\werkzeug\\sansio\\__init__.py',
   'PYMODULE'),
  ('werkzeug.sansio.http',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\werkzeug\\sansio\\http.py',
   'PYMODULE'),
  ('werkzeug.sansio.multipart',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\werkzeug\\sansio\\multipart.py',
   'PYMODULE'),
  ('werkzeug.sansio.request',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\werkzeug\\sansio\\request.py',
   'PYMODULE'),
  ('werkzeug.sansio.response',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\werkzeug\\sansio\\response.py',
   'PYMODULE'),
  ('werkzeug.sansio.utils',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\werkzeug\\sansio\\utils.py',
   'PYMODULE'),
  ('werkzeug.security',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\werkzeug\\security.py',
   'PYMODULE'),
  ('werkzeug.serving',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\werkzeug\\serving.py',
   'PYMODULE'),
  ('werkzeug.test',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\werkzeug\\test.py',
   'PYMODULE'),
  ('werkzeug.urls',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\werkzeug\\urls.py',
   'PYMODULE'),
  ('werkzeug.user_agent',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\werkzeug\\user_agent.py',
   'PYMODULE'),
  ('werkzeug.utils',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\werkzeug\\utils.py',
   'PYMODULE'),
  ('werkzeug.wrappers',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\werkzeug\\wrappers\\__init__.py',
   'PYMODULE'),
  ('werkzeug.wrappers.request',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\werkzeug\\wrappers\\request.py',
   'PYMODULE'),
  ('werkzeug.wrappers.response',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\werkzeug\\wrappers\\response.py',
   'PYMODULE'),
  ('werkzeug.wsgi',
   'D:\\anaconda3\\envs\\dt\\lib\\site-packages\\werkzeug\\wsgi.py',
   'PYMODULE'),
  ('zipfile', 'D:\\anaconda3\\envs\\dt\\lib\\zipfile.py', 'PYMODULE'),
  ('zipimport', 'D:\\anaconda3\\envs\\dt\\lib\\zipimport.py', 'PYMODULE'),
  ('zoneinfo',
   'D:\\anaconda3\\envs\\dt\\lib\\zoneinfo\\__init__.py',
   'PYMODULE'),
  ('zoneinfo._common',
   'D:\\anaconda3\\envs\\dt\\lib\\zoneinfo\\_common.py',
   'PYMODULE'),
  ('zoneinfo._tzpath',
   'D:\\anaconda3\\envs\\dt\\lib\\zoneinfo\\_tzpath.py',
   'PYMODULE'),
  ('zoneinfo._zoneinfo',
   'D:\\anaconda3\\envs\\dt\\lib\\zoneinfo\\_zoneinfo.py',
   'PYMODULE')])
