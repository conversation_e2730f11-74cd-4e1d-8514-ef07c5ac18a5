@echo off
chcp 65001 >nul
title DataTransmission 数据库优化工具

echo ========================================
echo    DataTransmission 数据库优化工具
echo ========================================
echo.

echo 此工具将帮助您优化数据库，解决死锁问题
echo.

echo 优化内容:
echo - 替换为优化的数据库管理器
echo - 检测和修复当前死锁
echo - 应用MySQL性能优化配置
echo - 启用死锁监控
echo.

set /p confirm="是否开始优化? (Y/N): "
if /i not "%confirm%"=="Y" goto end

echo.
echo ========================================
echo 步骤1: 备份当前数据库文件
echo ========================================
echo.

if exist "database.py" (
    echo 备份原始数据库文件...
    copy "database.py" "database_backup_%date:~0,4%%date:~5,2%%date:~8,2%.py" >nul
    if %errorlevel% equ 0 (
        echo ✓ 原始数据库文件已备份
    ) else (
        echo ✗ 备份失败
        goto end
    )
) else (
    echo ⚠️ 未找到原始database.py文件
)

echo.
echo ========================================
echo 步骤2: 应用优化的数据库管理器
echo ========================================
echo.

if exist "database_optimized.py" (
    echo 应用优化的数据库管理器...
    copy "database_optimized.py" "database.py" >nul
    if %errorlevel% equ 0 (
        echo ✓ 优化的数据库管理器已应用
    ) else (
        echo ✗ 应用失败
        goto end
    )
) else (
    echo ✗ 未找到database_optimized.py文件
    goto end
)

echo.
echo ========================================
echo 步骤3: 检测当前死锁情况
echo ========================================
echo.

echo 运行死锁检测器...
python database_deadlock_detector.py --analyze-only 2>nul
if %errorlevel% neq 0 (
    echo ⚠️ 死锁检测器运行异常，手动检查
    echo 请稍后运行: python database_deadlock_detector.py
)

echo.
echo ========================================
echo 步骤4: MySQL配置优化建议
echo ========================================
echo.

echo MySQL配置优化建议:
echo.
echo 1. 找到MySQL配置文件 (通常在以下位置之一):
echo    - C:\ProgramData\MySQL\MySQL Server 8.0\my.ini
echo    - C:\Program Files\MySQL\MySQL Server 8.0\my.ini
echo.
echo 2. 在 [mysqld] 部分添加以下优化配置:
echo.
echo    # 死锁优化
echo    innodb_deadlock_detect = ON
echo    innodb_lock_wait_timeout = 10
echo    innodb_rollback_on_timeout = ON
echo    transaction-isolation = READ-COMMITTED
echo.
echo    # 连接优化
echo    max_connections = 200
echo    connect_timeout = 10
echo    wait_timeout = 600
echo.
echo 3. 重启MySQL服务:
echo    net stop mysql
echo    net start mysql
echo.

set /p apply_config="是否现在应用MySQL配置? (Y/N): "
if /i "%apply_config%"=="Y" (
    echo.
    echo 查找MySQL配置文件...
    
    set "mysql_config="
    if exist "C:\ProgramData\MySQL\MySQL Server 8.0\my.ini" (
        set "mysql_config=C:\ProgramData\MySQL\MySQL Server 8.0\my.ini"
    ) else if exist "C:\Program Files\MySQL\MySQL Server 8.0\my.ini" (
        set "mysql_config=C:\Program Files\MySQL\MySQL Server 8.0\my.ini"
    )
    
    if defined mysql_config (
        echo ✓ 找到MySQL配置文件: %mysql_config%
        echo.
        echo 正在备份配置文件...
        copy "%mysql_config%" "%mysql_config%.backup.%date:~0,4%%date:~5,2%%date:~8,2%" >nul
        
        echo 正在应用优化配置...
        type mysql_optimization.cnf >> "%mysql_config%"
        
        echo ✓ 配置已应用，需要重启MySQL服务
        echo.
        set /p restart_mysql="是否现在重启MySQL服务? (Y/N): "
        if /i "!restart_mysql!"=="Y" (
            echo 重启MySQL服务...
            net stop mysql >nul 2>&1
            timeout /t 3 >nul
            net start mysql >nul 2>&1
            if %errorlevel% equ 0 (
                echo ✓ MySQL服务重启成功
            ) else (
                echo ✗ MySQL服务重启失败，请手动重启
            )
        )
    ) else (
        echo ⚠️ 未找到MySQL配置文件，请手动配置
    )
)

echo.
echo ========================================
echo 步骤5: 测试优化效果
echo ========================================
echo.

echo 测试优化后的数据库连接...
python -c "
try:
    from database import OptimizedDatabaseManager
    db = OptimizedDatabaseManager()
    stats = db.get_statistics()
    print('✓ 优化的数据库管理器工作正常')
    print(f'连接池大小: {stats[\"pool\"][\"pool_size\"]}')
    db.close()
except Exception as e:
    print(f'✗ 测试失败: {e}')
" 2>nul

echo.
echo ========================================
echo 优化完成
echo ========================================
echo.

echo 🎉 数据库优化完成！
echo.
echo 优化内容总结:
echo ✓ 应用了优化的数据库管理器
echo ✓ 启用了连接池管理
echo ✓ 添加了死锁自动重试机制
echo ✓ 优化了数据库查询和锁策略
echo.
echo 后续建议:
echo 1. 定期运行死锁检测器: python database_deadlock_detector.py
echo 2. 监控数据库性能和死锁情况
echo 3. 定期清理旧数据: db.cleanup_old_data()
echo.
echo 如果仍有死锁问题，请:
echo 1. 运行死锁检测器分析具体原因
echo 2. 检查是否有其他程序同时访问数据库
echo 3. 参考 DATABASE_OPTIMIZATION_GUIDE.md 进行进一步优化
echo.

:end
pause
