#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
二维码显示调试脚本
用于调试二维码显示问题
"""

import cv2
import numpy as np
import qrcode
import json
import time
import logging

# 配置日志
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(levelname)s - %(message)s')

def test_basic_opencv_display():
    """测试基本的OpenCV显示功能"""
    print("=" * 50)
    print("测试基本OpenCV显示功能")
    print("=" * 50)
    
    try:
        # 创建一个简单的测试图像
        test_image = np.zeros((300, 300, 3), dtype=np.uint8)
        test_image[:] = (0, 255, 0)  # 绿色背景
        
        # 添加文本
        cv2.putText(test_image, "OpenCV Test", (50, 150), cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
        
        # 显示图像
        window_name = "OpenCV Test"
        cv2.namedWindow(window_name, cv2.WINDOW_NORMAL)
        cv2.resizeWindow(window_name, 300, 300)
        cv2.imshow(window_name, test_image)
        
        print("显示绿色测试窗口，3秒后自动关闭...")
        cv2.waitKey(3000)
        cv2.destroyWindow(window_name)
        print("✓ OpenCV基本显示功能正常")
        return True
        
    except Exception as e:
        print(f"✗ OpenCV基本显示功能失败: {e}")
        return False

def test_qr_generation():
    """测试二维码生成"""
    print("\n" + "=" * 50)
    print("测试二维码生成功能")
    print("=" * 50)
    
    try:
        # 创建测试数据
        test_data = {
            "id": "debug_test_123",
            "type": 1,
            "data": "二维码生成测试数据"
        }
        
        json_string = json.dumps(test_data, ensure_ascii=False)
        print(f"测试数据: {json_string}")
        
        # 生成二维码
        qr = qrcode.QRCode(
            version=1,
            error_correction=qrcode.constants.ERROR_CORRECT_L,
            box_size=10,
            border=4,
        )
        
        qr.add_data(json_string)
        qr.make(fit=True)
        
        # 创建二维码图像
        qr_img = qr.make_image(fill_color="black", back_color="white")
        
        # 转换为OpenCV格式
        qr_array = np.array(qr_img)
        if len(qr_array.shape) == 2:  # 灰度图
            qr_cv = cv2.cvtColor(qr_array.astype(np.uint8) * 255, cv2.COLOR_GRAY2BGR)
        else:
            qr_cv = cv2.cvtColor(qr_array.astype(np.uint8), cv2.COLOR_RGB2BGR)
        
        print(f"✓ 二维码生成成功，尺寸: {qr_cv.shape}")
        return qr_cv
        
    except Exception as e:
        print(f"✗ 二维码生成失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_qr_display_with_centering():
    """测试二维码居中显示"""
    print("\n" + "=" * 50)
    print("测试二维码居中显示功能")
    print("=" * 50)
    
    # 生成二维码
    qr_image = test_qr_generation()
    if qr_image is None:
        return False
    
    try:
        # 调整尺寸为900x900
        target_size = 900
        resized_qr = cv2.resize(qr_image, (target_size, target_size), interpolation=cv2.INTER_NEAREST)
        print(f"二维码调整为: {target_size}x{target_size}")
        
        # 获取屏幕尺寸
        try:
            import tkinter as tk
            root = tk.Tk()
            root.withdraw()
            screen_width = root.winfo_screenwidth()
            screen_height = root.winfo_screenheight()
            root.destroy()
            
            print(f"屏幕尺寸: {screen_width}x{screen_height}")
            
            # 计算居中位置
            x = (screen_width - target_size) // 2
            y = (screen_height - target_size) // 2
            print(f"计算的居中位置: ({x}, {y})")
            
        except Exception as e:
            print(f"无法获取屏幕尺寸: {e}")
            x, y = 100, 100
        
        # 创建窗口
        window_name = "QR Code Debug Display"
        cv2.namedWindow(window_name, cv2.WINDOW_NORMAL)
        cv2.resizeWindow(window_name, target_size, target_size)
        cv2.moveWindow(window_name, x, y)
        
        # 尝试设置置顶
        try:
            cv2.setWindowProperty(window_name, cv2.WND_PROP_TOPMOST, 1)
        except:
            print("无法设置窗口置顶")
        
        # 显示二维码
        cv2.imshow(window_name, resized_qr)
        print("二维码已显示，将显示5秒...")
        
        # 显示5秒，每秒检查一次
        for i in range(5):
            key = cv2.waitKey(1000)
            if key == 27:  # ESC键
                print("用户按ESC键退出")
                break
            
            # 检查窗口是否被关闭
            try:
                if cv2.getWindowProperty(window_name, cv2.WND_PROP_VISIBLE) < 1:
                    print("窗口被用户关闭")
                    break
            except:
                pass
            
            print(f"显示中... {i+1}/5 秒")
        
        cv2.destroyWindow(window_name)
        cv2.waitKey(1)
        print("✓ 二维码显示测试完成")
        return True
        
    except Exception as e:
        print(f"✗ 二维码显示测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_environment():
    """测试环境检查"""
    print("\n" + "=" * 50)
    print("环境检查")
    print("=" * 50)
    
    # 检查Python版本
    import sys
    print(f"Python版本: {sys.version}")
    
    # 检查OpenCV
    try:
        import cv2
        print(f"OpenCV版本: {cv2.__version__}")
    except Exception as e:
        print(f"OpenCV问题: {e}")
        return False
    
    # 检查qrcode
    try:
        import qrcode
        print(f"QRCode库: 已安装")
    except Exception as e:
        print(f"QRCode库问题: {e}")
        return False
    
    # 检查tkinter
    try:
        import tkinter as tk
        root = tk.Tk()
        root.withdraw()
        print(f"Tkinter: 可用")
        root.destroy()
    except Exception as e:
        print(f"Tkinter问题: {e}")
        return False
    
    # 检查显示环境
    try:
        import os
        display = os.environ.get('DISPLAY', 'Not set')
        print(f"DISPLAY环境变量: {display}")
    except:
        pass
    
    print("✓ 环境检查完成")
    return True

def main():
    """主调试函数"""
    print("DataTransmission 二维码显示调试工具")
    print("用于诊断二维码显示问题")
    
    # 环境检查
    if not test_environment():
        print("环境检查失败，请解决环境问题后重试")
        return
    
    # 基本OpenCV测试
    if not test_basic_opencv_display():
        print("OpenCV基本功能测试失败")
        return
    
    # 二维码显示测试
    if not test_qr_display_with_centering():
        print("二维码显示测试失败")
        return
    
    print("\n" + "=" * 50)
    print("调试完成")
    print("=" * 50)
    print("如果所有测试都通过，说明二维码显示功能应该正常工作")
    print("如果某个测试失败，请根据错误信息进行相应的修复")

if __name__ == "__main__":
    main()
