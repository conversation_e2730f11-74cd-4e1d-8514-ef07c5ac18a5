# CentOS 7 带界面系统部署完整指南

## 系统要求确认

### 基础要求
- **操作系统**: CentOS 7.x (带GNOME桌面环境)
- **内存**: 至少4GB RAM
- **磁盘空间**: 至少5GB可用空间
- **权限**: root或sudo权限
- **网络**: 构建阶段需要网络下载依赖

### 图形界面确认
```bash
# 检查是否安装了图形界面
systemctl get-default

# 如果不是graphical.target，安装图形界面
sudo yum groupinstall -y "GNOME Desktop"
sudo systemctl set-default graphical.target
```

## 第一步：在开发环境准备部署包

### 方法一：使用CentOS 7专用构建脚本（推荐）
```bash
# 在您的Anaconda环境中运行
chmod +x build_centos7_package.sh
./build_centos7_package.sh
```

### 方法二：使用通用构建脚本
```bash
chmod +x build_offline_package.sh
./build_offline_package.sh
```

## 第二步：传输到CentOS 7机器

### 使用USB传输（离线环境推荐）
1. 将生成的 `DataTransmission_centos7_offline_*.tar.gz` 复制到USB设备
2. 在CentOS 7机器上挂载USB并复制文件

### 使用网络传输
```bash
scp DataTransmission_centos7_offline_*.tar.gz user@centos7-server:/tmp/
```

## 第三步：CentOS 7系统准备

### 1. 系统更新和基础工具
```bash
sudo yum update -y
sudo yum install -y wget curl vim net-tools
```

### 2. 安装EPEL仓库
```bash
sudo yum install -y epel-release
```

### 3. 检查图形界面
```bash
# 确保在图形界面下运行
echo $DISPLAY
# 应该显示类似 :0.0 的值

# 如果通过SSH连接，启用X11转发
ssh -X username@hostname
```

## 第四步：部署应用程序

### 解压部署包
```bash
cd /tmp
tar -xzf DataTransmission_centos7_offline_*.tar.gz
cd DataTransmission_centos7_offline
```

### 运行自动安装脚本
```bash
sudo ./scripts/install_centos7_offline.sh
```

### 如果有conda环境，使用conda部署
```bash
# 复制conda部署脚本到项目目录
cp centos7_conda_deploy.sh ./
chmod +x centos7_conda_deploy.sh
./centos7_conda_deploy.sh
```

## 第五步：数据库配置

### 1. MariaDB安全配置
```bash
sudo mysql_secure_installation
```
按提示设置：
- 设置root密码
- 删除匿名用户
- 禁止root远程登录
- 删除test数据库

### 2. 创建应用数据库
```bash
mysql -u root -p
```

在MySQL中执行：
```sql
CREATE DATABASE DataTransmission CHARACTER SET utf8 COLLATE utf8_general_ci;
CREATE USER 'datatrans'@'localhost' IDENTIFIED BY 'JKga#123';
GRANT ALL PRIVILEGES ON DataTransmission.* TO 'datatrans'@'localhost';
FLUSH PRIVILEGES;
EXIT;
```

## 第六步：应用程序配置

### 编辑配置文件
```bash
sudo vi /opt/DataTransmission/config.py
```

确认配置正确：
```python
DATABASE_CONFIG = {
    'host': 'localhost',
    'port': 3306,
    'database': 'DataTransmission',
    'user': 'datatrans',
    'password': 'JKga#123',
    'charset': 'utf8'
}

# 二维码显示配置
QR_DISPLAY_TIME = 2
QR_DISPLAY_SIZE = 900  # 900x900像素，居中显示

# 摄像头配置
CAMERA_INDEX = 0  # 根据实际摄像头调整
```

## 第七步：防火墙和SELinux配置

### 配置防火墙
```bash
# 检查防火墙状态
sudo systemctl status firewalld

# 开放HTTP端口
sudo firewall-cmd --permanent --add-port=5000/tcp
sudo firewall-cmd --reload

# 查看开放的端口
sudo firewall-cmd --list-ports
```

### SELinux配置（推荐临时禁用）
```bash
# 查看SELinux状态
getenforce

# 临时禁用SELinux
sudo setenforce 0

# 永久禁用（需要重启）
sudo sed -i 's/SELINUX=enforcing/SELINUX=disabled/' /etc/selinux/config
```

## 第八步：启动和测试

### 启动服务
```bash
# 启动服务
sudo systemctl start datatransmission

# 检查状态
sudo systemctl status datatransmission

# 设置开机自启
sudo systemctl enable datatransmission
```

### 测试功能

#### 1. 测试HTTP接口
```bash
# 健康检查
curl http://localhost:5000/health

# 测试数据接收接口
curl -X POST http://localhost:5000/receiveData \
  -H "Content-Type: application/json" \
  -d '{"id":"'$(date +%s)'","type":1,"data":"测试数据"}'
```

#### 2. 测试二维码显示
```bash
# 手动插入测试数据
mysql -u datatrans -p DataTransmission -e "
INSERT INTO transmission_data (id, type, data, status) 
VALUES ('$(date +%s)', 1, '测试二维码数据', 0);"

# 观察二维码是否在屏幕中央显示
```

#### 3. 测试摄像头功能
```bash
# 检查摄像头设备
ls /dev/video*

# 检查摄像头权限
sudo usermod -a -G video $USER
```

## CentOS 7特殊注意事项

### 1. Python版本兼容性
CentOS 7默认Python版本较低，建议：
```bash
# 安装Python 3.6+
sudo yum install -y python36 python36-pip python36-devel

# 或安装更新的Python版本
sudo yum install -y centos-release-scl
sudo yum install -y rh-python38
scl enable rh-python38 bash
```

### 2. 图形界面问题解决
```bash
# 如果二维码无法显示，检查图形环境
echo $DISPLAY
xhost +local:

# 安装额外的图形库
sudo yum install -y python3-tkinter
sudo yum install -y mesa-libGL-devel
```

### 3. 摄像头权限问题
```bash
# 添加用户到video组
sudo usermod -a -G video $USER

# 重新登录或使用newgrp
newgrp video

# 检查摄像头权限
ls -l /dev/video*
```

### 4. 依赖库问题
```bash
# 如果OpenCV有问题，安装额外依赖
sudo yum install -y gtk3-devel
sudo yum install -y libpng-devel libjpeg-turbo-devel

# 如果pyzbar有问题
sudo yum install -y zbar zbar-devel
```

## 故障排除

### 常见问题及解决方案

#### 1. 服务启动失败
```bash
# 查看详细日志
sudo journalctl -u datatransmission -f

# 手动启动测试
cd /opt/DataTransmission
sudo ./start.sh
```

#### 2. 数据库连接失败
```bash
# 检查MariaDB状态
sudo systemctl status mariadb

# 测试数据库连接
mysql -u datatrans -p DataTransmission -e "SHOW TABLES;"
```

#### 3. 二维码不显示
```bash
# 检查DISPLAY变量
echo $DISPLAY

# 测试图形界面
xclock &

# 检查tkinter
python3 -c "import tkinter; print('Tkinter OK')"
```

#### 4. 摄像头无法访问
```bash
# 检查摄像头设备
lsusb | grep -i camera
ls /dev/video*

# 测试摄像头
sudo yum install -y cheese
cheese  # 图形化摄像头测试工具
```

## 维护和监控

### 日常管理命令
```bash
# 服务管理
sudo systemctl start datatransmission
sudo systemctl stop datatransmission
sudo systemctl restart datatransmission
sudo systemctl status datatransmission

# 日志查看
sudo journalctl -u datatransmission -f
tail -f /opt/DataTransmission/data_transmission.log

# 进程监控
ps aux | grep python
netstat -tlnp | grep 5000
```

### 性能监控
```bash
# 系统资源监控
top
htop
iotop

# 磁盘空间
df -h
du -sh /opt/DataTransmission/

# 内存使用
free -h
```

### 备份策略
```bash
# 备份配置文件
sudo cp /opt/DataTransmission/config.py /opt/DataTransmission/config.py.backup

# 备份数据库
mysqldump -u datatrans -p DataTransmission > datatransmission_backup_$(date +%Y%m%d).sql

# 备份整个应用目录
sudo tar -czf datatransmission_backup_$(date +%Y%m%d).tar.gz /opt/DataTransmission/
```

## 安全建议

1. **定期更新系统**：`sudo yum update`
2. **强化数据库密码**：使用复杂密码
3. **限制网络访问**：配置防火墙规则
4. **监控日志**：定期检查应用和系统日志
5. **备份数据**：定期备份配置和数据库

这个指南专门针对CentOS 7带界面的系统，考虑了该系统的特殊性和兼容性问题。按照这个指南操作，应该能够成功部署您的DataTransmission项目。
