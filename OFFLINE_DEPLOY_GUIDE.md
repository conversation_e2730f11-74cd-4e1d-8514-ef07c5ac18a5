# 离线部署完整指南

## 概述
本指南将帮助您将DataTransmission项目从Anaconda环境打包并部署到离线的Ubuntu服务器上。

## 第一步：在开发环境中准备部署包

### 方法一：使用自动构建脚本（推荐）

#### 在Linux/macOS环境下：
```bash
chmod +x build_offline_package.sh
./build_offline_package.sh
```

#### 在Windows环境下：
```cmd
build_offline_package.bat
```

### 方法二：手动创建部署包

#### 1. 创建项目目录结构
```bash
mkdir -p DataTransmission_offline/{src,dependencies,scripts}
```

#### 2. 复制项目文件
```bash
cp *.py DataTransmission_offline/src/
cp requirements.txt DataTransmission_offline/src/
cp README.md DataTransmission_offline/
cp environment.yml DataTransmission_offline/
```

#### 3. 下载离线依赖包
```bash
cd DataTransmission_offline/dependencies
mkdir python_packages
pip download -r ../src/requirements.txt --dest ./python_packages
```

#### 4. 导出conda环境
```bash
conda env export > DataTransmission_offline/environment.yml
```

#### 5. 创建压缩包
```bash
tar -czf DataTransmission_offline.tar.gz DataTransmission_offline/
```

## 第二步：传输到目标Ubuntu机器

### 使用SCP传输（如果有网络连接）
```bash
scp DataTransmission_offline.tar.gz user@target-server:/tmp/
```

### 使用USB或其他离线方式
将压缩包复制到USB设备，然后在目标机器上解压。

## 第三步：在Ubuntu机器上部署

### 1. 解压部署包
```bash
cd /tmp
tar -xzf DataTransmission_offline.tar.gz
cd DataTransmission_offline
```

### 2. 运行自动安装脚本
```bash
sudo ./scripts/install_offline.sh
```

### 3. 手动安装（如果自动脚本失败）

#### 安装系统依赖
```bash
sudo apt update
sudo apt install -y python3.10 python3.10-pip python3.10-venv python3.10-dev
sudo apt install -y libzbar0 libzbar-dev libgl1-mesa-glx libglib2.0-0
sudo apt install -y mysql-server mysql-client libmysqlclient-dev pkg-config
```

#### 创建项目目录
```bash
sudo mkdir -p /opt/DataTransmission
sudo cp -r src/* /opt/DataTransmission/
cd /opt/DataTransmission
```

#### 创建虚拟环境
```bash
sudo python3.10 -m venv venv
sudo chown -R $USER:$USER venv
source venv/bin/activate
```

#### 安装Python依赖
```bash
pip install --no-index --find-links /tmp/DataTransmission_offline/dependencies/python_packages -r requirements.txt
```

## 第四步：配置数据库

### 1. 启动MySQL服务
```bash
sudo systemctl start mysql
sudo systemctl enable mysql
```

### 2. 创建数据库和用户
```bash
sudo mysql -u root -p
```

在MySQL中执行：
```sql
CREATE DATABASE DataTransmission CHARACTER SET utf8 COLLATE utf8_general_ci;
CREATE USER 'datatrans'@'localhost' IDENTIFIED BY 'your_password';
GRANT ALL PRIVILEGES ON DataTransmission.* TO 'datatrans'@'localhost';
FLUSH PRIVILEGES;
EXIT;
```

### 3. 配置应用程序
编辑配置文件：
```bash
sudo nano /opt/DataTransmission/config.py
```

修改数据库连接信息：
```python
DATABASE_CONFIG = {
    'host': 'localhost',
    'port': 3306,
    'database': 'DataTransmission',
    'user': 'datatrans',
    'password': 'your_password',  # 修改为实际密码
    'charset': 'utf8'
}
```

## 第五步：启动服务

### 方法一：使用systemd服务（推荐）
```bash
sudo systemctl start datatransmission
sudo systemctl enable datatransmission
sudo systemctl status datatransmission
```

### 方法二：手动启动
```bash
cd /opt/DataTransmission
sudo ./start.sh
```

## 第六步：验证部署

### 1. 检查服务状态
```bash
sudo systemctl status datatransmission
```

### 2. 检查端口监听
```bash
netstat -tlnp | grep 5000
```

### 3. 测试API接口
```bash
curl http://localhost:5000/health
```

### 4. 查看日志
```bash
sudo journalctl -u datatransmission -f
```

## 故障排除

### 常见问题及解决方案

#### 1. Python 3.10 未找到
```bash
# 添加deadsnakes PPA（如果有网络）
sudo add-apt-repository ppa:deadsnakes/ppa
sudo apt update
sudo apt install python3.10 python3.10-venv python3.10-dev
```

#### 2. MySQL连接失败
- 检查MySQL服务状态：`sudo systemctl status mysql`
- 检查防火墙设置：`sudo ufw status`
- 验证数据库用户权限

#### 3. 摄像头无法访问
- 检查摄像头设备：`ls /dev/video*`
- 修改摄像头索引：编辑`config.py`中的`CAMERA_INDEX`
- 检查用户权限：将用户添加到video组

#### 4. 二维码显示问题
- 确保有图形界面支持
- 检查X11转发（如果通过SSH连接）
- 安装图形库：`sudo apt install python3-tk`

#### 5. 端口被占用
```bash
# 查找占用端口的进程
sudo lsof -i :5000
# 修改config.py中的端口配置
```

## 维护和管理

### 日常管理命令
```bash
# 启动服务
sudo systemctl start datatransmission

# 停止服务
sudo systemctl stop datatransmission

# 重启服务
sudo systemctl restart datatransmission

# 查看状态
sudo systemctl status datatransmission

# 查看日志
sudo journalctl -u datatransmission -f

# 查看最近的错误日志
sudo journalctl -u datatransmission --since "1 hour ago" -p err
```

### 配置文件位置
- 主配置文件：`/opt/DataTransmission/config.py`
- 服务配置：`/etc/systemd/system/datatransmission.service`
- 日志文件：`/opt/DataTransmission/data_transmission.log`

### 备份重要文件
```bash
# 备份配置文件
sudo cp /opt/DataTransmission/config.py /opt/DataTransmission/config.py.backup

# 备份数据库
mysqldump -u datatrans -p DataTransmission > datatransmission_backup.sql
```

## 安全建议

1. **修改默认密码**：确保数据库密码足够强壮
2. **防火墙配置**：只开放必要的端口
3. **定期更新**：定期检查和更新系统安全补丁
4. **日志监控**：定期检查应用程序日志
5. **访问控制**：限制对配置文件的访问权限

```bash
sudo chmod 600 /opt/DataTransmission/config.py
sudo chown root:root /opt/DataTransmission/config.py
```
