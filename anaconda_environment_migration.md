# Anaconda环境搬运部署指南

## 方案概述

由于您已经在开发环境中建立了完整的Anaconda虚拟环境，推荐使用**Anaconda环境搬运**的方式，这是最可靠和高效的部署方法。

## 🎯 推荐方案：Anaconda环境完整搬运

### 优势：
- ✅ 环境一致性最高，避免依赖冲突
- ✅ 包含所有已测试的依赖版本
- ✅ 部署简单，减少出错概率
- ✅ 支持离线部署
- ✅ 便于环境管理和维护

## 第一步：在开发环境中准备环境包

### 1.1 导出环境配置
```bash
# 激活您的项目环境
conda activate your_project_env_name

# 导出环境配置（包含所有依赖和版本信息）
conda env export > datatransmission_environment.yml

# 导出pip依赖列表（备用）
pip freeze > requirements_exact.txt

# 查看环境信息
conda info --envs
conda list
```

### 1.2 创建环境压缩包（推荐方法）
```bash
# 方法一：使用conda-pack打包整个环境（推荐）
# 首先安装conda-pack
conda install conda-pack

# 打包当前环境
conda pack -n your_project_env_name -o datatransmission_env.tar.gz

# 或者如果当前就在目标环境中
conda pack -o datatransmission_env.tar.gz
```

### 1.3 备用方法：手动收集依赖
```bash
# 如果conda-pack不可用，使用传统方法
mkdir datatransmission_offline_env
cd datatransmission_offline_env

# 下载所有conda包
conda env export > environment.yml
mkdir conda_packages
conda list --explicit > spec-file.txt

# 下载pip包
mkdir pip_packages
pip download -r ../requirements.txt -d pip_packages/
```

## 第二步：准备项目文件和部署脚本

### 2.1 创建完整部署包
```bash
# 创建部署目录
mkdir DataTransmission_anaconda_deploy
cd DataTransmission_anaconda_deploy

# 复制项目文件
cp -r /path/to/your/project/* ./src/

# 复制环境包
cp datatransmission_env.tar.gz ./

# 复制环境配置文件
cp datatransmission_environment.yml ./
```

### 2.2 创建CentOS 7部署脚本
创建 `deploy_centos7_anaconda.sh`：

```bash
#!/bin/bash
# CentOS 7 Anaconda环境部署脚本

echo "=== DataTransmission Anaconda环境部署 (CentOS 7) ==="

# 检查系统
if ! grep -q "CentOS Linux release 7" /etc/redhat-release 2>/dev/null; then
    echo "警告: 此脚本为CentOS 7设计"
fi

# 设置变量
PROJECT_NAME="DataTransmission"
ENV_NAME="datatransmission"
INSTALL_DIR="/opt/${PROJECT_NAME}"
ANACONDA_DIR="/opt/anaconda3"

# 检查是否已安装Anaconda
if [ ! -d "$ANACONDA_DIR" ]; then
    echo "在CentOS 7上安装Anaconda..."
    
    # 下载并安装Anaconda（如果有网络）
    cd /tmp
    wget https://repo.anaconda.com/archive/Anaconda3-2023.09-0-Linux-x86_64.sh -O anaconda.sh
    sudo bash anaconda.sh -b -p $ANACONDA_DIR
    
    # 设置环境变量
    echo 'export PATH="/opt/anaconda3/bin:$PATH"' | sudo tee -a /etc/profile
    source /etc/profile
    
    # 初始化conda
    sudo $ANACONDA_DIR/bin/conda init bash
else
    echo "检测到已安装的Anaconda: $ANACONDA_DIR"
fi

# 激活conda
source $ANACONDA_DIR/etc/profile.d/conda.sh

# 解压环境包
echo "解压Anaconda环境..."
sudo mkdir -p $ANACONDA_DIR/envs/$ENV_NAME
sudo tar -xzf datatransmission_env.tar.gz -C $ANACONDA_DIR/envs/$ENV_NAME

# 激活环境
echo "激活环境..."
source $ANACONDA_DIR/envs/$ENV_NAME/bin/activate

# 修复环境路径
$ANACONDA_DIR/envs/$ENV_NAME/bin/conda-unpack

# 安装系统依赖
echo "安装CentOS 7系统依赖..."
sudo yum update -y
sudo yum install -y epel-release
sudo yum install -y mariadb-server mariadb
sudo yum install -y zbar zbar-devel
sudo yum install -y mesa-libGL mesa-libGL-devel
sudo yum install -y python3-tkinter
sudo yum install -y libX11-devel libXext-devel

# 启动数据库
sudo systemctl start mariadb
sudo systemctl enable mariadb

# 创建项目目录
sudo mkdir -p $INSTALL_DIR
sudo cp -r src/* $INSTALL_DIR/
sudo chown -R $USER:$USER $INSTALL_DIR

# 创建启动脚本
cat > $INSTALL_DIR/start_anaconda.sh << EOF
#!/bin/bash
# 使用Anaconda环境启动

# 激活conda
source $ANACONDA_DIR/etc/profile.d/conda.sh
conda activate $ENV_NAME

# 设置显示环境
export DISPLAY=\${DISPLAY:-:0.0}

# 切换到项目目录
cd $INSTALL_DIR

# 启动程序
python main.py
EOF

chmod +x $INSTALL_DIR/start_anaconda.sh

echo "部署完成！"
```

## 第三步：在CentOS 7上部署

### 3.1 传输部署包到CentOS 7
```bash
# 使用scp传输（如果有网络）
scp -r DataTransmission_anaconda_deploy/ user@centos7-server:/tmp/

# 或使用USB等离线方式传输
```

### 3.2 在CentOS 7上执行部署

#### 方法一：如果CentOS 7已安装Anaconda
```bash
cd /tmp/DataTransmission_anaconda_deploy

# 解压环境到现有Anaconda中
conda env create -f datatransmission_environment.yml -n datatransmission

# 或使用conda-pack包
mkdir -p ~/anaconda3/envs/datatransmission
tar -xzf datatransmission_env.tar.gz -C ~/anaconda3/envs/datatransmission
source ~/anaconda3/envs/datatransmission/bin/activate
conda-unpack

# 复制项目文件
sudo cp -r src/* /opt/DataTransmission/
```

#### 方法二：如果CentOS 7未安装Anaconda（推荐）
```bash
# 运行完整部署脚本
chmod +x deploy_centos7_anaconda.sh
sudo ./deploy_centos7_anaconda.sh
```

### 3.3 配置数据库
```bash
# 配置MariaDB
sudo mysql_secure_installation

# 创建数据库
mysql -u root -p << EOF
CREATE DATABASE DataTransmission CHARACTER SET utf8 COLLATE utf8_general_ci;
CREATE USER 'datatrans'@'localhost' IDENTIFIED BY 'JKga#123';
GRANT ALL PRIVILEGES ON DataTransmission.* TO 'datatrans'@'localhost';
FLUSH PRIVILEGES;
EOF
```

### 3.4 配置应用程序
```bash
# 编辑配置文件
sudo vi /opt/DataTransmission/config.py

# 确认数据库配置正确
# 确认摄像头索引正确
# 确认二维码显示配置
```

## 第四步：创建系统服务

### 4.1 创建systemd服务文件
```bash
sudo tee /etc/systemd/system/datatransmission-anaconda.service > /dev/null << EOF
[Unit]
Description=Data Transmission Client (Anaconda)
After=network.target mariadb.service

[Service]
Type=simple
User=root
WorkingDirectory=/opt/DataTransmission
Environment=DISPLAY=:0.0
ExecStart=/opt/DataTransmission/start_anaconda.sh
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
EOF
```

### 4.2 启动服务
```bash
# 重新加载systemd
sudo systemctl daemon-reload

# 启动服务
sudo systemctl start datatransmission-anaconda

# 设置开机自启
sudo systemctl enable datatransmission-anaconda

# 检查状态
sudo systemctl status datatransmission-anaconda
```

## 第五步：验证部署

### 5.1 功能测试
```bash
# 测试HTTP接口
curl http://localhost:5000/health

# 测试数据接收
curl -X POST http://localhost:5000/receiveData \
  -H "Content-Type: application/json" \
  -d '{"id":"'$(date +%s)'","type":1,"data":"测试数据"}'

# 查看日志
sudo journalctl -u datatransmission-anaconda -f
```

### 5.2 环境验证
```bash
# 激活环境并检查
source /opt/anaconda3/etc/profile.d/conda.sh
conda activate datatransmission

# 验证Python包
python -c "
import flask, mysql.connector, cv2, qrcode, pyzbar
print('所有依赖包导入成功')
"

# 检查环境信息
conda info
conda list
```

## 常见问题解决

### Q1: conda-pack不可用怎么办？
```bash
# 使用传统方法
conda env export > environment.yml
# 然后在目标机器上：
conda env create -f environment.yml -n datatransmission
```

### Q2: CentOS 7上Anaconda安装失败？
```bash
# 使用Miniconda（更轻量）
wget https://repo.anaconda.com/miniconda/Miniconda3-latest-Linux-x86_64.sh
bash Miniconda3-latest-Linux-x86_64.sh -b -p /opt/miniconda3
```

### Q3: 图形界面问题？
```bash
# 确保安装了图形界面
sudo yum groupinstall -y "GNOME Desktop"
sudo systemctl set-default graphical.target

# 设置DISPLAY变量
export DISPLAY=:0.0
```

### Q4: 权限问题？
```bash
# 修改Anaconda目录权限
sudo chown -R $USER:$USER /opt/anaconda3/envs/datatransmission

# 或使用用户级安装
bash Anaconda3-*.sh  # 安装到用户目录
```

## 总结

使用Anaconda环境搬运是最可靠的部署方式，特别适合您已经有完整开发环境的情况。这种方法确保了：

1. **环境一致性**：开发和生产环境完全一致
2. **依赖完整性**：所有包版本都经过测试
3. **部署简单性**：减少配置和调试时间
4. **维护便利性**：便于后续更新和管理

推荐使用conda-pack方法，如果不可用则使用environment.yml方法。
