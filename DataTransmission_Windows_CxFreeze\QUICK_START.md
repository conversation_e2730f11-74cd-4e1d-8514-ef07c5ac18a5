# DataTransmission 快速启动指南

## 🚀 快速解决依赖问题

### 第一步：安装Python依赖（必须）

在内网机器上，程序缺少必要的Python模块。请按以下步骤安装：

#### 方法1：离线安装（推荐）
```cmd
# 进入离线包目录
cd offline_packages

# 运行离线安装脚本
install_offline.bat
```

#### 方法2：在线安装（如果有网络）
```cmd
# 运行在线安装脚本
install_dependencies.bat
```

#### 方法3：手动安装
```cmd
# 使用离线包手动安装
pip install --no-index --find-links offline_packages mysql-connector-python
pip install --no-index --find-links offline_packages opencv-python
pip install --no-index --find-links offline_packages qrcode[pil]
pip install --no-index --find-links offline_packages pyzbar
pip install --no-index --find-links offline_packages Pillow
pip install --no-index --find-links offline_packages Flask
pip install --no-index --find-links offline_packages APScheduler
pip install --no-index --find-links offline_packages numpy
```

### 第二步：配置数据库

1. **安装MySQL数据库**（如果未安装）
2. **启动MySQL服务**：
   ```cmd
   net start mysql
   ```
3. **创建数据库**：
   ```sql
   CREATE DATABASE DataTransmission CHARACTER SET utf8 COLLATE utf8_general_ci;
   ```
4. **编辑配置文件** `config.py`：
   ```python
   DATABASE_CONFIG = {
       'host': 'localhost',
       'port': 3306,
       'database': 'DataTransmission',
       'user': 'root',
       'password': '你的MySQL密码',  # 修改这里
       'charset': 'utf8'
   }
   ```

### 第三步：启动程序

```cmd
# 使用调试启动（推荐，可以看到详细信息）
debug_start.bat

# 或直接启动
DataTransmission.exe
```

### 第四步：访问Web界面

打开浏览器访问：http://localhost:5000

## 🔧 故障排除

### 如果程序仍然无法启动

1. **运行诊断脚本**：
   ```cmd
   python diagnose_windows.py
   ```

2. **运行最小化测试**：
   ```cmd
   python test_minimal.py
   ```

3. **检查日志文件**：
   - `data_transmission.log`
   - `diagnose.log`
   - `test_minimal.log`

### 常见错误及解决方案

| 错误信息 | 解决方案 |
|---------|---------|
| `ModuleNotFoundError: No module named 'mysql'` | 运行 `offline_packages/install_offline.bat` |
| `Can't connect to MySQL server` | 检查MySQL服务是否启动，配置是否正确 |
| `Access denied for user 'root'` | 检查数据库密码是否正确 |
| `Address already in use` | 端口5000被占用，修改config.py中的端口 |

## 📁 文件说明

### 主要文件
- **DataTransmission.exe** - 主程序
- **config.py** - 配置文件
- **start.bat** - 普通启动脚本
- **debug_start.bat** - 调试启动脚本

### 依赖安装
- **offline_packages/** - 离线依赖包目录
- **install_dependencies.bat** - 在线安装脚本
- **requirements.txt** - 依赖列表

### 诊断工具
- **diagnose_windows.py** - 环境诊断脚本
- **test_minimal.py** - 最小化测试脚本

### 文档
- **README.md** - 详细说明文档
- **TROUBLESHOOTING.md** - 故障排除指南
- **DEPENDENCY_SOLUTION.md** - 依赖问题解决方案

## ⚡ 一键解决脚本

如果您想要一键解决所有问题，可以创建以下批处理文件：

```cmd
@echo off
echo 正在解决DataTransmission依赖问题...

echo 1. 安装Python依赖...
cd offline_packages
call install_offline.bat
cd ..

echo 2. 检查MySQL服务...
net start mysql

echo 3. 运行诊断...
python diagnose_windows.py

echo 4. 启动程序...
DataTransmission.exe

pause
```

## 📞 技术支持

如果仍然遇到问题，请提供：
1. 诊断脚本的完整输出
2. 错误日志文件
3. Python版本信息
4. MySQL版本和状态

---

**重要提示**：确保在运行程序之前先安装Python依赖，这是最常见的问题原因！
