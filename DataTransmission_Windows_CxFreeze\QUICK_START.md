# DataTransmission 快速启动指南

## 🚀 快速解决依赖问题

### 第一步：安装Python依赖（必须）

在内网机器上，程序缺少必要的Python模块。请按以下步骤安装：

#### 方法1：离线安装（推荐）
```cmd
# 进入离线包目录
cd offline_packages

# 运行离线安装脚本
install_offline.bat
```

#### 方法2：在线安装（如果有网络）
```cmd
# 运行在线安装脚本
install_dependencies.bat
```

#### 方法3：手动安装
```cmd
# 使用离线包手动安装
pip install --no-index --find-links offline_packages mysql-connector-python
pip install --no-index --find-links offline_packages opencv-python
pip install --no-index --find-links offline_packages qrcode[pil]
pip install --no-index --find-links offline_packages pyzbar
pip install --no-index --find-links offline_packages Pillow
pip install --no-index --find-links offline_packages Flask
pip install --no-index --find-links offline_packages APScheduler
pip install --no-index --find-links offline_packages numpy
```

### 第二步：配置数据库

#### 🚀 一键数据库设置（推荐）
```cmd
# 运行一键数据库设置脚本
setup_database.bat
```

#### 📋 手动设置步骤

1. **安装MySQL数据库**（如果未安装）
   - 下载MySQL 8.0安装包
   - 安装时记住root密码
   - 确保选择"Install as Windows Service"

2. **启动MySQL服务**：
   ```cmd
   net start mysql
   ```

3. **创建数据库**：
   ```cmd
   # 方法1: 使用SQL脚本
   mysql -u root -p < create_database.sql

   # 方法2: 手动创建
   mysql -u root -p
   CREATE DATABASE DataTransmission CHARACTER SET utf8 COLLATE utf8_general_ci;
   ```

4. **编辑配置文件** `config.py`：
   ```python
   DATABASE_CONFIG = {
       'host': 'localhost',
       'port': 3306,
       'database': 'DataTransmission',
       'user': 'root',
       'password': '你的MySQL密码',  # 修改这里
       'charset': 'utf8'
   }
   ```

#### 🔧 数据库问题诊断

**快速诊断**：
```cmd
quick_test.bat
```

**详细分步测试**：
```cmd
# 完整的5步测试
run_all_step_tests.bat

# 或单独运行每个步骤
test_step1_mysql_service.bat      # MySQL服务测试
python test_step2_python_modules.py    # Python模块测试
python test_step3_config_file.py       # 配置文件测试
python test_step4_mysql_connection.py  # MySQL连接测试
python test_step5_application_components.py  # 应用组件测试
```

**问题修复工具**：
```cmd
fix_database_issues.bat
```

### 第三步：启动程序

```cmd
# 使用调试启动（推荐，可以看到详细信息）
debug_start.bat

# 或直接启动
DataTransmission.exe
```

### 第四步：访问Web界面

打开浏览器访问：http://localhost:5000

## 🔧 故障排除

### 如果程序仍然无法启动

1. **运行诊断脚本**：
   ```cmd
   python diagnose_windows.py
   ```

2. **运行最小化测试**：
   ```cmd
   python test_minimal.py
   ```

3. **检查日志文件**：
   - `data_transmission.log`
   - `diagnose.log`
   - `test_minimal.log`

### 常见错误及解决方案

| 错误信息 | 解决方案 |
|---------|---------|
| `ModuleNotFoundError: No module named 'mysql'` | 运行 `offline_packages/install_offline.bat` |
| `Can't connect to MySQL server` | 运行 `fix_database_issues.bat` 或检查MySQL服务 |
| `Access denied for user 'root'` | 检查config.py中的密码，运行 `setup_database.bat` |
| `Unknown database 'DataTransmission'` | 运行 `setup_database.bat` 或手动创建数据库 |
| `Address already in use` | 端口5000被占用，修改config.py中的端口 |
| 程序启动后立即退出 | 运行 `python test_database_connection.py` 查看详细错误 |
| **连接时卡住不动，无异常输出** | **运行 `python test_connection_with_timeout.py`** |
| **MySQL服务状态异常** | **运行 `diagnose_mysql_service.bat`** |
| **数据库死锁，无法修改/打开** | **运行 `optimize_database.bat` 优化数据库** |
| **程序运行中出现死锁** | **运行 `python database_deadlock_detector.py`** |

### 🔍 数据库问题专项诊断

如果程序在数据库连接步骤失败，请按以下顺序排查：

#### 1. 快速诊断
```cmd
python test_database_connection.py
```

#### 2. 自动修复
```cmd
fix_database_issues.bat
```

#### 3. 一键重新设置
```cmd
setup_database.bat
```

#### 4. 连接超时问题专项诊断
如果程序在连接MySQL时卡住不动：
```cmd
# 带超时控制的连接测试
python test_connection_with_timeout.py

# MySQL服务详细诊断
diagnose_mysql_service.bat

# MySQL配置检查
python check_mysql_config.py
```

#### 5. 数据库死锁问题解决
如果程序运行中出现死锁：
```cmd
# 一键数据库优化
optimize_database.bat

# 死锁检测和修复
python database_deadlock_detector.py

# 查看优化指南
type DATABASE_OPTIMIZATION_GUIDE.md
```

#### 6. 手动检查清单
- [ ] MySQL服务是否运行：`sc query mysql`
- [ ] 密码是否正确：检查config.py
- [ ] 数据库是否存在：`mysql -u root -p -e "SHOW DATABASES;"`
- [ ] 防火墙是否阻止3306端口
- [ ] 端口3306是否在监听：`netstat -ano | findstr :3306`
- [ ] 是否有数据库死锁：`python database_deadlock_detector.py`

## 📁 文件说明

### 主要文件
- **DataTransmission.exe** - 主程序
- **config.py** - 配置文件
- **start.bat** - 普通启动脚本
- **debug_start.bat** - 调试启动脚本

### 依赖安装
- **offline_packages/** - 离线依赖包目录
- **install_dependencies.bat** - 在线安装脚本
- **requirements.txt** - 依赖列表

### 诊断工具
- **diagnose_windows.py** - 环境诊断脚本
- **test_minimal.py** - 最小化测试脚本

### 文档
- **README.md** - 详细说明文档
- **TROUBLESHOOTING.md** - 故障排除指南
- **DEPENDENCY_SOLUTION.md** - 依赖问题解决方案

## ⚡ 一键解决脚本

如果您想要一键解决所有问题，可以创建以下批处理文件：

```cmd
@echo off
echo 正在解决DataTransmission依赖问题...

echo 1. 安装Python依赖...
cd offline_packages
call install_offline.bat
cd ..

echo 2. 检查MySQL服务...
net start mysql

echo 3. 运行诊断...
python diagnose_windows.py

echo 4. 启动程序...
DataTransmission.exe

pause
```

## 📞 技术支持

如果仍然遇到问题，请提供：
1. 诊断脚本的完整输出
2. 错误日志文件
3. Python版本信息
4. MySQL版本和状态

---

**重要提示**：确保在运行程序之前先安装Python依赖，这是最常见的问题原因！
