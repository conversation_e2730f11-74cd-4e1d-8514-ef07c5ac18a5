#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
步骤2: Python模块测试
检查所有必需的Python模块是否正确安装
"""

import sys
import traceback

def test_module_import(module_name, description, import_statement=None):
    """测试单个模块导入"""
    try:
        if import_statement:
            exec(import_statement)
        else:
            __import__(module_name)
        print(f"✓ {module_name} ({description}) - 导入成功")
        return True
    except ImportError as e:
        print(f"✗ {module_name} ({description}) - 导入失败: {e}")
        return False
    except Exception as e:
        print(f"✗ {module_name} ({description}) - 异常: {e}")
        return False

def test_mysql_connector():
    """专门测试MySQL连接器"""
    print("\n" + "=" * 50)
    print("MySQL连接器详细测试")
    print("=" * 50)
    
    try:
        import mysql.connector
        print(f"✓ mysql.connector 导入成功")
        print(f"✓ 版本: {mysql.connector.__version__}")
        
        # 测试Error类
        from mysql.connector import Error
        print("✓ mysql.connector.Error 导入成功")
        
        # 测试连接函数
        connect_func = getattr(mysql.connector, 'connect', None)
        if connect_func:
            print("✓ mysql.connector.connect 函数可用")
        else:
            print("✗ mysql.connector.connect 函数不可用")
            return False
            
        return True
        
    except Exception as e:
        print(f"✗ MySQL连接器测试失败: {e}")
        traceback.print_exc()
        return False

def test_opencv():
    """专门测试OpenCV"""
    print("\n" + "=" * 50)
    print("OpenCV详细测试")
    print("=" * 50)
    
    try:
        import cv2
        print(f"✓ cv2 导入成功")
        print(f"✓ 版本: {cv2.__version__}")
        
        # 测试基本功能
        test_array = cv2.imread
        if test_array:
            print("✓ cv2.imread 函数可用")
        
        return True
        
    except Exception as e:
        print(f"✗ OpenCV测试失败: {e}")
        traceback.print_exc()
        return False

def test_qr_modules():
    """测试二维码相关模块"""
    print("\n" + "=" * 50)
    print("二维码模块详细测试")
    print("=" * 50)
    
    success = True
    
    # 测试qrcode
    try:
        import qrcode
        print(f"✓ qrcode 导入成功")
        print(f"✓ 版本: {qrcode.__version__}")
        
        # 测试创建二维码
        qr = qrcode.QRCode()
        print("✓ qrcode.QRCode 创建成功")
        
    except Exception as e:
        print(f"✗ qrcode 测试失败: {e}")
        success = False
    
    # 测试pyzbar
    try:
        import pyzbar
        from pyzbar import pyzbar as pyzbar_module
        print(f"✓ pyzbar 导入成功")
        print(f"✓ pyzbar.pyzbar 导入成功")
        
        # 测试解码函数
        decode_func = getattr(pyzbar_module, 'decode', None)
        if decode_func:
            print("✓ pyzbar.decode 函数可用")
        
    except Exception as e:
        print(f"✗ pyzbar 测试失败: {e}")
        success = False
    
    return success

def test_web_modules():
    """测试Web相关模块"""
    print("\n" + "=" * 50)
    print("Web框架模块详细测试")
    print("=" * 50)
    
    success = True
    
    # 测试Flask
    try:
        import flask
        from flask import Flask
        print(f"✓ Flask 导入成功")
        print(f"✓ 版本: {flask.__version__}")
        
        # 测试创建应用
        app = Flask(__name__)
        print("✓ Flask应用创建成功")
        
    except Exception as e:
        print(f"✗ Flask 测试失败: {e}")
        success = False
    
    # 测试APScheduler
    try:
        import apscheduler
        from apscheduler.schedulers.background import BackgroundScheduler
        print(f"✓ APScheduler 导入成功")
        print(f"✓ 版本: {apscheduler.__version__}")
        
        # 测试创建调度器
        scheduler = BackgroundScheduler()
        print("✓ BackgroundScheduler 创建成功")
        
    except Exception as e:
        print(f"✗ APScheduler 测试失败: {e}")
        success = False
    
    return success

def main():
    """主函数"""
    print("=" * 60)
    print("步骤2: Python模块导入测试")
    print("=" * 60)
    
    print(f"Python版本: {sys.version}")
    print(f"Python路径: {sys.executable}")
    print()
    
    # 基础模块测试
    print("基础模块测试")
    print("-" * 30)
    
    modules = [
        ("logging", "日志模块"),
        ("threading", "线程模块"),
        ("time", "时间模块"),
        ("datetime", "日期时间模块"),
        ("json", "JSON模块"),
        ("os", "操作系统模块"),
        ("sys", "系统模块"),
        ("pathlib", "路径模块"),
    ]
    
    basic_success = True
    for module_name, description in modules:
        if not test_module_import(module_name, description):
            basic_success = False
    
    # 图像处理模块测试
    print("\n图像处理模块测试")
    print("-" * 30)
    pil_success = test_module_import("PIL", "Pillow图像处理库", "from PIL import Image")
    numpy_success = test_module_import("numpy", "NumPy数值计算库")
    
    # 专项测试
    mysql_success = test_mysql_connector()
    opencv_success = test_opencv()
    qr_success = test_qr_modules()
    web_success = test_web_modules()
    
    # 总结
    print("\n" + "=" * 60)
    print("步骤2测试结果总结")
    print("=" * 60)
    
    results = [
        ("基础Python模块", basic_success),
        ("PIL/Pillow", pil_success),
        ("NumPy", numpy_success),
        ("MySQL连接器", mysql_success),
        ("OpenCV", opencv_success),
        ("二维码模块", qr_success),
        ("Web框架模块", web_success),
    ]
    
    all_success = True
    for test_name, result in results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name}: {status}")
        if not result:
            all_success = False
    
    print()
    if all_success:
        print("🎉 所有Python模块测试通过！")
        print("可以继续下一步: test_step3_config_file.py")
    else:
        print("❌ 部分模块测试失败")
        print("请运行 offline_packages/install_offline.bat 安装缺失的模块")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n用户中断测试")
    except Exception as e:
        print(f"\n测试过程中出现未预期的错误: {e}")
        traceback.print_exc()
        input("按回车键退出...")
