# Configuration file for the Sphinx documentation builder.
#
# For the full list of built-in configuration values, see the documentation:
# https://www.sphinx-doc.org/en/master/usage/configuration.html

{% if append_syspath -%}
# -- Path setup --------------------------------------------------------------

import os
import sys
sys.path.insert(0, {{ module_path | repr }})

{% endif -%}
# -- Project information -----------------------------------------------------
# https://www.sphinx-doc.org/en/master/usage/configuration.html#project-information

project = {{ project | repr }}
copyright = {{ copyright | repr }}
author = {{ author | repr }}

{%- if version %}

version = {{ version | repr }}
{%- endif %}
{%- if release %}
release = {{ release | repr }}
{%- endif %}

# -- General configuration ---------------------------------------------------
# https://www.sphinx-doc.org/en/master/usage/configuration.html#general-configuration

extensions = [{% if extensions %}
{%- for ext in extensions %}
    '{{ ext }}',
{%- endfor %}
{% endif %}]

templates_path = ['{{ dot }}templates']
exclude_patterns = [{{ exclude_patterns }}]

{% if suffix != '.rst' -%}
source_suffix = {{ suffix | repr }}
{% endif -%}

{% if root_doc != 'index' -%}
root_doc = {{ root_doc | repr }}
{% endif -%}

{% if language -%}
language = {{ language | repr }}
{%- endif %}

# -- Options for HTML output -------------------------------------------------
# https://www.sphinx-doc.org/en/master/usage/configuration.html#options-for-html-output

html_theme = 'alabaster'
html_static_path = ['{{ dot }}static']
{% if 'sphinx.ext.intersphinx' in extensions %}
# -- Options for intersphinx extension ---------------------------------------
# https://www.sphinx-doc.org/en/master/usage/extensions/intersphinx.html#configuration

intersphinx_mapping = {
    'python': ('https://docs.python.org/3', None),
}
{% endif -%}
{% if 'sphinx.ext.todo' in extensions %}
# -- Options for todo extension ----------------------------------------------
# https://www.sphinx-doc.org/en/master/usage/extensions/todo.html#configuration

todo_include_todos = True
{% endif %}
