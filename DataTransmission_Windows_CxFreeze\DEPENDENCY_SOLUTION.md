# DataTransmission 依赖问题解决方案

## 问题描述
在内网环境中运行DataTransmission时，提示缺少以下必备模块：
- mysql.connector
- cv2 (OpenCV)
- qrcode
- pyzbar
- PIL (Pillow)
- flask
- apscheduler
- numpy

## 解决方案

### 方案1：离线安装包（推荐）

我们已经为您准备了完整的离线依赖包，包含所有必需的模块。

#### 使用步骤：

1. **确保Python环境**
   ```cmd
   python --version
   ```
   如果没有Python，请先安装Python 3.8或更高版本。

2. **运行离线安装**
   ```cmd
   # 方法1：使用离线安装脚本（推荐）
   cd offline_packages
   install_offline.bat
   
   # 方法2：手动安装
   pip install --no-index --find-links offline_packages -r requirements.txt
   ```

3. **验证安装**
   运行诊断脚本检查：
   ```cmd
   python diagnose_windows.py
   ```

### 方案2：在线安装

如果内网机器可以访问互联网：

```cmd
# 运行在线安装脚本
install_dependencies.bat
```

### 方案3：手动安装单个模块

如果只缺少某些特定模块：

```cmd
# 从离线包安装单个模块
pip install --no-index --find-links offline_packages mysql-connector-python
pip install --no-index --find-links offline_packages opencv-python
pip install --no-index --find-links offline_packages qrcode[pil]
pip install --no-index --find-links offline_packages pyzbar
pip install --no-index --find-links offline_packages Pillow
pip install --no-index --find-links offline_packages Flask
pip install --no-index --find-links offline_packages APScheduler
pip install --no-index --find-links offline_packages numpy
```

## 文件说明

### 离线依赖包 (offline_packages/)
- **APScheduler-3.11.0-py3-none-any.whl** - 任务调度器
- **mysql_connector_python-9.3.0-cp312-cp312-win_amd64.whl** - MySQL连接器
- **opencv_python-*********-cp37-abi3-win_amd64.whl** - OpenCV图像处理
- **qrcode-8.2-py3-none-any.whl** - 二维码生成
- **pyzbar-0.1.9-py2.py3-none-win_amd64.whl** - 二维码识别
- **pillow-11.3.0-cp312-cp312-win_amd64.whl** - 图像处理库
- **flask-3.1.1-py3-none-any.whl** - Web框架
- **numpy-2.3.1-cp312-cp312-win_amd64.whl** - 数值计算
- **其他依赖** - Flask相关依赖包

### 安装脚本
- **install_offline.bat** - 离线安装脚本
- **install_dependencies.bat** - 在线安装脚本

### 配置文件
- **requirements.txt** - 依赖列表文件

## 常见问题

### Q1: 提示"python不是内部或外部命令"
**A**: 需要先安装Python或将Python添加到系统PATH中。

### Q2: 安装时提示权限错误
**A**: 以管理员身份运行命令提示符，然后执行安装命令。

### Q3: 某些模块安装失败
**A**: 检查Python版本是否兼容，建议使用Python 3.8-3.12。

### Q4: 安装后仍然提示模块不存在
**A**: 
1. 检查是否使用了正确的Python环境
2. 运行 `python -m pip list` 查看已安装的包
3. 重新运行诊断脚本

## 验证安装

安装完成后，运行以下命令验证：

```cmd
python -c "import mysql.connector; print('✓ mysql.connector')"
python -c "import cv2; print('✓ cv2')"
python -c "import qrcode; print('✓ qrcode')"
python -c "import pyzbar; print('✓ pyzbar')"
python -c "import PIL; print('✓ PIL')"
python -c "import flask; print('✓ flask')"
python -c "import apscheduler; print('✓ apscheduler')"
python -c "import numpy; print('✓ numpy')"
```

如果所有模块都显示 ✓，则安装成功。

## 启动程序

依赖安装完成后，可以正常启动DataTransmission：

```cmd
# 使用调试启动（推荐）
debug_start.bat

# 或直接启动
DataTransmission.exe
```

## 技术说明

### 为什么cx_Freeze打包没有包含这些模块？

1. **动态导入问题**: 某些模块使用动态导入，cx_Freeze无法自动检测
2. **二进制依赖**: OpenCV、NumPy等包含大量二进制文件，打包时可能遗漏
3. **路径问题**: 打包后的模块路径与原始环境不同

### 离线包的优势

1. **完整性**: 包含所有必需的依赖和子依赖
2. **兼容性**: 针对Windows x64环境优化
3. **便携性**: 可以在没有网络的环境中安装
4. **可靠性**: 避免了网络问题和版本冲突

## 联系支持

如果仍然遇到问题，请提供：
1. Python版本信息
2. 错误日志
3. 诊断脚本输出
4. 系统环境信息
