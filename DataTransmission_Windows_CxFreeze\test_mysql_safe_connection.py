#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
安全的MySQL连接测试
解决连接时静默退出的问题
"""

import sys
import os
import signal
import threading
import time
import traceback
import subprocess
from concurrent.futures import ThreadPoolExecutor, TimeoutError as FutureTimeoutError

# 全局变量用于控制测试
test_running = True
connection_result = None

def signal_handler(signum, frame):
    """信号处理器"""
    global test_running
    print(f"\n收到信号 {signum}，正在安全退出...")
    test_running = False
    sys.exit(1)

def safe_mysql_connect(config, timeout=10):
    """安全的MySQL连接函数"""
    global connection_result
    
    try:
        print(f"开始连接测试 (超时: {timeout}秒)...")
        print(f"连接参数: {config['user']}@{config['host']}:{config['port']}")
        
        # 导入MySQL连接器
        import mysql.connector
        from mysql.connector import Error
        
        # 设置连接超时
        safe_config = config.copy()
        safe_config['connection_timeout'] = timeout
        safe_config['autocommit'] = True
        safe_config['raise_on_warnings'] = True
        
        print("正在建立连接...")
        
        # 尝试连接
        connection = mysql.connector.connect(**safe_config)
        
        if connection.is_connected():
            print("✓ 连接建立成功")
            
            # 快速测试
            cursor = connection.cursor()
            cursor.execute("SELECT 1")
            result = cursor.fetchone()
            
            if result and result[0] == 1:
                print("✓ 连接测试查询成功")
                connection_result = "SUCCESS"
            else:
                print("✗ 连接测试查询失败")
                connection_result = "QUERY_FAILED"
            
            cursor.close()
            connection.close()
            print("✓ 连接已安全关闭")
            
        else:
            print("✗ 连接建立失败")
            connection_result = "CONNECTION_FAILED"
            
    except Error as e:
        error_msg = f"MySQL错误: {e.errno} - {e.msg}"
        print(f"✗ {error_msg}")
        connection_result = f"MYSQL_ERROR: {error_msg}"
        
    except Exception as e:
        error_msg = f"连接异常: {str(e)}"
        print(f"✗ {error_msg}")
        connection_result = f"EXCEPTION: {error_msg}"
        traceback.print_exc()

def test_with_subprocess(config, timeout=15):
    """使用子进程测试连接"""
    print(f"\n" + "=" * 50)
    print("子进程连接测试")
    print("=" * 50)
    
    # 创建测试脚本
    test_script = f'''
import sys
import mysql.connector
from mysql.connector import Error

config = {config}
config['connection_timeout'] = {timeout}
config['autocommit'] = True

try:
    print("子进程: 开始连接...")
    connection = mysql.connector.connect(**config)
    
    if connection.is_connected():
        print("子进程: 连接成功")
        cursor = connection.cursor()
        cursor.execute("SELECT VERSION()")
        version = cursor.fetchone()
        print(f"子进程: MySQL版本 {{version[0]}}")
        cursor.close()
        connection.close()
        print("子进程: 测试完成")
        sys.exit(0)
    else:
        print("子进程: 连接失败")
        sys.exit(1)
        
except Error as e:
    print(f"子进程: MySQL错误 {{e.errno}} - {{e.msg}}")
    sys.exit(2)
except Exception as e:
    print(f"子进程: 异常 {{str(e)}}")
    sys.exit(3)
'''
    
    try:
        # 写入临时脚本
        with open('temp_mysql_test.py', 'w', encoding='utf-8') as f:
            f.write(test_script)
        
        # 运行子进程
        print(f"启动子进程测试 (超时: {timeout}秒)...")
        
        result = subprocess.run([sys.executable, 'temp_mysql_test.py'], 
                              capture_output=True, text=True, timeout=timeout)
        
        print("子进程输出:")
        if result.stdout:
            print(result.stdout)
        if result.stderr:
            print("错误输出:")
            print(result.stderr)
        
        print(f"子进程退出代码: {result.returncode}")
        
        if result.returncode == 0:
            print("✓ 子进程连接测试成功")
            return True
        else:
            print("✗ 子进程连接测试失败")
            return False
            
    except subprocess.TimeoutExpired:
        print(f"✗ 子进程测试超时 (超过{timeout}秒)")
        return False
    except Exception as e:
        print(f"✗ 子进程测试异常: {e}")
        return False
    finally:
        # 清理临时文件
        try:
            os.remove('temp_mysql_test.py')
        except:
            pass

def test_basic_tcp_first(host, port, timeout=5):
    """先测试基本TCP连接"""
    print(f"\n" + "=" * 50)
    print("基础TCP连接测试")
    print("=" * 50)
    
    import socket
    
    try:
        print(f"测试TCP连接到 {host}:{port}...")
        
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(timeout)
        
        start_time = time.time()
        result = sock.connect_ex((host, port))
        end_time = time.time()
        
        sock.close()
        
        if result == 0:
            print(f"✓ TCP连接成功 (耗时: {end_time - start_time:.2f}秒)")
            return True
        else:
            print(f"✗ TCP连接失败 (错误代码: {result})")
            return False
            
    except socket.timeout:
        print(f"✗ TCP连接超时 (超过{timeout}秒)")
        return False
    except Exception as e:
        print(f"✗ TCP连接异常: {e}")
        return False

def check_mysql_process():
    """检查MySQL进程"""
    print(f"\n" + "=" * 50)
    print("MySQL进程检查")
    print("=" * 50)
    
    try:
        result = subprocess.run(['tasklist', '/FI', 'IMAGENAME eq mysqld.exe'], 
                              capture_output=True, text=True, timeout=10)
        
        if 'mysqld.exe' in result.stdout:
            print("✓ 找到MySQL进程")
            lines = result.stdout.split('\n')
            for line in lines:
                if 'mysqld.exe' in line:
                    print(f"  {line.strip()}")
            return True
        else:
            print("✗ 未找到MySQL进程")
            return False
            
    except Exception as e:
        print(f"✗ 检查MySQL进程失败: {e}")
        return False

def main():
    """主函数"""
    global test_running, connection_result
    
    # 注册信号处理器
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    print("=" * 60)
    print("安全MySQL连接测试工具")
    print("=" * 60)
    print("此工具专门解决连接时静默退出的问题")
    print()
    
    # 加载配置
    try:
        import config
        if not hasattr(config, 'DATABASE_CONFIG'):
            print("✗ 配置文件中缺少DATABASE_CONFIG")
            input("按回车键退出...")
            return
        
        db_config = config.DATABASE_CONFIG
        print("✓ 配置加载成功")
        
        # 显示配置（隐藏密码）
        display_config = db_config.copy()
        display_config['password'] = '***' if db_config.get('password') else '未设置'
        print(f"配置: {display_config}")
        
    except Exception as e:
        print(f"✗ 配置加载失败: {e}")
        input("按回车键退出...")
        return
    
    # 检查MySQL进程
    process_ok = check_mysql_process()
    
    # 测试TCP连接
    tcp_ok = test_basic_tcp_first(db_config['host'], db_config['port'])
    
    if not tcp_ok:
        print("\n❌ TCP连接失败，无法进行MySQL连接测试")
        print("请先解决网络连接问题")
        input("按回车键退出...")
        return
    
    # 创建基础配置（不包含数据库）
    basic_config = {
        'host': db_config['host'],
        'port': db_config['port'],
        'user': db_config['user'],
        'password': db_config['password'],
        'charset': db_config.get('charset', 'utf8')
    }
    
    # 方法1: 使用线程池测试
    print(f"\n" + "=" * 50)
    print("方法1: 线程池连接测试")
    print("=" * 50)
    
    try:
        with ThreadPoolExecutor(max_workers=1) as executor:
            future = executor.submit(safe_mysql_connect, basic_config, 10)
            
            # 等待结果，带超时
            try:
                future.result(timeout=15)
                print(f"线程池测试结果: {connection_result}")
                
                if connection_result == "SUCCESS":
                    print("✓ 方法1测试成功")
                    method1_success = True
                else:
                    print("✗ 方法1测试失败")
                    method1_success = False
                    
            except FutureTimeoutError:
                print("✗ 方法1测试超时")
                method1_success = False
                
    except Exception as e:
        print(f"✗ 方法1测试异常: {e}")
        method1_success = False
    
    # 方法2: 使用子进程测试
    print(f"\n" + "=" * 50)
    print("方法2: 子进程连接测试")
    print("=" * 50)
    
    method2_success = test_with_subprocess(basic_config, 15)
    
    # 总结
    print(f"\n" + "=" * 60)
    print("测试结果总结")
    print("=" * 60)
    
    results = [
        ("MySQL进程检查", process_ok),
        ("TCP连接测试", tcp_ok),
        ("线程池连接测试", method1_success),
        ("子进程连接测试", method2_success),
    ]
    
    for test_name, result in results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name}: {status}")
    
    if method1_success or method2_success:
        print("\n🎉 MySQL连接测试成功！")
        print("数据库连接正常，可以启动DataTransmission程序")
    else:
        print("\n❌ MySQL连接测试失败")
        print("\n💡 建议的解决步骤:")
        print("1. 重启MySQL服务: net stop mysql && net start mysql")
        print("2. 检查MySQL错误日志")
        print("3. 检查防火墙设置")
        print("4. 重新安装MySQL")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n用户中断测试")
    except Exception as e:
        print(f"\n程序异常: {e}")
        traceback.print_exc()
        input("按回车键退出...")
