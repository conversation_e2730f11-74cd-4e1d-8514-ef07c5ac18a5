# DataTransmission 故障排除指南

## 问题描述
程序启动后显示"正在初始化数据传输客户端"然后自动关闭。

## 诊断步骤

### 第一步：运行诊断脚本
在程序目录中运行以下命令来诊断问题：

```bash
# 方法1：使用调试启动脚本（推荐）
debug_start.bat

# 方法2：直接运行诊断脚本
python diagnose_windows.py

# 方法3：运行最小化测试
python test_minimal.py
```

### 第二步：检查日志文件
程序运行后会生成以下日志文件：
- `data_transmission.log` - 主程序日志
- `diagnose.log` - 诊断日志
- `test_minimal.log` - 测试日志

查看这些日志文件中的错误信息。

## 常见问题及解决方案

### 1. 数据库连接问题

**症状**: 程序启动后立即退出，日志显示数据库连接错误

**可能原因**:
- MySQL服务未启动
- 数据库配置错误
- 数据库不存在
- 用户权限不足

**解决方案**:

1. **检查MySQL服务状态**:
   ```cmd
   sc query mysql
   net start mysql
   ```

2. **检查配置文件** (`config.py`):
   ```python
   DATABASE_CONFIG = {
       'host': 'localhost',        # 数据库服务器地址
       'port': 3306,              # 数据库端口
       'database': 'DataTransmission',  # 数据库名称
       'user': 'root',            # 用户名
       'password': '你的密码',     # 密码（必须修改）
       'charset': 'utf8'
   }
   ```

3. **创建数据库**:
   ```sql
   CREATE DATABASE DataTransmission CHARACTER SET utf8 COLLATE utf8_general_ci;
   ```

4. **测试数据库连接**:
   ```cmd
   mysql -u root -p -e "SHOW DATABASES;"
   ```

### 2. 缺少依赖库

**症状**: 程序启动时提示模块导入错误

**解决方案**:
- 确保使用完整的部署包，包含 `lib/` 目录
- 检查 `lib/` 目录中是否包含所需的模块
- 重新下载完整的部署包

### 3. 权限问题

**症状**: 程序无法创建日志文件或访问某些资源

**解决方案**:
- 以管理员身份运行程序
- 检查程序目录的写入权限
- 确保防火墙允许程序访问网络

### 4. 端口占用问题

**症状**: Web服务器启动失败，提示端口被占用

**解决方案**:
1. **检查端口占用**:
   ```cmd
   netstat -ano | findstr :5000
   ```

2. **修改端口配置** (`config.py`):
   ```python
   FLASK_CONFIG = {
       'host': '0.0.0.0',
       'port': 5001,  # 改为其他端口
       'debug': False
   }
   ```

### 5. 摄像头问题

**症状**: 摄像头相关功能异常

**解决方案**:
- 检查摄像头是否被其他程序占用
- 确认摄像头驱动正常
- 修改摄像头索引配置:
  ```python
  CAMERA_INDEX = 1  # 尝试不同的摄像头索引
  ```

## 详细诊断命令

### 检查系统环境
```cmd
# 检查Python版本
python --version

# 检查MySQL服务
sc query mysql

# 检查端口占用
netstat -ano | findstr :5000

# 检查防火墙状态
netsh advfirewall show allprofiles
```

### 手动测试组件

1. **测试数据库连接**:
   ```python
   import mysql.connector
   config = {
       'host': 'localhost',
       'port': 3306,
       'database': 'DataTransmission',
       'user': 'root',
       'password': '你的密码',
       'charset': 'utf8'
   }
   conn = mysql.connector.connect(**config)
   print("数据库连接成功")
   conn.close()
   ```

2. **测试Web服务器**:
   ```python
   from flask import Flask
   app = Flask(__name__)
   app.run(host='0.0.0.0', port=5000)
   ```

## 获取详细错误信息

### 方法1：使用调试模式启动
```cmd
DataTransmission.exe --debug
```

### 方法2：捕获控制台输出
```cmd
DataTransmission.exe > output.log 2>&1
```

### 方法3：使用调试启动脚本
```cmd
debug_start.bat
```

## 联系支持

如果以上方法都无法解决问题，请提供以下信息：

1. **系统信息**:
   - Windows版本
   - 是否有管理员权限
   - 防火墙和杀毒软件状态

2. **错误信息**:
   - 完整的错误日志
   - 控制台输出
   - 诊断脚本结果

3. **环境信息**:
   - MySQL版本和状态
   - 网络配置
   - 其他相关软件

## 常用命令参考

```cmd
# 启动MySQL服务
net start mysql

# 停止MySQL服务  
net stop mysql

# 检查服务状态
sc query mysql

# 测试网络连接
telnet localhost 3306

# 查看进程
tasklist | findstr DataTransmission

# 结束进程
taskkill /f /im DataTransmission.exe

# 检查日志
type data_transmission.log
```

## 预防措施

1. **定期备份配置**:
   - 备份 `config.py` 文件
   - 记录数据库配置信息

2. **监控系统资源**:
   - 确保足够的磁盘空间
   - 监控内存使用情况

3. **保持环境稳定**:
   - 避免频繁修改系统配置
   - 定期更新MySQL和系统补丁
