
This file lists modules PyInstaller was not able to find. This does not
necessarily mean this module is required for running your program. Python and
Python 3rd-party packages include a lot of conditional or optional modules. For
example the module 'ntpath' only exists on Windows, whereas the module
'posixpath' only exists on Posix systems.

Types if import:
* top-level: imported at the top-level - look at these first
* conditional: imported within an if-statement
* delayed: imported within a function
* optional: imported within a try-except-statement

IMPORTANT: Do NOT post this list to the issue-tracker. Use it as a basis for
            tracking down the missing module yourself. Thanks!

missing module named _frozen_importlib_external - imported by importlib._bootstrap (delayed), importlib (optional), importlib.abc (optional), zipimport (top-level)
missing module named pep517 - imported by importlib.metadata (delayed)
excluded module named _frozen_importlib - imported by importlib (optional), importlib.abc (optional), zipimport (top-level)
missing module named 'email.message' - imported by importlib.metadata._adapters (top-level), mysql.opentelemetry.importlib_metadata._adapters (top-level), http.client (top-level), smtplib (top-level), logging.handlers (delayed, optional)
excluded module named email - imported by importlib.metadata (top-level), mysql.opentelemetry.importlib_metadata (top-level), urllib.request (top-level), urllib3.util.retry (top-level)
missing module named posix - imported by os (conditional, optional), shutil (conditional), importlib._bootstrap_external (conditional)
missing module named resource - imported by posix (top-level)
excluded module named marshal - imported by importlib._bootstrap_external (top-level), pkgutil (delayed), zipimport (top-level), jinja2.bccache (top-level), modulefinder (top-level), profile (top-level), pstats (top-level)
missing module named pyimod02_importers - imported by D:\anaconda3\envs\dt\Lib\site-packages\PyInstaller\hooks\rthooks\pyi_rth_pkgutil.py (delayed)
missing module named pwd - imported by posixpath (delayed, conditional), subprocess (delayed, conditional, optional), shutil (delayed, optional), tarfile (optional), pathlib (delayed, optional), getpass (delayed), http.server (delayed, optional), netrc (delayed, conditional), webbrowser (delayed)
missing module named grp - imported by subprocess (delayed, conditional, optional), shutil (delayed, optional), tarfile (optional), pathlib (delayed, optional)
missing module named _posixsubprocess - imported by subprocess (optional)
missing module named fcntl - imported by subprocess (optional)
excluded module named pprint - imported by pickle (conditional), sysconfig (delayed), werkzeug.routing.map (top-level), jinja2.utils (delayed), jinja2.ext (top-level), numpy.testing._private.utils (top-level), numpy.lib.utils (delayed), pytz.tzfile (conditional), pdb (top-level)
excluded module named readline - imported by code (delayed, conditional, optional), flask.cli (delayed, conditional, optional), pdb (delayed, optional), pstats (conditional, optional)
excluded module named cmd - imported by pdb (top-level), pstats (conditional)
excluded module named pydoc - imported by werkzeug.debug.repr (delayed), numpy.lib.utils (delayed), pdb (delayed)
excluded module named shlex - imported by netrc (top-level), click.shell_completion (delayed), click._termui_impl (top-level), webbrowser (top-level), click.testing (top-level), PIL.ImageShow (top-level), pdb (delayed, conditional)
missing module named 'typing.io' - imported by importlib.resources (top-level)
excluded module named bisect - imported by random (top-level), statistics (top-level), urllib.request (top-level), qrcode.main (top-level), zoneinfo._zoneinfo (top-level), pytz.tzinfo (top-level), idna.core (top-level), idna.intranges (top-level)
missing module named org - imported by pickle (optional)
missing module named 'org.python' - imported by copy (optional)
missing module named vms_lib - imported by platform (delayed, optional)
missing module named 'java.lang' - imported by platform (delayed, optional)
missing module named java - imported by platform (delayed)
missing module named _winreg - imported by platform (delayed, optional), tzlocal.win32 (optional)
missing module named 'email.parser' - imported by http.client (top-level), urllib3.contrib.emscripten.fetch (top-level)
excluded module named html - imported by http.server (top-level), markupsafe (delayed)
missing module named 'email.utils' - imported by http.server (top-level), werkzeug.http (top-level), urllib.request (delayed), urllib3.fields (top-level), smtplib (top-level), logging.handlers (delayed, optional)
missing module named _scproxy - imported by urllib.request (conditional)
missing module named termios - imported by getpass (optional), werkzeug._reloader (delayed, optional), click._termui_impl (conditional), tty (top-level)
missing module named win32evtlog - imported by logging.handlers (delayed, optional)
missing module named win32evtlogutil - imported by logging.handlers (delayed, optional)
missing module named 'email.base64mime' - imported by smtplib (top-level)
missing module named 'email.generator' - imported by smtplib (top-level)
excluded module named queue - imported by mysql.connector.pooling (top-level), urllib3.connectionpool (top-level), logging.handlers (top-level)
missing module named simplejson - imported by requests.compat (conditional, optional)
missing module named 'asyncio.coroutines' - imported by typing_extensions (delayed, conditional)
missing module named annotationlib - imported by typing_extensions (conditional)
missing module named chardet - imported by requests.compat (optional), requests (optional), requests.packages (optional)
missing module named zstandard - imported by urllib3.util.request (optional), urllib3.response (optional)
missing module named compression - imported by urllib3.util.request (optional), urllib3.response (optional)
missing module named brotli - imported by urllib3.util.request (optional), urllib3.response (optional)
missing module named brotlicffi - imported by urllib3.util.request (optional), urllib3.response (optional)
missing module named 'h2.events' - imported by urllib3.http2.connection (top-level)
missing module named 'h2.connection' - imported by urllib3.http2.connection (top-level)
missing module named h2 - imported by urllib3.http2.connection (top-level)
missing module named 'email.errors' - imported by urllib3.exceptions (top-level), urllib3.util.response (top-level)
missing module named 'OpenSSL.crypto' - imported by urllib3.contrib.pyopenssl (delayed, conditional)
missing module named 'cryptography.x509' - imported by werkzeug.serving (delayed, conditional, optional), urllib3.contrib.pyopenssl (delayed, optional)
missing module named cryptography - imported by werkzeug.serving (delayed, conditional, optional), flask.cli (delayed, conditional, optional), urllib3.contrib.pyopenssl (top-level), requests (conditional, optional)
missing module named OpenSSL - imported by urllib3.contrib.pyopenssl (top-level)
missing module named 'pyodide.ffi' - imported by urllib3.contrib.emscripten.fetch (delayed, optional)
missing module named pyodide - imported by urllib3.contrib.emscripten.fetch (top-level)
missing module named js - imported by urllib3.contrib.emscripten.fetch (top-level)
missing module named dummy_threading - imported by requests.cookies (optional)
missing module named socks - imported by urllib3.contrib.socks (optional)
missing module named 'xml.etree' - imported by qrcode.compat.etree (optional)
missing module named lxml - imported by qrcode.compat.etree (optional)
missing module named ImageDraw - imported by qrcode.compat.pil (optional)
missing module named Image - imported by qrcode.compat.pil (optional)
missing module named pickle5 - imported by numpy.compat.py3k (optional)
missing module named threadpoolctl - imported by numpy.lib.utils (delayed, optional)
missing module named numpy.core.asarray - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.lib.utils (top-level), numpy.fft._pocketfft (top-level), numpy.fft.helper (top-level)
missing module named numpy.core.ufunc - imported by numpy.core (top-level), numpy.lib.utils (top-level)
missing module named numpy.core.ndarray - imported by numpy.core (top-level), numpy.testing._private.utils (top-level), numpy.lib.utils (top-level)
excluded module named doctest - imported by numpy.testing._private.utils (delayed), numpy.testing._private.noseclasses (top-level), numpy.testing._private.nosetester (delayed), pytz (delayed)
missing module named psutil - imported by numpy.testing._private.utils (delayed, optional)
excluded module named difflib - imported by click.parser (delayed, conditional), werkzeug.routing.exceptions (top-level), numpy.testing._private.utils (delayed)
missing module named numpy.core.result_type - imported by numpy.core (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.core.float_ - imported by numpy.core (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.core.number - imported by numpy.core (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.core.object_ - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.testing._private.utils (delayed)
missing module named numpy.core.all - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.testing._private.utils (delayed)
missing module named numpy.core.bool_ - imported by numpy.core (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.core.inf - imported by numpy.core (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.core.array2string - imported by numpy.core (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.core.signbit - imported by numpy.core (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.core.isscalar - imported by numpy.core (delayed), numpy.testing._private.utils (delayed), numpy.lib.polynomial (top-level)
missing module named win32pdh - imported by numpy.testing._private.utils (delayed, conditional)
missing module named numpy.core.isinf - imported by numpy.core (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.core.errstate - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.testing._private.utils (delayed)
missing module named numpy.core.isfinite - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.testing._private.utils (delayed)
missing module named numpy.core.isnan - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.testing._private.utils (delayed)
missing module named numpy.core.array - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.testing._private.utils (top-level), numpy.lib.polynomial (top-level)
missing module named numpy.core.isnat - imported by numpy.core (top-level), numpy.testing._private.utils (top-level)
missing module named numpy.core.array_repr - imported by numpy.core (top-level), numpy.testing._private.utils (top-level)
missing module named numpy.core.arange - imported by numpy.core (top-level), numpy.testing._private.utils (top-level), numpy.fft.helper (top-level)
missing module named numpy.core.empty - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.testing._private.utils (top-level), numpy.fft.helper (top-level)
missing module named numpy.core.float32 - imported by numpy.core (top-level), numpy.testing._private.utils (top-level)
missing module named numpy.core.intp - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.testing._private.utils (top-level)
missing module named 'unittest.case' - imported by numpy.testing._private.utils (top-level)
excluded module named unittest - imported by numpy.testing (top-level), numpy.testing._private.utils (top-level), numpy.testing._private.parameterized (top-level)
missing module named numpy._typing._ufunc - imported by numpy._typing (conditional)
missing module named numpy.bytes_ - imported by numpy (top-level), numpy._typing._array_like (top-level)
missing module named numpy.str_ - imported by numpy (top-level), numpy._typing._array_like (top-level)
missing module named numpy.void - imported by numpy (top-level), numpy._typing._array_like (top-level)
missing module named numpy.object_ - imported by numpy (top-level), numpy._typing._array_like (top-level)
missing module named numpy.datetime64 - imported by numpy (top-level), numpy._typing._array_like (top-level)
missing module named numpy.timedelta64 - imported by numpy (top-level), numpy._typing._array_like (top-level)
missing module named numpy.number - imported by numpy (top-level), numpy._typing._array_like (top-level)
missing module named numpy.complexfloating - imported by numpy (top-level), numpy._typing._array_like (top-level)
missing module named numpy.floating - imported by numpy (top-level), numpy._typing._array_like (top-level)
missing module named numpy.integer - imported by numpy (top-level), numpy._typing._array_like (top-level), numpy.ctypeslib (top-level)
missing module named numpy.unsignedinteger - imported by numpy (top-level), numpy._typing._array_like (top-level)
missing module named numpy.bool_ - imported by numpy (top-level), numpy._typing._array_like (top-level), numpy.ma.core (top-level), numpy.ma.mrecords (top-level), numpy.random.mtrand (top-level), numpy.random._generator (top-level)
missing module named numpy.generic - imported by numpy (top-level), numpy._typing._array_like (top-level)
missing module named numpy.dtype - imported by numpy (top-level), numpy._typing._array_like (top-level), numpy.array_api._typing (top-level), numpy.ma.mrecords (top-level), numpy.random.mtrand (top-level), numpy.random.bit_generator (top-level), numpy.random._philox (top-level), numpy.random._sfc64 (top-level), numpy.random._generator (top-level), numpy.random._mt19937 (top-level), numpy.ctypeslib (top-level)
missing module named numpy.ndarray - imported by numpy (top-level), numpy._typing._array_like (top-level), numpy.ma.core (top-level), numpy.ma.extras (top-level), numpy.ma.mrecords (top-level), numpy.random.mtrand (top-level), numpy.random.bit_generator (top-level), numpy.random._philox (top-level), numpy.random._sfc64 (top-level), numpy.random._generator (top-level), numpy.random._mt19937 (top-level), numpy.ctypeslib (top-level)
missing module named numpy.ufunc - imported by numpy (top-level), numpy._typing (top-level)
missing module named numpy.float64 - imported by numpy (top-level), numpy.array_api._typing (top-level), numpy.random.mtrand (top-level), numpy.random._generator (top-level)
missing module named numpy.float32 - imported by numpy (top-level), numpy.array_api._typing (top-level), numpy.random.mtrand (top-level), numpy.random._generator (top-level)
missing module named numpy.uint64 - imported by numpy (top-level), numpy.array_api._typing (top-level), numpy.random.mtrand (top-level), numpy.random.bit_generator (top-level), numpy.random._philox (top-level), numpy.random._sfc64 (top-level), numpy.random._generator (top-level)
missing module named numpy.uint32 - imported by numpy (top-level), numpy.array_api._typing (top-level), numpy.random.mtrand (top-level), numpy.random.bit_generator (top-level), numpy.random._generator (top-level), numpy.random._mt19937 (top-level)
missing module named numpy.uint16 - imported by numpy (top-level), numpy.array_api._typing (top-level), numpy.random.mtrand (top-level), numpy.random._generator (top-level)
missing module named numpy.uint8 - imported by numpy (top-level), numpy.array_api._typing (top-level), numpy.random.mtrand (top-level), numpy.random._generator (top-level)
missing module named numpy.int64 - imported by numpy (top-level), numpy.array_api._typing (top-level), numpy.random.mtrand (top-level), numpy.random._generator (top-level)
missing module named numpy.int32 - imported by numpy (top-level), numpy.array_api._typing (top-level), numpy.random.mtrand (top-level), numpy.random._generator (top-level)
missing module named numpy.int16 - imported by numpy (top-level), numpy.array_api._typing (top-level), numpy.random.mtrand (top-level), numpy.random._generator (top-level)
missing module named numpy.int8 - imported by numpy (top-level), numpy.array_api._typing (top-level), numpy.random.mtrand (top-level), numpy.random._generator (top-level)
missing module named numpy.core.reciprocal - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.sort - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.argsort - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.sign - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.count_nonzero - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.divide - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.swapaxes - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.fft._pocketfft (top-level)
missing module named numpy.core.matmul - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.asanyarray - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.atleast_2d - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.product - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.amax - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.amin - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.moveaxis - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.geterrobj - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.finfo - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.lib.polynomial (top-level)
missing module named numpy.core.sum - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.sqrt - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.fft._pocketfft (top-level)
missing module named numpy.core.multiply - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.add - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.dot - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.lib.polynomial (top-level)
missing module named numpy.core.Inf - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.newaxis - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.complexfloating - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.inexact - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.cdouble - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.csingle - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.double - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.single - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.intc - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.empty_like - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.zeros - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.fft._pocketfft (top-level)
missing module named _dummy_thread - imported by numpy.core.arrayprint (optional)
missing module named numpy.eye - imported by numpy (delayed), numpy.core.numeric (delayed)
missing module named numpy.array - imported by numpy (top-level), numpy.ma.core (top-level), numpy.ma.extras (top-level), numpy.ma.mrecords (top-level)
missing module named numpy.recarray - imported by numpy (top-level), numpy.ma.mrecords (top-level)
missing module named numpy.expand_dims - imported by numpy (top-level), numpy.ma.core (top-level)
missing module named numpy.iscomplexobj - imported by numpy (top-level), numpy.ma.core (top-level)
missing module named numpy.amin - imported by numpy (top-level), numpy.ma.core (top-level)
missing module named numpy.amax - imported by numpy (top-level), numpy.ma.core (top-level)
missing module named numpy.core.ones - imported by numpy.core (top-level), numpy.lib.polynomial (top-level)
missing module named numpy.core.hstack - imported by numpy.core (top-level), numpy.lib.polynomial (top-level)
missing module named numpy.core.atleast_1d - imported by numpy.core (top-level), numpy.lib.polynomial (top-level)
missing module named numpy.histogramdd - imported by numpy (delayed), numpy.lib.twodim_base (delayed)
missing module named numpy.core.iinfo - imported by numpy.core (top-level), numpy.lib.twodim_base (top-level)
missing module named numpy.core.atleast_3d - imported by numpy.core (top-level), numpy.lib.shape_base (top-level)
missing module named numpy.core.vstack - imported by numpy.core (top-level), numpy.lib.shape_base (top-level)
missing module named numpy.core.transpose - imported by numpy.core (top-level), numpy.lib.function_base (top-level)
missing module named numpy.core.linspace - imported by numpy.core (top-level), numpy.lib.index_tricks (top-level)
missing module named numpy.lib.imag - imported by numpy.lib (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.lib.real - imported by numpy.lib (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.lib.iscomplexobj - imported by numpy.lib (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.uint - imported by numpy (top-level), numpy.random.mtrand (top-level), numpy.random._generator (top-level)
missing module named numpy.core.integer - imported by numpy.core (top-level), numpy.fft.helper (top-level)
missing module named numpy.core.conjugate - imported by numpy.core (top-level), numpy.fft._pocketfft (top-level)
excluded module named asyncio - imported by jinja2.environment (delayed, conditional), apscheduler.util (top-level), apscheduler.schedulers.asyncio (top-level)
missing module named '_typeshed.wsgi' - imported by werkzeug.exceptions (conditional), werkzeug.http (conditional), werkzeug.wsgi (conditional), werkzeug.utils (conditional), werkzeug.wrappers.response (conditional), werkzeug.test (conditional), werkzeug.datastructures.headers (conditional), werkzeug.formparser (conditional), werkzeug.wrappers.request (conditional), werkzeug.serving (conditional), werkzeug.debug (conditional), werkzeug.middleware.shared_data (conditional), werkzeug.local (conditional), werkzeug.routing.exceptions (conditional), werkzeug.routing.map (conditional), flask.typing (conditional)
missing module named _typeshed - imported by werkzeug._internal (conditional), click.testing (conditional)
missing module named 'watchdog.observers' - imported by werkzeug._reloader (delayed)
missing module named 'watchdog.events' - imported by werkzeug._reloader (delayed)
missing module named watchdog - imported by werkzeug._reloader (delayed)
excluded module named multiprocessing - imported by werkzeug.debug (top-level)
missing module named 'cryptography.hazmat' - imported by werkzeug.serving (delayed, optional)
missing module named asgiref - imported by flask.app (delayed, optional)
excluded module named rlcompleter - imported by flask.cli (delayed, conditional, optional)
missing module named dotenv - imported by flask.cli (delayed, optional)
missing module named 'setuptools._vendor'.entry_points - imported by 'setuptools._vendor' (top-level), apscheduler.schedulers.base (optional)
missing module named 'setuptools._vendor' - imported by 'setuptools._vendor' (top-level), 'setuptools._vendor' (top-level)
missing module named funcsigs - imported by apscheduler.util (optional)
missing module named StringIO - imported by six (conditional)
missing module named sets - imported by pytz.tzinfo (optional)
missing module named collections.Mapping - imported by collections (optional), pytz.lazy (optional), apscheduler.job (optional)
missing module named UserDict - imported by pytz.lazy (optional)
missing module named 'concurrent.futures' - imported by mysql.opentelemetry.sdk.trace (top-level), mysql.opentelemetry.sdk.resources (top-level), apscheduler.executors.pool (top-level), apscheduler.executors.tornado (top-level)
missing module named collections.MutableMapping - imported by collections (optional), apscheduler.schedulers.base (optional)
missing module named collections.Iterable - imported by collections (optional), apscheduler.job (optional)
missing module named twisted - imported by apscheduler.schedulers.twisted (optional)
missing module named 'tornado.ioloop' - imported by apscheduler.schedulers.tornado (optional)
missing module named gevent - imported by apscheduler.executors.gevent (optional), apscheduler.schedulers.gevent (optional)
missing module named 'gevent.lock' - imported by apscheduler.schedulers.gevent (optional)
missing module named 'gevent.event' - imported by apscheduler.schedulers.gevent (optional)
missing module named 'kazoo.client' - imported by apscheduler.jobstores.zookeeper (optional)
missing module named cPickle - imported by apscheduler.jobstores.mongodb (optional), apscheduler.jobstores.redis (optional), apscheduler.jobstores.rethinkdb (optional), apscheduler.jobstores.sqlalchemy (optional), apscheduler.jobstores.zookeeper (optional)
missing module named kazoo - imported by apscheduler.jobstores.zookeeper (top-level)
missing module named 'sqlalchemy.sql' - imported by apscheduler.jobstores.sqlalchemy (optional)
missing module named 'sqlalchemy.exc' - imported by apscheduler.jobstores.sqlalchemy (optional)
missing module named sqlalchemy - imported by apscheduler.jobstores.sqlalchemy (optional)
missing module named rethinkdb - imported by apscheduler.jobstores.rethinkdb (optional)
missing module named redis - imported by apscheduler.jobstores.redis (optional)
missing module named pymongo - imported by apscheduler.jobstores.mongodb (optional)
missing module named bson - imported by apscheduler.jobstores.mongodb (optional)
missing module named tornado - imported by apscheduler.executors.tornado (top-level)
missing module named PIL._imagingagg - imported by PIL (delayed, conditional, optional), PIL.ImageDraw (delayed, conditional, optional)
missing module named olefile - imported by PIL.FpxImagePlugin (top-level), PIL.MicImagePlugin (top-level)
missing module named cffi - imported by PIL.Image (optional), PIL.PyAccess (optional)
missing module named defusedxml - imported by PIL.Image (optional)
missing module named deprecated - imported by mysql.opentelemetry.trace (top-level), mysql.opentelemetry.sdk.trace (top-level), mysql.opentelemetry.sdk.util (top-level), mysql.opentelemetry.sdk.util.instrumentation (top-level)
missing module named 'opentelemetry.semconv' - imported by mysql.connector.opentelemetry.constants (optional), mysql.connector.opentelemetry.instrumentation (optional)
missing module named 'opentelemetry.sdk' - imported by mysql.connector.opentelemetry.constants (optional), mysql.connector.opentelemetry.instrumentation (optional)
missing module named opentelemetry - imported by mysql.connector.opentelemetry.constants (optional), mysql.connector.opentelemetry.context_propagation (conditional), mysql.connector.opentelemetry.instrumentation (optional)
missing module named 'opentelemetry.trace' - imported by mysql.connector.opentelemetry.context_propagation (conditional)
excluded module named stringprep - imported by encodings.idna (top-level), mysql.connector.utils (top-level)
missing module named 'dns.resolver' - imported by mysql.connector.pooling (optional)
missing module named dns - imported by mysql.connector.pooling (optional)
excluded module named enum - imported by re (top-level), signal (top-level), py_compile (top-level), socket (top-level), ast (top-level), inspect (top-level), ssl (top-level), typing_extensions (top-level), mysql.opentelemetry.trace (top-level), uuid (top-level), mysql.opentelemetry.trace.status (top-level), mysql.opentelemetry.semconv.resource (top-level), mysql.opentelemetry.sdk.trace.sampling (top-level), mysql.opentelemetry.semconv.trace (top-level), http (top-level), werkzeug.http (top-level), werkzeug.sansio.multipart (top-level), click.core (top-level), click.types (top-level), jinja2.utils (top-level), PIL.Image (top-level), PIL.ExifTags (top-level), PIL.GifImagePlugin (top-level), PIL.PngImagePlugin (top-level), numpy._globals (top-level), numpy.array_api._array_object (top-level), PIL.ImageCms (top-level), PIL.ImageFont (top-level), tkinter (top-level), pyzbar.wrapper (top-level), E:\01.代码\嘉兴公安自主申报\DataTransmission\main.py (top-level), urllib3.util.timeout (top-level), urllib3.util.request (top-level), urllib3._collections (top-level), pstats (top-level), PIL.BlpImagePlugin (top-level), PIL.FtexImagePlugin (top-level)
excluded module named reprlib - imported by collections (top-level), functools (top-level), E:\01.代码\嘉兴公安自主申报\DataTransmission\main.py (top-level), bdb (delayed)
excluded module named copyreg - imported by pickle (top-level), _pickle (top-level), re (top-level), copy (top-level), numpy.core (top-level), E:\01.代码\嘉兴公安自主申报\DataTransmission\main.py (top-level)
excluded module named collections.abc - imported by selectors (top-level), typing (top-level), tracemalloc (top-level), logging (top-level), inspect (top-level), typing_extensions (top-level), mysql.opentelemetry.attributes (top-level), mysql.opentelemetry.sdk.util (top-level), configparser (top-level), http.client (top-level), werkzeug.wrappers.request (top-level), werkzeug.datastructures.accept (top-level), werkzeug.datastructures.structures (top-level), markupsafe (top-level), werkzeug.datastructures.cache_control (top-level), werkzeug.datastructures.mixins (top-level), werkzeug.datastructures.auth (top-level), werkzeug.datastructures.csp (top-level), werkzeug.datastructures.etag (top-level), werkzeug.datastructures.file_storage (top-level), werkzeug.datastructures.headers (top-level), werkzeug.datastructures.range (top-level), werkzeug.middleware.shared_data (top-level), flask.app (top-level), click.core (top-level), click.types (top-level), click._compat (top-level), click._winconsole (top-level), click.exceptions (top-level), click.utils (top-level), click.shell_completion (top-level), click.formatting (top-level), click.parser (top-level), click._textwrap (top-level), click.termui (top-level), click._termui_impl (top-level), blinker.base (top-level), blinker._utilities (top-level), jinja2.utils (top-level), jinja2.runtime (top-level), jinja2.filters (top-level), jinja2.sandbox (top-level), jinja2.tests (top-level), jinja2.loaders (top-level), flask.sessions (top-level), itsdangerous.serializer (top-level), itsdangerous.signer (top-level), itsdangerous.timed (top-level), click.testing (top-level), PIL.Image (top-level), PIL.TiffImagePlugin (top-level), numpy.core._ufunc_config (top-level), numpy._typing._generic_alias (top-level), numpy._typing._array_like (top-level), numpy.array_api._data_type_functions (conditional), numpy.array_api._creation_functions (conditional), numpy.testing._private.decorators (top-level), numpy.lib.function_base (top-level), numpy.lib.npyio (top-level), numpy.random.mtrand (top-level), numpy.random.bit_generator (top-level), numpy.random._generator (top-level), importlib.resources (top-level), pytz.lazy (optional), apscheduler.job (optional), apscheduler.schedulers.base (optional), E:\01.代码\嘉兴公安自主申报\DataTransmission\main.py (top-level), requests.compat (top-level), sqlite3.dbapi2 (top-level)
excluded module named heapq - imported by collections (delayed), E:\01.代码\嘉兴公安自主申报\DataTransmission\main.py (top-level)
