# Windows开发环境到CentOS 7生产环境部署指南

## 环境概述
- **开发环境**: Windows + Anaconda + 项目虚拟环境
- **生产环境**: CentOS 7 + 图形界面
- **部署方式**: Anaconda环境迁移

## 🚨 跨平台注意事项

由于Windows和Linux平台差异，**不能直接复制conda环境**，需要重新构建Linux兼容的环境。

## 第一步：在Windows开发环境中准备部署信息

### 1.1 导出环境配置
```cmd
REM 激活您的项目环境
conda activate your_project_env_name

REM 导出跨平台环境配置（不包含平台特定的构建信息）
conda env export --no-builds > datatransmission_environment.yml

REM 导出pip依赖
pip freeze > requirements.txt

REM 查看当前环境信息
conda list
conda info --envs
```

### 1.2 创建部署包
在Windows上创建 `create_deployment_package.bat`：

```batch
@echo off
echo 创建CentOS 7部署包...

REM 创建部署目录
mkdir DataTransmission_CentOS7_Deploy
cd DataTransmission_CentOS7_Deploy

REM 复制项目源代码
mkdir src
copy ..\*.py src\
copy ..\requirements.txt src\
copy ..\README.md .\

REM 复制环境配置
copy ..\datatransmission_environment.yml .\

REM 创建CentOS 7部署脚本
echo 创建部署脚本...
(
echo #!/bin/bash
echo # CentOS 7 Anaconda部署脚本
echo echo "=== DataTransmission CentOS 7部署开始 ==="
echo.
echo # 检查系统版本
echo if ! grep -q "CentOS Linux release 7" /etc/redhat-release 2^>/dev/null; then
echo     echo "警告: 此脚本专为CentOS 7设计"
echo     echo "当前系统: $(cat /etc/redhat-release 2^>/dev/null ^|^| echo '未知')"
echo fi
echo.
echo # 设置变量
echo PROJECT_NAME="DataTransmission"
echo ENV_NAME="datatransmission"
echo INSTALL_DIR="/opt/$PROJECT_NAME"
echo.
echo # 检查并安装Anaconda
echo if ! command -v conda ^&^> /dev/null; then
echo     echo "安装Anaconda到CentOS 7..."
echo     cd /tmp
echo     
echo     # 下载Anaconda for Linux
echo     if [ ! -f "Anaconda3-2023.09-0-Linux-x86_64.sh" ]; then
echo         echo "请手动下载Anaconda3-2023.09-0-Linux-x86_64.sh到/tmp目录"
echo         echo "下载地址: https://repo.anaconda.com/archive/Anaconda3-2023.09-0-Linux-x86_64.sh"
echo         exit 1
echo     fi
echo     
echo     # 安装Anaconda
echo     sudo bash Anaconda3-2023.09-0-Linux-x86_64.sh -b -p /opt/anaconda3
echo     
echo     # 设置环境变量
echo     echo 'export PATH="/opt/anaconda3/bin:$PATH"' ^| sudo tee -a /etc/profile
echo     source /etc/profile
echo     
echo     # 初始化conda
echo     sudo /opt/anaconda3/bin/conda init bash
echo     echo "请重新登录shell以使conda生效，然后重新运行此脚本"
echo     exit 0
echo else
echo     echo "检测到已安装的Anaconda"
echo fi
echo.
echo # 激活conda
echo source $(conda info --base^)/etc/profile.d/conda.sh
echo.
echo # 安装CentOS 7系统依赖
echo echo "安装系统依赖..."
echo sudo yum update -y
echo sudo yum install -y epel-release
echo sudo yum groupinstall -y "Development Tools"
echo sudo yum install -y mariadb-server mariadb mariadb-devel
echo sudo yum install -y zbar zbar-devel
echo sudo yum install -y mesa-libGL mesa-libGL-devel glib2-devel
echo sudo yum install -y python3-tkinter
echo sudo yum install -y libX11-devel libXext-devel libXrender-devel
echo sudo yum install -y libICE-devel libSM-devel
echo sudo yum install -y gtk3-devel
echo sudo yum install -y libpng-devel libjpeg-turbo-devel
echo.
echo # 创建conda环境
echo echo "创建conda环境..."
echo if conda env list ^| grep -q "^$ENV_NAME "; then
echo     echo "环境已存在，删除重建..."
echo     conda env remove -n $ENV_NAME -y
echo fi
echo.
echo # 从yml文件创建环境
echo conda env create -f datatransmission_environment.yml -n $ENV_NAME
echo.
echo # 激活环境
echo conda activate $ENV_NAME
echo.
echo # 安装可能缺失的依赖
echo echo "安装额外依赖..."
echo pip install --upgrade pip
echo pip install -r src/requirements.txt
echo.
echo # 创建项目目录
echo sudo mkdir -p $INSTALL_DIR
echo sudo cp -r src/* $INSTALL_DIR/
echo sudo chown -R $USER:$USER $INSTALL_DIR
echo.
echo # 启动MariaDB
echo sudo systemctl start mariadb
echo sudo systemctl enable mariadb
echo.
echo # 创建启动脚本
echo cat ^> $INSTALL_DIR/start_centos7.sh ^<^< 'STARTEOF'
echo #!/bin/bash
echo # CentOS 7启动脚本
echo.
echo # 激活conda环境
echo source $(conda info --base^)/etc/profile.d/conda.sh
echo conda activate datatransmission
echo.
echo # 设置显示环境变量
echo export DISPLAY=${DISPLAY:-:0.0}
echo.
echo # 设置库路径
echo export LD_LIBRARY_PATH=/usr/lib64:$LD_LIBRARY_PATH
echo.
echo # 切换到项目目录
echo cd $INSTALL_DIR
echo.
echo # 启动程序
echo python main.py
echo STARTEOF
echo.
echo chmod +x $INSTALL_DIR/start_centos7.sh
echo.
echo # 创建systemd服务
echo sudo tee /etc/systemd/system/datatransmission.service ^> /dev/null ^<^< 'SERVICEEOF'
echo [Unit]
echo Description=Data Transmission Client
echo After=network.target mariadb.service
echo.
echo [Service]
echo Type=simple
echo User=$USER
echo WorkingDirectory=$INSTALL_DIR
echo Environment=DISPLAY=:0.0
echo Environment=LD_LIBRARY_PATH=/usr/lib64
echo ExecStart=$INSTALL_DIR/start_centos7.sh
echo Restart=always
echo RestartSec=10
echo.
echo [Install]
echo WantedBy=multi-user.target
echo SERVICEEOF
echo.
echo # 重新加载systemd
echo sudo systemctl daemon-reload
echo.
echo # 配置防火墙
echo if systemctl is-active --quiet firewalld; then
echo     sudo firewall-cmd --permanent --add-port=5000/tcp
echo     sudo firewall-cmd --reload
echo fi
echo.
echo echo "部署完成！"
echo echo ""
echo echo "下一步操作："
echo echo "1. 配置MariaDB数据库:"
echo echo "   sudo mysql_secure_installation"
echo echo "2. 创建数据库:"
echo echo "   mysql -u root -p"
echo echo "   CREATE DATABASE DataTransmission CHARACTER SET utf8;"
echo echo "   CREATE USER 'datatrans'@'localhost' IDENTIFIED BY 'JKga#123';"
echo echo "   GRANT ALL PRIVILEGES ON DataTransmission.* TO 'datatrans'@'localhost';"
echo echo "   FLUSH PRIVILEGES;"
echo echo "3. 配置应用: vi $INSTALL_DIR/config.py"
echo echo "4. 启动服务: sudo systemctl start datatransmission"
echo echo "5. 设置开机自启: sudo systemctl enable datatransmission"
) > deploy_centos7.sh

REM 创建Anaconda下载说明
(
echo # Anaconda离线安装包下载说明
echo.
echo 由于需要在CentOS 7上安装Anaconda，请下载以下文件：
echo.
echo 1. Anaconda3-2023.09-0-Linux-x86_64.sh
echo    下载地址: https://repo.anaconda.com/archive/Anaconda3-2023.09-0-Linux-x86_64.sh
echo    大小: 约900MB
echo.
echo 2. 将下载的文件放到CentOS 7的/tmp目录下
echo.
echo 3. 或者使用Miniconda（更小）:
echo    Miniconda3-latest-Linux-x86_64.sh
echo    下载地址: https://repo.anaconda.com/miniconda/Miniconda3-latest-Linux-x86_64.sh
echo    大小: 约100MB
) > anaconda_download_guide.txt

echo 部署包创建完成！
echo.
echo 生成的文件：
echo - datatransmission_environment.yml  (环境配置)
echo - deploy_centos7.sh                 (部署脚本)
echo - src/                             (项目源代码)
echo - anaconda_download_guide.txt       (Anaconda下载说明)
echo.
echo 下一步：
echo 1. 将整个 DataTransmission_CentOS7_Deploy 文件夹传输到CentOS 7
echo 2. 按照 anaconda_download_guide.txt 下载Anaconda
echo 3. 运行 deploy_centos7.sh 脚本

pause
```

运行此脚本：
```cmd
create_deployment_package.bat
```

## 第二步：传输到CentOS 7生产环境

### 2.1 准备传输文件
将以下文件/文件夹传输到CentOS 7：
- `DataTransmission_CentOS7_Deploy/` (整个文件夹)
- `Anaconda3-2023.09-0-Linux-x86_64.sh` (Anaconda安装包)

### 2.2 传输方式
```bash
# 方法1: 使用scp (如果有网络)
scp -r DataTransmission_CentOS7_Deploy/ user@centos7-server:/tmp/
scp Anaconda3-2023.09-0-Linux-x86_64.sh user@centos7-server:/tmp/

# 方法2: 使用USB等离线方式
# 将文件复制到USB，然后在CentOS 7上挂载复制
```

## 第三步：在CentOS 7上执行部署

### 3.1 准备部署环境
```bash
# 登录CentOS 7
cd /tmp/DataTransmission_CentOS7_Deploy

# 确保Anaconda安装包在/tmp目录
ls -la /tmp/Anaconda3-2023.09-0-Linux-x86_64.sh

# 给部署脚本执行权限
chmod +x deploy_centos7.sh
```

### 3.2 执行部署
```bash
# 运行部署脚本
./deploy_centos7.sh

# 如果提示需要重新登录，则：
# 1. 重新登录shell
# 2. 再次运行 ./deploy_centos7.sh
```

### 3.3 配置数据库
```bash
# 配置MariaDB安全设置
sudo mysql_secure_installation

# 创建数据库和用户
mysql -u root -p << 'EOF'
CREATE DATABASE DataTransmission CHARACTER SET utf8 COLLATE utf8_general_ci;
CREATE USER 'datatrans'@'localhost' IDENTIFIED BY 'JKga#123';
GRANT ALL PRIVILEGES ON DataTransmission.* TO 'datatrans'@'localhost';
FLUSH PRIVILEGES;
EXIT;
EOF
```

### 3.4 配置应用程序
```bash
# 编辑配置文件
sudo vi /opt/DataTransmission/config.py

# 确认以下配置：
# - 数据库连接信息
# - 摄像头索引 (通常为0)
# - 二维码显示尺寸 (900x900)
```

## 第四步：启动和验证

### 4.1 启动服务
```bash
# 启动服务
sudo systemctl start datatransmission

# 检查状态
sudo systemctl status datatransmission

# 设置开机自启
sudo systemctl enable datatransmission

# 查看日志
sudo journalctl -u datatransmission -f
```

### 4.2 功能验证
```bash
# 测试HTTP接口
curl http://localhost:5000/health

# 测试数据接收接口
curl -X POST http://localhost:5000/receiveData \
  -H "Content-Type: application/json" \
  -d '{"id":"'$(date +%s)'","type":1,"data":"测试数据"}'

# 检查摄像头
ls /dev/video*

# 测试二维码显示（插入测试数据后观察）
```

## 常见问题和解决方案

### Q1: conda环境创建失败
```bash
# 手动创建环境
conda create -n datatransmission python=3.10 -y
conda activate datatransmission
pip install -r src/requirements.txt
```

### Q2: 图形界面问题
```bash
# 确保图形界面正常
echo $DISPLAY
# 应该显示 :0.0

# 如果通过SSH连接
ssh -X username@hostname

# 测试图形界面
xclock &
```

### Q3: 摄像头权限问题
```bash
# 添加用户到video组
sudo usermod -a -G video $USER
newgrp video

# 检查摄像头设备
ls -l /dev/video*
```

### Q4: 依赖包安装失败
```bash
# 手动安装问题包
conda activate datatransmission

# OpenCV问题
pip uninstall opencv-python
pip install opencv-python-headless

# pyzbar问题
sudo yum install -y zbar-devel
pip install pyzbar
```

## 维护和管理

### 日常管理命令
```bash
# 服务管理
sudo systemctl start datatransmission
sudo systemctl stop datatransmission
sudo systemctl restart datatransmission
sudo systemctl status datatransmission

# 环境管理
conda activate datatransmission
conda list
conda info --envs

# 日志查看
sudo journalctl -u datatransmission -f
tail -f /opt/DataTransmission/data_transmission.log
```

### 更新部署
如果需要更新代码：
```bash
# 停止服务
sudo systemctl stop datatransmission

# 备份当前版本
sudo cp -r /opt/DataTransmission /opt/DataTransmission.backup

# 更新代码文件
sudo cp new_files/* /opt/DataTransmission/

# 重启服务
sudo systemctl start datatransmission
```

这个方案确保了从Windows开发环境到CentOS 7生产环境的平滑迁移，同时保持了Anaconda环境管理的优势。
