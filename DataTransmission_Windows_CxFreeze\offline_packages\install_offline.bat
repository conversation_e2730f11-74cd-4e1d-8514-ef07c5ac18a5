@echo off
chcp 65001 >nul
title 离线安装DataTransmission依赖

echo ========================================
echo    离线安装DataTransmission依赖
echo ========================================
echo.

echo 检查Python环境...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ✗ Python未安装或不在PATH中
    pause
    exit /b 1
)

echo ✓ Python环境正常
python --version

echo.
echo 开始离线安装依赖包...
echo 安装目录: offline_packages
echo.

python -m pip install --no-index --find-links offline_packages mysql-connector-python
python -m pip install --no-index --find-links offline_packages opencv-python
python -m pip install --no-index --find-links offline_packages qrcode[pil]
python -m pip install --no-index --find-links offline_packages pyzbar
python -m pip install --no-index --find-links offline_packages Pillow
python -m pip install --no-index --find-links offline_packages Flask
python -m pip install --no-index --find-links offline_packages APScheduler
python -m pip install --no-index --find-links offline_packages numpy

echo.
echo 验证安装...
python -c "import mysql.connector; print('✓ mysql.connector')" 2>nul || echo "✗ mysql.connector"
python -c "import cv2; print('✓ cv2')" 2>nul || echo "✗ cv2"
python -c "import qrcode; print('✓ qrcode')" 2>nul || echo "✗ qrcode"
python -c "import pyzbar; print('✓ pyzbar')" 2>nul || echo "✗ pyzbar"
python -c "import PIL; print('✓ PIL')" 2>nul || echo "✗ PIL"
python -c "import flask; print('✓ flask')" 2>nul || echo "✗ flask"
python -c "import apscheduler; print('✓ apscheduler')" 2>nul || echo "✗ apscheduler"
python -c "import numpy; print('✓ numpy')" 2>nul || echo "✗ numpy"

echo.
echo 离线安装完成！
pause
