#!/bin/bash

# CentOS 7 离线部署包构建脚本
# 用于创建包含所有依赖的CentOS 7离线部署包

echo "开始构建CentOS 7离线部署包..."

# 设置变量
PROJECT_NAME="DataTransmission"
BUILD_DIR="build_centos7"
PACKAGE_DIR="${BUILD_DIR}/${PROJECT_NAME}_centos7_offline"
PYTHON_VERSION="3.10"

# 清理之前的构建
echo "清理之前的构建文件..."
rm -rf ${BUILD_DIR}

# 创建构建目录
echo "创建构建目录..."
mkdir -p ${PACKAGE_DIR}
mkdir -p ${PACKAGE_DIR}/src
mkdir -p ${PACKAGE_DIR}/dependencies
mkdir -p ${PACKAGE_DIR}/scripts
mkdir -p ${PACKAGE_DIR}/rpm_packages

# 复制项目源代码
echo "复制项目源代码..."
cp *.py ${PACKAGE_DIR}/src/
cp requirements.txt ${PACKAGE_DIR}/src/
cp README.md ${PACKAGE_DIR}/
cp config.py ${PACKAGE_DIR}/src/
cp environment.yml ${PACKAGE_DIR}/

# 创建conda环境导出文件
echo "导出conda环境..."
conda env export --name base > ${PACKAGE_DIR}/environment.yml

# 下载Python依赖包（离线包）
echo "下载Python依赖包..."
cd ${PACKAGE_DIR}/dependencies
mkdir python_packages

# 使用pip下载所有依赖包到本地
pip download -r ../src/requirements.txt --dest ./python_packages

# 下载CentOS 7系统依赖包信息
echo "记录CentOS 7系统依赖信息..."
cat > system_dependencies_centos7.txt << 'EOF'
# CentOS 7系统依赖包
epel-release
python3
python3-pip
python3-devel
python3-tkinter
zbar
zbar-devel
mesa-libGL
mesa-libGL-devel
glib2-devel
mariadb-server
mariadb
mariadb-devel
libX11-devel
libXext-devel
libXrender-devel
libICE-devel
libSM-devel
gcc
gcc-c++
make
cmake
pkg-config
EOF

cd ../../..

# 创建CentOS 7离线安装脚本
echo "创建CentOS 7离线安装脚本..."
cat > ${PACKAGE_DIR}/scripts/install_centos7_offline.sh << 'EOF'
#!/bin/bash

# CentOS 7离线安装脚本
echo "开始在CentOS 7上离线安装数据传输客户端..."

# 检查是否为root用户
if [ "$EUID" -ne 0 ]; then
    echo "请使用sudo权限运行此脚本"
    exit 1
fi

# 检查系统版本
if ! grep -q "CentOS Linux release 7" /etc/redhat-release 2>/dev/null; then
    echo "警告: 此脚本专为CentOS 7设计，当前系统可能不兼容"
    echo "当前系统: $(cat /etc/redhat-release 2>/dev/null || echo '未知')"
    read -p "是否继续安装？(y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        exit 1
    fi
fi

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"

echo "项目目录: $PROJECT_DIR"

# 安装系统依赖
echo "安装系统依赖..."
yum update -y 2>/dev/null || echo "跳过yum update"

# 安装EPEL仓库
yum install -y epel-release 2>/dev/null || echo "EPEL可能已安装"

# 从文件读取系统依赖并尝试安装
while IFS= read -r package; do
    if [[ ! $package =~ ^#.* ]] && [[ -n $package ]]; then
        echo "尝试安装: $package"
        yum install -y $package 2>/dev/null || echo "跳过: $package"
    fi
done < "$PROJECT_DIR/dependencies/system_dependencies_centos7.txt"

# 安装开发工具组
yum groupinstall -y "Development Tools" 2>/dev/null || echo "开发工具可能已安装"

# 检查Python 3是否可用
if ! command -v python3 &> /dev/null; then
    echo "错误: Python 3 未安装"
    echo "尝试安装Python 3..."
    yum install -y python3 python3-pip python3-devel
    
    if ! command -v python3 &> /dev/null; then
        echo "Python 3安装失败，请手动安装"
        exit 1
    fi
fi

# 检查Python版本
PYTHON_VERSION=$(python3 --version 2>&1 | awk '{print $2}' | cut -d. -f1,2)
echo "检测到Python版本: $PYTHON_VERSION"

# 创建项目目录
PROJECT_INSTALL_DIR="/opt/DataTransmission"
echo "创建安装目录: $PROJECT_INSTALL_DIR"
mkdir -p $PROJECT_INSTALL_DIR

# 复制源代码
echo "复制源代码..."
cp -r $PROJECT_DIR/src/* $PROJECT_INSTALL_DIR/

# 创建虚拟环境
echo "创建Python虚拟环境..."
cd $PROJECT_INSTALL_DIR
python3 -m venv venv

# 激活虚拟环境并安装依赖
echo "安装Python依赖包..."
source venv/bin/activate

# 升级pip
pip install --upgrade pip

# 离线安装pip包
pip install --no-index --find-links $PROJECT_DIR/dependencies/python_packages -r requirements.txt

# 创建启动脚本
echo "创建启动脚本..."
cat > start.sh << 'STARTEOF'
#!/bin/bash
cd "$(dirname "$0")"
source venv/bin/activate

# 设置显示环境变量（支持图形界面）
export DISPLAY=${DISPLAY:-:0.0}

# 启动程序
python main.py
STARTEOF

chmod +x start.sh

# 创建停止脚本
cat > stop.sh << 'STOPEOF'
#!/bin/bash
pkill -f "python main.py"
echo "数据传输客户端已停止"
STOPEOF

chmod +x stop.sh

# 启动并配置MariaDB
echo "配置MariaDB数据库..."
systemctl start mariadb
systemctl enable mariadb

# 创建systemd服务文件
echo "创建systemd服务文件..."
cat > /etc/systemd/system/datatransmission.service << 'SERVICEEOF'
[Unit]
Description=Data Transmission Client
After=network.target mariadb.service

[Service]
Type=simple
User=root
WorkingDirectory=/opt/DataTransmission
Environment=DISPLAY=:0.0
ExecStart=/opt/DataTransmission/start.sh
ExecStop=/opt/DataTransmission/stop.sh
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
SERVICEEOF

# 重新加载systemd
systemctl daemon-reload

# 配置防火墙（如果启用）
if systemctl is-active --quiet firewalld; then
    echo "配置防火墙..."
    firewall-cmd --permanent --add-port=5000/tcp
    firewall-cmd --reload
fi

echo "安装完成！"
echo ""
echo "配置步骤："
echo "1. 配置MariaDB数据库:"
echo "   mysql_secure_installation"
echo "2. 创建数据库和用户:"
echo "   mysql -u root -p"
echo "   CREATE DATABASE DataTransmission CHARACTER SET utf8 COLLATE utf8_general_ci;"
echo "   CREATE USER 'datatrans'@'localhost' IDENTIFIED BY 'your_password';"
echo "   GRANT ALL PRIVILEGES ON DataTransmission.* TO 'datatrans'@'localhost';"
echo "   FLUSH PRIVILEGES;"
echo "3. 编辑配置文件: vi $PROJECT_INSTALL_DIR/config.py"
echo "4. 启动服务: systemctl start datatransmission"
echo "5. 设置开机自启: systemctl enable datatransmission"
echo ""
echo "管理命令："
echo "- 启动服务: systemctl start datatransmission"
echo "- 停止服务: systemctl stop datatransmission"
echo "- 查看状态: systemctl status datatransmission"
echo "- 查看日志: journalctl -u datatransmission -f"
echo ""
echo "手动运行："
echo "- cd $PROJECT_INSTALL_DIR && ./start.sh"
EOF

chmod +x ${PACKAGE_DIR}/scripts/install_centos7_offline.sh

# 创建CentOS 7部署说明文档
cat > ${PACKAGE_DIR}/CENTOS7_DEPLOY_README.md << 'EOF'
# CentOS 7 离线部署说明

## 系统要求
- CentOS 7.x (带图形界面)
- 至少2GB可用磁盘空间
- root权限或sudo权限

## 部署步骤

### 1. 传输部署包到CentOS 7机器
```bash
# 解压部署包
tar -xzf DataTransmission_centos7_offline_*.tar.gz
cd DataTransmission_centos7_offline
```

### 2. 运行安装脚本
```bash
sudo ./scripts/install_centos7_offline.sh
```

### 3. 配置MariaDB数据库
```bash
# 安全配置MariaDB
sudo mysql_secure_installation

# 创建数据库
mysql -u root -p
CREATE DATABASE DataTransmission CHARACTER SET utf8 COLLATE utf8_general_ci;
CREATE USER 'datatrans'@'localhost' IDENTIFIED BY 'your_password';
GRANT ALL PRIVILEGES ON DataTransmission.* TO 'datatrans'@'localhost';
FLUSH PRIVILEGES;
EXIT;
```

### 4. 配置应用程序
```bash
sudo vi /opt/DataTransmission/config.py
```

### 5. 启动服务
```bash
sudo systemctl start datatransmission
sudo systemctl enable datatransmission
```

## CentOS 7特殊配置

### 图形界面支持
确保系统安装了图形界面：
```bash
sudo yum groupinstall -y "GNOME Desktop" "Graphical Administration Tools"
```

### 摄像头权限
将用户添加到video组：
```bash
sudo usermod -a -G video $USER
```

### SELinux配置（如果启用）
```bash
# 临时禁用SELinux
sudo setenforce 0

# 永久禁用（重启后生效）
sudo sed -i 's/SELINUX=enforcing/SELINUX=disabled/' /etc/selinux/config
```

### 防火墙配置
```bash
# 开放端口
sudo firewall-cmd --permanent --add-port=5000/tcp
sudo firewall-cmd --reload
```

## 故障排除

### Python版本问题
CentOS 7默认Python版本较低，如果遇到问题：
```bash
# 安装Python 3.6+
sudo yum install -y python36 python36-pip python36-devel
# 或使用软件集合
sudo yum install -y centos-release-scl
sudo yum install -y rh-python38
```

### 图形界面问题
```bash
# 检查DISPLAY环境变量
echo $DISPLAY

# 如果通过SSH连接，启用X11转发
ssh -X username@hostname
```

### 依赖包问题
```bash
# 手动安装缺失的依赖
sudo yum install -y python3-tkinter
sudo yum install -y mesa-libGL-devel
```
EOF

echo "创建最终部署包..."
cd ${BUILD_DIR}
tar -czf ${PROJECT_NAME}_centos7_offline_$(date +%Y%m%d_%H%M%S).tar.gz ${PROJECT_NAME}_centos7_offline/

echo "CentOS 7部署包构建完成！"
echo ""
echo "部署包位置: ${BUILD_DIR}/${PROJECT_NAME}_centos7_offline_$(date +%Y%m%d_%H%M%S).tar.gz"
echo ""
echo "CentOS 7部署步骤："
echo "1. 将tar.gz文件传输到目标CentOS 7机器"
echo "2. 解压: tar -xzf ${PROJECT_NAME}_centos7_offline_*.tar.gz"
echo "3. 运行安装: sudo ./DataTransmission_centos7_offline/scripts/install_centos7_offline.sh"
echo "4. 配置MariaDB数据库"
echo "5. 配置应用程序连接信息"
echo "6. 启动服务"
