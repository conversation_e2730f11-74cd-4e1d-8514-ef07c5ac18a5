## Why this file is included

This program has been frozen with cx_Freeze.  The freezing process
resulted in certain components from the cx_Freeze software being included
in the frozen application, in particular bootstrap code for launching
the frozen python script.  The cx_Freeze software is subject to the
license set out below.

# Licensing

-   Copyright © 2020-2025, <PERSON><PERSON>.
-   Copyright © 2007-2019, <PERSON>.
-   Copyright © 2001-2006, Computronix (Canada) Ltd., Edmonton, Alberta,
    Canada.
-   All rights reserved.

NOTE: This license is derived from the Python Software Foundation
License which can be found at
<https://docs.python.org/3/license.html#psf-license-agreement-for-python-release>

## License for cx_Freeze

1.  This LICENSE AGREEMENT is between the copyright holders and the
    Individual or Organization ("Licensee") accessing and otherwise
    using cx_Freeze software in source or binary form and its associated
    documentation.
2.  Subject to the terms and conditions of this License Agreement, the
    copyright holders hereby grant Licensee a nonexclusive,
    royalty-free, world-wide license to reproduce, analyze, test,
    perform and/or display publicly, prepare derivative works,
    distribute, and otherwise use cx_Freeze alone or in any derivative
    version, provided, however, that this License Agreement and this
    notice of copyright are retained in cx_Freeze alone or in any
    derivative version prepared by Licensee.
3.  In the event Licensee prepares a derivative work that is based on or
    incorporates cx_Freeze or any part thereof, and wants to make the
    derivative work available to others as provided herein, then
    Licensee hereby agrees to include in any such work a brief summary
    of the changes made to cx_Freeze.
4.  The copyright holders are making cx_Freeze available to Licensee on
    an "AS IS" basis. THE COPYRIGHT HOLDERS MAKE NO REPRESENTATIONS OR
    WARRANTIES, EXPRESS OR IMPLIED. BY WAY OF EXAMPLE, BUT NOT
    LIMITATION, THE COPYRIGHT HOLDERS MAKE NO AND DISCLAIM ANY
    REPRESENTATION OR WARRANTY OF MERCHANTABILITY OR FITNESS FOR ANY
    PARTICULAR PURPOSE OR THAT THE USE OF CX_FREEZE WILL NOT INFRINGE
    ANY THIRD PARTY RIGHTS.
5.  THE COPYRIGHT HOLDERS SHALL NOT BE LIABLE TO LICENSEE OR ANY OTHER
    USERS OF CX_FREEZE FOR ANY INCIDENTAL, SPECIAL, OR CONSEQUENTIAL
    DAMAGES OR LOSS AS A RESULT OF MODIFYING, DISTRIBUTING, OR OTHERWISE
    USING CX_FREEZE, OR ANY DERIVATIVE THEREOF, EVEN IF ADVISED OF THE
    POSSIBILITY THEREOF.
6.  This License Agreement will automatically terminate upon a material
    breach of its terms and conditions.
7.  Nothing in this License Agreement shall be deemed to create any
    relationship of agency, partnership, or joint venture between the
    copyright holders and Licensee. This License Agreement does not
    grant permission to use copyright holder's trademarks or trade name
    in a trademark sense to endorse or promote products or services of
    Licensee, or any third party.
8.  By copying, installing or otherwise using cx_Freeze, Licensee agrees
    to be bound by the terms and conditions of this License Agreement.

Computronix® is a registered trademark of Computronix (Canada) Ltd.
