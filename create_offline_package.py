#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
创建包含所有依赖的离线安装包
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path
import zipfile

def download_dependencies():
    """下载所有依赖包到本地"""
    print("下载依赖包...")
    
    # 创建依赖包目录
    deps_dir = Path("offline_dependencies")
    if deps_dir.exists():
        shutil.rmtree(deps_dir)
    deps_dir.mkdir()
    
    # 核心依赖列表
    dependencies = [
        "mysql-connector-python",
        "opencv-python",
        "qrcode[pil]",
        "pyzbar",
        "Pillow",
        "Flask",
        "APScheduler",
        "numpy",
        "Werkzeug",
        "Jinja2",
        "MarkupSafe",
        "itsdangerous",
        "click",
        "blinker",
        "pytz",
        "tzlocal",
        "six",
        "setuptools",
        "wheel",
    ]
    
    # 下载依赖包
    for dep in dependencies:
        print(f"下载 {dep}...")
        try:
            cmd = [
                sys.executable, "-m", "pip", "download",
                "--dest", str(deps_dir),
                "--no-deps",  # 不下载子依赖，避免冲突
                dep
            ]
            subprocess.run(cmd, check=True)
            print(f"✓ {dep} 下载完成")
        except subprocess.CalledProcessError as e:
            print(f"✗ {dep} 下载失败: {e}")
    
    # 下载所有依赖（包括子依赖）
    print("下载完整依赖树...")
    try:
        cmd = [
            sys.executable, "-m", "pip", "download",
            "--dest", str(deps_dir),
            "--requirement", "requirements.txt"
        ]
        subprocess.run(cmd, check=True)
        print("✓ 完整依赖树下载完成")
    except subprocess.CalledProcessError as e:
        print(f"⚠ 完整依赖树下载失败: {e}")
    
    return deps_dir

def create_portable_python():
    """创建便携式Python环境"""
    print("创建便携式Python环境...")
    
    portable_dir = Path("DataTransmission_Portable")
    if portable_dir.exists():
        shutil.rmtree(portable_dir)
    portable_dir.mkdir()
    
    # 复制Python可执行文件
    python_dir = Path(sys.executable).parent
    
    # 复制必要的Python文件
    essential_files = [
        "python.exe",
        "python3.dll",
        "python312.dll",
        "vcruntime140.dll",
        "vcruntime140_1.dll",
    ]
    
    for file in essential_files:
        src = python_dir / file
        if src.exists():
            shutil.copy2(src, portable_dir / file)
            print(f"✓ 复制 {file}")
        else:
            print(f"⚠ 未找到 {file}")
    
    # 复制Lib目录
    lib_src = python_dir / "Lib"
    lib_dst = portable_dir / "Lib"
    if lib_src.exists():
        shutil.copytree(lib_src, lib_dst)
        print("✓ 复制Python标准库")
    
    # 复制DLLs目录
    dlls_src = python_dir / "DLLs"
    dlls_dst = portable_dir / "DLLs"
    if dlls_src.exists():
        shutil.copytree(dlls_src, dlls_dst)
        print("✓ 复制Python DLLs")
    
    return portable_dir

def install_dependencies_to_portable(portable_dir, deps_dir):
    """将依赖安装到便携式Python环境"""
    print("安装依赖到便携式环境...")
    
    # 创建site-packages目录
    site_packages = portable_dir / "Lib" / "site-packages"
    site_packages.mkdir(parents=True, exist_ok=True)
    
    # 安装所有下载的包
    python_exe = portable_dir / "python.exe"
    
    for wheel_file in deps_dir.glob("*.whl"):
        print(f"安装 {wheel_file.name}...")
        try:
            cmd = [
                str(python_exe), "-m", "pip", "install",
                "--no-index",
                "--find-links", str(deps_dir),
                "--target", str(site_packages),
                str(wheel_file)
            ]
            subprocess.run(cmd, check=True, capture_output=True)
            print(f"✓ {wheel_file.name} 安装完成")
        except subprocess.CalledProcessError as e:
            print(f"✗ {wheel_file.name} 安装失败: {e}")
    
    # 安装tar.gz文件
    for tar_file in deps_dir.glob("*.tar.gz"):
        print(f"安装 {tar_file.name}...")
        try:
            cmd = [
                str(python_exe), "-m", "pip", "install",
                "--no-index",
                "--find-links", str(deps_dir),
                "--target", str(site_packages),
                str(tar_file)
            ]
            subprocess.run(cmd, check=True, capture_output=True)
            print(f"✓ {tar_file.name} 安装完成")
        except subprocess.CalledProcessError as e:
            print(f"✗ {tar_file.name} 安装失败: {e}")

def create_deployment_package(portable_dir):
    """创建最终的部署包"""
    print("创建部署包...")
    
    # 复制应用程序文件
    app_files = [
        "main.py",
        "config.py",
        "database.py",
        "web_server.py",
        "qr_generator.py",
        "camera_monitor.py",
        "templates",
        "static",
    ]
    
    for item in app_files:
        src = Path(item)
        if src.exists():
            if src.is_file():
                shutil.copy2(src, portable_dir / item)
                print(f"✓ 复制文件 {item}")
            elif src.is_dir():
                dst = portable_dir / item
                if dst.exists():
                    shutil.rmtree(dst)
                shutil.copytree(src, dst)
                print(f"✓ 复制目录 {item}")
        else:
            print(f"⚠ 未找到 {item}")
    
    # 创建启动脚本
    start_script = '''@echo off
chcp 65001 >nul
title DataTransmission 便携版

echo ========================================
echo    DataTransmission 便携版
echo ========================================
echo.

echo 检查MySQL服务...
sc query mysql >nul 2>&1
if %errorlevel% equ 0 (
    echo ✓ MySQL服务已安装
) else (
    echo ⚠ 未检测到MySQL服务
    echo   请确保已安装MySQL数据库
)

echo.
echo 启动DataTransmission...
echo Web界面: http://localhost:5000
echo 按 Ctrl+C 停止程序
echo.

python.exe main.py

echo.
echo 程序已退出
pause
'''
    
    with open(portable_dir / "start.bat", 'w', encoding='utf-8') as f:
        f.write(start_script)
    
    # 创建安装脚本
    install_script = '''@echo off
chcp 65001 >nul
title 安装依赖包

echo ========================================
echo    安装DataTransmission依赖包
echo ========================================
echo.

echo 正在安装依赖包...
python.exe -m pip install --upgrade pip
python.exe -m pip install mysql-connector-python
python.exe -m pip install opencv-python
python.exe -m pip install qrcode[pil]
python.exe -m pip install pyzbar
python.exe -m pip install Pillow
python.exe -m pip install Flask
python.exe -m pip install APScheduler
python.exe -m pip install numpy

echo.
echo 依赖包安装完成
pause
'''
    
    with open(portable_dir / "install_deps.bat", 'w', encoding='utf-8') as f:
        f.write(install_script)
    
    # 创建README
    readme_content = '''# DataTransmission 便携版

这是一个包含完整Python环境的便携版本。

## 使用方法

1. 确保已安装MySQL数据库
2. 编辑config.py配置数据库连接
3. 双击start.bat启动程序
4. 访问 http://localhost:5000

## 如果缺少依赖

如果程序提示缺少某些模块，请：
1. 双击install_deps.bat安装依赖
2. 或者手动运行: python.exe -m pip install 模块名

## 文件说明

- python.exe: Python解释器
- Lib/: Python标准库和第三方库
- DLLs/: Python动态链接库
- main.py: 主程序
- config.py: 配置文件
- start.bat: 启动脚本
- install_deps.bat: 依赖安装脚本
'''
    
    with open(portable_dir / "README.md", 'w', encoding='utf-8') as f:
        f.write(readme_content)
    
    print(f"✓ 部署包创建完成: {portable_dir}")

def main():
    """主函数"""
    print("=" * 50)
    print("DataTransmission 离线包创建工具")
    print("=" * 50)
    
    try:
        # 下载依赖
        deps_dir = download_dependencies()
        
        # 创建便携式Python环境
        portable_dir = create_portable_python()
        
        # 安装依赖到便携式环境
        install_dependencies_to_portable(portable_dir, deps_dir)
        
        # 创建部署包
        create_deployment_package(portable_dir)
        
        print("\n" + "=" * 50)
        print("离线包创建完成！")
        print("=" * 50)
        print(f"生成的文件夹: {portable_dir}")
        print("可以将整个文件夹复制到目标机器上运行")
        
    except Exception as e:
        print(f"创建离线包时出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
