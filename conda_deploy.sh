#!/bin/bash

# Conda环境部署脚本
# 用于在有conda的Ubuntu机器上快速部署

echo "=== DataTransmission Conda环境部署脚本 ==="

# 检查conda是否安装
if ! command -v conda &> /dev/null; then
    echo "错误: 未找到conda命令"
    echo "请先安装Anaconda或Miniconda"
    exit 1
fi

# 设置变量
ENV_NAME="datatransmission"
PROJECT_DIR="/opt/DataTransmission"

echo "开始部署DataTransmission项目..."

# 检查环境是否已存在
if conda env list | grep -q "^${ENV_NAME} "; then
    echo "环境 ${ENV_NAME} 已存在，是否删除并重新创建？(y/N)"
    read -r response
    if [[ "$response" =~ ^[Yy]$ ]]; then
        echo "删除现有环境..."
        conda env remove -n ${ENV_NAME}
    else
        echo "取消部署"
        exit 1
    fi
fi

# 创建conda环境
echo "创建conda环境: ${ENV_NAME}"
if [ -f "environment.yml" ]; then
    conda env create -f environment.yml -n ${ENV_NAME}
else
    echo "未找到environment.yml，使用默认配置创建环境..."
    conda create -n ${ENV_NAME} python=3.10 -y
    conda activate ${ENV_NAME}
    pip install -r requirements.txt
fi

# 激活环境
echo "激活conda环境..."
source $(conda info --base)/etc/profile.d/conda.sh
conda activate ${ENV_NAME}

# 验证环境
echo "验证Python环境..."
python --version
pip list | grep -E "(Flask|mysql-connector|opencv|qrcode|pyzbar)"

# 创建项目目录
echo "创建项目目录: ${PROJECT_DIR}"
sudo mkdir -p ${PROJECT_DIR}

# 复制项目文件
echo "复制项目文件..."
sudo cp *.py ${PROJECT_DIR}/
sudo cp requirements.txt ${PROJECT_DIR}/
sudo cp README.md ${PROJECT_DIR}/

# 设置权限
sudo chown -R $USER:$USER ${PROJECT_DIR}

# 创建启动脚本
echo "创建启动脚本..."
cat > ${PROJECT_DIR}/start_conda.sh << EOF
#!/bin/bash
# 使用conda环境启动DataTransmission

# 获取conda路径
CONDA_BASE=\$(conda info --base)
source \${CONDA_BASE}/etc/profile.d/conda.sh

# 激活环境
conda activate ${ENV_NAME}

# 切换到项目目录
cd ${PROJECT_DIR}

# 启动程序
python main.py
EOF

chmod +x ${PROJECT_DIR}/start_conda.sh

# 创建停止脚本
cat > ${PROJECT_DIR}/stop.sh << 'EOF'
#!/bin/bash
pkill -f "python main.py"
echo "DataTransmission服务已停止"
EOF

chmod +x ${PROJECT_DIR}/stop.sh

# 创建systemd服务文件（使用conda环境）
echo "创建systemd服务文件..."
sudo tee /etc/systemd/system/datatransmission-conda.service > /dev/null << EOF
[Unit]
Description=Data Transmission Client (Conda)
After=network.target mysql.service

[Service]
Type=simple
User=$USER
WorkingDirectory=${PROJECT_DIR}
ExecStart=${PROJECT_DIR}/start_conda.sh
ExecStop=${PROJECT_DIR}/stop.sh
Restart=always
RestartSec=10
Environment=PATH=/home/<USER>/anaconda3/envs/${ENV_NAME}/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin

[Install]
WantedBy=multi-user.target
EOF

# 重新加载systemd
sudo systemctl daemon-reload

# 安装系统依赖 (CentOS 7)
echo "安装系统依赖..."
sudo yum update -y
sudo yum install -y epel-release
sudo yum groupinstall -y "Development Tools"
sudo yum install -y zbar-devel zbar
sudo yum install -y mesa-libGL mesa-libGL-devel glib2-devel
sudo yum install -y mariadb-server mariadb mariadb-devel
sudo yum install -y python3-devel python3-tkinter
sudo yum install -y libX11-devel libXext-devel libXrender-devel libICE-devel libSM-devel

# 创建测试脚本
echo "创建测试脚本..."
cat > ${PROJECT_DIR}/test_conda_env.py << 'EOF'
#!/usr/bin/env python3
"""测试conda环境中的依赖包"""

import sys
print(f"Python版本: {sys.version}")

try:
    import flask
    print(f"✓ Flask: {flask.__version__}")
except ImportError as e:
    print(f"✗ Flask导入失败: {e}")

try:
    import mysql.connector
    print(f"✓ MySQL Connector: {mysql.connector.__version__}")
except ImportError as e:
    print(f"✗ MySQL Connector导入失败: {e}")

try:
    import cv2
    print(f"✓ OpenCV: {cv2.__version__}")
except ImportError as e:
    print(f"✗ OpenCV导入失败: {e}")

try:
    import qrcode
    print(f"✓ QRCode: {qrcode.__version__}")
except ImportError as e:
    print(f"✗ QRCode导入失败: {e}")

try:
    import pyzbar
    print("✓ PyZBar: 已安装")
except ImportError as e:
    print(f"✗ PyZBar导入失败: {e}")

try:
    import numpy as np
    print(f"✓ NumPy: {np.__version__}")
except ImportError as e:
    print(f"✗ NumPy导入失败: {e}")

print("\n依赖检查完成！")
EOF

# 运行依赖测试
echo "测试conda环境依赖..."
python ${PROJECT_DIR}/test_conda_env.py

echo ""
echo "=== 部署完成 ==="
echo ""
echo "项目位置: ${PROJECT_DIR}"
echo "Conda环境: ${ENV_NAME}"
echo ""
echo "下一步操作："
echo "1. 配置数据库连接: nano ${PROJECT_DIR}/config.py"
echo "2. 设置MySQL数据库"
echo "3. 启动服务:"
echo "   方法1 (systemd): sudo systemctl start datatransmission-conda"
echo "   方法2 (手动): ${PROJECT_DIR}/start_conda.sh"
echo ""
echo "管理命令："
echo "- 启动: sudo systemctl start datatransmission-conda"
echo "- 停止: sudo systemctl stop datatransmission-conda"
echo "- 状态: sudo systemctl status datatransmission-conda"
echo "- 开机自启: sudo systemctl enable datatransmission-conda"
echo ""
echo "手动运行："
echo "- conda activate ${ENV_NAME}"
echo "- cd ${PROJECT_DIR}"
echo "- python main.py"
