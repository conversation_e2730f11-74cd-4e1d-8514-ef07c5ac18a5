#使用python 3.10版本 写1个客户端。需要连接数据库，数据库使用mysql，需要开通web的http接口，可以使用http接口接收数据，运行环境为乌班图操作系统。数据库库名为DataTransmission。链接字符串为localhost:3306/DataTransmission，utf8的字符集。

##数据库结构：mysql里有两张表，表名为receive_data用于存放二维码解析出的数据。字段有三个，id，type，data 。
表名为：transmission_data，用于存放待发送的数据，字段有三个，id，type，data，status。
其中id为时间戳，数据库使用varchar类型，type是数据类型，数据库使用int类型，data为数据，使用varchar类型，status，表示是否展示过，默认为0，在取出，进行生成二维码后，变成1.
 
##主要功能一：对外开放一个接口，可以将要传输的数据收集过来。
接口名称为：receiveData(string id，int type，string data)
收到数据，存入本地数据库的transmission_data表，存入前，先根据id和type和status判断表内是否存在，存在则丢弃。

##主要功能二：设置一个定时任务，从transmission中取出status=0的数据，按照顺序重前往后，一次取1条，然后将这一行数据组成json对象；格式如下：
{
	"id":id,
	"type":type,
	"data":data
}
将json对象转成string，将生成的string变成二维码展示。每次数据生成的二维码展示2秒，完成后，根据id和type，将表中的对应数据的status设置成1。

##主要功能三：实时监测当前设备上的摄像头采集的数据，按照一秒截取一张
图片的数据，识别解析图片内是否存在二维码数据，如果存在二维码，则将二维码上的
数据进行解析，将解析后的数据进行json序列化。判断数据库中，当前数据是否已经存在，根据id，type和status判断。如果存在，则丢弃该数据。
