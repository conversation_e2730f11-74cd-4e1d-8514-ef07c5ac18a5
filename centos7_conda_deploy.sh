#!/bin/bash

# CentOS 7 Conda环境部署脚本
# 用于在有conda的CentOS 7机器上快速部署

echo "=== DataTransmission CentOS 7 Conda环境部署脚本 ==="

# 检查系统版本
if ! grep -q "CentOS Linux release 7" /etc/redhat-release 2>/dev/null; then
    echo "警告: 此脚本专为CentOS 7设计"
    echo "当前系统: $(cat /etc/redhat-release 2>/dev/null || echo '未知')"
    read -p "是否继续？(y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        exit 1
    fi
fi

# 检查conda是否安装
if ! command -v conda &> /dev/null; then
    echo "错误: 未找到conda命令"
    echo "请先安装Anaconda或Miniconda"
    echo ""
    echo "CentOS 7安装conda步骤："
    echo "1. 下载Miniconda: wget https://repo.anaconda.com/miniconda/Miniconda3-latest-Linux-x86_64.sh"
    echo "2. 安装: bash Miniconda3-latest-Linux-x86_64.sh"
    echo "3. 重新加载shell: source ~/.bashrc"
    exit 1
fi

# 设置变量
ENV_NAME="datatransmission"
PROJECT_DIR="/opt/DataTransmission"

echo "开始在CentOS 7上部署DataTransmission项目..."

# 检查环境是否已存在
if conda env list | grep -q "^${ENV_NAME} "; then
    echo "环境 ${ENV_NAME} 已存在，是否删除并重新创建？(y/N)"
    read -r response
    if [[ "$response" =~ ^[Yy]$ ]]; then
        echo "删除现有环境..."
        conda env remove -n ${ENV_NAME}
    else
        echo "取消部署"
        exit 1
    fi
fi

# 安装CentOS 7系统依赖
echo "安装CentOS 7系统依赖..."
sudo yum update -y
sudo yum install -y epel-release

# 安装基础开发工具
sudo yum groupinstall -y "Development Tools"

# 安装图形界面相关依赖
sudo yum install -y python3-tkinter
sudo yum install -y libX11-devel libXext-devel libXrender-devel
sudo yum install -y libICE-devel libSM-devel

# 安装二维码相关依赖
sudo yum install -y zbar zbar-devel

# 安装OpenCV相关依赖
sudo yum install -y mesa-libGL mesa-libGL-devel glib2-devel
sudo yum install -y gtk3-devel
sudo yum install -y libpng-devel libjpeg-turbo-devel

# 安装数据库
sudo yum install -y mariadb-server mariadb mariadb-devel

# 启动MariaDB
sudo systemctl start mariadb
sudo systemctl enable mariadb

# 创建conda环境
echo "创建conda环境: ${ENV_NAME}"
if [ -f "environment.yml" ]; then
    # 修改environment.yml以适配CentOS 7
    cp environment.yml environment_centos7.yml
    sed -i 's/name: datatransmission/name: datatransmission_centos7/' environment_centos7.yml
    conda env create -f environment_centos7.yml -n ${ENV_NAME}
else
    echo "未找到environment.yml，使用默认配置创建环境..."
    conda create -n ${ENV_NAME} python=3.8 -y  # CentOS 7使用Python 3.8更稳定
fi

# 激活环境
echo "激活conda环境..."
source $(conda info --base)/etc/profile.d/conda.sh
conda activate ${ENV_NAME}

# 安装额外的依赖包
echo "安装Python依赖包..."
pip install --upgrade pip

# 逐个安装依赖，处理可能的兼容性问题
echo "安装Flask..."
pip install Flask==2.3.3

echo "安装MySQL连接器..."
pip install mysql-connector-python==8.1.0

echo "安装OpenCV..."
pip install opencv-python==********

echo "安装二维码库..."
pip install qrcode[pil]==7.4.2
pip install pyzbar==0.1.9

echo "安装其他依赖..."
pip install Pillow==10.0.1
pip install APScheduler==3.10.4
pip install numpy==1.24.3
pip install requests==2.31.0

# 验证环境
echo "验证Python环境..."
python --version
pip list | grep -E "(Flask|mysql-connector|opencv|qrcode|pyzbar)"

# 创建项目目录
echo "创建项目目录: ${PROJECT_DIR}"
sudo mkdir -p ${PROJECT_DIR}

# 复制项目文件
echo "复制项目文件..."
sudo cp *.py ${PROJECT_DIR}/
sudo cp requirements.txt ${PROJECT_DIR}/
sudo cp README.md ${PROJECT_DIR}/

# 设置权限
sudo chown -R $USER:$USER ${PROJECT_DIR}

# 创建CentOS 7专用启动脚本
echo "创建CentOS 7启动脚本..."
cat > ${PROJECT_DIR}/start_centos7_conda.sh << EOF
#!/bin/bash
# 使用conda环境在CentOS 7上启动DataTransmission

# 获取conda路径
CONDA_BASE=\$(conda info --base)
source \${CONDA_BASE}/etc/profile.d/conda.sh

# 激活环境
conda activate ${ENV_NAME}

# 设置显示环境变量（支持图形界面）
export DISPLAY=\${DISPLAY:-:0.0}

# 设置库路径（解决CentOS 7兼容性问题）
export LD_LIBRARY_PATH=/usr/lib64:\$LD_LIBRARY_PATH

# 切换到项目目录
cd ${PROJECT_DIR}

# 启动程序
python main.py
EOF

chmod +x ${PROJECT_DIR}/start_centos7_conda.sh

# 创建停止脚本
cat > ${PROJECT_DIR}/stop.sh << 'EOF'
#!/bin/bash
pkill -f "python main.py"
echo "DataTransmission服务已停止"
EOF

chmod +x ${PROJECT_DIR}/stop.sh

# 创建CentOS 7专用systemd服务文件
echo "创建systemd服务文件..."
sudo tee /etc/systemd/system/datatransmission-centos7.service > /dev/null << EOF
[Unit]
Description=Data Transmission Client (CentOS 7 Conda)
After=network.target mariadb.service

[Service]
Type=simple
User=$USER
WorkingDirectory=${PROJECT_DIR}
Environment=DISPLAY=:0.0
Environment=LD_LIBRARY_PATH=/usr/lib64
ExecStart=${PROJECT_DIR}/start_centos7_conda.sh
ExecStop=${PROJECT_DIR}/stop.sh
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
EOF

# 重新加载systemd
sudo systemctl daemon-reload

# 配置防火墙
if systemctl is-active --quiet firewalld; then
    echo "配置防火墙..."
    sudo firewall-cmd --permanent --add-port=5000/tcp
    sudo firewall-cmd --reload
fi

# 创建测试脚本
echo "创建CentOS 7测试脚本..."
cat > ${PROJECT_DIR}/test_centos7_env.py << 'EOF'
#!/usr/bin/env python3
"""测试CentOS 7 conda环境中的依赖包"""

import sys
import os
print(f"Python版本: {sys.version}")
print(f"操作系统: {os.uname()}")

try:
    import flask
    print(f"✓ Flask: {flask.__version__}")
except ImportError as e:
    print(f"✗ Flask导入失败: {e}")

try:
    import mysql.connector
    print(f"✓ MySQL Connector: {mysql.connector.__version__}")
except ImportError as e:
    print(f"✗ MySQL Connector导入失败: {e}")

try:
    import cv2
    print(f"✓ OpenCV: {cv2.__version__}")
except ImportError as e:
    print(f"✗ OpenCV导入失败: {e}")

try:
    import qrcode
    print(f"✓ QRCode: {qrcode.__version__}")
except ImportError as e:
    print(f"✗ QRCode导入失败: {e}")

try:
    import pyzbar
    print("✓ PyZBar: 已安装")
except ImportError as e:
    print(f"✗ PyZBar导入失败: {e}")

try:
    import numpy as np
    print(f"✓ NumPy: {np.__version__}")
except ImportError as e:
    print(f"✗ NumPy导入失败: {e}")

try:
    import tkinter
    print("✓ Tkinter: 已安装")
except ImportError as e:
    print(f"✗ Tkinter导入失败: {e}")

print("\nCentOS 7环境依赖检查完成！")
EOF

# 运行依赖测试
echo "测试CentOS 7 conda环境依赖..."
python ${PROJECT_DIR}/test_centos7_env.py

echo ""
echo "=== CentOS 7部署完成 ==="
echo ""
echo "项目位置: ${PROJECT_DIR}"
echo "Conda环境: ${ENV_NAME}"
echo ""
echo "下一步操作："
echo "1. 配置MariaDB数据库:"
echo "   sudo mysql_secure_installation"
echo "   mysql -u root -p"
echo "   CREATE DATABASE DataTransmission CHARACTER SET utf8;"
echo "   CREATE USER 'datatrans'@'localhost' IDENTIFIED BY 'password';"
echo "   GRANT ALL PRIVILEGES ON DataTransmission.* TO 'datatrans'@'localhost';"
echo "   FLUSH PRIVILEGES;"
echo ""
echo "2. 配置应用程序: vi ${PROJECT_DIR}/config.py"
echo ""
echo "3. 启动服务:"
echo "   方法1 (systemd): sudo systemctl start datatransmission-centos7"
echo "   方法2 (手动): ${PROJECT_DIR}/start_centos7_conda.sh"
echo ""
echo "管理命令："
echo "- 启动: sudo systemctl start datatransmission-centos7"
echo "- 停止: sudo systemctl stop datatransmission-centos7"
echo "- 状态: sudo systemctl status datatransmission-centos7"
echo "- 开机自启: sudo systemctl enable datatransmission-centos7"
echo ""
echo "手动运行："
echo "- conda activate ${ENV_NAME}"
echo "- cd ${PROJECT_DIR}"
echo "- export DISPLAY=:0.0"
echo "- python main.py"
