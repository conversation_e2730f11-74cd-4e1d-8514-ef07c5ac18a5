#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
数据传输客户端演示脚本
用于演示各个功能模块的基本使用
"""

import time
import json
import logging
from database import DatabaseManager
from qr_generator import QRGenerator
from camera_monitor import CameraMonitor

# 配置日志
logging.basicConfig(level=logging.INFO)

def demo_database():
    """演示数据库操作"""
    print("\n=== 数据库操作演示 ===")
    
    try:
        db = DatabaseManager()
        
        # 插入测试数据到transmission_data
        test_id = str(int(time.time() * 1000))
        success = db.insert_transmission_data(test_id, 1, "演示数据")
        print(f"插入传输数据: {'成功' if success else '失败'}")
        
        # 获取待传输数据
        data = db.get_next_transmission_data()
        if data:
            print(f"获取到待传输数据: {data}")
            
            # 更新状态
            success = db.update_transmission_status(data[0], data[1])
            print(f"更新状态: {'成功' if success else '失败'}")
        else:
            print("没有待传输数据")
        
        # 插入测试数据到receive_data
        test_id2 = str(int(time.time() * 1000) + 1)
        success = db.insert_receive_data(test_id2, 2, "接收演示数据")
        print(f"插入接收数据: {'成功' if success else '失败'}")
        
        db.close()
        
    except Exception as e:
        print(f"数据库演示出错: {e}")

def demo_qr_generator():
    """演示二维码生成"""
    print("\n=== 二维码生成演示 ===")
    
    try:
        db = DatabaseManager()
        qr_gen = QRGenerator(db)
        
        # 插入一些测试数据
        test_data = [
            (str(int(time.time() * 1000)), 1, "二维码测试数据1"),
            (str(int(time.time() * 1000) + 1), 2, "二维码测试数据2"),
        ]
        
        for id_val, type_val, data_val in test_data:
            db.insert_transmission_data(id_val, type_val, data_val)
        
        print("已插入测试数据，开始生成二维码...")
        
        # 手动处理一次
        qr_gen.manual_process()
        
        print("二维码生成演示完成")
        
        db.close()
        
    except Exception as e:
        print(f"二维码生成演示出错: {e}")

def demo_camera_monitor():
    """演示摄像头监控（仅测试初始化）"""
    print("\n=== 摄像头监控演示 ===")
    
    try:
        db = DatabaseManager()
        camera = CameraMonitor(db)
        
        # 测试摄像头初始化
        if camera.initialize_camera():
            print("摄像头初始化成功")
            
            # 捕获一帧测试
            frame = camera.capture_single_frame()
            if frame is not None:
                print(f"成功捕获图像，尺寸: {frame.shape}")
            else:
                print("捕获图像失败")
            
            camera.stop_monitoring()
        else:
            print("摄像头初始化失败")
        
        db.close()
        
    except Exception as e:
        print(f"摄像头监控演示出错: {e}")

def demo_json_processing():
    """演示JSON数据处理"""
    print("\n=== JSON数据处理演示 ===")
    
    # 创建测试JSON数据
    test_json = {
        "id": str(int(time.time() * 1000)),
        "type": 1,
        "data": "JSON处理测试数据"
    }
    
    # 转换为字符串
    json_string = json.dumps(test_json, ensure_ascii=False)
    print(f"JSON字符串: {json_string}")
    
    # 解析回字典
    parsed_data = json.loads(json_string)
    print(f"解析后的数据: {parsed_data}")
    
    # 验证字段
    required_fields = ['id', 'type', 'data']
    missing_fields = [field for field in required_fields if field not in parsed_data]
    
    if not missing_fields:
        print("JSON数据格式验证通过")
    else:
        print(f"JSON数据缺少字段: {missing_fields}")

def main():
    """主演示函数"""
    print("=" * 50)
    print("数据传输客户端功能演示")
    print("=" * 50)
    
    try:
        # JSON处理演示
        demo_json_processing()
        
        # 数据库操作演示
        demo_database()
        
        # 二维码生成演示
        demo_qr_generator()
        
        # 摄像头监控演示
        demo_camera_monitor()
        
    except KeyboardInterrupt:
        print("\n演示被用户中断")
    except Exception as e:
        print(f"\n演示过程中出错: {e}")
    
    print("\n" + "=" * 50)
    print("演示完成")
    print("=" * 50)

if __name__ == "__main__":
    main()
