#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
快速MySQL连接测试
专门解决连接静默退出问题
"""

import sys
import time
import socket

def test_step_by_step():
    """逐步测试，每步都有输出"""
    
    print("=" * 50)
    print("快速MySQL连接诊断")
    print("=" * 50)
    
    # 步骤1: 检查配置
    print("\n[步骤1] 检查配置文件...")
    try:
        import config
        db_config = config.DATABASE_CONFIG
        print(f"✓ 配置加载成功")
        print(f"  主机: {db_config['host']}")
        print(f"  端口: {db_config['port']}")
        print(f"  用户: {db_config['user']}")
        print(f"  密码: {'已设置' if db_config.get('password') else '未设置'}")
    except Exception as e:
        print(f"✗ 配置加载失败: {e}")
        return False
    
    # 步骤2: 检查MySQL模块
    print("\n[步骤2] 检查MySQL模块...")
    try:
        import mysql.connector
        print(f"✓ mysql.connector 导入成功")
        print(f"  版本: {mysql.connector.__version__}")
    except Exception as e:
        print(f"✗ mysql.connector 导入失败: {e}")
        return False
    
    # 步骤3: 测试TCP连接
    print(f"\n[步骤3] 测试TCP连接到 {db_config['host']}:{db_config['port']}...")
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(5)  # 5秒超时
        
        print("  正在连接...")
        start_time = time.time()
        result = sock.connect_ex((db_config['host'], db_config['port']))
        end_time = time.time()
        
        sock.close()
        
        if result == 0:
            print(f"✓ TCP连接成功 (耗时: {end_time - start_time:.2f}秒)")
        else:
            print(f"✗ TCP连接失败 (错误代码: {result})")
            print("  可能原因: MySQL服务未启动或端口被阻止")
            return False
            
    except socket.timeout:
        print("✗ TCP连接超时 (5秒)")
        print("  可能原因: 防火墙阻止或MySQL服务异常")
        return False
    except Exception as e:
        print(f"✗ TCP连接异常: {e}")
        return False
    
    # 步骤4: 测试MySQL连接（极短超时）
    print(f"\n[步骤4] 测试MySQL连接 (3秒超时)...")
    try:
        from mysql.connector import Error
        
        # 创建连接配置，设置极短超时
        connect_config = {
            'host': db_config['host'],
            'port': db_config['port'],
            'user': db_config['user'],
            'password': db_config['password'],
            'charset': db_config.get('charset', 'utf8'),
            'connection_timeout': 3,  # 3秒超时
            'autocommit': True
        }
        
        print("  正在建立MySQL连接...")
        print("  (如果这里卡住，说明MySQL服务有问题)")
        
        # 这里是最容易卡住的地方
        connection = mysql.connector.connect(**connect_config)
        
        print("✓ MySQL连接建立成功")
        
        if connection.is_connected():
            print("✓ 连接状态验证成功")
            
            # 快速测试查询
            cursor = connection.cursor()
            cursor.execute("SELECT 1")
            result = cursor.fetchone()
            
            if result and result[0] == 1:
                print("✓ 测试查询成功")
            
            cursor.close()
            connection.close()
            print("✓ 连接已关闭")
            
            return True
        else:
            print("✗ 连接状态验证失败")
            return False
            
    except Error as e:
        print(f"✗ MySQL连接错误: {e.errno} - {e.msg}")
        
        # 提供具体建议
        if e.errno == 2003:
            print("  建议: 检查MySQL服务是否启动")
        elif e.errno == 1045:
            print("  建议: 检查用户名和密码")
        elif e.errno == 1049:
            print("  建议: 数据库不存在，但连接正常")
            return True  # 连接本身是成功的
        
        return False
        
    except Exception as e:
        print(f"✗ MySQL连接异常: {e}")
        print(f"  异常类型: {type(e).__name__}")
        return False

def main():
    """主函数"""
    print("快速MySQL连接测试工具")
    print("专门解决连接时静默退出的问题")
    print()
    
    success = test_step_by_step()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 MySQL连接测试成功！")
        print("数据库连接正常，可以启动DataTransmission程序")
    else:
        print("❌ MySQL连接测试失败")
        print("\n常见解决方案:")
        print("1. 重启MySQL服务: net stop mysql && net start mysql")
        print("2. 检查MySQL服务状态: sc query mysql")
        print("3. 检查端口监听: netstat -ano | findstr :3306")
        print("4. 运行详细诊断: diagnose_mysql_service.bat")
    
    print("=" * 50)
    input("按回车键退出...")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n用户中断")
    except Exception as e:
        print(f"\n程序异常: {e}")
        import traceback
        traceback.print_exc()
        input("按回车键退出...")
