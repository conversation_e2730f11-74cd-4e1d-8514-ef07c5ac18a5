# Translations template for Sphinx.
# Copyright (C) 2024 ORGANIZATION
# This file is distributed under the same license as the Sphinx project.
# 
# Translators:
# <PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2017-2020
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON> <<EMAIL>>, 2014
# <AUTHOR> <EMAIL>, 2018
# Tawez, 2013-2019
msgid ""
msgstr ""
"Project-Id-Version: Sphinx\n"
"Report-Msgid-Bugs-To: EMAIL@ADDRESS\n"
"POT-Creation-Date: 2024-04-14 23:27+0000\n"
"PO-Revision-Date: 2013-04-02 08:44+0000\n"
"Last-Translator: Adam <PERSON> <<EMAIL>>, 2023\n"
"Language-Team: Polish (http://app.transifex.com/sphinx-doc/sphinx-1/language/pl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.14.0\n"
"Language: pl\n"
"Plural-Forms: nplurals=4; plural=(n==1 ? 0 : (n%10>=2 && n%10<=4) && (n%100<12 || n%100>14) ? 1 : n!=1 && (n%10>=0 && n%10<=1) || (n%10>=5 && n%10<=9) || (n%100>=12 && n%100<=14) ? 2 : 3);\n"

#: sphinx/application.py:157
#, python-format
msgid "Cannot find source directory (%s)"
msgstr "Nie odnaleziono katalogu źródłowego (%s)"

#: sphinx/application.py:161
#, python-format
msgid "Output directory (%s) is not a directory"
msgstr "Katalog wyjściowy (%s) nie jest katalogiem"

#: sphinx/application.py:165
msgid "Source directory and destination directory cannot be identical"
msgstr "Katalog źródłowy i katalog docelowy nie mogą być identyczne"

#: sphinx/application.py:197
#, python-format
msgid "Running Sphinx v%s"
msgstr "Uruchamianie Sphinksa v%s"

#: sphinx/application.py:219
#, python-format
msgid ""
"This project needs at least Sphinx v%s and therefore cannot be built with "
"this version."
msgstr "Ten projekt potrzebuje Sphinksa w wersji co najmniej %s, dlatego nie może zostać zbudowany z tą wersją."

#: sphinx/application.py:235
msgid "making output directory"
msgstr "tworzenie katalogu wyjścia"

#: sphinx/application.py:240 sphinx/registry.py:450
#, python-format
msgid "while setting up extension %s:"
msgstr "podczas ustawiania rozszerzenia %s:"

#: sphinx/application.py:246
msgid ""
"'setup' as currently defined in conf.py isn't a Python callable. Please "
"modify its definition to make it a callable function. This is needed for "
"conf.py to behave as a Sphinx extension."
msgstr "'setup' podany w conf.py nie jest wywoływalny. Prosimy zmienić jego definicję tak, aby była wywoływalną funkcją. Jest to potrzebne w conf.py, aby zachowywało się jak rozszerzenie Sphinksa."

#: sphinx/application.py:277
#, python-format
msgid "loading translations [%s]... "
msgstr "ładowanie tłumaczeń [%s]..."

#: sphinx/application.py:294 sphinx/util/display.py:85
msgid "done"
msgstr "gotowe"

#: sphinx/application.py:296
msgid "not available for built-in messages"
msgstr "niedostępne dla wbudowanych wiadomości"

#: sphinx/application.py:310
msgid "loading pickled environment"
msgstr "ładowanie zapakowanego środowiska"

#: sphinx/application.py:318
#, python-format
msgid "failed: %s"
msgstr "nie powiodło się: %s"

#: sphinx/application.py:332
msgid "No builder selected, using default: html"
msgstr "Nie wybrano buildera, używamy domyślnego: html"

#: sphinx/application.py:365
msgid "succeeded"
msgstr "zakończony sukcesem"

#: sphinx/application.py:366
msgid "finished with problems"
msgstr "zakończony z problemami"

#: sphinx/application.py:370
#, python-format
msgid "build %s, %s warning (with warnings treated as errors)."
msgstr "build %s, %s ostrzeżenie (z ostrzeżeniami traktowanymi jako błędy)."

#: sphinx/application.py:372
#, python-format
msgid "build %s, %s warnings (with warnings treated as errors)."
msgstr ""

#: sphinx/application.py:375
#, python-format
msgid "build %s, %s warning."
msgstr "build %s, %s ostrzeżenie."

#: sphinx/application.py:377
#, python-format
msgid "build %s, %s warnings."
msgstr ""

#: sphinx/application.py:381
#, python-format
msgid "build %s."
msgstr "build %s."

#: sphinx/application.py:610
#, python-format
msgid "node class %r is already registered, its visitors will be overridden"
msgstr "klasa %r jest już zarejestrowana, jej wizytorzy zostaną nadpisani"

#: sphinx/application.py:689
#, python-format
msgid "directive %r is already registered, it will be overridden"
msgstr "dyrektywa %r jest już zarejestrowana, jej wizytorzy zostaną nadpisani"

#: sphinx/application.py:711 sphinx/application.py:733
#, python-format
msgid "role %r is already registered, it will be overridden"
msgstr "rola %r jest już zarejestrowana, jej wizytorzy zostaną nadpisani"

#: sphinx/application.py:1282
#, python-format
msgid ""
"the %s extension does not declare if it is safe for parallel reading, "
"assuming it isn't - please ask the extension author to check and make it "
"explicit"
msgstr "rozszerzenie %s nie deklaruje, czy jest bezpieczne do czytania współbieżnego, zakładamy że nie jest – prosimy zapytać autora rozszerzenie o sprawdzenie i zadeklarowania tego wprost"

#: sphinx/application.py:1286
#, python-format
msgid "the %s extension is not safe for parallel reading"
msgstr ""

#: sphinx/application.py:1289
#, python-format
msgid ""
"the %s extension does not declare if it is safe for parallel writing, "
"assuming it isn't - please ask the extension author to check and make it "
"explicit"
msgstr "rozszerzenie %s nie deklaruje, czy jest bezpieczne do pisania współbieżnego, zakładamy że nie jest – prosimy zapytać autora rozszerzenia o sprawdzenie i zadeklarowanie tego wprost"

#: sphinx/application.py:1293
#, python-format
msgid "the %s extension is not safe for parallel writing"
msgstr ""

#: sphinx/application.py:1301 sphinx/application.py:1305
#, python-format
msgid "doing serial %s"
msgstr "tworzenie serii %s"

#: sphinx/config.py:309
#, python-format
msgid "config directory doesn't contain a conf.py file (%s)"
msgstr "folder konfiguracyjny nie zawiera pliku conf.py (%s)"

#: sphinx/config.py:318
msgid ""
"Invalid configuration value found: 'language = None'. Update your "
"configuration to a valid language code. Falling back to 'en' (English)."
msgstr ""

#: sphinx/config.py:341
#, python-format
msgid ""
"cannot override dictionary config setting %r, ignoring (use %r to set "
"individual elements)"
msgstr "nie można nadpisać słownikowego ustawienia konfiguracji %r, ignorowanie (użyj %r, by ustawić poszczególne elementy)"

#: sphinx/config.py:350
#, python-format
msgid "invalid number %r for config value %r, ignoring"
msgstr "niepoprawna liczba %r dla wartości konfiguracji %r, ignorowanie"

#: sphinx/config.py:355
#, python-format
msgid "cannot override config setting %r with unsupported type, ignoring"
msgstr "nie można nadpisać ustawienia konfiguracji %r nie wspieranym typem, ignorowanie"

#: sphinx/config.py:378
#, python-format
msgid "unknown config value %r in override, ignoring"
msgstr "nieznana wartość konfiguracji %r w nadpisaniu, ignorowanie"

#: sphinx/config.py:418
#, python-format
msgid "No such config value: %r"
msgstr ""

#: sphinx/config.py:440
#, python-format
msgid "Config value %r already present"
msgstr "Wartość konfiguracji %r już podana"

#: sphinx/config.py:473
#, python-format
msgid "cannot cache unpickable configuration value: %r"
msgstr ""

#: sphinx/config.py:509
#, python-format
msgid "There is a syntax error in your configuration file: %s\n"
msgstr "W twoim piku konfiguracyjnym jest błąd składniowy: %s\n"

#: sphinx/config.py:512
msgid ""
"The configuration file (or one of the modules it imports) called sys.exit()"
msgstr "Plik konfiguracyjny (albo jeden z modułów przez niego zaimportowanych) wywołał sys.exit()"

#: sphinx/config.py:519
#, python-format
msgid ""
"There is a programmable error in your configuration file:\n"
"\n"
"%s"
msgstr "W twoim piku konfiguracyjnym jest błąd programowalny: \n\n%s"

#: sphinx/config.py:540
#, python-format
msgid "Failed to convert %r to a set or tuple"
msgstr ""

#: sphinx/config.py:565
#, python-format
msgid ""
"The config value `source_suffix' expects a string, list of strings, or "
"dictionary. But `%r' is given."
msgstr ""

#: sphinx/config.py:585
#, python-format
msgid "Section %s"
msgstr "Rozdział %s"

#: sphinx/config.py:586
#, python-format
msgid "Fig. %s"
msgstr "Rys. %s"

#: sphinx/config.py:587
#, python-format
msgid "Table %s"
msgstr "Tabela %s"

#: sphinx/config.py:588
#, python-format
msgid "Listing %s"
msgstr "Listing %s"

#: sphinx/config.py:663
msgid ""
"The config value `{name}` has to be a one of {candidates}, but `{current}` "
"is given."
msgstr "Wartość konfiguracyjna `{name}` musi być jednym z {candidates}, a podany jest `{current}`."

#: sphinx/config.py:687
msgid ""
"The config value `{name}' has type `{current.__name__}'; expected "
"{permitted}."
msgstr ""

#: sphinx/config.py:700
msgid ""
"The config value `{name}' has type `{current.__name__}', defaults to "
"`{default.__name__}'."
msgstr ""

#: sphinx/config.py:711
#, python-format
msgid "primary_domain %r not found, ignored."
msgstr "Nie odnaleziono primary_domain %r, zignorowano."

#: sphinx/config.py:723
msgid ""
"Since v2.0, Sphinx uses \"index\" as root_doc by default. Please add "
"\"root_doc = 'contents'\" to your conf.py."
msgstr ""

#: sphinx/events.py:64
#, python-format
msgid "Event %r already present"
msgstr "Zdarzenie %r już obecne"

#: sphinx/events.py:70
#, python-format
msgid "Unknown event name: %s"
msgstr "Nieznana nazwa zdarzenia: %s"

#: sphinx/events.py:109
#, python-format
msgid "Handler %r for event %r threw an exception"
msgstr ""

#: sphinx/extension.py:55
#, python-format
msgid ""
"The %s extension is required by needs_extensions settings, but it is not "
"loaded."
msgstr ""

#: sphinx/extension.py:76
#, python-format
msgid ""
"This project needs the extension %s at least in version %s and therefore "
"cannot be built with the loaded version (%s)."
msgstr "Ten projekt potrzebuje rozszerzenia %s co najmniej w wersji %s, dlatego nie może zostać zbudowany z załadowaną wersją (%s)."

#: sphinx/highlighting.py:155
#, python-format
msgid "Pygments lexer name %r is not known"
msgstr ""

#: sphinx/highlighting.py:189
#, python-format
msgid ""
"Lexing literal_block %r as \"%s\" resulted in an error at token: %r. "
"Retrying in relaxed mode."
msgstr ""

#: sphinx/project.py:66
#, python-format
msgid ""
"multiple files found for the document \"%s\": %r\n"
"Use %r for the build."
msgstr ""

#: sphinx/project.py:81
#, python-format
msgid "Ignored unreadable document %r."
msgstr ""

#: sphinx/registry.py:142
#, python-format
msgid "Builder class %s has no \"name\" attribute"
msgstr "Klasa buildera %s nie ma atrybutu \"name\""

#: sphinx/registry.py:144
#, python-format
msgid "Builder %r already exists (in module %s)"
msgstr "Builder %r już istnieje (w module %s)"

#: sphinx/registry.py:157
#, python-format
msgid "Builder name %s not registered or available through entry point"
msgstr "Builder o nazwie %s jest niezarejestrowany lub dostępny przez punkt wejścia"

#: sphinx/registry.py:164
#, python-format
msgid "Builder name %s not registered"
msgstr "Builder o nazwie %s jest niezarejestrowany"

#: sphinx/registry.py:171
#, python-format
msgid "domain %s already registered"
msgstr "domena %s jest już zarejestrowana"

#: sphinx/registry.py:194 sphinx/registry.py:207 sphinx/registry.py:218
#, python-format
msgid "domain %s not yet registered"
msgstr "domena %s nie została jeszcze zarejestrowana"

#: sphinx/registry.py:198
#, python-format
msgid "The %r directive is already registered to domain %s"
msgstr ""

#: sphinx/registry.py:210
#, python-format
msgid "The %r role is already registered to domain %s"
msgstr ""

#: sphinx/registry.py:221
#, python-format
msgid "The %r index is already registered to domain %s"
msgstr ""

#: sphinx/registry.py:252
#, python-format
msgid "The %r object_type is already registered"
msgstr "%r object_type jest już zarejestrowany"

#: sphinx/registry.py:278
#, python-format
msgid "The %r crossref_type is already registered"
msgstr "%r crossref_type jest już zarejestrowany"

#: sphinx/registry.py:285
#, python-format
msgid "source_suffix %r is already registered"
msgstr "source_suffix %r jest już zarejestrowany"

#: sphinx/registry.py:294
#, python-format
msgid "source_parser for %r is already registered"
msgstr "source_parser dla %r jest już zarejestrowany"

#: sphinx/registry.py:302
#, python-format
msgid "Source parser for %s not registered"
msgstr "Parser źródeł dla %s jest nie zarejestrowany"

#: sphinx/registry.py:318
#, python-format
msgid "Translator for %r already exists"
msgstr ""

#: sphinx/registry.py:334
#, python-format
msgid "kwargs for add_node() must be a (visit, depart) function tuple: %r=%r"
msgstr ""

#: sphinx/registry.py:417
#, python-format
msgid "enumerable_node %r already registered"
msgstr "enumerable_node %r już zarejestrowany"

#: sphinx/registry.py:429
#, python-format
msgid "math renderer %s is already registered"
msgstr ""

#: sphinx/registry.py:444
#, python-format
msgid ""
"the extension %r was already merged with Sphinx since version %s; this "
"extension is ignored."
msgstr "rozszerzenie %r zostało już włączone do Sphinx'a, począwszy od wersji %s; to rozszerzenie jest zignorowane."

#: sphinx/registry.py:455
msgid "Original exception:\n"
msgstr "Pierwotny wyjątek:\n"

#: sphinx/registry.py:456
#, python-format
msgid "Could not import extension %s"
msgstr "Nie można zaimportować rozszerzenia %s"

#: sphinx/registry.py:461
#, python-format
msgid ""
"extension %r has no setup() function; is it really a Sphinx extension "
"module?"
msgstr "rozszerzenie %r nie zawiera funkcji setup(); czy to na pewno moduł rozszerzenia Sphinx?"

#: sphinx/registry.py:470
#, python-format
msgid ""
"The %s extension used by this project needs at least Sphinx v%s; it "
"therefore cannot be built with this version."
msgstr "Rozszerzenie %s używane przez ten projekt potrzebuje Sphinksa w wersji co najmniej %s; dlatego nie może zostać zbudowane z tą wersją."

#: sphinx/registry.py:478
#, python-format
msgid ""
"extension %r returned an unsupported object from its setup() function; it "
"should return None or a metadata dictionary"
msgstr "rozszerzenie %r zwróciło nie wspierany obiekt ze swojej funkcji setup(); powinno zwrócić None lub słownik metadanych"

#: sphinx/roles.py:201
#, python-format
msgid "Python Enhancement Proposals; PEP %s"
msgstr "Python Enhancement Proposals; PEP %s"

#: sphinx/roles.py:222
#, python-format
msgid "invalid PEP number %s"
msgstr ""

#: sphinx/roles.py:257
#, python-format
msgid "invalid RFC number %s"
msgstr ""

#: sphinx/theming.py:125
#, python-format
msgid "setting %s.%s occurs in none of the searched theme configs"
msgstr "ustawienie %s.%s nie występuje w żadnej z przeszukiwanych konfiguracji motywów"

#: sphinx/theming.py:140
#, python-format
msgid "unsupported theme option %r given"
msgstr ""

#: sphinx/theming.py:206
#, python-format
msgid "file %r on theme path is not a valid zipfile or contains no theme"
msgstr "plik %r na ścieżce motywu nie jest poprawnym plikiem zip lub nie zawiera motywu"

#: sphinx/theming.py:226
#, python-format
msgid "no theme named %r found (missing theme.toml?)"
msgstr ""

#: sphinx/theming.py:259
#, python-format
msgid "The %r theme has circular inheritance"
msgstr ""

#: sphinx/theming.py:262
#, python-format
msgid ""
"The %r theme inherits from %r, which is not a loaded theme. Loaded themes "
"are: %s"
msgstr ""

#: sphinx/theming.py:269
#, python-format
msgid "The %r theme has too many ancestors"
msgstr ""

#: sphinx/theming.py:295
#, python-format
msgid "no theme configuration file found in %r"
msgstr ""

#: sphinx/theming.py:323 sphinx/theming.py:374
#, python-format
msgid "theme %r doesn't have the \"theme\" table"
msgstr ""

#: sphinx/theming.py:327
#, python-format
msgid "The %r theme \"[theme]\" table is not a table"
msgstr ""

#: sphinx/theming.py:331 sphinx/theming.py:377
#, python-format
msgid "The %r theme must define the \"theme.inherit\" setting"
msgstr ""

#: sphinx/theming.py:335
#, python-format
msgid "The %r theme \"[options]\" table is not a table"
msgstr ""

#: sphinx/theming.py:353
#, python-format
msgid "The \"theme.pygments_style\" setting must be a table. Hint: \"%s\""
msgstr ""

#: sphinx/builders/__init__.py:183
#, python-format
msgid "a suitable image for %s builder not found: %s (%s)"
msgstr ""

#: sphinx/builders/__init__.py:187
#, python-format
msgid "a suitable image for %s builder not found: %s"
msgstr ""

#: sphinx/builders/__init__.py:207
msgid "building [mo]: "
msgstr "budowanie [mo]:"

#: sphinx/builders/__init__.py:208 sphinx/builders/__init__.py:574
#: sphinx/builders/__init__.py:601
msgid "writing output... "
msgstr "pisanie wyjścia..."

#: sphinx/builders/__init__.py:217
#, python-format
msgid "all of %d po files"
msgstr "wszystkie z %d plików po"

#: sphinx/builders/__init__.py:235
#, python-format
msgid "targets for %d po files that are specified"
msgstr ""

#: sphinx/builders/__init__.py:243
#, python-format
msgid "targets for %d po files that are out of date"
msgstr ""

#: sphinx/builders/__init__.py:252
msgid "all source files"
msgstr "wsztstkie pliki źródłowe"

#: sphinx/builders/__init__.py:262
#, python-format
msgid "file %r given on command line does not exist, "
msgstr ""

#: sphinx/builders/__init__.py:267
#, python-format
msgid ""
"file %r given on command line is not under the source directory, ignoring"
msgstr "plik %r podany w wierszu poleceń nie znajduje się w katalogu źródłowym, ignoruję"

#: sphinx/builders/__init__.py:273
#, python-format
msgid "file %r given on command line is not a valid document, ignoring"
msgstr ""

#: sphinx/builders/__init__.py:282
#, python-format
msgid "%d source files given on command line"
msgstr "%d plików źródłowych podano w wierszu poleceń"

#: sphinx/builders/__init__.py:294
#, python-format
msgid "targets for %d source files that are out of date"
msgstr ""

#: sphinx/builders/__init__.py:309 sphinx/builders/gettext.py:243
#, python-format
msgid "building [%s]: "
msgstr ""

#: sphinx/builders/__init__.py:316
msgid "looking for now-outdated files... "
msgstr ""

#: sphinx/builders/__init__.py:320
#, python-format
msgid "%d found"
msgstr "znaleziono %d"

#: sphinx/builders/__init__.py:322
msgid "none found"
msgstr "nic nie znaleziono"

#: sphinx/builders/__init__.py:327
msgid "pickling environment"
msgstr ""

#: sphinx/builders/__init__.py:333
msgid "checking consistency"
msgstr ""

#: sphinx/builders/__init__.py:337
msgid "no targets are out of date."
msgstr ""

#: sphinx/builders/__init__.py:376
msgid "updating environment: "
msgstr ""

#: sphinx/builders/__init__.py:397
#, python-format
msgid "%s added, %s changed, %s removed"
msgstr ""

#: sphinx/builders/__init__.py:435 sphinx/builders/__init__.py:447
msgid "reading sources... "
msgstr ""

#: sphinx/builders/__init__.py:549
#, python-format
msgid "docnames to write: %s"
msgstr ""

#: sphinx/builders/__init__.py:558 sphinx/builders/singlehtml.py:157
msgid "preparing documents"
msgstr ""

#: sphinx/builders/__init__.py:561
msgid "copying assets"
msgstr ""

#: sphinx/builders/_epub_base.py:215
#, python-format
msgid "duplicated ToC entry found: %s"
msgstr ""

#: sphinx/builders/_epub_base.py:404 sphinx/builders/html/__init__.py:758
#: sphinx/builders/latex/__init__.py:432 sphinx/builders/texinfo.py:187
msgid "copying images... "
msgstr "kopiowanie obrazków..."

#: sphinx/builders/_epub_base.py:411
#, python-format
msgid "cannot read image file %r: copying it instead"
msgstr ""

#: sphinx/builders/_epub_base.py:417 sphinx/builders/html/__init__.py:766
#: sphinx/builders/latex/__init__.py:440 sphinx/builders/texinfo.py:197
#, python-format
msgid "cannot copy image file %r: %s"
msgstr ""

#: sphinx/builders/_epub_base.py:434
#, python-format
msgid "cannot write image file %r: %s"
msgstr ""

#: sphinx/builders/_epub_base.py:444
msgid "Pillow not found - copying image files"
msgstr ""

#: sphinx/builders/_epub_base.py:470
msgid "writing mimetype file..."
msgstr ""

#: sphinx/builders/_epub_base.py:475
msgid "writing META-INF/container.xml file..."
msgstr ""

#: sphinx/builders/_epub_base.py:508
msgid "writing content.opf file..."
msgstr ""

#: sphinx/builders/_epub_base.py:539
#, python-format
msgid "unknown mimetype for %s, ignoring"
msgstr "nieznany mimetype dla %s, ignoruję"

#: sphinx/builders/_epub_base.py:686
msgid "writing toc.ncx file..."
msgstr ""

#: sphinx/builders/_epub_base.py:711
#, python-format
msgid "writing %s file..."
msgstr "pisanie pliku %s..."

#: sphinx/builders/changes.py:32
#, python-format
msgid "The overview file is in %(outdir)s."
msgstr ""

#: sphinx/builders/changes.py:59
#, python-format
msgid "no changes in version %s."
msgstr "brak zmian w wersji %s."

#: sphinx/builders/changes.py:61
msgid "writing summary file..."
msgstr ""

#: sphinx/builders/changes.py:76
msgid "Builtins"
msgstr "Wbudowane"

#: sphinx/builders/changes.py:78
msgid "Module level"
msgstr "Poziom modułu"

#: sphinx/builders/changes.py:123
msgid "copying source files..."
msgstr "kopiowanie plików źródłowych..."

#: sphinx/builders/changes.py:130
#, python-format
msgid "could not read %r for changelog creation"
msgstr ""

#: sphinx/builders/dummy.py:19
msgid "The dummy builder generates no files."
msgstr ""

#: sphinx/builders/epub3.py:81
#, python-format
msgid "The ePub file is in %(outdir)s."
msgstr "Plik ePub znajduje się w %(outdir)s."

#: sphinx/builders/epub3.py:185
msgid "writing nav.xhtml file..."
msgstr ""

#: sphinx/builders/epub3.py:211
msgid "conf value \"epub_language\" (or \"language\") should not be empty for EPUB3"
msgstr ""

#: sphinx/builders/epub3.py:215
msgid "conf value \"epub_uid\" should be XML NAME for EPUB3"
msgstr ""

#: sphinx/builders/epub3.py:218
msgid "conf value \"epub_title\" (or \"html_title\") should not be empty for EPUB3"
msgstr ""

#: sphinx/builders/epub3.py:222
msgid "conf value \"epub_author\" should not be empty for EPUB3"
msgstr ""

#: sphinx/builders/epub3.py:225
msgid "conf value \"epub_contributor\" should not be empty for EPUB3"
msgstr ""

#: sphinx/builders/epub3.py:228
msgid "conf value \"epub_description\" should not be empty for EPUB3"
msgstr ""

#: sphinx/builders/epub3.py:231
msgid "conf value \"epub_publisher\" should not be empty for EPUB3"
msgstr ""

#: sphinx/builders/epub3.py:234
msgid "conf value \"epub_copyright\" (or \"copyright\")should not be empty for EPUB3"
msgstr ""

#: sphinx/builders/epub3.py:238
msgid "conf value \"epub_identifier\" should not be empty for EPUB3"
msgstr ""

#: sphinx/builders/epub3.py:241
msgid "conf value \"version\" should not be empty for EPUB3"
msgstr ""

#: sphinx/builders/epub3.py:255 sphinx/builders/html/__init__.py:1187
#, python-format
msgid "invalid css_file: %r, ignored"
msgstr "nieprawidłowy css_file: %r, zignorowano"

#: sphinx/builders/gettext.py:222
#, python-format
msgid "The message catalogs are in %(outdir)s."
msgstr ""

#: sphinx/builders/gettext.py:244
#, python-format
msgid "targets for %d template files"
msgstr ""

#: sphinx/builders/gettext.py:248
msgid "reading templates... "
msgstr "wczytywanie szablonów... "

#: sphinx/builders/gettext.py:282
msgid "writing message catalogs... "
msgstr ""

#: sphinx/builders/linkcheck.py:59
#, python-format
msgid "Look for any errors in the above output or in %(outdir)s/output.txt"
msgstr ""

#: sphinx/builders/linkcheck.py:137
#, python-format
msgid "broken link: %s (%s)"
msgstr "zepsuty odnośnik: %s (%s)"

#: sphinx/builders/linkcheck.py:660
#, python-format
msgid "Failed to compile regex in linkcheck_allowed_redirects: %r %s"
msgstr ""

#: sphinx/builders/manpage.py:37
#, python-format
msgid "The manual pages are in %(outdir)s."
msgstr ""

#: sphinx/builders/manpage.py:44
msgid "no \"man_pages\" config value found; no manual pages will be written"
msgstr ""

#: sphinx/builders/latex/__init__.py:314 sphinx/builders/manpage.py:53
#: sphinx/builders/singlehtml.py:165 sphinx/builders/texinfo.py:112
msgid "writing"
msgstr ""

#: sphinx/builders/manpage.py:68
#, python-format
msgid "\"man_pages\" config value references unknown document %s"
msgstr ""

#: sphinx/builders/singlehtml.py:34
#, python-format
msgid "The HTML page is in %(outdir)s."
msgstr "Strona HTML jest w %(outdir)s."

#: sphinx/builders/singlehtml.py:160
msgid "assembling single document"
msgstr ""

#: sphinx/builders/singlehtml.py:178
msgid "writing additional files"
msgstr ""

#: sphinx/builders/texinfo.py:48
#, python-format
msgid "The Texinfo files are in %(outdir)s."
msgstr "Pliki Texinfo znajdują się w %(outdir)s."

#: sphinx/builders/texinfo.py:50
msgid ""
"\n"
"Run 'make' in that directory to run these through makeinfo\n"
"(use 'make info' here to do that automatically)."
msgstr ""

#: sphinx/builders/texinfo.py:77
msgid "no \"texinfo_documents\" config value found; no documents will be written"
msgstr "nie znaleziono wartości konfiguracyjnej \"texinfo_documents\"; żadne dokumenty nie zostaną zapisane"

#: sphinx/builders/texinfo.py:85
#, python-format
msgid "\"texinfo_documents\" config value references unknown document %s"
msgstr "wartość konfiguracyjna \"texinfo_documents\" odwołuje się do nieznanego dokumentu %s"

#: sphinx/builders/latex/__init__.py:296 sphinx/builders/texinfo.py:108
#, python-format
msgid "processing %s"
msgstr ""

#: sphinx/builders/latex/__init__.py:369 sphinx/builders/texinfo.py:161
msgid "resolving references..."
msgstr ""

#: sphinx/builders/latex/__init__.py:380 sphinx/builders/texinfo.py:171
msgid " (in "
msgstr " (w "

#: sphinx/builders/texinfo.py:202
msgid "copying Texinfo support files"
msgstr ""

#: sphinx/builders/texinfo.py:206
#, python-format
msgid "error writing file Makefile: %s"
msgstr ""

#: sphinx/builders/text.py:30
#, python-format
msgid "The text files are in %(outdir)s."
msgstr "Pliki tekstowe są w %(outdir)s."

#: sphinx/builders/html/__init__.py:1138 sphinx/builders/text.py:77
#: sphinx/builders/xml.py:96
#, python-format
msgid "error writing file %s: %s"
msgstr "błąd zapisu pliku %s: %s"

#: sphinx/builders/xml.py:36
#, python-format
msgid "The XML files are in %(outdir)s."
msgstr "Pliki XML znajdują się w %(outdir)s."

#: sphinx/builders/xml.py:109
#, python-format
msgid "The pseudo-XML files are in %(outdir)s."
msgstr "Pliki pseudo-XML są w %(outdir)s."

#: sphinx/builders/html/__init__.py:130
#, python-format
msgid "build info file is broken: %r"
msgstr ""

#: sphinx/builders/html/__init__.py:168
#, python-format
msgid "The HTML pages are in %(outdir)s."
msgstr "Strony HTML są w %(outdir)s."

#: sphinx/builders/html/__init__.py:394
#, python-format
msgid "Failed to read build info file: %r"
msgstr ""

#: sphinx/builders/html/__init__.py:487 sphinx/builders/latex/__init__.py:189
#: sphinx/transforms/__init__.py:119 sphinx/writers/manpage.py:101
#: sphinx/writers/texinfo.py:227
#, python-format
msgid "%b %d, %Y"
msgstr "%d %b %Y"

#: sphinx/builders/html/__init__.py:506 sphinx/themes/basic/defindex.html:30
msgid "General Index"
msgstr "Indeks ogólny"

#: sphinx/builders/html/__init__.py:506
msgid "index"
msgstr "indeks"

#: sphinx/builders/html/__init__.py:579
msgid "next"
msgstr "dalej"

#: sphinx/builders/html/__init__.py:588
msgid "previous"
msgstr "wstecz"

#: sphinx/builders/html/__init__.py:684
msgid "generating indices"
msgstr ""

#: sphinx/builders/html/__init__.py:699
msgid "writing additional pages"
msgstr ""

#: sphinx/builders/html/__init__.py:776
msgid "copying downloadable files... "
msgstr "kopiowanie plików do pobrania..."

#: sphinx/builders/html/__init__.py:784
#, python-format
msgid "cannot copy downloadable file %r: %s"
msgstr ""

#: sphinx/builders/html/__init__.py:817 sphinx/builders/html/__init__.py:829
#, python-format
msgid "Failed to copy a file in html_static_file: %s: %r"
msgstr ""

#: sphinx/builders/html/__init__.py:850
msgid "copying static files"
msgstr ""

#: sphinx/builders/html/__init__.py:866
#, python-format
msgid "cannot copy static file %r"
msgstr "nie można skopiować pliku statycznego %r"

#: sphinx/builders/html/__init__.py:871
msgid "copying extra files"
msgstr ""

#: sphinx/builders/html/__init__.py:877
#, python-format
msgid "cannot copy extra file %r"
msgstr "nie można skopiować dodatkowego pliku %r"

#: sphinx/builders/html/__init__.py:884
#, python-format
msgid "Failed to write build info file: %r"
msgstr ""

#: sphinx/builders/html/__init__.py:933
msgid ""
"search index couldn't be loaded, but not all documents will be built: the "
"index will be incomplete."
msgstr ""

#: sphinx/builders/html/__init__.py:978
#, python-format
msgid "page %s matches two patterns in html_sidebars: %r and %r"
msgstr ""

#: sphinx/builders/html/__init__.py:1121
#, python-format
msgid ""
"a Unicode error occurred when rendering the page %s. Please make sure all "
"config values that contain non-ASCII content are Unicode strings."
msgstr ""

#: sphinx/builders/html/__init__.py:1126
#, python-format
msgid ""
"An error happened in rendering the page %s.\n"
"Reason: %r"
msgstr "Wystąpił błąd podczas renderowania strony %s.\nPowód: %r"

#: sphinx/builders/html/__init__.py:1154
msgid "dumping object inventory"
msgstr ""

#: sphinx/builders/html/__init__.py:1162
#, python-format
msgid "dumping search index in %s"
msgstr ""

#: sphinx/builders/html/__init__.py:1210
#, python-format
msgid "invalid js_file: %r, ignored"
msgstr "nieprawidłowy js_file: %r, zignorowano"

#: sphinx/builders/html/__init__.py:1238
msgid "Many math_renderers are registered. But no math_renderer is selected."
msgstr ""

#: sphinx/builders/html/__init__.py:1241
#, python-format
msgid "Unknown math_renderer %r is given."
msgstr "Podano nieznany math_renderer %r."

#: sphinx/builders/html/__init__.py:1249
#, python-format
msgid "html_extra_path entry %r does not exist"
msgstr ""

#: sphinx/builders/html/__init__.py:1253
#, python-format
msgid "html_extra_path entry %r is placed inside outdir"
msgstr ""

#: sphinx/builders/html/__init__.py:1262
#, python-format
msgid "html_static_path entry %r does not exist"
msgstr ""

#: sphinx/builders/html/__init__.py:1266
#, python-format
msgid "html_static_path entry %r is placed inside outdir"
msgstr ""

#: sphinx/builders/html/__init__.py:1275 sphinx/builders/latex/__init__.py:444
#, python-format
msgid "logo file %r does not exist"
msgstr ""

#: sphinx/builders/html/__init__.py:1284
#, python-format
msgid "favicon file %r does not exist"
msgstr "plik favicon %r nie istnieje"

#: sphinx/builders/html/__init__.py:1291
msgid ""
"HTML 4 is no longer supported by Sphinx. (\"html4_writer=True\" detected in "
"configuration options)"
msgstr ""

#: sphinx/builders/html/__init__.py:1306
#, python-format
msgid "%s %s documentation"
msgstr "%s %s - dokumentacja"

#: sphinx/builders/latex/__init__.py:115
#, python-format
msgid "The LaTeX files are in %(outdir)s."
msgstr "Pliki LaTeX znajdują się w %(outdir)s."

#: sphinx/builders/latex/__init__.py:117
msgid ""
"\n"
"Run 'make' in that directory to run these through (pdf)latex\n"
"(use `make latexpdf' here to do that automatically)."
msgstr ""

#: sphinx/builders/latex/__init__.py:152
msgid "no \"latex_documents\" config value found; no documents will be written"
msgstr "nie znaleziono wartości konfiguracyjnej \"latex_documents\"; żadne dokumenty nie zostaną zapisane"

#: sphinx/builders/latex/__init__.py:160
#, python-format
msgid "\"latex_documents\" config value references unknown document %s"
msgstr "wartość konfiguracyjna \"latex_documents\" odwołuje się do nieznanego dokumentu %s"

#: sphinx/builders/latex/__init__.py:196 sphinx/domains/std/__init__.py:559
#: sphinx/domains/std/__init__.py:571 sphinx/templates/latex/latex.tex_t:106
#: sphinx/themes/basic/genindex-single.html:30
#: sphinx/themes/basic/genindex-single.html:55
#: sphinx/themes/basic/genindex-split.html:11
#: sphinx/themes/basic/genindex-split.html:14
#: sphinx/themes/basic/genindex.html:11 sphinx/themes/basic/genindex.html:34
#: sphinx/themes/basic/genindex.html:67 sphinx/themes/basic/layout.html:138
#: sphinx/writers/texinfo.py:497
msgid "Index"
msgstr "Indeks"

#: sphinx/builders/latex/__init__.py:199 sphinx/templates/latex/latex.tex_t:91
msgid "Release"
msgstr "Wydanie"

#: sphinx/builders/latex/__init__.py:213 sphinx/writers/latex.py:370
#, python-format
msgid "no Babel option known for language %r"
msgstr ""

#: sphinx/builders/latex/__init__.py:394
msgid "copying TeX support files"
msgstr ""

#: sphinx/builders/latex/__init__.py:410
msgid "copying TeX support files..."
msgstr ""

#: sphinx/builders/latex/__init__.py:423
msgid "copying additional files"
msgstr ""

#: sphinx/builders/latex/__init__.py:466
#, python-format
msgid "Unknown configure key: latex_elements[%r], ignored."
msgstr ""

#: sphinx/builders/latex/__init__.py:474
#, python-format
msgid "Unknown theme option: latex_theme_options[%r], ignored."
msgstr ""

#: sphinx/builders/latex/theming.py:87
#, python-format
msgid "%r doesn't have \"theme\" setting"
msgstr ""

#: sphinx/builders/latex/theming.py:90
#, python-format
msgid "%r doesn't have \"%s\" setting"
msgstr ""

#: sphinx/builders/latex/transforms.py:120
msgid "Failed to get a docname!"
msgstr ""

#: sphinx/builders/latex/transforms.py:121
msgid "Failed to get a docname for source {source!r}!"
msgstr ""

#: sphinx/builders/latex/transforms.py:482
#, python-format
msgid "No footnote was found for given reference node %r"
msgstr ""

#: sphinx/cmd/build.py:46
msgid "Exception occurred while building, starting debugger:"
msgstr ""

#: sphinx/cmd/build.py:61
msgid "Interrupted!"
msgstr ""

#: sphinx/cmd/build.py:63
msgid "reST markup error:"
msgstr ""

#: sphinx/cmd/build.py:69
msgid "Encoding error:"
msgstr "Błąd kodowania:"

#: sphinx/cmd/build.py:72 sphinx/cmd/build.py:87
#, python-format
msgid ""
"The full traceback has been saved in %s, if you want to report the issue to "
"the developers."
msgstr ""

#: sphinx/cmd/build.py:76
msgid "Recursion error:"
msgstr "Błąd rekursji:"

#: sphinx/cmd/build.py:79
msgid ""
"This can happen with very large or deeply nested source files. You can "
"carefully increase the default Python recursion limit of 1000 in conf.py "
"with e.g.:"
msgstr ""

#: sphinx/cmd/build.py:84
msgid "Exception occurred:"
msgstr "Wystąpił wyjątek:"

#: sphinx/cmd/build.py:90
msgid ""
"Please also report this if it was a user error, so that a better error "
"message can be provided next time."
msgstr ""

#: sphinx/cmd/build.py:93
msgid ""
"A bug report can be filed in the tracker at <https://github.com/sphinx-"
"doc/sphinx/issues>. Thanks!"
msgstr "Raport o błędzie można zgłosić pod adresem <https://github.com/sphinx-doc/sphinx/issues>. Dzięki!"

#: sphinx/cmd/build.py:109
msgid "job number should be a positive number"
msgstr ""

#: sphinx/cmd/build.py:117 sphinx/cmd/quickstart.py:474
#: sphinx/ext/apidoc.py:317 sphinx/ext/autosummary/generate.py:689
msgid "For more information, visit <https://www.sphinx-doc.org/>."
msgstr ""

#: sphinx/cmd/build.py:118
msgid ""
"\n"
"Generate documentation from source files.\n"
"\n"
"sphinx-build generates documentation from the files in SOURCEDIR and places it\n"
"in OUTPUTDIR. It looks for 'conf.py' in SOURCEDIR for the configuration\n"
"settings. The 'sphinx-quickstart' tool may be used to generate template files,\n"
"including 'conf.py'\n"
"\n"
"sphinx-build can create documentation in different formats. A format is\n"
"selected by specifying the builder name on the command line; it defaults to\n"
"HTML. Builders can also perform other tasks related to documentation\n"
"processing.\n"
"\n"
"By default, everything that is outdated is built. Output only for selected\n"
"files can be built by specifying individual filenames.\n"
msgstr ""

#: sphinx/cmd/build.py:139
msgid "path to documentation source files"
msgstr ""

#: sphinx/cmd/build.py:141
msgid "path to output directory"
msgstr ""

#: sphinx/cmd/build.py:143
msgid ""
"(optional) a list of specific files to rebuild. Ignored if --write-all is "
"specified"
msgstr ""

#: sphinx/cmd/build.py:146
msgid "general options"
msgstr "ogólne opcje"

#: sphinx/cmd/build.py:149
msgid "builder to use (default: 'html')"
msgstr ""

#: sphinx/cmd/build.py:152
msgid ""
"run in parallel with N processes, when possible. 'auto' uses the number of "
"CPU cores"
msgstr ""

#: sphinx/cmd/build.py:155
msgid "write all files (default: only write new and changed files)"
msgstr "zapisz wszystkie pliki (domyślnie: zapisz tylko nowe i zmienione pliki)"

#: sphinx/cmd/build.py:158
msgid "don't use a saved environment, always read all files"
msgstr ""

#: sphinx/cmd/build.py:161
msgid "path options"
msgstr ""

#: sphinx/cmd/build.py:163
msgid ""
"directory for doctree and environment files (default: OUTPUT_DIR/.doctrees)"
msgstr ""

#: sphinx/cmd/build.py:166
msgid "directory for the configuration file (conf.py) (default: SOURCE_DIR)"
msgstr ""

#: sphinx/cmd/build.py:171
msgid "use no configuration file, only use settings from -D options"
msgstr ""

#: sphinx/cmd/build.py:174
msgid "override a setting in configuration file"
msgstr "zastąp ustawienie w pliku konfiguracyjnym"

#: sphinx/cmd/build.py:177
msgid "pass a value into HTML templates"
msgstr "przekaż wartość do szablonów HTML"

#: sphinx/cmd/build.py:180
msgid "define tag: include \"only\" blocks with TAG"
msgstr ""

#: sphinx/cmd/build.py:182
msgid "nit-picky mode: warn about all missing references"
msgstr ""

#: sphinx/cmd/build.py:184
msgid "console output options"
msgstr ""

#: sphinx/cmd/build.py:187
msgid "increase verbosity (can be repeated)"
msgstr "zwiększ szczegółowość (może być powtórzone)"

#: sphinx/cmd/build.py:189 sphinx/ext/apidoc.py:340
msgid "no output on stdout, just warnings on stderr"
msgstr ""

#: sphinx/cmd/build.py:191
msgid "no output at all, not even warnings"
msgstr ""

#: sphinx/cmd/build.py:194
msgid "do emit colored output (default: auto-detect)"
msgstr ""

#: sphinx/cmd/build.py:197
msgid "do not emit colored output (default: auto-detect)"
msgstr ""

#: sphinx/cmd/build.py:199
msgid "warning control options"
msgstr ""

#: sphinx/cmd/build.py:201
msgid "write warnings (and errors) to given file"
msgstr "zapisz ostrzeżenia (i błędy) do podanego pliku"

#: sphinx/cmd/build.py:203
msgid "turn warnings into errors"
msgstr "zamień ostrzeżenia na błędy"

#: sphinx/cmd/build.py:205
msgid "with --fail-on-warning, keep going when getting warnings"
msgstr ""

#: sphinx/cmd/build.py:207
msgid "show full traceback on exception"
msgstr ""

#: sphinx/cmd/build.py:209
msgid "run Pdb on exception"
msgstr ""

#: sphinx/cmd/build.py:244
msgid "cannot combine -a option and filenames"
msgstr ""

#: sphinx/cmd/build.py:276
#, python-format
msgid "cannot open warning file %r: %s"
msgstr ""

#: sphinx/cmd/build.py:296
msgid "-D option argument must be in the form name=value"
msgstr "Argument opcji -D musi mieć postać nazwa=wartość"

#: sphinx/cmd/build.py:303
msgid "-A option argument must be in the form name=value"
msgstr "Argument opcji -A musi mieć postać nazwa=wartość"

#: sphinx/cmd/quickstart.py:42
msgid "automatically insert docstrings from modules"
msgstr ""

#: sphinx/cmd/quickstart.py:43
msgid "automatically test code snippets in doctest blocks"
msgstr ""

#: sphinx/cmd/quickstart.py:44
msgid "link between Sphinx documentation of different projects"
msgstr ""

#: sphinx/cmd/quickstart.py:45
msgid "write \"todo\" entries that can be shown or hidden on build"
msgstr ""

#: sphinx/cmd/quickstart.py:46
msgid "checks for documentation coverage"
msgstr ""

#: sphinx/cmd/quickstart.py:47
msgid "include math, rendered as PNG or SVG images"
msgstr ""

#: sphinx/cmd/quickstart.py:48
msgid "include math, rendered in the browser by MathJax"
msgstr ""

#: sphinx/cmd/quickstart.py:49
msgid "conditional inclusion of content based on config values"
msgstr "warunkowe włączenie treści na podstawie wartości konfiguracyjnych"

#: sphinx/cmd/quickstart.py:50
msgid "include links to the source code of documented Python objects"
msgstr ""

#: sphinx/cmd/quickstart.py:51
msgid "create .nojekyll file to publish the document on GitHub pages"
msgstr ""

#: sphinx/cmd/quickstart.py:93
msgid "Please enter a valid path name."
msgstr "Wprowadź poprawną nazwę ścieżki."

#: sphinx/cmd/quickstart.py:109
msgid "Please enter some text."
msgstr ""

#: sphinx/cmd/quickstart.py:116
#, python-format
msgid "Please enter one of %s."
msgstr ""

#: sphinx/cmd/quickstart.py:123
msgid "Please enter either 'y' or 'n'."
msgstr "Wprowadź \"y\" lub \"n\"."

#: sphinx/cmd/quickstart.py:129
msgid "Please enter a file suffix, e.g. '.rst' or '.txt'."
msgstr "Podaj rozszerzenie pliku, na przykład '.rst' lub '.txt'."

#: sphinx/cmd/quickstart.py:208
#, python-format
msgid "Welcome to the Sphinx %s quickstart utility."
msgstr "Witamy w narzędziu quickstart Sphinksa %s."

#: sphinx/cmd/quickstart.py:210
msgid ""
"Please enter values for the following settings (just press Enter to\n"
"accept a default value, if one is given in brackets)."
msgstr ""

#: sphinx/cmd/quickstart.py:215
#, python-format
msgid "Selected root path: %s"
msgstr ""

#: sphinx/cmd/quickstart.py:218
msgid "Enter the root path for documentation."
msgstr ""

#: sphinx/cmd/quickstart.py:219
msgid "Root path for the documentation"
msgstr "Ścieżka root dla dokumentacji"

#: sphinx/cmd/quickstart.py:224
msgid "Error: an existing conf.py has been found in the selected root path."
msgstr "Błąd: znaleziono istniejący conf.py na wskazanej ścieżce root."

#: sphinx/cmd/quickstart.py:226
msgid "sphinx-quickstart will not overwrite existing Sphinx projects."
msgstr "sphinx-quickstart nie nadpisze istniejących projektów Sphinx."

#: sphinx/cmd/quickstart.py:228
msgid "Please enter a new root path (or just Enter to exit)"
msgstr "Wprowadź, prosimy, nową ścieżkę root (lub tylko Enter, aby wyjść)"

#: sphinx/cmd/quickstart.py:235
msgid ""
"You have two options for placing the build directory for Sphinx output.\n"
"Either, you use a directory \"_build\" within the root path, or you separate\n"
"\"source\" and \"build\" directories within the root path."
msgstr ""

#: sphinx/cmd/quickstart.py:238
msgid "Separate source and build directories (y/n)"
msgstr "Rozdziel katalogi source i build (y/n)"

#: sphinx/cmd/quickstart.py:242
msgid ""
"Inside the root directory, two more directories will be created; \"_templates\"\n"
"for custom HTML templates and \"_static\" for custom stylesheets and other static\n"
"files. You can enter another prefix (such as \".\") to replace the underscore."
msgstr ""

#: sphinx/cmd/quickstart.py:245
msgid "Name prefix for templates and static dir"
msgstr "Prefiks nazw dla katalogów templates i static"

#: sphinx/cmd/quickstart.py:249
msgid ""
"The project name will occur in several places in the built documentation."
msgstr ""

#: sphinx/cmd/quickstart.py:250
msgid "Project name"
msgstr "Nazwa projektu"

#: sphinx/cmd/quickstart.py:252
msgid "Author name(s)"
msgstr "Nazwisko autora"

#: sphinx/cmd/quickstart.py:256
msgid ""
"Sphinx has the notion of a \"version\" and a \"release\" for the\n"
"software. Each version can have multiple releases. For example, for\n"
"Python the version is something like 2.5 or 3.0, while the release is\n"
"something like 2.5.1 or 3.0a1. If you don't need this dual structure,\n"
"just set both to the same value."
msgstr ""

#: sphinx/cmd/quickstart.py:261
msgid "Project version"
msgstr "Wersja projektu"

#: sphinx/cmd/quickstart.py:263
msgid "Project release"
msgstr "Wydanie projektu"

#: sphinx/cmd/quickstart.py:267
msgid ""
"If the documents are to be written in a language other than English,\n"
"you can select a language here by its language code. Sphinx will then\n"
"translate text that it generates into that language.\n"
"\n"
"For a list of supported codes, see\n"
"https://www.sphinx-doc.org/en/master/usage/configuration.html#confval-language."
msgstr ""

#: sphinx/cmd/quickstart.py:275
msgid "Project language"
msgstr "Język projektu"

#: sphinx/cmd/quickstart.py:281
msgid ""
"The file name suffix for source files. Commonly, this is either \".txt\"\n"
"or \".rst\". Only files with this suffix are considered documents."
msgstr ""

#: sphinx/cmd/quickstart.py:283
msgid "Source file suffix"
msgstr "Sufiks pliku źródłowego"

#: sphinx/cmd/quickstart.py:287
msgid ""
"One document is special in that it is considered the top node of the\n"
"\"contents tree\", that is, it is the root of the hierarchical structure\n"
"of the documents. Normally, this is \"index\", but if your \"index\"\n"
"document is a custom template, you can also set this to another filename."
msgstr ""

#: sphinx/cmd/quickstart.py:291
msgid "Name of your master document (without suffix)"
msgstr ""

#: sphinx/cmd/quickstart.py:296
#, python-format
msgid ""
"Error: the master file %s has already been found in the selected root path."
msgstr ""

#: sphinx/cmd/quickstart.py:298
msgid "sphinx-quickstart will not overwrite the existing file."
msgstr "sphinx-quickstart nie nadpisze istniejącego pliku."

#: sphinx/cmd/quickstart.py:300
msgid ""
"Please enter a new file name, or rename the existing file and press Enter"
msgstr ""

#: sphinx/cmd/quickstart.py:304
msgid "Indicate which of the following Sphinx extensions should be enabled:"
msgstr "Wskaż, które z następujących rozszerzeń Sphinx powinny być włączone:"

#: sphinx/cmd/quickstart.py:312
msgid ""
"Note: imgmath and mathjax cannot be enabled at the same time. imgmath has "
"been deselected."
msgstr ""

#: sphinx/cmd/quickstart.py:318
msgid ""
"A Makefile and a Windows command file can be generated for you so that you\n"
"only have to run e.g. `make html' instead of invoking sphinx-build\n"
"directly."
msgstr ""

#: sphinx/cmd/quickstart.py:321
msgid "Create Makefile? (y/n)"
msgstr "Stworzyć Makefile? (y/n)"

#: sphinx/cmd/quickstart.py:324
msgid "Create Windows command file? (y/n)"
msgstr ""

#: sphinx/cmd/quickstart.py:368 sphinx/ext/apidoc.py:93
#, python-format
msgid "Creating file %s."
msgstr "Tworzenie pliku %s."

#: sphinx/cmd/quickstart.py:373 sphinx/ext/apidoc.py:90
#, python-format
msgid "File %s already exists, skipping."
msgstr "Plik %s już istnieje, pomijam."

#: sphinx/cmd/quickstart.py:418
msgid "Finished: An initial directory structure has been created."
msgstr "Zakończono: Utworzono początkową strukturę katalogów."

#: sphinx/cmd/quickstart.py:420
#, python-format
msgid ""
"You should now populate your master file %s and create other documentation\n"
"source files. "
msgstr ""

#: sphinx/cmd/quickstart.py:423
msgid ""
"Use the Makefile to build the docs, like so:\n"
"   make builder"
msgstr ""

#: sphinx/cmd/quickstart.py:426
#, python-format
msgid ""
"Use the sphinx-build command to build the docs, like so:\n"
"   sphinx-build -b builder %s %s"
msgstr ""

#: sphinx/cmd/quickstart.py:428
msgid ""
"where \"builder\" is one of the supported builders, e.g. html, latex or "
"linkcheck."
msgstr ""

#: sphinx/cmd/quickstart.py:464
msgid ""
"\n"
"Generate required files for a Sphinx project.\n"
"\n"
"sphinx-quickstart is an interactive tool that asks some questions about your\n"
"project and then generates a complete documentation directory and sample\n"
"Makefile to be used with sphinx-build.\n"
msgstr ""

#: sphinx/cmd/quickstart.py:479
msgid "quiet mode"
msgstr "tryb cichy"

#: sphinx/cmd/quickstart.py:484
msgid "project root"
msgstr ""

#: sphinx/cmd/quickstart.py:486
msgid "Structure options"
msgstr ""

#: sphinx/cmd/quickstart.py:488
msgid "if specified, separate source and build dirs"
msgstr ""

#: sphinx/cmd/quickstart.py:490
msgid "if specified, create build dir under source dir"
msgstr ""

#: sphinx/cmd/quickstart.py:492
msgid "replacement for dot in _templates etc."
msgstr ""

#: sphinx/cmd/quickstart.py:494
msgid "Project basic options"
msgstr "Podstawowe opcje projektu"

#: sphinx/cmd/quickstart.py:496
msgid "project name"
msgstr "nazwa projektu"

#: sphinx/cmd/quickstart.py:498
msgid "author names"
msgstr "nazwiska autorów"

#: sphinx/cmd/quickstart.py:500
msgid "version of project"
msgstr "wersja projektu"

#: sphinx/cmd/quickstart.py:502
msgid "release of project"
msgstr ""

#: sphinx/cmd/quickstart.py:504
msgid "document language"
msgstr "język dokumentu"

#: sphinx/cmd/quickstart.py:506
msgid "source file suffix"
msgstr "rozszerzenie pliku źródłowego"

#: sphinx/cmd/quickstart.py:508
msgid "master document name"
msgstr "nazwa głównego dokumentu"

#: sphinx/cmd/quickstart.py:510
msgid "use epub"
msgstr ""

#: sphinx/cmd/quickstart.py:512
msgid "Extension options"
msgstr "Opcje rozszerzeń"

#: sphinx/cmd/quickstart.py:516 sphinx/ext/apidoc.py:400
#, python-format
msgid "enable %s extension"
msgstr "włącz rozszerzenie %s"

#: sphinx/cmd/quickstart.py:518 sphinx/ext/apidoc.py:396
msgid "enable arbitrary extensions"
msgstr ""

#: sphinx/cmd/quickstart.py:520
msgid "Makefile and Batchfile creation"
msgstr ""

#: sphinx/cmd/quickstart.py:522
msgid "create makefile"
msgstr "utwórz plik makefile"

#: sphinx/cmd/quickstart.py:524
msgid "do not create makefile"
msgstr "nie twórz pliku makefile"

#: sphinx/cmd/quickstart.py:526
msgid "create batchfile"
msgstr "twórz plik wsadowy"

#: sphinx/cmd/quickstart.py:529
msgid "do not create batchfile"
msgstr "nie twórz pliku wsadowego"

#: sphinx/cmd/quickstart.py:532
msgid "use make-mode for Makefile/make.bat"
msgstr ""

#: sphinx/cmd/quickstart.py:535
msgid "do not use make-mode for Makefile/make.bat"
msgstr ""

#: sphinx/cmd/quickstart.py:537 sphinx/ext/apidoc.py:402
msgid "Project templating"
msgstr ""

#: sphinx/cmd/quickstart.py:540 sphinx/ext/apidoc.py:405
msgid "template directory for template files"
msgstr ""

#: sphinx/cmd/quickstart.py:543
msgid "define a template variable"
msgstr ""

#: sphinx/cmd/quickstart.py:576
msgid "\"quiet\" is specified, but any of \"project\" or \"author\" is not specified."
msgstr ""

#: sphinx/cmd/quickstart.py:590
msgid ""
"Error: specified path is not a directory, or sphinx files already exist."
msgstr ""

#: sphinx/cmd/quickstart.py:592
msgid ""
"sphinx-quickstart only generate into a empty directory. Please specify a new"
" root path."
msgstr ""

#: sphinx/cmd/quickstart.py:607
#, python-format
msgid "Invalid template variable: %s"
msgstr ""

#: sphinx/directives/code.py:61
msgid "non-whitespace stripped by dedent"
msgstr ""

#: sphinx/directives/code.py:82
#, python-format
msgid "Invalid caption: %s"
msgstr "Nieprawidłowy podpis: %s"

#: sphinx/directives/code.py:127 sphinx/directives/code.py:277
#: sphinx/directives/code.py:453
#, python-format
msgid "line number spec is out of range(1-%d): %r"
msgstr ""

#: sphinx/directives/code.py:206
#, python-format
msgid "Cannot use both \"%s\" and \"%s\" options"
msgstr "Nie można użyć jednocześnie opcji \"%s\" i \"%s\""

#: sphinx/directives/code.py:220
#, python-format
msgid "Include file %r not found or reading it failed"
msgstr "Plik include %r nie znaleziony lub nie powiódł się jego odczyt"

#: sphinx/directives/code.py:223
#, python-format
msgid ""
"Encoding %r used for reading included file %r seems to be wrong, try giving "
"an :encoding: option"
msgstr "Kodowanie %r użyte do odczytu pliku include %r wydaje się być złe, spróbuj dając opcję :encoding:"

#: sphinx/directives/code.py:260
#, python-format
msgid "Object named %r not found in include file %r"
msgstr "Nie znaleziono obiektu o nazwie %r w pliku include %r"

#: sphinx/directives/code.py:286
msgid "Cannot use \"lineno-match\" with a disjoint set of \"lines\""
msgstr "Nie można użyć „lineno-match” z rozłącznym zbiorem „lines”"

#: sphinx/directives/code.py:291
#, python-format
msgid "Line spec %r: no lines pulled from include file %r"
msgstr "Specyfikacja linii %r: nie wyciągnięto żadnych linii z pliku include %r"

#: sphinx/directives/other.py:120
#, python-format
msgid "toctree glob pattern %r didn't match any documents"
msgstr ""

#: sphinx/directives/other.py:146 sphinx/environment/adapters/toctree.py:324
#, python-format
msgid "toctree contains reference to excluded document %r"
msgstr ""

#: sphinx/directives/other.py:149 sphinx/environment/adapters/toctree.py:328
#, python-format
msgid "toctree contains reference to nonexisting document %r"
msgstr ""

#: sphinx/directives/other.py:160
#, python-format
msgid "duplicated entry found in toctree: %s"
msgstr ""

#: sphinx/directives/other.py:193
msgid "Section author: "
msgstr "Autor rozdziału: "

#: sphinx/directives/other.py:195
msgid "Module author: "
msgstr "Autor modułu: "

#: sphinx/directives/other.py:197
msgid "Code author: "
msgstr "Autor kodu: "

#: sphinx/directives/other.py:199
msgid "Author: "
msgstr "Autor: "

#: sphinx/directives/other.py:275
msgid ".. acks content is not a list"
msgstr ""

#: sphinx/directives/other.py:301
msgid ".. hlist content is not a list"
msgstr ""

#: sphinx/directives/patches.py:66
msgid ""
"\":file:\" option for csv-table directive now recognizes an absolute path as"
" a relative path from source directory. Please update your document."
msgstr ""

#: sphinx/domains/__init__.py:397
#, python-format
msgid "%s %s"
msgstr "%s %s"

#: sphinx/domains/changeset.py:23
#, python-format
msgid "Added in version %s"
msgstr ""

#: sphinx/domains/changeset.py:24
#, python-format
msgid "Changed in version %s"
msgstr "Zmienione w wersji %s"

#: sphinx/domains/changeset.py:25
#, python-format
msgid "Deprecated since version %s"
msgstr "Niezalecane od wersji %s"

#: sphinx/domains/changeset.py:26
#, python-format
msgid "Removed in version %s"
msgstr ""

#: sphinx/domains/citation.py:71
#, python-format
msgid "duplicate citation %s, other instance in %s"
msgstr ""

#: sphinx/domains/citation.py:82
#, python-format
msgid "Citation [%s] is not referenced."
msgstr "Cytat [%s] nie ma odniesienia."

#: sphinx/domains/javascript.py:165
#, python-format
msgid "%s() (built-in function)"
msgstr "%s() (funkcja wbudowana)"

#: sphinx/domains/javascript.py:166 sphinx/domains/python/__init__.py:240
#, python-format
msgid "%s() (%s method)"
msgstr "%s() (%s metoda)"

#: sphinx/domains/javascript.py:168
#, python-format
msgid "%s() (class)"
msgstr "%s() (klasa)"

#: sphinx/domains/javascript.py:170
#, python-format
msgid "%s (global variable or constant)"
msgstr "%s (zmienna globalna lub stała)"

#: sphinx/domains/javascript.py:172 sphinx/domains/python/__init__.py:325
#, python-format
msgid "%s (%s attribute)"
msgstr "%s (%s atrybut)"

#: sphinx/domains/javascript.py:255
msgid "Arguments"
msgstr "Argumenty"

#: sphinx/domains/cpp/__init__.py:350 sphinx/domains/javascript.py:258
msgid "Throws"
msgstr "Wyrzuca"

#: sphinx/domains/c/__init__.py:251 sphinx/domains/cpp/__init__.py:361
#: sphinx/domains/javascript.py:261 sphinx/domains/python/_object.py:175
msgid "Returns"
msgstr "Zwraca"

#: sphinx/domains/c/__init__.py:253 sphinx/domains/javascript.py:263
#: sphinx/domains/python/_object.py:177
msgid "Return type"
msgstr "Typ zwracany"

#: sphinx/domains/javascript.py:331
#, python-format
msgid "%s (module)"
msgstr "%s (moduł)"

#: sphinx/domains/c/__init__.py:622 sphinx/domains/cpp/__init__.py:764
#: sphinx/domains/javascript.py:368 sphinx/domains/python/__init__.py:574
msgid "function"
msgstr "funkcja"

#: sphinx/domains/javascript.py:369 sphinx/domains/python/__init__.py:578
msgid "method"
msgstr "metoda"

#: sphinx/domains/cpp/__init__.py:762 sphinx/domains/javascript.py:370
#: sphinx/domains/python/__init__.py:576
msgid "class"
msgstr "klasa"

#: sphinx/domains/javascript.py:371 sphinx/domains/python/__init__.py:575
msgid "data"
msgstr "dane"

#: sphinx/domains/javascript.py:372 sphinx/domains/python/__init__.py:581
msgid "attribute"
msgstr "atrybut"

#: sphinx/domains/javascript.py:373 sphinx/domains/python/__init__.py:583
msgid "module"
msgstr "moduł"

#: sphinx/domains/javascript.py:404
#, python-format
msgid "duplicate %s description of %s, other %s in %s"
msgstr ""

#: sphinx/domains/math.py:63
#, python-format
msgid "duplicate label of equation %s, other instance in %s"
msgstr "zduplikowana etykieta równania %s, inne wystąpienie w %s"

#: sphinx/domains/math.py:118 sphinx/writers/latex.py:2252
#, python-format
msgid "Invalid math_eqref_format: %r"
msgstr "Nieprawidłowy math_eqref_format: %r"

#: sphinx/domains/rst.py:127 sphinx/domains/rst.py:184
#, python-format
msgid "%s (directive)"
msgstr "%s (dyrektywa)"

#: sphinx/domains/rst.py:185 sphinx/domains/rst.py:189
#, python-format
msgid ":%s: (directive option)"
msgstr ""

#: sphinx/domains/rst.py:213
#, python-format
msgid "%s (role)"
msgstr "%s (rola)"

#: sphinx/domains/rst.py:223
msgid "directive"
msgstr "dyrektywa"

#: sphinx/domains/rst.py:224
msgid "directive-option"
msgstr ""

#: sphinx/domains/rst.py:225
msgid "role"
msgstr "rola"

#: sphinx/domains/rst.py:247
#, python-format
msgid "duplicate description of %s %s, other instance in %s"
msgstr ""

#: sphinx/domains/c/__init__.py:146
#, python-format
msgid "%s (C %s)"
msgstr ""

#: sphinx/domains/c/__init__.py:207 sphinx/domains/c/_symbol.py:552
#, python-format
msgid ""
"Duplicate C declaration, also defined at %s:%s.\n"
"Declaration is '.. c:%s:: %s'."
msgstr ""

#: sphinx/domains/c/__init__.py:245 sphinx/domains/cpp/__init__.py:344
#: sphinx/domains/python/_object.py:163 sphinx/ext/napoleon/docstring.py:762
msgid "Parameters"
msgstr "Parametry"

#: sphinx/domains/c/__init__.py:248 sphinx/domains/cpp/__init__.py:357
msgid "Return values"
msgstr ""

#: sphinx/domains/c/__init__.py:620 sphinx/domains/cpp/__init__.py:765
msgid "member"
msgstr "pole"

#: sphinx/domains/c/__init__.py:621
msgid "variable"
msgstr "zmienna"

#: sphinx/domains/c/__init__.py:623
msgid "macro"
msgstr "makro"

#: sphinx/domains/c/__init__.py:624
msgid "struct"
msgstr ""

#: sphinx/domains/c/__init__.py:625 sphinx/domains/cpp/__init__.py:763
msgid "union"
msgstr "unia"

#: sphinx/domains/c/__init__.py:626 sphinx/domains/cpp/__init__.py:768
msgid "enum"
msgstr "enum"

#: sphinx/domains/c/__init__.py:627 sphinx/domains/cpp/__init__.py:769
msgid "enumerator"
msgstr "enumerator"

#: sphinx/domains/c/__init__.py:628 sphinx/domains/cpp/__init__.py:766
msgid "type"
msgstr "typ"

#: sphinx/domains/c/__init__.py:630 sphinx/domains/cpp/__init__.py:771
msgid "function parameter"
msgstr ""

#: sphinx/domains/cpp/__init__.py:63
msgid "Template Parameters"
msgstr "Parametry szablonu"

#: sphinx/domains/cpp/__init__.py:185
#, python-format
msgid "%s (C++ %s)"
msgstr ""

#: sphinx/domains/cpp/__init__.py:268 sphinx/domains/cpp/_symbol.py:790
#, python-format
msgid ""
"Duplicate C++ declaration, also defined at %s:%s.\n"
"Declaration is '.. cpp:%s:: %s'."
msgstr ""

#: sphinx/domains/cpp/__init__.py:767
msgid "concept"
msgstr "koncepcja"

#: sphinx/domains/cpp/__init__.py:772
msgid "template parameter"
msgstr ""

#: sphinx/domains/python/__init__.py:94 sphinx/domains/python/__init__.py:231
#, python-format
msgid "%s() (in module %s)"
msgstr "%s() (w module %s)"

#: sphinx/domains/python/__init__.py:154 sphinx/domains/python/__init__.py:321
#: sphinx/domains/python/__init__.py:372
#, python-format
msgid "%s (in module %s)"
msgstr "%s (w module %s)"

#: sphinx/domains/python/__init__.py:156
#, python-format
msgid "%s (built-in variable)"
msgstr "%s (zmienna wbudowana)"

#: sphinx/domains/python/__init__.py:181
#, python-format
msgid "%s (built-in class)"
msgstr "%s (klasa wbudowana)"

#: sphinx/domains/python/__init__.py:182
#, python-format
msgid "%s (class in %s)"
msgstr "%s (klasa w module %s)"

#: sphinx/domains/python/__init__.py:236
#, python-format
msgid "%s() (%s class method)"
msgstr "%s() (%s metoda klasy)"

#: sphinx/domains/python/__init__.py:238
#, python-format
msgid "%s() (%s static method)"
msgstr "%s() (%s metoda statyczna)"

#: sphinx/domains/python/__init__.py:376
#, python-format
msgid "%s (%s property)"
msgstr ""

#: sphinx/domains/python/__init__.py:502
msgid "Python Module Index"
msgstr "Indeks modułów Pythona"

#: sphinx/domains/python/__init__.py:503
msgid "modules"
msgstr "moduły"

#: sphinx/domains/python/__init__.py:552
msgid "Deprecated"
msgstr "Niezalecane"

#: sphinx/domains/python/__init__.py:577
msgid "exception"
msgstr "wyjątek"

#: sphinx/domains/python/__init__.py:579
msgid "class method"
msgstr "metoda klasy"

#: sphinx/domains/python/__init__.py:580
msgid "static method"
msgstr "statyczna metoda"

#: sphinx/domains/python/__init__.py:582
msgid "property"
msgstr ""

#: sphinx/domains/python/__init__.py:640
#, python-format
msgid ""
"duplicate object description of %s, other instance in %s, use :no-index: for"
" one of them"
msgstr ""

#: sphinx/domains/python/__init__.py:760
#, python-format
msgid "more than one target found for cross-reference %r: %s"
msgstr ""

#: sphinx/domains/python/__init__.py:821
msgid " (deprecated)"
msgstr " (niezalecane)"

#: sphinx/domains/python/_object.py:168
msgid "Variables"
msgstr "Zmienne"

#: sphinx/domains/python/_object.py:172
msgid "Raises"
msgstr "Wyrzuca"

#: sphinx/domains/std/__init__.py:80 sphinx/domains/std/__init__.py:97
#, python-format
msgid "environment variable; %s"
msgstr "zmienna środowiskowa; %s"

#: sphinx/domains/std/__init__.py:157
#, python-format
msgid ""
"Malformed option description %r, should look like \"opt\", \"-opt args\", \""
"--opt args\", \"/opt args\" or \"+opt args\""
msgstr ""

#: sphinx/domains/std/__init__.py:228
#, python-format
msgid "%s command line option"
msgstr ""

#: sphinx/domains/std/__init__.py:230
msgid "command line option"
msgstr ""

#: sphinx/domains/std/__init__.py:348
msgid "glossary term must be preceded by empty line"
msgstr ""

#: sphinx/domains/std/__init__.py:356
msgid "glossary terms must not be separated by empty lines"
msgstr ""

#: sphinx/domains/std/__init__.py:362 sphinx/domains/std/__init__.py:375
msgid "glossary seems to be misformatted, check indentation"
msgstr ""

#: sphinx/domains/std/__init__.py:518
msgid "glossary term"
msgstr "termin glosariusza"

#: sphinx/domains/std/__init__.py:519
msgid "grammar token"
msgstr "symbol gramatyki"

#: sphinx/domains/std/__init__.py:520
msgid "reference label"
msgstr "etykieta odsyłacza"

#: sphinx/domains/std/__init__.py:522
msgid "environment variable"
msgstr "zmienna środowiskowa"

#: sphinx/domains/std/__init__.py:523
msgid "program option"
msgstr "opcja programu"

#: sphinx/domains/std/__init__.py:524
msgid "document"
msgstr "dokument"

#: sphinx/domains/std/__init__.py:560 sphinx/domains/std/__init__.py:572
msgid "Module Index"
msgstr "Indeks modułów"

#: sphinx/domains/std/__init__.py:561 sphinx/domains/std/__init__.py:573
#: sphinx/themes/basic/defindex.html:25
msgid "Search Page"
msgstr "Wyszukiwanie"

#: sphinx/domains/std/__init__.py:616 sphinx/domains/std/__init__.py:722
#: sphinx/ext/autosectionlabel.py:53
#, python-format
msgid "duplicate label %s, other instance in %s"
msgstr ""

#: sphinx/domains/std/__init__.py:635
#, python-format
msgid "duplicate %s description of %s, other instance in %s"
msgstr ""

#: sphinx/domains/std/__init__.py:841
msgid "numfig is disabled. :numref: is ignored."
msgstr ""

#: sphinx/domains/std/__init__.py:849
#, python-format
msgid "Failed to create a cross reference. Any number is not assigned: %s"
msgstr ""

#: sphinx/domains/std/__init__.py:861
#, python-format
msgid "the link has no caption: %s"
msgstr ""

#: sphinx/domains/std/__init__.py:875
#, python-format
msgid "invalid numfig_format: %s (%r)"
msgstr ""

#: sphinx/domains/std/__init__.py:878
#, python-format
msgid "invalid numfig_format: %s"
msgstr ""

#: sphinx/domains/std/__init__.py:1109
#, python-format
msgid "undefined label: %r"
msgstr ""

#: sphinx/domains/std/__init__.py:1111
#, python-format
msgid "Failed to create a cross reference. A title or caption not found: %r"
msgstr ""

#: sphinx/environment/__init__.py:71
msgid "new config"
msgstr "nowa konfiguracja"

#: sphinx/environment/__init__.py:72
msgid "config changed"
msgstr "konfiguracja zmieniona"

#: sphinx/environment/__init__.py:73
msgid "extensions changed"
msgstr "rozszerzenie zmienione"

#: sphinx/environment/__init__.py:279
msgid "build environment version not current"
msgstr ""

#: sphinx/environment/__init__.py:281
msgid "source directory has changed"
msgstr "katalog źródłowy został zmieniony"

#: sphinx/environment/__init__.py:360
msgid ""
"This environment is incompatible with the selected builder, please choose "
"another doctree directory."
msgstr ""

#: sphinx/environment/__init__.py:459
#, python-format
msgid "Failed to scan documents in %s: %r"
msgstr ""

#: sphinx/environment/__init__.py:596
#, python-format
msgid "Domain %r is not registered"
msgstr "Domena %r nie jest zarejestrowana"

#: sphinx/environment/__init__.py:730
msgid "document isn't included in any toctree"
msgstr ""

#: sphinx/environment/__init__.py:766
msgid "self referenced toctree found. Ignored."
msgstr ""

#: sphinx/environment/adapters/indexentries.py:69
#, python-format
msgid "see %s"
msgstr "zobacz %s"

#: sphinx/environment/adapters/indexentries.py:73
#, python-format
msgid "see also %s"
msgstr "zobacz także %s"

#: sphinx/environment/adapters/indexentries.py:76
#, python-format
msgid "unknown index entry type %r"
msgstr ""

#: sphinx/environment/adapters/indexentries.py:187
#: sphinx/templates/latex/sphinxmessages.sty_t:11
msgid "Symbols"
msgstr "Symbole"

#: sphinx/environment/adapters/toctree.py:297
#, python-format
msgid "circular toctree references detected, ignoring: %s <- %s"
msgstr ""

#: sphinx/environment/adapters/toctree.py:317
#, python-format
msgid ""
"toctree contains reference to document %r that doesn't have a title: no link"
" will be generated"
msgstr ""

#: sphinx/environment/adapters/toctree.py:326
#, python-format
msgid "toctree contains reference to non-included document %r"
msgstr ""

#: sphinx/environment/collectors/asset.py:89
#, python-format
msgid "image file not readable: %s"
msgstr ""

#: sphinx/environment/collectors/asset.py:108
#, python-format
msgid "image file %s not readable: %s"
msgstr ""

#: sphinx/environment/collectors/asset.py:134
#, python-format
msgid "download file not readable: %s"
msgstr ""

#: sphinx/environment/collectors/toctree.py:225
#, python-format
msgid "%s is already assigned section numbers (nested numbered toctree?)"
msgstr ""

#: sphinx/ext/apidoc.py:86
#, python-format
msgid "Would create file %s."
msgstr ""

#: sphinx/ext/apidoc.py:318
msgid ""
"\n"
"Look recursively in <MODULE_PATH> for Python modules and packages and create\n"
"one reST file with automodule directives per package in the <OUTPUT_PATH>.\n"
"\n"
"The <EXCLUDE_PATTERN>s can be file and/or directory patterns that will be\n"
"excluded from generation.\n"
"\n"
"Note: By default this script will not overwrite already created files."
msgstr ""

#: sphinx/ext/apidoc.py:331
msgid "path to module to document"
msgstr ""

#: sphinx/ext/apidoc.py:333
msgid ""
"fnmatch-style file and/or directory patterns to exclude from generation"
msgstr ""

#: sphinx/ext/apidoc.py:338
msgid "directory to place all output"
msgstr ""

#: sphinx/ext/apidoc.py:343
msgid "maximum depth of submodules to show in the TOC (default: 4)"
msgstr "maksymalna głębokość submodułów wyświetlanych w spisie treści (domyślnie: 4)"

#: sphinx/ext/apidoc.py:346
msgid "overwrite existing files"
msgstr "nadpisz istniejące pliki"

#: sphinx/ext/apidoc.py:349
msgid ""
"follow symbolic links. Powerful when combined with "
"collective.recipe.omelette."
msgstr ""

#: sphinx/ext/apidoc.py:352
msgid "run the script without creating files"
msgstr "wykonaj skrypt bez tworzenia plików"

#: sphinx/ext/apidoc.py:355
msgid "put documentation for each module on its own page"
msgstr ""

#: sphinx/ext/apidoc.py:358
msgid "include \"_private\" modules"
msgstr ""

#: sphinx/ext/apidoc.py:360
msgid "filename of table of contents (default: modules)"
msgstr ""

#: sphinx/ext/apidoc.py:362
msgid "don't create a table of contents file"
msgstr "nie twórz pliku spisu treści"

#: sphinx/ext/apidoc.py:365
msgid ""
"don't create headings for the module/package packages (e.g. when the "
"docstrings already contain them)"
msgstr ""

#: sphinx/ext/apidoc.py:370
msgid "put module documentation before submodule documentation"
msgstr ""

#: sphinx/ext/apidoc.py:374
msgid ""
"interpret module paths according to PEP-0420 implicit namespaces "
"specification"
msgstr ""

#: sphinx/ext/apidoc.py:378
msgid "file suffix (default: rst)"
msgstr "rozszerzenie pliku (domyślnie: rst)"

#: sphinx/ext/apidoc.py:380
msgid "generate a full project with sphinx-quickstart"
msgstr ""

#: sphinx/ext/apidoc.py:383
msgid "append module_path to sys.path, used when --full is given"
msgstr ""

#: sphinx/ext/apidoc.py:385
msgid "project name (default: root module name)"
msgstr ""

#: sphinx/ext/apidoc.py:387
msgid "project author(s), used when --full is given"
msgstr ""

#: sphinx/ext/apidoc.py:389
msgid "project version, used when --full is given"
msgstr ""

#: sphinx/ext/apidoc.py:391
msgid "project release, used when --full is given, defaults to --doc-version"
msgstr ""

#: sphinx/ext/apidoc.py:394
msgid "extension options"
msgstr ""

#: sphinx/ext/apidoc.py:427
#, python-format
msgid "%s is not a directory."
msgstr "%s nie jest katalogiem."

#: sphinx/ext/autosectionlabel.py:49
#, python-format
msgid "section \"%s\" gets labeled as \"%s\""
msgstr ""

#: sphinx/ext/coverage.py:46
#, python-format
msgid "invalid regex %r in %s"
msgstr "nieprawidłowe wyrażenie regularne %r w %s"

#: sphinx/ext/coverage.py:75
#, python-format
msgid ""
"Testing of coverage in the sources finished, look at the results in "
"%(outdir)spython.txt."
msgstr ""

#: sphinx/ext/coverage.py:89
#, python-format
msgid "invalid regex %r in coverage_c_regexes"
msgstr "nieprawidłowe wyrażenie regularne %r w coverage_c_regexes"

#: sphinx/ext/coverage.py:157
#, python-format
msgid "undocumented c api: %s [%s] in file %s"
msgstr ""

#: sphinx/ext/coverage.py:189
#, python-format
msgid "module %s could not be imported: %s"
msgstr "moduł %s nie mógł zostać zaimportowany: %s"

#: sphinx/ext/coverage.py:340
#, python-format
msgid "undocumented python function: %s :: %s"
msgstr ""

#: sphinx/ext/coverage.py:356
#, python-format
msgid "undocumented python class: %s :: %s"
msgstr ""

#: sphinx/ext/coverage.py:369
#, python-format
msgid "undocumented python method: %s :: %s :: %s"
msgstr ""

#: sphinx/ext/doctest.py:115
#, python-format
msgid "missing '+' or '-' in '%s' option."
msgstr "brak '+' lub '-' w opcji '%s'."

#: sphinx/ext/doctest.py:120
#, python-format
msgid "'%s' is not a valid option."
msgstr "'%s' nie jest prawidłową opcją."

#: sphinx/ext/doctest.py:134
#, python-format
msgid "'%s' is not a valid pyversion option"
msgstr "'%s' nie jest prawidłową opcją pyversion."

#: sphinx/ext/doctest.py:220
msgid "invalid TestCode type"
msgstr ""

#: sphinx/ext/doctest.py:281
#, python-format
msgid ""
"Testing of doctests in the sources finished, look at the results in "
"%(outdir)s/output.txt."
msgstr ""

#: sphinx/ext/doctest.py:438
#, python-format
msgid "no code/output in %s block at %s:%s"
msgstr ""

#: sphinx/ext/doctest.py:526
#, python-format
msgid "ignoring invalid doctest code: %r"
msgstr ""

#: sphinx/ext/duration.py:77
msgid ""
"====================== slowest reading durations ======================="
msgstr ""

#: sphinx/ext/extlinks.py:82
#, python-format
msgid ""
"hardcoded link %r could be replaced by an extlink (try using %r instead)"
msgstr ""

#: sphinx/ext/graphviz.py:135
msgid "Graphviz directive cannot have both content and a filename argument"
msgstr "dyrektywa Graphviz nie może mieć jednocześnie argumentów content i filename"

#: sphinx/ext/graphviz.py:145
#, python-format
msgid "External Graphviz file %r not found or reading it failed"
msgstr "Nie znaleziono zewnętrznego pliku Graphviz %r lub jego odczyt się nie powiódł"

#: sphinx/ext/graphviz.py:152
msgid "Ignoring \"graphviz\" directive without content."
msgstr "Ignorujemy dyrektywę „graphviz” bez treści."

#: sphinx/ext/graphviz.py:268
#, python-format
msgid "graphviz_dot executable path must be set! %r"
msgstr ""

#: sphinx/ext/graphviz.py:303
#, python-format
msgid ""
"dot command %r cannot be run (needed for graphviz output), check the "
"graphviz_dot setting"
msgstr "komenda dot %r nie może zostać uruchomiona (potrzebna do wyjścia graphviz), sprawdź ustawienia graphviz_dot"

#: sphinx/ext/graphviz.py:310
#, python-format
msgid ""
"dot exited with error:\n"
"[stderr]\n"
"%r\n"
"[stdout]\n"
"%r"
msgstr ""

#: sphinx/ext/graphviz.py:313
#, python-format
msgid ""
"dot did not produce an output file:\n"
"[stderr]\n"
"%r\n"
"[stdout]\n"
"%r"
msgstr ""

#: sphinx/ext/graphviz.py:329
#, python-format
msgid "graphviz_output_format must be one of 'png', 'svg', but is %r"
msgstr "graphviz_output_format musi mieć wartość „png” lub „svg” a ma %r"

#: sphinx/ext/graphviz.py:333 sphinx/ext/graphviz.py:386
#: sphinx/ext/graphviz.py:423
#, python-format
msgid "dot code %r: %s"
msgstr ""

#: sphinx/ext/graphviz.py:436 sphinx/ext/graphviz.py:444
#, python-format
msgid "[graph: %s]"
msgstr "[wykres: %s]"

#: sphinx/ext/graphviz.py:438 sphinx/ext/graphviz.py:446
msgid "[graph]"
msgstr "[wykres]"

#: sphinx/ext/imgconverter.py:39
#, python-format
msgid ""
"Unable to run the image conversion command %r. 'sphinx.ext.imgconverter' requires ImageMagick by default. Ensure it is installed, or set the 'image_converter' option to a custom conversion command.\n"
"\n"
"Traceback: %s"
msgstr ""

#: sphinx/ext/imgconverter.py:48 sphinx/ext/imgconverter.py:72
#, python-format
msgid ""
"convert exited with error:\n"
"[stderr]\n"
"%r\n"
"[stdout]\n"
"%r"
msgstr ""

#: sphinx/ext/imgconverter.py:67
#, python-format
msgid "convert command %r cannot be run, check the image_converter setting"
msgstr ""

#: sphinx/ext/imgmath.py:158
#, python-format
msgid ""
"LaTeX command %r cannot be run (needed for math display), check the "
"imgmath_latex setting"
msgstr ""

#: sphinx/ext/imgmath.py:173
#, python-format
msgid ""
"%s command %r cannot be run (needed for math display), check the imgmath_%s "
"setting"
msgstr ""

#: sphinx/ext/imgmath.py:327
#, python-format
msgid "display latex %r: %s"
msgstr ""

#: sphinx/ext/imgmath.py:361
#, python-format
msgid "inline latex %r: %s"
msgstr ""

#: sphinx/ext/imgmath.py:368 sphinx/ext/mathjax.py:53
msgid "Link to this equation"
msgstr ""

#: sphinx/ext/intersphinx.py:195
#, python-format
msgid "intersphinx inventory has moved: %s -> %s"
msgstr ""

#: sphinx/ext/intersphinx.py:230
#, python-format
msgid "loading intersphinx inventory from %s..."
msgstr ""

#: sphinx/ext/intersphinx.py:244
msgid ""
"encountered some issues with some of the inventories, but they had working "
"alternatives:"
msgstr ""

#: sphinx/ext/intersphinx.py:250
msgid "failed to reach any of the inventories with the following issues:"
msgstr ""

#: sphinx/ext/intersphinx.py:303
#, python-format
msgid "(in %s v%s)"
msgstr "(w %s v%s)"

#: sphinx/ext/intersphinx.py:305
#, python-format
msgid "(in %s)"
msgstr " (w %s)"

#: sphinx/ext/intersphinx.py:538
#, python-format
msgid "inventory for external cross-reference not found: %r"
msgstr ""

#: sphinx/ext/intersphinx.py:546
#, python-format
msgid "invalid external cross-reference suffix: %r"
msgstr ""

#: sphinx/ext/intersphinx.py:557
#, python-format
msgid "domain for external cross-reference not found: %r"
msgstr ""

#: sphinx/ext/intersphinx.py:750
#, python-format
msgid "external %s:%s reference target not found: %s"
msgstr ""

#: sphinx/ext/intersphinx.py:775
#, python-format
msgid "intersphinx identifier %r is not string. Ignored"
msgstr ""

#: sphinx/ext/intersphinx.py:797
#, python-format
msgid "Failed to read intersphinx_mapping[%s], ignored: %r"
msgstr ""

#: sphinx/ext/linkcode.py:69 sphinx/ext/viewcode.py:199
msgid "[source]"
msgstr "[źródło]"

#: sphinx/ext/todo.py:69
msgid "Todo"
msgstr "Todo"

#: sphinx/ext/todo.py:102
#, python-format
msgid "TODO entry found: %s"
msgstr ""

#: sphinx/ext/todo.py:161
msgid "<<original entry>>"
msgstr "<<oryginalny wpis>>"

#: sphinx/ext/todo.py:163
#, python-format
msgid "(The <<original entry>> is located in %s, line %d.)"
msgstr "(<<Oryginalny wpis>> znajduje się w pliku %s, w linii %d.)"

#: sphinx/ext/todo.py:173
msgid "original entry"
msgstr "oryginalny wpis"

#: sphinx/ext/viewcode.py:256
msgid "highlighting module code... "
msgstr ""

#: sphinx/ext/viewcode.py:284
msgid "[docs]"
msgstr "[dokumentacja]"

#: sphinx/ext/viewcode.py:304
msgid "Module code"
msgstr "Kod modułu"

#: sphinx/ext/viewcode.py:310
#, python-format
msgid "<h1>Source code for %s</h1>"
msgstr "<h1>Kod źródłowy modułu %s</h1>"

#: sphinx/ext/viewcode.py:336
msgid "Overview: module code"
msgstr "Przeglądanie: kod modułu"

#: sphinx/ext/viewcode.py:337
msgid "<h1>All modules for which code is available</h1>"
msgstr "<h1>Wszystkie moduły, dla których jest dostępny kod</h1>"

#: sphinx/ext/autodoc/__init__.py:135
#, python-format
msgid "invalid value for member-order option: %s"
msgstr ""

#: sphinx/ext/autodoc/__init__.py:143
#, python-format
msgid "invalid value for class-doc-from option: %s"
msgstr ""

#: sphinx/ext/autodoc/__init__.py:399
#, python-format
msgid "invalid signature for auto%s (%r)"
msgstr ""

#: sphinx/ext/autodoc/__init__.py:515
#, python-format
msgid "error while formatting arguments for %s: %s"
msgstr "błąd podczas formatowania argumentów dla %s: %s"

#: sphinx/ext/autodoc/__init__.py:798
#, python-format
msgid ""
"autodoc: failed to determine %s.%s (%r) to be documented, the following exception was raised:\n"
"%s"
msgstr ""

#: sphinx/ext/autodoc/__init__.py:893
#, python-format
msgid ""
"don't know which module to import for autodocumenting %r (try placing a "
"\"module\" or \"currentmodule\" directive in the document, or giving an "
"explicit module name)"
msgstr ""

#: sphinx/ext/autodoc/__init__.py:937
#, python-format
msgid "A mocked object is detected: %r"
msgstr ""

#: sphinx/ext/autodoc/__init__.py:956
#, python-format
msgid "error while formatting signature for %s: %s"
msgstr ""

#: sphinx/ext/autodoc/__init__.py:1019
msgid "\"::\" in automodule name doesn't make sense"
msgstr ""

#: sphinx/ext/autodoc/__init__.py:1026
#, python-format
msgid "signature arguments or return annotation given for automodule %s"
msgstr ""

#: sphinx/ext/autodoc/__init__.py:1039
#, python-format
msgid ""
"__all__ should be a list of strings, not %r (in module %s) -- ignoring "
"__all__"
msgstr ""

#: sphinx/ext/autodoc/__init__.py:1105
#, python-format
msgid ""
"missing attribute mentioned in :members: option: module %s, attribute %s"
msgstr ""

#: sphinx/ext/autodoc/__init__.py:1327 sphinx/ext/autodoc/__init__.py:1404
#: sphinx/ext/autodoc/__init__.py:2824
#, python-format
msgid "Failed to get a function signature for %s: %s"
msgstr ""

#: sphinx/ext/autodoc/__init__.py:1618
#, python-format
msgid "Failed to get a constructor signature for %s: %s"
msgstr ""

#: sphinx/ext/autodoc/__init__.py:1745
#, python-format
msgid "Bases: %s"
msgstr "Klasy bazowe: %s"

#: sphinx/ext/autodoc/__init__.py:1759
#, python-format
msgid "missing attribute %s in object %s"
msgstr "brakujący atrybut %s w obiekcie %s"

#: sphinx/ext/autodoc/__init__.py:1858 sphinx/ext/autodoc/__init__.py:1895
#: sphinx/ext/autodoc/__init__.py:1990
#, python-format
msgid "alias of %s"
msgstr ""

#: sphinx/ext/autodoc/__init__.py:1878
#, python-format
msgid "alias of TypeVar(%s)"
msgstr ""

#: sphinx/ext/autodoc/__init__.py:2216 sphinx/ext/autodoc/__init__.py:2316
#, python-format
msgid "Failed to get a method signature for %s: %s"
msgstr ""

#: sphinx/ext/autodoc/__init__.py:2447
#, python-format
msgid "Invalid __slots__ found on %s. Ignored."
msgstr ""

#: sphinx/ext/autodoc/preserve_defaults.py:190
#, python-format
msgid "Failed to parse a default argument value for %r: %s"
msgstr ""

#: sphinx/ext/autodoc/type_comment.py:132
#, python-format
msgid "Failed to update signature for %r: parameter not found: %s"
msgstr ""

#: sphinx/ext/autodoc/type_comment.py:135
#, python-format
msgid "Failed to parse type_comment for %r: %s"
msgstr ""

#: sphinx/ext/autosummary/__init__.py:251
#, python-format
msgid "autosummary references excluded document %r. Ignored."
msgstr ""

#: sphinx/ext/autosummary/__init__.py:253
#, python-format
msgid ""
"autosummary: stub file not found %r. Check your autosummary_generate "
"setting."
msgstr ""

#: sphinx/ext/autosummary/__init__.py:272
msgid "A captioned autosummary requires :toctree: option. ignored."
msgstr ""

#: sphinx/ext/autosummary/__init__.py:325
#, python-format
msgid ""
"autosummary: failed to import %s.\n"
"Possible hints:\n"
"%s"
msgstr ""

#: sphinx/ext/autosummary/__init__.py:339
#, python-format
msgid "failed to parse name %s"
msgstr ""

#: sphinx/ext/autosummary/__init__.py:344
#, python-format
msgid "failed to import object %s"
msgstr ""

#: sphinx/ext/autosummary/__init__.py:802
#, python-format
msgid "autosummary_generate: file not found: %s"
msgstr ""

#: sphinx/ext/autosummary/__init__.py:810
msgid ""
"autosummary generates .rst files internally. But your source_suffix does not"
" contain .rst. Skipped."
msgstr ""

#: sphinx/ext/autosummary/generate.py:200
#: sphinx/ext/autosummary/generate.py:358
#, python-format
msgid ""
"autosummary: failed to determine %r to be documented, the following exception was raised:\n"
"%s"
msgstr ""

#: sphinx/ext/autosummary/generate.py:470
#, python-format
msgid "[autosummary] generating autosummary for: %s"
msgstr ""

#: sphinx/ext/autosummary/generate.py:474
#, python-format
msgid "[autosummary] writing to %s"
msgstr ""

#: sphinx/ext/autosummary/generate.py:517
#, python-format
msgid ""
"[autosummary] failed to import %s.\n"
"Possible hints:\n"
"%s"
msgstr ""

#: sphinx/ext/autosummary/generate.py:690
msgid ""
"\n"
"Generate ReStructuredText using autosummary directives.\n"
"\n"
"sphinx-autogen is a frontend to sphinx.ext.autosummary.generate. It generates\n"
"the reStructuredText files from the autosummary directives contained in the\n"
"given input files.\n"
"\n"
"The format of the autosummary directive is documented in the\n"
"``sphinx.ext.autosummary`` Python module and can be read using::\n"
"\n"
"  pydoc sphinx.ext.autosummary\n"
msgstr ""

#: sphinx/ext/autosummary/generate.py:707
msgid "source files to generate rST files for"
msgstr ""

#: sphinx/ext/autosummary/generate.py:711
msgid "directory to place all output in"
msgstr ""

#: sphinx/ext/autosummary/generate.py:714
#, python-format
msgid "default suffix for files (default: %(default)s)"
msgstr "domyślny sufiks dla plików (domyślnie: %(default)s)"

#: sphinx/ext/autosummary/generate.py:718
#, python-format
msgid "custom template directory (default: %(default)s)"
msgstr ""

#: sphinx/ext/autosummary/generate.py:722
#, python-format
msgid "document imported members (default: %(default)s)"
msgstr ""

#: sphinx/ext/autosummary/generate.py:726
#, python-format
msgid ""
"document exactly the members in module __all__ attribute. (default: "
"%(default)s)"
msgstr ""

#: sphinx/ext/napoleon/__init__.py:341 sphinx/ext/napoleon/docstring.py:728
msgid "Keyword Arguments"
msgstr "Argumenty Nazwane"

#: sphinx/ext/napoleon/docstring.py:682
msgid "Example"
msgstr "Przykład"

#: sphinx/ext/napoleon/docstring.py:683
msgid "Examples"
msgstr "Przykłady"

#: sphinx/ext/napoleon/docstring.py:744
msgid "Notes"
msgstr "Uwagi"

#: sphinx/ext/napoleon/docstring.py:753
msgid "Other Parameters"
msgstr "Pozostałe parametry"

#: sphinx/ext/napoleon/docstring.py:789
msgid "Receives"
msgstr ""

#: sphinx/ext/napoleon/docstring.py:793
msgid "References"
msgstr ""

#: sphinx/ext/napoleon/docstring.py:825
msgid "Warns"
msgstr ""

#: sphinx/ext/napoleon/docstring.py:829
msgid "Yields"
msgstr ""

#: sphinx/ext/napoleon/docstring.py:987
#, python-format
msgid "invalid value set (missing closing brace): %s"
msgstr ""

#: sphinx/ext/napoleon/docstring.py:994
#, python-format
msgid "invalid value set (missing opening brace): %s"
msgstr ""

#: sphinx/ext/napoleon/docstring.py:1001
#, python-format
msgid "malformed string literal (missing closing quote): %s"
msgstr ""

#: sphinx/ext/napoleon/docstring.py:1008
#, python-format
msgid "malformed string literal (missing opening quote): %s"
msgstr ""

#: sphinx/locale/__init__.py:228
msgid "Attention"
msgstr "Uwaga"

#: sphinx/locale/__init__.py:229
msgid "Caution"
msgstr "Ostrzeżenie"

#: sphinx/locale/__init__.py:230
msgid "Danger"
msgstr "Niebezpieczeństwo"

#: sphinx/locale/__init__.py:231
msgid "Error"
msgstr "Błąd"

#: sphinx/locale/__init__.py:232
msgid "Hint"
msgstr "Podpowiedź"

#: sphinx/locale/__init__.py:233
msgid "Important"
msgstr "Ważne"

#: sphinx/locale/__init__.py:234
msgid "Note"
msgstr "Informacja"

#: sphinx/locale/__init__.py:235
msgid "See also"
msgstr "Zobacz także"

#: sphinx/locale/__init__.py:236
msgid "Tip"
msgstr "Wskazówka"

#: sphinx/locale/__init__.py:237
msgid "Warning"
msgstr "Ostrzeżenie"

#: sphinx/templates/latex/longtable.tex_t:52
#: sphinx/templates/latex/sphinxmessages.sty_t:8
msgid "continued from previous page"
msgstr "kontynuacja poprzedniej strony"

#: sphinx/templates/latex/longtable.tex_t:63
#: sphinx/templates/latex/sphinxmessages.sty_t:9
msgid "continues on next page"
msgstr "ciąg dalszy na następnej stronie"

#: sphinx/templates/latex/sphinxmessages.sty_t:10
msgid "Non-alphabetical"
msgstr "Niealfabetyczny"

#: sphinx/templates/latex/sphinxmessages.sty_t:12
msgid "Numbers"
msgstr "Liczby"

#: sphinx/templates/latex/sphinxmessages.sty_t:13
msgid "page"
msgstr "strona"

#: sphinx/themes/agogo/layout.html:38 sphinx/themes/basic/globaltoc.html:10
#: sphinx/themes/basic/localtoc.html:12 sphinx/themes/scrolls/layout.html:41
msgid "Table of Contents"
msgstr "Spis treści"

#: sphinx/themes/agogo/layout.html:43 sphinx/themes/basic/layout.html:141
#: sphinx/themes/basic/search.html:11 sphinx/themes/basic/search.html:23
msgid "Search"
msgstr "Szukaj"

#: sphinx/themes/agogo/layout.html:46 sphinx/themes/basic/searchbox.html:16
#: sphinx/themes/basic/searchfield.html:18
msgid "Go"
msgstr "Szukaj"

#: sphinx/themes/agogo/layout.html:90 sphinx/themes/basic/sourcelink.html:15
msgid "Show Source"
msgstr "Pokaż źródło"

#: sphinx/themes/basic/defindex.html:11
msgid "Overview"
msgstr "Przegląd"

#: sphinx/themes/basic/defindex.html:15
msgid "Welcome! This is"
msgstr "Witaj! To jest"

#: sphinx/themes/basic/defindex.html:16
msgid "the documentation for"
msgstr "dokumentacja do"

#: sphinx/themes/basic/defindex.html:17
msgid "last updated"
msgstr "ostatnio aktualizowana"

#: sphinx/themes/basic/defindex.html:20
msgid "Indices and tables:"
msgstr "Indeksy i tablice:"

#: sphinx/themes/basic/defindex.html:23
msgid "Complete Table of Contents"
msgstr "Kompletny spis treści"

#: sphinx/themes/basic/defindex.html:24
msgid "lists all sections and subsections"
msgstr "wszystkie rozdziały i podrozdziały"

#: sphinx/themes/basic/defindex.html:26
msgid "search this documentation"
msgstr "przeszukaj tę dokumentację"

#: sphinx/themes/basic/defindex.html:28
msgid "Global Module Index"
msgstr "Globalny indeks modułów"

#: sphinx/themes/basic/defindex.html:29
msgid "quick access to all modules"
msgstr "szybki dostęp do wszystkich modułów"

#: sphinx/themes/basic/defindex.html:31
msgid "all functions, classes, terms"
msgstr "wszystkie funkcje, klasy, terminy"

#: sphinx/themes/basic/genindex-single.html:33
#, python-format
msgid "Index &ndash; %(key)s"
msgstr "Indeks &ndash; %(key)s"

#: sphinx/themes/basic/genindex-single.html:61
#: sphinx/themes/basic/genindex-split.html:24
#: sphinx/themes/basic/genindex-split.html:38
#: sphinx/themes/basic/genindex.html:73
msgid "Full index on one page"
msgstr "Cały indeks na jednej stronie"

#: sphinx/themes/basic/genindex-split.html:16
msgid "Index pages by letter"
msgstr "Strony indeksu alfabetycznie"

#: sphinx/themes/basic/genindex-split.html:25
msgid "can be huge"
msgstr "może być ogromny"

#: sphinx/themes/basic/layout.html:26
msgid "Navigation"
msgstr "Nawigacja"

#: sphinx/themes/basic/layout.html:126
#, python-format
msgid "Search within %(docstitle)s"
msgstr "Szukaj pośród %(docstitle)s"

#: sphinx/themes/basic/layout.html:135
msgid "About these documents"
msgstr "O tych dokumentach"

#: sphinx/themes/basic/layout.html:144 sphinx/themes/basic/layout.html:188
#: sphinx/themes/basic/layout.html:190
msgid "Copyright"
msgstr "Copyright"

#: sphinx/themes/basic/layout.html:194 sphinx/themes/basic/layout.html:200
#, python-format
msgid "&#169; %(copyright_prefix)s %(copyright)s."
msgstr ""

#: sphinx/themes/basic/layout.html:212
#, python-format
msgid "Last updated on %(last_updated)s."
msgstr "Ostatnia modyfikacja %(last_updated)s."

#: sphinx/themes/basic/layout.html:215
#, python-format
msgid ""
"Created using <a href=\"https://www.sphinx-doc.org/\">Sphinx</a> "
"%(sphinx_version)s."
msgstr ""

#: sphinx/themes/basic/opensearch.xml:4
#, python-format
msgid "Search %(docstitle)s"
msgstr "Przeszukaj %(docstitle)s"

#: sphinx/themes/basic/relations.html:12
msgid "Previous topic"
msgstr "Poprzedni temat"

#: sphinx/themes/basic/relations.html:14
msgid "previous chapter"
msgstr "poprzedni rozdział"

#: sphinx/themes/basic/relations.html:19
msgid "Next topic"
msgstr "Następny temat"

#: sphinx/themes/basic/relations.html:21
msgid "next chapter"
msgstr "następny rozdział"

#: sphinx/themes/basic/search.html:28
msgid ""
"Please activate JavaScript to enable the search\n"
"    functionality."
msgstr "Aby umożliwić wyszukiwanie, proszę włączyć JavaScript."

#: sphinx/themes/basic/search.html:36
msgid ""
"Searching for multiple words only shows matches that contain\n"
"    all words."
msgstr ""

#: sphinx/themes/basic/search.html:43
msgid "search"
msgstr "szukaj"

#: sphinx/themes/basic/searchbox.html:12
msgid "Quick search"
msgstr "Szybkie wyszukiwanie"

#: sphinx/themes/basic/sourcelink.html:12
msgid "This Page"
msgstr "Ta strona"

#: sphinx/themes/basic/changes/frameset.html:5
#: sphinx/themes/basic/changes/versionchanges.html:12
#, python-format
msgid "Changes in Version %(version)s &#8212; %(docstitle)s"
msgstr "Zmiany w wersji %(version)s &#8212; %(docstitle)s"

#: sphinx/themes/basic/changes/rstsource.html:5
#, python-format
msgid "%(filename)s &#8212; %(docstitle)s"
msgstr "%(filename)s &#8212; %(docstitle)s"

#: sphinx/themes/basic/changes/versionchanges.html:17
#, python-format
msgid "Automatically generated list of changes in version %(version)s"
msgstr "Automatycznie wygenerowana lista zmian w wersji %(version)s"

#: sphinx/themes/basic/changes/versionchanges.html:18
msgid "Library changes"
msgstr "Zmiany w bibliotekach"

#: sphinx/themes/basic/changes/versionchanges.html:23
msgid "C API changes"
msgstr "Zmiany w C API"

#: sphinx/themes/basic/changes/versionchanges.html:25
msgid "Other changes"
msgstr "Inne zmiany"

#: sphinx/themes/basic/static/searchtools.js:112
msgid "Search Results"
msgstr "Wyniki wyszukiwania"

#: sphinx/themes/basic/static/searchtools.js:114
msgid ""
"Your search did not match any documents. Please make sure that all words are"
" spelled correctly and that you've selected enough categories."
msgstr "Twoje wyszukiwanie nie dało żadnych wyników. Upewnij się, że wszystkie słowa są wpisane prawidłowo i że wybrałeś dostateczną ilość kategorii."

#: sphinx/themes/basic/static/searchtools.js:118
msgid ""
"Search finished, found ${resultCount} page(s) matching the search query."
msgstr ""

#: sphinx/themes/basic/static/searchtools.js:246
msgid "Searching"
msgstr "Wyszukiwanie"

#: sphinx/themes/basic/static/searchtools.js:262
msgid "Preparing search..."
msgstr "Inicjalizacja wyszukiwania..."

#: sphinx/themes/basic/static/searchtools.js:463
msgid ", in "
msgstr ", w "

#: sphinx/themes/basic/static/sphinx_highlight.js:112
msgid "Hide Search Matches"
msgstr "Ukryj wyniki wyszukiwania"

#: sphinx/themes/classic/layout.html:20
#: sphinx/themes/classic/static/sidebar.js_t:57
msgid "Collapse sidebar"
msgstr "Zwiń pasek boczny"

#: sphinx/themes/classic/static/sidebar.js_t:48
msgid "Expand sidebar"
msgstr "Rozwiń pasek boczny"

#: sphinx/themes/haiku/layout.html:24
msgid "Contents"
msgstr "Treść"

#: sphinx/transforms/__init__.py:128
msgid "could not calculate translation progress!"
msgstr ""

#: sphinx/transforms/__init__.py:133
msgid "no translated elements!"
msgstr ""

#: sphinx/transforms/__init__.py:250
#, python-format
msgid ""
"4 column based index found. It might be a bug of extensions you use: %r"
msgstr ""

#: sphinx/transforms/__init__.py:291
#, python-format
msgid "Footnote [%s] is not referenced."
msgstr ""

#: sphinx/transforms/__init__.py:297
msgid "Footnote [#] is not referenced."
msgstr ""

#: sphinx/transforms/i18n.py:205 sphinx/transforms/i18n.py:270
msgid ""
"inconsistent footnote references in translated message. original: {0}, "
"translated: {1}"
msgstr ""

#: sphinx/transforms/i18n.py:245
msgid ""
"inconsistent references in translated message. original: {0}, translated: "
"{1}"
msgstr ""

#: sphinx/transforms/i18n.py:285
msgid ""
"inconsistent citation references in translated message. original: {0}, "
"translated: {1}"
msgstr ""

#: sphinx/transforms/i18n.py:302
msgid ""
"inconsistent term references in translated message. original: {0}, "
"translated: {1}"
msgstr ""

#: sphinx/transforms/post_transforms/__init__.py:116
msgid ""
"Could not determine the fallback text for the cross-reference. Might be a "
"bug."
msgstr ""

#: sphinx/transforms/post_transforms/__init__.py:158
#, python-format
msgid "more than one target found for 'any' cross-reference %r: could be %s"
msgstr "znaleziono więcej niż jeden cel dla cross-referencji „any” %r: może być %s"

#: sphinx/transforms/post_transforms/__init__.py:209
#, python-format
msgid "%s:%s reference target not found: %s"
msgstr ""

#: sphinx/transforms/post_transforms/__init__.py:212
#, python-format
msgid "%r reference target not found: %s"
msgstr ""

#: sphinx/transforms/post_transforms/images.py:89
#, python-format
msgid "Could not fetch remote image: %s [%d]"
msgstr ""

#: sphinx/transforms/post_transforms/images.py:117
#, python-format
msgid "Could not fetch remote image: %s [%s]"
msgstr ""

#: sphinx/transforms/post_transforms/images.py:135
#, python-format
msgid "Unknown image format: %s..."
msgstr "Nieznany format obrazka: %s..."

#: sphinx/util/__init__.py:168
#, python-format
msgid "undecodable source characters, replacing with \"?\": %r"
msgstr ""

#: sphinx/util/display.py:78
msgid "skipped"
msgstr ""

#: sphinx/util/display.py:83
msgid "failed"
msgstr ""

#: sphinx/util/docfields.py:87
#, python-format
msgid ""
"Problem in %s domain: field is supposed to use role '%s', but that role is "
"not in the domain."
msgstr ""

#: sphinx/util/docutils.py:295
#, python-format
msgid "unknown directive or role name: %s:%s"
msgstr ""

#: sphinx/util/docutils.py:591
#, python-format
msgid "unknown node type: %r"
msgstr ""

#: sphinx/util/i18n.py:94
#, python-format
msgid "reading error: %s, %s"
msgstr "błąd odczytu: %s, %s"

#: sphinx/util/i18n.py:101
#, python-format
msgid "writing error: %s, %s"
msgstr "błąd zapisu: %s, %s"

#: sphinx/util/i18n.py:125
#, python-format
msgid "locale_dir %s does not exist"
msgstr ""

#: sphinx/util/i18n.py:215
#, python-format
msgid ""
"Invalid date format. Quote the string by single quote if you want to output "
"it directly: %s"
msgstr ""

#: sphinx/util/nodes.py:386
#, python-format
msgid ""
"%r is deprecated for index entries (from entry %r). Use 'pair: %s' instead."
msgstr ""

#: sphinx/util/nodes.py:439
#, python-format
msgid "toctree contains ref to nonexisting file %r"
msgstr ""

#: sphinx/util/nodes.py:637
#, python-format
msgid "exception while evaluating only directive expression: %s"
msgstr ""

#: sphinx/util/rst.py:71
#, python-format
msgid "default role %s not found"
msgstr ""

#: sphinx/writers/html5.py:100 sphinx/writers/html5.py:109
msgid "Link to this definition"
msgstr ""

#: sphinx/writers/html5.py:397
#, python-format
msgid "numfig_format is not defined for %s"
msgstr ""

#: sphinx/writers/html5.py:407
#, python-format
msgid "Any IDs not assigned for %s node"
msgstr ""

#: sphinx/writers/html5.py:462
msgid "Link to this term"
msgstr ""

#: sphinx/writers/html5.py:496 sphinx/writers/html5.py:501
msgid "Link to this heading"
msgstr ""

#: sphinx/writers/html5.py:505
msgid "Link to this table"
msgstr ""

#: sphinx/writers/html5.py:548
msgid "Link to this code"
msgstr ""

#: sphinx/writers/html5.py:550
msgid "Link to this image"
msgstr ""

#: sphinx/writers/html5.py:552
msgid "Link to this toctree"
msgstr ""

#: sphinx/writers/html5.py:688
msgid "Could not obtain image size. :scale: option is ignored."
msgstr ""

#: sphinx/writers/latex.py:335
#, python-format
msgid "unknown %r toplevel_sectioning for class %r"
msgstr ""

#: sphinx/writers/latex.py:386
msgid "too large :maxdepth:, ignored."
msgstr ""

#: sphinx/writers/latex.py:625
msgid "document title is not a single Text node"
msgstr ""

#: sphinx/writers/latex.py:656 sphinx/writers/texinfo.py:626
msgid ""
"encountered title node not in section, topic, table, admonition or sidebar"
msgstr ""

#: sphinx/writers/latex.py:959 sphinx/writers/manpage.py:259
#: sphinx/writers/texinfo.py:641
msgid "Footnotes"
msgstr "Przypisy"

#: sphinx/writers/latex.py:1028
msgid ""
"both tabularcolumns and :widths: option are given. :widths: is ignored."
msgstr ""

#: sphinx/writers/latex.py:1388
#, python-format
msgid "dimension unit %s is invalid. Ignored."
msgstr "%s"

#: sphinx/writers/latex.py:1722
#, python-format
msgid "unknown index entry type %s found"
msgstr ""

#: sphinx/writers/manpage.py:305 sphinx/writers/text.py:907
#, python-format
msgid "[image: %s]"
msgstr "[obraz: %s]"

#: sphinx/writers/manpage.py:306 sphinx/writers/text.py:908
msgid "[image]"
msgstr "[obraz]"

#: sphinx/writers/texinfo.py:1197
msgid "caption not inside a figure."
msgstr ""

#: sphinx/writers/texinfo.py:1284
#, python-format
msgid "unimplemented node type: %r"
msgstr ""
