#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import logging
import signal
import sys
import threading
import time
from database import DatabaseManager
from web_server import WebServer
from qr_generator import QRGenerator
from camera_monitor import CameraMonitor

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('data_transmission.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)

class DataTransmissionClient:
    def __init__(self):
        self.db_manager = None
        self.web_server = None
        self.qr_generator = None
        self.camera_monitor = None
        self.web_thread = None
        self.is_running = False
    
    def initialize(self):
        """初始化所有组件"""
        try:
            logging.info("正在初始化数据传输客户端...")
            
            # 初始化数据库管理器
            self.db_manager = DatabaseManager()
            logging.info("数据库管理器初始化完成")
            
            # 初始化Web服务器
            self.web_server = WebServer(self.db_manager)
            logging.info("Web服务器初始化完成")
            
            # 初始化二维码生成器
            self.qr_generator = QRGenerator(self.db_manager)
            logging.info("二维码生成器初始化完成")
            
            # 初始化摄像头监控
            self.camera_monitor = CameraMonitor(self.db_manager)
            logging.info("摄像头监控初始化完成")
            
            logging.info("所有组件初始化完成")
            return True
            
        except Exception as e:
            logging.error(f"初始化时出错: {e}")
            return False
    
    def start_services(self):
        """启动所有服务"""
        try:
            logging.info("正在启动所有服务...")
            
            # 启动Web服务器（在单独线程中）
            self.web_thread = threading.Thread(target=self.web_server.run, daemon=True)
            self.web_thread.start()
            logging.info("Web服务器已启动")
            
            # 等待Web服务器启动
            time.sleep(2)
            
            # 启动二维码生成定时任务
            self.qr_generator.start_scheduler()
            logging.info("二维码生成服务已启动")
            
            # 启动摄像头监控
            if self.camera_monitor.start_monitoring():
                logging.info("摄像头监控服务已启动")
            else:
                logging.warning("摄像头监控服务启动失败，但程序将继续运行")
            
            self.is_running = True
            logging.info("所有服务启动完成")
            return True
            
        except Exception as e:
            logging.error(f"启动服务时出错: {e}")
            return False
    
    def stop_services(self):
        """停止所有服务"""
        logging.info("正在停止所有服务...")
        
        self.is_running = False
        
        # 停止摄像头监控
        if self.camera_monitor:
            self.camera_monitor.stop_monitoring()
            logging.info("摄像头监控服务已停止")
        
        # 停止二维码生成定时任务
        if self.qr_generator:
            self.qr_generator.stop_scheduler()
            logging.info("二维码生成服务已停止")
        
        # 关闭数据库连接
        if self.db_manager:
            self.db_manager.close()
            logging.info("数据库连接已关闭")
        
        logging.info("所有服务已停止")
    
    def run(self):
        """运行主程序"""
        if not self.initialize():
            logging.error("初始化失败，程序退出")
            return False
        
        if not self.start_services():
            logging.error("启动服务失败，程序退出")
            return False
        
        try:
            logging.info("数据传输客户端正在运行...")
            logging.info("按 Ctrl+C 停止程序")
            
            # 主循环
            while self.is_running:
                time.sleep(1)
                
        except KeyboardInterrupt:
            logging.info("接收到停止信号")
        except Exception as e:
            logging.error(f"运行时出错: {e}")
        finally:
            self.stop_services()
        
        return True

def signal_handler(signum, frame):
    """信号处理器"""
    logging.info(f"接收到信号 {signum}，正在停止程序...")
    sys.exit(0)

def main():
    """主函数"""
    # 注册信号处理器
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # 创建并运行客户端
    client = DataTransmissionClient()
    success = client.run()
    
    if success:
        logging.info("程序正常退出")
        sys.exit(0)
    else:
        logging.error("程序异常退出")
        sys.exit(1)

if __name__ == "__main__":
    main()
