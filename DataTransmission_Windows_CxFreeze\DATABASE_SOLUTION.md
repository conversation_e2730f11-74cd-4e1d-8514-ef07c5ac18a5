# DataTransmission 数据库连接问题解决方案

## 🚨 问题描述
程序在"步骤三：测试数据库连接"时报错并直接退出。

## 🎯 一键解决方案

### 方案1：一键数据库设置（推荐）
```cmd
setup_database.bat
```
这个脚本会自动：
- 检查MySQL服务状态
- 创建DataTransmission数据库
- 更新配置文件密码
- 测试数据库连接

### 方案2：数据库问题诊断和修复
```cmd
# 先诊断问题
python test_database_connection.py

# 然后运行修复工具
fix_database_issues.bat
```

## 🔍 详细诊断步骤

### 步骤1：检查MySQL服务
```cmd
# 检查服务状态
sc query mysql

# 启动服务（如果未运行）
net start mysql
```

### 步骤2：测试基本连接
```cmd
python test_database_connection.py
```
这个脚本会逐步测试：
1. MySQL模块导入
2. 配置文件读取
3. MySQL服务器连接
4. 数据库存在性检查
5. 完整连接测试

### 步骤3：根据错误信息处理

#### 错误1：`Can't connect to MySQL server on 'localhost' (10061)`
**原因**：MySQL服务未启动
**解决**：
```cmd
net start mysql
```

#### 错误2：`Access denied for user 'root'@'localhost'`
**原因**：密码错误
**解决**：
1. 检查config.py中的密码
2. 或重置MySQL密码

#### 错误3：`Unknown database 'DataTransmission'`
**原因**：数据库不存在
**解决**：
```cmd
mysql -u root -p < create_database.sql
```

#### 错误4：`No module named 'mysql'`
**原因**：MySQL连接器未安装
**解决**：
```cmd
cd offline_packages
install_offline.bat
```

## 📋 手动解决步骤

### 1. 确保MySQL已安装并运行
```cmd
# 检查MySQL服务
sc query mysql

# 如果服务不存在，需要安装MySQL
# 下载地址：https://dev.mysql.com/downloads/mysql/
```

### 2. 创建数据库
```cmd
# 方法1：使用提供的SQL脚本
mysql -u root -p < create_database.sql

# 方法2：手动创建
mysql -u root -p
CREATE DATABASE DataTransmission CHARACTER SET utf8 COLLATE utf8_general_ci;
EXIT;
```

### 3. 更新配置文件
编辑 `config.py`，确保密码正确：
```python
DATABASE_CONFIG = {
    'host': 'localhost',
    'port': 3306,
    'database': 'DataTransmission',
    'user': 'root',
    'password': '你的实际MySQL密码',  # 这里必须是正确的密码
    'charset': 'utf8'
}
```

### 4. 验证连接
```cmd
python test_database_connection.py
```

## 🛠️ 工具文件说明

### 诊断工具
- **`test_database_connection.py`** - 详细的数据库连接测试
- **`fix_database_issues.bat`** - 数据库问题自动修复工具

### 设置工具
- **`setup_database.bat`** - 一键数据库设置脚本
- **`create_database.sql`** - 数据库创建SQL脚本

### 配置文件
- **`config.py`** - 数据库连接配置

## 🔧 高级故障排除

### 检查MySQL错误日志
```cmd
# 常见日志位置
C:\ProgramData\MySQL\MySQL Server 8.0\Data\*.err
C:\Program Files\MySQL\MySQL Server 8.0\data\*.err
```

### 重置MySQL root密码
如果忘记了MySQL密码：
1. 停止MySQL服务：`net stop mysql`
2. 以安全模式启动MySQL
3. 重置密码
4. 重启MySQL服务

### 检查端口占用
```cmd
# 检查3306端口是否被占用
netstat -ano | findstr :3306
```

### 防火墙设置
确保Windows防火墙允许MySQL通信：
```cmd
# 添加防火墙规则
netsh advfirewall firewall add rule name="MySQL" dir=in action=allow protocol=TCP localport=3306
```

## ✅ 验证解决方案

完成修复后，按以下步骤验证：

1. **运行连接测试**：
   ```cmd
   python test_database_connection.py
   ```

2. **启动程序**：
   ```cmd
   DataTransmission.exe
   ```

3. **检查Web界面**：
   访问 http://localhost:5000

## 📞 仍然有问题？

如果以上方法都无法解决问题，请提供：

1. **`test_database_connection.py`的完整输出**
2. **MySQL版本信息**：`mysql --version`
3. **Windows版本信息**
4. **config.py的内容**（隐藏密码）
5. **MySQL错误日志内容**

## 💡 预防措施

为避免将来出现类似问题：

1. **定期备份数据库**
2. **记录MySQL密码**
3. **确保MySQL服务设置为自动启动**
4. **定期检查MySQL服务状态**

---

**重要提示**：大多数数据库连接问题都是由于MySQL服务未启动或密码配置错误导致的。使用提供的一键设置脚本可以快速解决90%的问题！
