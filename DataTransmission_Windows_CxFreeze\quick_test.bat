@echo off
chcp 65001 >nul
title DataTransmission 快速测试

echo ========================================
echo    DataTransmission 快速测试
echo ========================================
echo.

echo 正在进行快速诊断...
echo.

echo [1/4] 检查MySQL服务...
sc query mysql >nul 2>&1
if %errorlevel% equ 0 (
    sc query mysql | find "RUNNING" >nul
    if %errorlevel% equ 0 (
        echo ✓ MySQL服务正在运行
    ) else (
        echo ✗ MySQL服务未运行
        echo   解决方案: net start mysql
    )
) else (
    echo ✗ MySQL服务未安装
    echo   解决方案: 安装MySQL数据库
)

echo.
echo [2/4] 检查Python模块...
python -c "import mysql.connector; print('✓ mysql.connector')" 2>nul || echo "✗ mysql.connector - 运行: offline_packages/install_offline.bat"
python -c "import cv2; print('✓ cv2')" 2>nul || echo "✗ cv2 - 运行: offline_packages/install_offline.bat"
python -c "import flask; print('✓ flask')" 2>nul || echo "✗ flask - 运行: offline_packages/install_offline.bat"

echo.
echo [3/4] 检查配置文件...
if exist "config.py" (
    python -c "import config; print('✓ 配置文件导入成功')" 2>nul || echo "✗ 配置文件语法错误"
    python -c "import config; print('✓ 数据库配置存在') if hasattr(config, 'DATABASE_CONFIG') else print('✗ 缺少数据库配置')" 2>nul
) else (
    echo ✗ config.py 文件不存在
)

echo.
echo [4/4] 测试数据库连接...
python -c "
import mysql.connector
import config
try:
    conn = mysql.connector.connect(**config.DATABASE_CONFIG)
    if conn.is_connected():
        print('✓ 数据库连接成功')
        conn.close()
    else:
        print('✗ 数据库连接失败')
except Exception as e:
    print(f'✗ 数据库连接错误: {str(e)[:50]}...')
" 2>nul

echo.
echo ========================================
echo 快速测试完成
echo ========================================
echo.

echo 如需详细测试，请运行:
echo - run_all_step_tests.bat (完整分步测试)
echo - fix_database_issues.bat (问题修复工具)
echo.

pause
