#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
下载离线依赖包
"""

import os
import sys
import subprocess
from pathlib import Path

def download_dependencies():
    """下载所有依赖包"""
    print("下载DataTransmission离线依赖包...")
    
    # 创建离线包目录
    offline_dir = Path("offline_packages")
    if offline_dir.exists():
        import shutil
        shutil.rmtree(offline_dir)
    offline_dir.mkdir()
    
    # 核心依赖列表
    dependencies = [
        "mysql-connector-python",
        "opencv-python",
        "qrcode[pil]", 
        "pyzbar",
        "Pillow",
        "Flask",
        "APScheduler",
        "numpy",
        "Werkzeug",
        "Jinja2",
        "MarkupSafe",
        "itsdangerous",
        "click",
        "blinker",
        "pytz",
        "tzlocal",
        "six",
        "setuptools",
        "wheel",
    ]
    
    print(f"将下载 {len(dependencies)} 个核心包...")
    
    # 下载每个依赖包及其依赖
    for i, dep in enumerate(dependencies, 1):
        print(f"\n[{i}/{len(dependencies)}] 下载 {dep}...")
        try:
            cmd = [
                sys.executable, "-m", "pip", "download",
                "--dest", str(offline_dir),
                dep
            ]
            result = subprocess.run(cmd, capture_output=True, text=True)
            if result.returncode == 0:
                print(f"✓ {dep} 下载成功")
            else:
                print(f"✗ {dep} 下载失败: {result.stderr}")
        except Exception as e:
            print(f"✗ {dep} 下载异常: {e}")
    
    # 统计下载的文件
    files = list(offline_dir.glob("*"))
    print(f"\n下载完成！共下载 {len(files)} 个文件")
    
    # 创建离线安装脚本
    install_script = f'''@echo off
chcp 65001 >nul
title 离线安装DataTransmission依赖

echo ========================================
echo    离线安装DataTransmission依赖
echo ========================================
echo.

echo 检查Python环境...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ✗ Python未安装或不在PATH中
    pause
    exit /b 1
)

echo ✓ Python环境正常
python --version

echo.
echo 开始离线安装依赖包...
echo 安装目录: {offline_dir}
echo.

python -m pip install --no-index --find-links {offline_dir} mysql-connector-python
python -m pip install --no-index --find-links {offline_dir} opencv-python
python -m pip install --no-index --find-links {offline_dir} qrcode[pil]
python -m pip install --no-index --find-links {offline_dir} pyzbar
python -m pip install --no-index --find-links {offline_dir} Pillow
python -m pip install --no-index --find-links {offline_dir} Flask
python -m pip install --no-index --find-links {offline_dir} APScheduler
python -m pip install --no-index --find-links {offline_dir} numpy

echo.
echo 验证安装...
python -c "import mysql.connector; print('✓ mysql.connector')" 2>nul || echo "✗ mysql.connector"
python -c "import cv2; print('✓ cv2')" 2>nul || echo "✗ cv2"
python -c "import qrcode; print('✓ qrcode')" 2>nul || echo "✗ qrcode"
python -c "import pyzbar; print('✓ pyzbar')" 2>nul || echo "✗ pyzbar"
python -c "import PIL; print('✓ PIL')" 2>nul || echo "✗ PIL"
python -c "import flask; print('✓ flask')" 2>nul || echo "✗ flask"
python -c "import apscheduler; print('✓ apscheduler')" 2>nul || echo "✗ apscheduler"
python -c "import numpy; print('✓ numpy')" 2>nul || echo "✗ numpy"

echo.
echo 离线安装完成！
pause
'''
    
    with open(offline_dir / "install_offline.bat", 'w', encoding='utf-8') as f:
        f.write(install_script)
    
    print(f"✓ 离线安装脚本已创建: {offline_dir}/install_offline.bat")
    
    return offline_dir

def create_requirements_txt():
    """创建requirements.txt文件"""
    requirements = """mysql-connector-python>=8.0.0
opencv-python>=4.5.0
qrcode[pil]>=7.0.0
pyzbar>=0.1.8
Pillow>=8.0.0
Flask>=2.0.0
APScheduler>=3.9.0
numpy>=1.21.0
Werkzeug>=2.0.0
Jinja2>=3.0.0
MarkupSafe>=2.0.0
itsdangerous>=2.0.0
click>=8.0.0
blinker>=1.4.0
pytz>=2021.1
tzlocal>=4.0.0
six>=1.16.0
"""
    
    with open("requirements.txt", 'w', encoding='utf-8') as f:
        f.write(requirements)
    
    print("✓ requirements.txt 已创建")

def main():
    """主函数"""
    print("=" * 50)
    print("DataTransmission 离线依赖包下载工具")
    print("=" * 50)
    
    try:
        # 创建requirements.txt
        create_requirements_txt()
        
        # 下载依赖包
        offline_dir = download_dependencies()
        
        print("\n" + "=" * 50)
        print("下载完成！")
        print("=" * 50)
        print(f"离线包目录: {offline_dir}")
        print("使用方法:")
        print("1. 将整个 offline_packages 文件夹复制到目标机器")
        print("2. 在目标机器上运行 offline_packages/install_offline.bat")
        print("3. 或者使用: pip install --no-index --find-links offline_packages -r requirements.txt")
        
    except Exception as e:
        print(f"下载过程中出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
