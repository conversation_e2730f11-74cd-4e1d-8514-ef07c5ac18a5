2025-07-02 17:59:27,295 - root - INFO - 正在初始化数据传输客户端...
2025-07-02 17:59:27,323 - root - INFO - 成功连接到MySQL数据库
2025-07-02 17:59:27,326 - root - INFO - 数据库表创建成功
2025-07-02 17:59:27,326 - root - INFO - 数据库管理器初始化完成
2025-07-02 17:59:27,328 - root - INFO - Web服务器初始化完成
2025-07-02 17:59:27,329 - root - INFO - 二维码生成器初始化完成
2025-07-02 17:59:27,329 - root - INFO - 摄像头监控初始化完成
2025-07-02 17:59:27,330 - root - INFO - 所有组件初始化完成
2025-07-02 17:59:27,330 - root - INFO - 正在启动所有服务...
2025-07-02 17:59:27,331 - root - INFO - 启动Web服务器，监听 0.0.0.0:5000
2025-07-02 17:59:27,331 - root - INFO - Web服务器已启动
2025-07-02 17:59:27,340 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://***********:5000
2025-07-02 17:59:27,341 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-02 17:59:29,333 - apscheduler.scheduler - INFO - Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-07-02 17:59:29,334 - apscheduler.scheduler - INFO - Added job "QRGenerator.process_transmission_data" to job store "default"
2025-07-02 17:59:29,334 - apscheduler.scheduler - INFO - Scheduler started
2025-07-02 17:59:29,335 - root - INFO - 二维码生成定时任务已启动
2025-07-02 17:59:29,335 - root - INFO - 二维码生成服务已启动
2025-07-02 17:59:32,336 - apscheduler.executors.default - INFO - Running job "QRGenerator.process_transmission_data (trigger: interval[0:00:03], next run at: 2025-07-02 17:59:32 CST)" (scheduled at 2025-07-02 17:59:32.333231+08:00)
2025-07-02 17:59:32,337 - apscheduler.executors.default - INFO - Job "QRGenerator.process_transmission_data (trigger: interval[0:00:03], next run at: 2025-07-02 17:59:35 CST)" executed successfully
2025-07-02 17:59:35,348 - apscheduler.executors.default - INFO - Running job "QRGenerator.process_transmission_data (trigger: interval[0:00:03], next run at: 2025-07-02 17:59:38 CST)" (scheduled at 2025-07-02 17:59:35.333231+08:00)
2025-07-02 17:59:35,349 - apscheduler.executors.default - INFO - Job "QRGenerator.process_transmission_data (trigger: interval[0:00:03], next run at: 2025-07-02 17:59:38 CST)" executed successfully
2025-07-02 17:59:38,335 - apscheduler.executors.default - INFO - Running job "QRGenerator.process_transmission_data (trigger: interval[0:00:03], next run at: 2025-07-02 17:59:41 CST)" (scheduled at 2025-07-02 17:59:38.333231+08:00)
2025-07-02 17:59:38,336 - apscheduler.executors.default - INFO - Job "QRGenerator.process_transmission_data (trigger: interval[0:00:03], next run at: 2025-07-02 17:59:41 CST)" executed successfully
2025-07-02 17:59:41,341 - apscheduler.executors.default - INFO - Running job "QRGenerator.process_transmission_data (trigger: interval[0:00:03], next run at: 2025-07-02 17:59:44 CST)" (scheduled at 2025-07-02 17:59:41.333231+08:00)
2025-07-02 17:59:41,342 - apscheduler.executors.default - INFO - Job "QRGenerator.process_transmission_data (trigger: interval[0:00:03], next run at: 2025-07-02 17:59:44 CST)" executed successfully
2025-07-02 17:59:44,243 - root - INFO - 接收到信号 2，正在停止程序...
2025-07-02 18:00:00,470 - root - INFO - 正在初始化数据传输客户端...
2025-07-02 18:00:00,496 - root - INFO - 成功连接到MySQL数据库
2025-07-02 18:00:00,499 - root - INFO - 数据库表创建成功
2025-07-02 18:00:00,500 - root - INFO - 数据库管理器初始化完成
2025-07-02 18:00:00,502 - root - INFO - Web服务器初始化完成
2025-07-02 18:00:00,502 - root - INFO - 二维码生成器初始化完成
2025-07-02 18:00:00,503 - root - INFO - 摄像头监控初始化完成
2025-07-02 18:00:00,503 - root - INFO - 所有组件初始化完成
2025-07-02 18:00:00,503 - root - INFO - 正在启动所有服务...
2025-07-02 18:00:00,504 - root - INFO - 启动Web服务器，监听 0.0.0.0:5000
2025-07-02 18:00:00,504 - root - INFO - Web服务器已启动
2025-07-02 18:00:00,513 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://***********:5000
2025-07-02 18:00:00,513 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-02 18:00:02,505 - apscheduler.scheduler - INFO - Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-07-02 18:00:02,506 - apscheduler.scheduler - INFO - Added job "QRGenerator.process_transmission_data" to job store "default"
2025-07-02 18:00:02,506 - apscheduler.scheduler - INFO - Scheduler started
2025-07-02 18:00:02,507 - root - INFO - 二维码生成定时任务已启动
2025-07-02 18:00:02,507 - root - INFO - 二维码生成服务已启动
2025-07-02 18:00:05,520 - apscheduler.executors.default - INFO - Running job "QRGenerator.process_transmission_data (trigger: interval[0:00:03], next run at: 2025-07-02 18:00:05 CST)" (scheduled at 2025-07-02 18:00:05.505295+08:00)
2025-07-02 18:00:05,522 - apscheduler.executors.default - INFO - Job "QRGenerator.process_transmission_data (trigger: interval[0:00:03], next run at: 2025-07-02 18:00:08 CST)" executed successfully
2025-07-02 18:00:08,520 - apscheduler.executors.default - INFO - Running job "QRGenerator.process_transmission_data (trigger: interval[0:00:03], next run at: 2025-07-02 18:00:11 CST)" (scheduled at 2025-07-02 18:00:08.505295+08:00)
2025-07-02 18:00:08,521 - apscheduler.executors.default - INFO - Job "QRGenerator.process_transmission_data (trigger: interval[0:00:03], next run at: 2025-07-02 18:00:11 CST)" executed successfully
2025-07-02 18:01:30,313 - root - INFO - 正在初始化数据传输客户端...
2025-07-02 18:01:30,338 - root - INFO - 成功连接到MySQL数据库
2025-07-02 18:01:30,342 - root - INFO - 数据库表创建成功
2025-07-02 18:01:30,342 - root - INFO - 数据库管理器初始化完成
2025-07-02 18:01:30,344 - root - INFO - Web服务器初始化完成
2025-07-02 18:01:30,345 - root - INFO - 二维码生成器初始化完成
2025-07-02 18:01:30,345 - root - INFO - 摄像头监控初始化完成
2025-07-02 18:01:30,345 - root - INFO - 所有组件初始化完成
2025-07-02 18:01:30,345 - root - INFO - 正在启动所有服务...
2025-07-02 18:01:30,346 - root - INFO - 启动Web服务器，监听 0.0.0.0:5000
2025-07-02 18:01:30,346 - root - INFO - Web服务器已启动
2025-07-02 18:01:30,355 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://***********:5000
2025-07-02 18:01:30,355 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-02 18:01:32,350 - apscheduler.scheduler - INFO - Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-07-02 18:01:32,352 - apscheduler.scheduler - INFO - Added job "QRGenerator.process_transmission_data" to job store "default"
2025-07-02 18:01:32,352 - apscheduler.scheduler - INFO - Scheduler started
2025-07-02 18:01:32,353 - root - INFO - 二维码生成定时任务已启动
2025-07-02 18:01:32,355 - root - INFO - 二维码生成服务已启动
2025-07-02 18:01:35,357 - apscheduler.executors.default - INFO - Running job "QRGenerator.process_transmission_data (trigger: interval[0:00:03], next run at: 2025-07-02 18:01:35 CST)" (scheduled at 2025-07-02 18:01:35.348997+08:00)
2025-07-02 18:01:35,358 - apscheduler.executors.default - INFO - Job "QRGenerator.process_transmission_data (trigger: interval[0:00:03], next run at: 2025-07-02 18:01:38 CST)" executed successfully
2025-07-02 18:01:38,358 - apscheduler.executors.default - INFO - Running job "QRGenerator.process_transmission_data (trigger: interval[0:00:03], next run at: 2025-07-02 18:01:41 CST)" (scheduled at 2025-07-02 18:01:38.348997+08:00)
2025-07-02 18:01:38,359 - apscheduler.executors.default - INFO - Job "QRGenerator.process_transmission_data (trigger: interval[0:00:03], next run at: 2025-07-02 18:01:41 CST)" executed successfully
2025-07-02 18:01:41,364 - apscheduler.executors.default - INFO - Running job "QRGenerator.process_transmission_data (trigger: interval[0:00:03], next run at: 2025-07-02 18:01:44 CST)" (scheduled at 2025-07-02 18:01:41.348997+08:00)
2025-07-02 18:01:41,365 - apscheduler.executors.default - INFO - Job "QRGenerator.process_transmission_data (trigger: interval[0:00:03], next run at: 2025-07-02 18:01:44 CST)" executed successfully
2025-07-02 18:01:44,356 - apscheduler.executors.default - INFO - Running job "QRGenerator.process_transmission_data (trigger: interval[0:00:03], next run at: 2025-07-02 18:01:47 CST)" (scheduled at 2025-07-02 18:01:44.348997+08:00)
2025-07-02 18:01:44,357 - apscheduler.executors.default - INFO - Job "QRGenerator.process_transmission_data (trigger: interval[0:00:03], next run at: 2025-07-02 18:01:47 CST)" executed successfully
2025-07-02 18:01:46,911 - root - INFO - 摄像头 0 初始化成功
2025-07-02 18:01:46,911 - root - INFO - 开始摄像头监控
2025-07-02 18:01:46,912 - root - INFO - 启动预览窗口线程
2025-07-02 18:01:46,912 - root - INFO - 预览窗口线程已启动
2025-07-02 18:01:46,912 - root - INFO - 摄像头监控已启动
2025-07-02 18:01:46,912 - root - INFO - 摄像头监控服务已启动
2025-07-02 18:01:46,912 - root - INFO - 所有服务启动完成
2025-07-02 18:01:46,913 - root - INFO - 数据传输客户端正在运行...
2025-07-02 18:01:46,913 - root - INFO - 按 Ctrl+C 停止程序
2025-07-02 18:01:46,942 - root - INFO - 摄像头预览窗口已创建: (320, 240)
2025-07-02 18:01:47,358 - apscheduler.executors.default - INFO - Running job "QRGenerator.process_transmission_data (trigger: interval[0:00:03], next run at: 2025-07-02 18:01:50 CST)" (scheduled at 2025-07-02 18:01:47.348997+08:00)
2025-07-02 18:01:47,359 - apscheduler.executors.default - INFO - Job "QRGenerator.process_transmission_data (trigger: interval[0:00:03], next run at: 2025-07-02 18:01:50 CST)" executed successfully
2025-07-02 18:01:50,350 - apscheduler.executors.default - INFO - Running job "QRGenerator.process_transmission_data (trigger: interval[0:00:03], next run at: 2025-07-02 18:01:53 CST)" (scheduled at 2025-07-02 18:01:50.348997+08:00)
2025-07-02 18:01:50,352 - apscheduler.executors.default - INFO - Job "QRGenerator.process_transmission_data (trigger: interval[0:00:03], next run at: 2025-07-02 18:01:53 CST)" executed successfully
2025-07-02 18:01:53,349 - apscheduler.executors.default - INFO - Running job "QRGenerator.process_transmission_data (trigger: interval[0:00:03], next run at: 2025-07-02 18:01:56 CST)" (scheduled at 2025-07-02 18:01:53.348997+08:00)
2025-07-02 18:01:53,350 - apscheduler.executors.default - INFO - Job "QRGenerator.process_transmission_data (trigger: interval[0:00:03], next run at: 2025-07-02 18:01:56 CST)" executed successfully
2025-07-02 18:01:56,351 - apscheduler.executors.default - INFO - Running job "QRGenerator.process_transmission_data (trigger: interval[0:00:03], next run at: 2025-07-02 18:01:59 CST)" (scheduled at 2025-07-02 18:01:56.348997+08:00)
2025-07-02 18:01:56,353 - apscheduler.executors.default - INFO - Job "QRGenerator.process_transmission_data (trigger: interval[0:00:03], next run at: 2025-07-02 18:01:59 CST)" executed successfully
2025-07-02 18:23:16,313 - root - INFO - 正在初始化数据传输客户端...
2025-07-02 18:23:16,338 - root - INFO - 成功连接到MySQL数据库
2025-07-02 18:23:16,346 - root - INFO - 数据库表创建成功
2025-07-02 18:23:16,346 - root - INFO - 数据库管理器初始化完成
2025-07-02 18:23:16,346 - root - INFO - Web服务器初始化完成
2025-07-02 18:23:16,346 - root - INFO - 二维码生成器初始化完成
2025-07-02 18:23:16,346 - root - INFO - 摄像头监控初始化完成
2025-07-02 18:23:16,346 - root - INFO - 所有组件初始化完成
2025-07-02 18:23:16,346 - root - INFO - 正在启动所有服务...
2025-07-02 18:23:16,346 - root - INFO - 启动Web服务器，监听 0.0.0.0:5000
2025-07-02 18:23:16,346 - root - INFO - Web服务器已启动
2025-07-02 18:23:16,363 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://***********:5000
2025-07-02 18:23:16,363 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-02 18:23:17,418 - root - INFO - 接收到信号 2，正在停止程序...
