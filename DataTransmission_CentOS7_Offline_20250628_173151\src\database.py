import mysql.connector
from mysql.connector import Error
import logging
from config import DATABASE_CONFIG
import time

class DatabaseManager:
    def __init__(self):
        self.connection = None
        self.connect()
        self.create_tables()
    
    def connect(self):
        """连接到MySQL数据库"""
        try:
            self.connection = mysql.connector.connect(**DATABASE_CONFIG)
            if self.connection.is_connected():
                logging.info("成功连接到MySQL数据库")
        except Error as e:
            logging.error(f"连接数据库时出错: {e}")
            raise
    
    def create_tables(self):
        """创建数据库表"""
        cursor = self.connection.cursor()
        
        # 创建receive_data表
        create_receive_data_table = """
        CREATE TABLE IF NOT EXISTS receive_data (
            id VARCHAR(255) PRIMARY KEY,
            type INT NOT NULL,
            data VARCHAR(1000) NOT NULL
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8;
        """
        
        # 创建transmission_data表
        create_transmission_data_table = """
        CREATE TABLE IF NOT EXISTS transmission_data (
            id VARCHAR(255) NOT NULL,
            type INT NOT NULL,
            data VARCHAR(1000) NOT NULL,
            status INT DEFAULT 0,
            PRIMARY KEY (id, type)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8;
        """
        
        try:
            cursor.execute(create_receive_data_table)
            cursor.execute(create_transmission_data_table)
            self.connection.commit()
            logging.info("数据库表创建成功")
        except Error as e:
            logging.error(f"创建表时出错: {e}")
            raise
        finally:
            cursor.close()
    
    def insert_transmission_data(self, id_val, type_val, data_val):
        """插入数据到transmission_data表"""
        cursor = self.connection.cursor()
        
        # 检查数据是否已存在
        check_query = "SELECT COUNT(*) FROM transmission_data WHERE id = %s AND type = %s AND status = 0"
        cursor.execute(check_query, (id_val, type_val))
        count = cursor.fetchone()[0]
        
        if count > 0:
            logging.info(f"数据已存在，丢弃: id={id_val}, type={type_val}")
            cursor.close()
            return False
        
        # 插入新数据
        insert_query = "INSERT INTO transmission_data (id, type, data, status) VALUES (%s, %s, %s, 0)"
        try:
            cursor.execute(insert_query, (id_val, type_val, data_val))
            self.connection.commit()
            logging.info(f"数据插入成功: id={id_val}, type={type_val}")
            return True
        except Error as e:
            logging.error(f"插入数据时出错: {e}")
            return False
        finally:
            cursor.close()
    
    def get_next_transmission_data(self):
        """获取下一条待传输的数据"""
        cursor = self.connection.cursor()
        
        query = "SELECT id, type, data FROM transmission_data WHERE status = 0 ORDER BY id LIMIT 1"
        try:
            cursor.execute(query)
            result = cursor.fetchone()
            return result
        except Error as e:
            logging.error(f"查询数据时出错: {e}")
            return None
        finally:
            cursor.close()
    
    def update_transmission_status(self, id_val, type_val):
        """更新传输数据状态为已处理"""
        cursor = self.connection.cursor()
        
        update_query = "UPDATE transmission_data SET status = 1 WHERE id = %s AND type = %s"
        try:
            cursor.execute(update_query, (id_val, type_val))
            self.connection.commit()
            logging.info(f"状态更新成功: id={id_val}, type={type_val}")
            return True
        except Error as e:
            logging.error(f"更新状态时出错: {e}")
            return False
        finally:
            cursor.close()
    
    def insert_receive_data(self, id_val, type_val, data_val):
        """插入数据到receive_data表"""
        cursor = self.connection.cursor()
        
        # 检查数据是否已存在
        check_query = "SELECT COUNT(*) FROM receive_data WHERE id = %s AND type = %s"
        cursor.execute(check_query, (id_val, type_val))
        count = cursor.fetchone()[0]
        
        if count > 0:
            logging.info(f"接收数据已存在，丢弃: id={id_val}, type={type_val}")
            cursor.close()
            return False
        
        # 插入新数据
        insert_query = "INSERT INTO receive_data (id, type, data) VALUES (%s, %s, %s)"
        try:
            cursor.execute(insert_query, (id_val, type_val, data_val))
            self.connection.commit()
            logging.info(f"接收数据插入成功: id={id_val}, type={type_val}")
            return True
        except Error as e:
            logging.error(f"插入接收数据时出错: {e}")
            return False
        finally:
            cursor.close()
    
    def close(self):
        """关闭数据库连接"""
        if self.connection and self.connection.is_connected():
            self.connection.close()
            logging.info("数据库连接已关闭")
