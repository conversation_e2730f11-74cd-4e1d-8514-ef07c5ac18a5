@echo off
chcp 65001 >nul
echo ========================================
echo   DataTransmission CentOS 7 离线程序包创建
echo   基于Conda环境 - 桌面版本
echo ========================================
echo.

REM 检查是否在conda环境中
conda info --envs >nul 2>&1
if errorlevel 1 (
    echo 错误: 未检测到conda环境，请确保已安装Anaconda
    pause
    exit /b 1
)

REM 获取当前激活的环境名
for /f "tokens=1" %%i in ('conda info --envs ^| findstr "*"') do set CURRENT_ENV=%%i

if "%CURRENT_ENV%"=="" (
    echo 警告: 未检测到激活的conda环境
    echo 请先激活您的项目环境: conda activate your_env_name
    pause
    exit /b 1
)

echo 当前激活的环境: %CURRENT_ENV%
echo 目标系统: CentOS 7 桌面版本
echo 打包类型: 离线可执行程序
echo.

REM 确认是否继续
set /p confirm="是否使用当前环境创建CentOS 7离线程序包? (y/N): "
if /i not "%confirm%"=="y" (
    echo 操作已取消
    pause
    exit /b 0
)

REM 创建CentOS 7离线程序部署目录
set DEPLOY_DIR=DataTransmission_CentOS7_Offline
if exist %DEPLOY_DIR% (
    echo 删除现有部署目录...
    rmdir /s /q %DEPLOY_DIR%
)

echo 创建CentOS 7离线程序部署目录: %DEPLOY_DIR%
mkdir %DEPLOY_DIR%
mkdir %DEPLOY_DIR%\src
mkdir %DEPLOY_DIR%\conda_env
mkdir %DEPLOY_DIR%\scripts
mkdir %DEPLOY_DIR%\config

REM 导出conda环境配置（跨平台兼容）
echo 导出conda环境配置（CentOS 7兼容）...
conda env export --no-builds --from-history > %DEPLOY_DIR%\conda_env\environment.yml
if errorlevel 1 (
    echo 错误: 环境配置导出失败
    pause
    exit /b 1
)

REM 导出详细的包列表
echo 导出详细包信息...
conda list --export > %DEPLOY_DIR%\conda_env\conda_packages.txt
pip freeze > %DEPLOY_DIR%\conda_env\pip_packages.txt

REM 安装conda-pack用于环境打包
echo 安装conda-pack...
conda install conda-pack -y >nul 2>&1
pip install conda-pack >nul 2>&1

REM 打包整个conda环境
echo 打包conda环境（这可能需要几分钟）...
conda pack -n %CURRENT_ENV% -o %DEPLOY_DIR%\conda_env\datatransmission_env.tar.gz
if errorlevel 1 (
    echo 警告: conda-pack打包失败，将使用传统方式
    echo 创建环境重建脚本...
) else (
    echo ✓ conda环境打包成功
)

REM 复制项目文件
echo 复制项目源代码...
copy *.py %DEPLOY_DIR%\src\ >nul 2>&1
copy config.py %DEPLOY_DIR%\src\ >nul 2>&1
copy README.md %DEPLOY_DIR%\ >nul 2>&1

REM 创建CentOS 7离线安装脚本
echo 创建CentOS 7离线安装脚本...
(
echo #!/bin/bash
echo # DataTransmission CentOS 7 离线程序安装脚本
echo # 基于conda环境的离线可执行程序
echo.
echo set -e  # 遇到错误立即退出
echo.
echo echo "=== DataTransmission CentOS 7 离线程序安装开始 ==="
echo.
echo # 检查系统版本
echo if ! grep -q "CentOS Linux release 7" /etc/redhat-release 2^>/dev/null; then
echo     echo "警告: 此脚本专为CentOS 7设计"
echo     echo "当前系统: $(cat /etc/redhat-release 2^>/dev/null ^|^| echo '未知')"
echo     read -p "是否继续? (y/N): " -n 1 -r
echo     echo
echo     if [[ ! $REPLY =~ ^[Yy]$ ]]; then
echo         exit 1
echo     fi
echo fi
echo.
echo # 设置变量
echo PROJECT_NAME="DataTransmission"
echo ENV_NAME="datatransmission"
echo INSTALL_DIR="/opt/$PROJECT_NAME"
echo CONDA_DIR="/opt/miniconda3"
echo CURRENT_DIR="$(pwd^)"
echo.
echo # 检查是否为CentOS 7桌面版本
echo echo "检查系统环境..."
echo if ! grep -q "CentOS Linux release 7" /etc/redhat-release 2^>/dev/null; then
echo     echo "警告: 此脚本专为CentOS 7设计"
echo     echo "当前系统: $(cat /etc/redhat-release 2^>/dev/null ^|^| echo '未知')"
echo     read -p "是否继续安装? (y/N): " -n 1 -r
echo     echo
echo     if [[ ! $REPLY =~ ^[Yy]$ ]]; then
echo         exit 1
echo     fi
echo fi
echo.
echo # 检查桌面环境
echo if [ -z "$DISPLAY" ]; then
echo     echo "警告: 未检测到图形桌面环境"
echo     echo "请确保在CentOS 7桌面版本中运行此程序"
echo     read -p "是否继续? (y/N): " -n 1 -r
echo     echo
echo     if [[ ! $REPLY =~ ^[Yy]$ ]]; then
echo         exit 1
echo     fi
echo fi
echo.
echo # 检查root权限
echo if [ "$EUID" -ne 0 ]; then
echo     echo "此脚本需要root权限，请使用sudo运行"
echo     exit 1
echo fi
echo.
echo # 安装系统依赖
echo echo "安装CentOS 7系统依赖..."
echo yum update -y
echo yum install -y epel-release
echo yum groupinstall -y "Development Tools"
echo yum install -y wget curl vim
echo.
echo # 安装图形界面相关依赖
echo yum install -y python3-tkinter
echo yum install -y libX11-devel libXext-devel libXrender-devel
echo yum install -y libICE-devel libSM-devel
echo yum install -y gtk3-devel
echo.
echo # 安装二维码和摄像头相关依赖
echo yum install -y zbar zbar-devel
echo yum install -y mesa-libGL mesa-libGL-devel glib2-devel
echo yum install -y libpng-devel libjpeg-turbo-devel
echo.
echo # 安装数据库
echo yum install -y mariadb-server mariadb mariadb-devel
echo systemctl start mariadb
echo systemctl enable mariadb
echo.
echo # 安装或使用conda环境
echo echo "设置conda环境..."
echo.
echo # 检查是否已有conda环境包
echo if [ -f "conda_env/datatransmission_env.tar.gz" ]; then
echo     echo "发现conda环境包，进行离线安装..."
echo
echo     # 检查是否已安装miniconda
echo     if [ ! -d "$CONDA_DIR" ]; then
echo         echo "安装Miniconda..."
echo         cd /tmp
echo
echo         # 下载Miniconda（如果有网络）
echo         if ! wget -q --spider https://repo.anaconda.com/miniconda/Miniconda3-latest-Linux-x86_64.sh; then
echo             echo "无网络连接，请手动下载Miniconda到/tmp目录"
echo             echo "下载地址: https://repo.anaconda.com/miniconda/Miniconda3-latest-Linux-x86_64.sh"
echo             exit 1
echo         fi
echo
echo         wget -O Miniconda3-latest-Linux-x86_64.sh \
echo              "https://repo.anaconda.com/miniconda/Miniconda3-latest-Linux-x86_64.sh"
echo
echo         # 安装Miniconda
echo         sudo bash Miniconda3-latest-Linux-x86_64.sh -b -p $CONDA_DIR
echo         sudo chown -R $USER:$USER $CONDA_DIR
echo
echo         # 设置环境变量
echo         echo 'export PATH="'$CONDA_DIR'/bin:$PATH"' ^>^> ~/.bashrc
echo         export PATH="$CONDA_DIR/bin:$PATH"
echo
echo         # 初始化conda
echo         $CONDA_DIR/bin/conda init bash
echo         source ~/.bashrc
echo
echo         cd $CURRENT_DIR
echo     fi
echo
echo     # 解压conda环境包
echo     echo "解压conda环境包..."
echo     sudo mkdir -p $CONDA_DIR/envs/$ENV_NAME
echo     sudo tar -xzf conda_env/datatransmission_env.tar.gz -C $CONDA_DIR/envs/$ENV_NAME
echo     sudo chown -R $USER:$USER $CONDA_DIR/envs/$ENV_NAME
echo
echo     # 修复环境路径
echo     echo "修复环境路径..."
echo     source $CONDA_DIR/etc/profile.d/conda.sh
echo     conda activate $ENV_NAME
echo     conda-unpack 2^>/dev/null ^|^| echo "环境已准备就绪"
echo
echo else
echo     echo "未找到conda环境包，使用环境配置文件重建..."
echo
echo     # 安装Miniconda（如果需要）
echo     if [ ! -d "$CONDA_DIR" ]; then
echo         echo "请先安装Miniconda或将conda环境包放到conda_env目录"
echo         exit 1
echo     fi
echo
echo     # 从配置文件创建环境
echo     source $CONDA_DIR/etc/profile.d/conda.sh
echo     conda env create -f conda_env/environment.yml -n $ENV_NAME
echo fi
echo.
echo # 激活conda
echo source $ANACONDA_DIR/etc/profile.d/conda.sh 2^>/dev/null ^|^| source $(conda info --base^)/etc/profile.d/conda.sh
echo.
echo # 创建conda环境
echo echo "创建conda环境: $ENV_NAME"
echo if conda env list ^| grep -q "^$ENV_NAME "; then
echo     echo "环境已存在，删除重建..."
echo     conda env remove -n $ENV_NAME -y
echo fi
echo.
echo # 从yml文件创建环境
echo echo "从环境配置文件创建环境..."
echo conda env create -f datatransmission_environment.yml -n $ENV_NAME
echo.
echo # 激活环境并安装额外依赖
echo echo "激活环境并安装依赖..."
echo conda activate $ENV_NAME
echo pip install --upgrade pip
echo pip install -r requirements.txt
echo.
echo # 创建项目目录和安装程序
echo echo "安装程序到: $INSTALL_DIR"
echo sudo mkdir -p $INSTALL_DIR
echo sudo cp -r src/* $INSTALL_DIR/
echo sudo cp -r config/* $INSTALL_DIR/ 2^>/dev/null ^|^| echo "无额外配置文件"
echo.
echo # 获取当前用户
echo REAL_USER=$(whoami^)
echo sudo chown -R $REAL_USER:$REAL_USER $INSTALL_DIR
echo.
echo # 创建conda环境激活脚本
echo echo "创建环境激活脚本..."
echo cat ^> $INSTALL_DIR/activate_env.sh ^<^< 'ENVEOF'
echo #!/bin/bash
echo # 激活DataTransmission conda环境
echo export PATH="$CONDA_DIR/bin:$PATH"
echo source $CONDA_DIR/etc/profile.d/conda.sh
echo conda activate $ENV_NAME
echo echo "DataTransmission环境已激活"
echo echo "Python路径: $(which python^)"
echo echo "环境路径: $CONDA_PREFIX"
echo ENVEOF
echo chmod +x $INSTALL_DIR/activate_env.sh
echo.
echo # 创建CentOS 7专用启动脚本
echo echo "创建CentOS 7启动脚本..."
echo cat ^> $INSTALL_DIR/start_datatransmission.sh ^<^< 'STARTEOF'
echo #!/bin/bash
echo # DataTransmission CentOS 7 桌面版启动脚本
echo.
echo echo "=== DataTransmission 启动中 ==="
echo.
echo # 设置变量
echo CONDA_DIR="/opt/miniconda3"
echo ENV_NAME="datatransmission"
echo INSTALL_DIR="/opt/DataTransmission"
echo.
echo # 检查桌面环境
echo if [ -z "$DISPLAY" ]; then
echo     echo "错误: 未检测到图形桌面环境"
echo     echo "请在CentOS 7桌面环境中运行此程序"
echo     exit 1
echo fi
echo.
echo # 激活conda环境
echo echo "激活conda环境..."
echo export PATH="$CONDA_DIR/bin:$PATH"
echo source $CONDA_DIR/etc/profile.d/conda.sh 2^>/dev/null ^|^| {
echo     echo "错误: 无法找到conda环境"
echo     echo "请确保conda已正确安装"
echo     exit 1
echo }
echo.
echo conda activate $ENV_NAME ^|^| {
echo     echo "错误: 无法激活环境 $ENV_NAME"
echo     echo "可用环境:"
echo     conda env list
echo     exit 1
echo }
echo.
echo echo "✓ conda环境已激活: $(conda info --envs ^| grep '*'^)"
echo echo "✓ Python路径: $(which python^)"
echo.
echo # 设置显示和库路径
echo export DISPLAY=${DISPLAY:-:0.0}
echo export LD_LIBRARY_PATH=/usr/lib64:/usr/local/lib:$LD_LIBRARY_PATH
echo.
echo # 切换到项目目录
echo cd $INSTALL_DIR ^|^| {
echo     echo "错误: 无法进入项目目录 $INSTALL_DIR"
echo     exit 1
echo }
echo.
echo echo "✓ 工作目录: $(pwd^)"
echo echo "✓ 显示环境: $DISPLAY"
echo.
echo # 检查主程序文件
echo if [ ! -f "main.py" ]; then
echo     echo "错误: 未找到主程序文件 main.py"
echo     echo "当前目录内容:"
echo     ls -la
echo     exit 1
echo fi
echo.
echo echo "启动DataTransmission程序..."
echo echo "注意: 程序将在桌面环境中显示二维码和摄像头预览"
echo echo "按Ctrl+C停止程序"
echo echo ""
echo.
echo # 启动程序
echo python main.py
echo STARTEOF
echo.
echo chmod +x $INSTALL_DIR/start_datatransmission.sh
echo.
echo # 创建桌面快捷方式
echo echo "创建桌面快捷方式..."
echo DESKTOP_FILE="/home/<USER>/Desktop/DataTransmission.desktop"
echo cat ^> $DESKTOP_FILE ^<^< 'DESKTOPEOF'
echo [Desktop Entry]
echo Version=1.0
echo Type=Application
echo Name=DataTransmission
echo Comment=数据传输客户端 - 二维码生成和识别
echo Exec=/opt/DataTransmission/start_datatransmission.sh
echo Icon=/opt/DataTransmission/icon.png
echo Terminal=true
echo StartupNotify=true
echo Categories=Application;Network;
echo Keywords=QR;Data;Transmission;
echo DESKTOPEOF
echo.
echo chmod +x $DESKTOP_FILE
echo chown $REAL_USER:$REAL_USER $DESKTOP_FILE
echo.
echo # 创建系统服务（可选）
echo echo "创建系统服务..."
echo sudo cat ^> /etc/systemd/system/datatransmission.service ^<^< 'SERVICEEOF'
echo [Unit]
echo Description=DataTransmission Client (CentOS 7)
echo After=network.target mariadb.service graphical-session.target
echo Wants=graphical-session.target
echo.
echo [Service]
echo Type=simple
echo User=$REAL_USER
echo Group=$REAL_USER
echo WorkingDirectory=/opt/DataTransmission
echo Environment=DISPLAY=:0.0
echo Environment=LD_LIBRARY_PATH=/usr/lib64:/usr/local/lib
echo Environment=PATH=/opt/miniconda3/bin:/usr/local/bin:/usr/bin:/bin
echo ExecStartPre=/bin/bash -c 'source /opt/miniconda3/etc/profile.d/conda.sh ^&^& conda activate datatransmission'
echo ExecStart=/opt/DataTransmission/start_datatransmission.sh
echo Restart=on-failure
echo RestartSec=10
echo StandardOutput=journal
echo StandardError=journal
echo.
echo [Install]
echo WantedBy=graphical-session.target
echo SERVICEEOF
echo.
echo # 重新加载systemd
echo systemctl daemon-reload
echo.
echo # 配置防火墙
echo if systemctl is-active --quiet firewalld; then
echo     echo "配置防火墙..."
echo     firewall-cmd --permanent --add-port=5000/tcp
echo     firewall-cmd --reload
echo fi
echo.
echo echo "=== 部署完成 ==="
echo echo ""
echo echo "下一步操作："
echo echo "1. 配置MariaDB数据库:"
echo echo "   mysql_secure_installation"
echo echo ""
echo echo "2. 创建数据库和用户:"
echo echo "   mysql -u root -p"
echo echo "   CREATE DATABASE DataTransmission CHARACTER SET utf8 COLLATE utf8_general_ci;"
echo echo "   CREATE USER 'datatrans'@'localhost' IDENTIFIED BY 'JKga#123';"
echo echo "   GRANT ALL PRIVILEGES ON DataTransmission.* TO 'datatrans'@'localhost';"
echo echo "   FLUSH PRIVILEGES;"
echo echo "   EXIT;"
echo echo ""
echo echo "3. 编辑配置文件:"
echo echo "   vi /opt/DataTransmission/config.py"
echo echo ""
echo echo "4. 启动服务:"
echo echo "   systemctl start datatransmission"
echo echo "   systemctl enable datatransmission"
echo echo ""
echo echo "5. 检查状态:"
echo echo "   systemctl status datatransmission"
echo echo "   curl http://localhost:5000/health"
) > %DEPLOY_DIR%\deploy_centos7.sh

REM 创建使用说明
echo 创建使用说明文档...
(
echo # CentOS 7 部署说明
echo.
echo ## 文件说明
echo - datatransmission_environment.yml: conda环境配置文件
echo - requirements.txt: pip依赖列表
echo - deploy_centos7.sh: 自动部署脚本
echo - src/: 项目源代码
echo.
echo ## 部署步骤
echo.
echo ### 1. 传输文件到CentOS 7
echo 将整个 DataTransmission_CentOS7_Deploy 文件夹传输到CentOS 7机器的 /tmp 目录
echo.
echo ### 2. 执行部署
echo ```bash
echo cd /tmp/DataTransmission_CentOS7_Deploy
echo chmod +x deploy_centos7.sh
echo sudo ./deploy_centos7.sh
echo ```
echo.
echo ### 3. 配置数据库
echo ```bash
echo sudo mysql_secure_installation
echo mysql -u root -p
echo CREATE DATABASE DataTransmission CHARACTER SET utf8 COLLATE utf8_general_ci;
echo CREATE USER 'datatrans'@'localhost' IDENTIFIED BY 'JKga#123';
echo GRANT ALL PRIVILEGES ON DataTransmission.* TO 'datatrans'@'localhost';
echo FLUSH PRIVILEGES;
echo EXIT;
echo ```
echo.
echo ### 4. 配置应用
echo ```bash
echo sudo vi /opt/DataTransmission/config.py
echo # 修改数据库连接信息
echo ```
echo.
echo ### 5. 启动服务
echo ```bash
echo sudo systemctl start datatransmission
echo sudo systemctl enable datatransmission
echo sudo systemctl status datatransmission
echo ```
echo.
echo ### 6. 测试功能
echo ```bash
echo curl http://localhost:5000/health
echo ```
echo.
echo ## 故障排除
echo - 查看日志: sudo journalctl -u datatransmission -f
echo - 手动启动: cd /opt/DataTransmission ^&^& ./start_centos7.sh
echo - 检查环境: conda activate datatransmission ^&^& conda list
) > %DEPLOY_DIR%\README_DEPLOY.md

echo.
echo ========================================
echo           部署包创建完成！
echo ========================================
echo.
echo 生成的文件：
echo - %DEPLOY_DIR%\datatransmission_environment.yml
echo - %DEPLOY_DIR%\requirements.txt  
echo - %DEPLOY_DIR%\deploy_centos7.sh
echo - %DEPLOY_DIR%\src\               (项目源代码)
echo - %DEPLOY_DIR%\README_DEPLOY.md   (部署说明)
echo.
echo 下一步操作：
echo 1. 将 %DEPLOY_DIR% 文件夹传输到CentOS 7机器
echo 2. 在CentOS 7上运行: sudo ./deploy_centos7.sh
echo 3. 按照 README_DEPLOY.md 完成配置
echo.
echo 按任意键退出...
pause >nul
