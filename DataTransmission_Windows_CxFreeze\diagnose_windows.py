#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Windows环境诊断脚本
用于排查DataTransmission程序运行问题
"""

import sys
import os
import traceback
import logging

# 配置日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('diagnose.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)

def check_python_environment():
    """检查Python环境"""
    print("=" * 50)
    print("检查Python环境")
    print("=" * 50)
    
    print(f"Python版本: {sys.version}")
    print(f"Python路径: {sys.executable}")
    print(f"当前工作目录: {os.getcwd()}")
    print(f"Python路径: {sys.path}")
    print()

def check_required_modules():
    """检查必需的模块"""
    print("=" * 50)
    print("检查必需的模块")
    print("=" * 50)
    
    required_modules = {
        'mysql.connector': 'MySQL连接器',
        'cv2': 'OpenCV',
        'qrcode': '二维码生成',
        'pyzbar': '二维码识别',
        'PIL': 'Pillow图像处理',
        'flask': 'Flask Web框架',
        'apscheduler': '任务调度器',
        'numpy': 'NumPy数值计算',
        'logging': '日志模块',
        'threading': '线程模块',
        'time': '时间模块',
        'json': 'JSON模块',
        'os': '操作系统模块',
        'sys': '系统模块'
    }
    
    missing_modules = []
    
    for module_name, description in required_modules.items():
        try:
            __import__(module_name)
            print(f"✓ {module_name} ({description})")
        except ImportError as e:
            missing_modules.append(module_name)
            print(f"✗ {module_name} ({description}) - 错误: {e}")
    
    if missing_modules:
        print(f"\n缺少模块: {missing_modules}")
        return False
    else:
        print("\n✓ 所有必需模块都可用")
        return True

def check_config_file():
    """检查配置文件"""
    print("=" * 50)
    print("检查配置文件")
    print("=" * 50)
    
    config_files = ['config.py', 'config_windows.py']
    
    for config_file in config_files:
        if os.path.exists(config_file):
            print(f"✓ 找到配置文件: {config_file}")
            try:
                # 尝试导入配置
                if config_file == 'config.py':
                    import config
                    print(f"✓ 配置文件导入成功")
                    
                    # 检查数据库配置
                    if hasattr(config, 'DATABASE_CONFIG'):
                        db_config = config.DATABASE_CONFIG
                        print(f"数据库配置:")
                        print(f"  主机: {db_config.get('host', 'N/A')}")
                        print(f"  端口: {db_config.get('port', 'N/A')}")
                        print(f"  数据库: {db_config.get('database', 'N/A')}")
                        print(f"  用户: {db_config.get('user', 'N/A')}")
                        print(f"  密码: {'***' if db_config.get('password') else '未设置'}")
                    else:
                        print("⚠️  配置文件中缺少DATABASE_CONFIG")
                        
            except Exception as e:
                print(f"✗ 配置文件导入失败: {e}")
                return False
        else:
            print(f"✗ 配置文件不存在: {config_file}")
    
    return True

def test_database_connection():
    """测试数据库连接"""
    print("=" * 50)
    print("测试数据库连接")
    print("=" * 50)
    
    try:
        import config
        import mysql.connector
        from mysql.connector import Error
        
        db_config = config.DATABASE_CONFIG
        
        print("尝试连接数据库...")
        connection = mysql.connector.connect(**db_config)
        
        if connection.is_connected():
            print("✓ 数据库连接成功")
            
            # 获取数据库信息
            cursor = connection.cursor()
            cursor.execute("SELECT VERSION()")
            version = cursor.fetchone()
            print(f"MySQL版本: {version[0]}")
            
            # 检查数据库是否存在
            cursor.execute(f"SHOW DATABASES LIKE '{db_config['database']}'")
            result = cursor.fetchone()
            if result:
                print(f"✓ 数据库 '{db_config['database']}' 存在")
            else:
                print(f"⚠️  数据库 '{db_config['database']}' 不存在，程序会尝试创建")
            
            cursor.close()
            connection.close()
            return True
            
    except Error as e:
        print(f"✗ 数据库连接失败: {e}")
        return False
    except Exception as e:
        print(f"✗ 数据库测试时出错: {e}")
        return False

def test_component_initialization():
    """测试组件初始化"""
    print("=" * 50)
    print("测试组件初始化")
    print("=" * 50)
    
    try:
        # 测试数据库管理器
        print("测试数据库管理器...")
        from database import DatabaseManager
        db_manager = DatabaseManager()
        print("✓ 数据库管理器初始化成功")
        
        # 测试Web服务器
        print("测试Web服务器...")
        from web_server import WebServer
        web_server = WebServer(db_manager)
        print("✓ Web服务器初始化成功")
        
        # 测试二维码生成器
        print("测试二维码生成器...")
        from qr_generator import QRGenerator
        qr_generator = QRGenerator(db_manager)
        print("✓ 二维码生成器初始化成功")
        
        # 测试摄像头监控
        print("测试摄像头监控...")
        from camera_monitor import CameraMonitor
        camera_monitor = CameraMonitor(db_manager)
        print("✓ 摄像头监控初始化成功")
        
        return True
        
    except Exception as e:
        print(f"✗ 组件初始化失败: {e}")
        print("详细错误信息:")
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("DataTransmission Windows环境诊断工具")
    print("=" * 60)
    
    results = []
    
    # 检查Python环境
    check_python_environment()
    
    # 检查必需模块
    results.append(("模块检查", check_required_modules()))
    
    # 检查配置文件
    results.append(("配置文件", check_config_file()))
    
    # 测试数据库连接
    results.append(("数据库连接", test_database_connection()))
    
    # 测试组件初始化
    results.append(("组件初始化", test_component_initialization()))
    
    # 总结
    print("=" * 60)
    print("诊断结果总结")
    print("=" * 60)
    
    all_passed = True
    for test_name, result in results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name}: {status}")
        if not result:
            all_passed = False
    
    print()
    if all_passed:
        print("✓ 所有检查都通过，程序应该能正常运行")
    else:
        print("✗ 发现问题，请根据上述信息进行修复")
    
    print("\n诊断日志已保存到 diagnose.log 文件")
    input("按回车键退出...")

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        print(f"诊断脚本运行时出错: {e}")
        traceback.print_exc()
        input("按回车键退出...")
