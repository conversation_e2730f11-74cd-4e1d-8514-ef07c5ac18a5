import sys
import os
from cx_Freeze import setup, Executable

# 获取当前Python环境的site-packages路径
import site
site_packages = site.getsitepackages()[0]

# 包含的文件和目录
include_files = [
    "config.py",
    # 手动包含关键模块的完整路径
    (os.path.join(site_packages, "mysql"), "lib/mysql"),
    (os.path.join(site_packages, "cv2"), "lib/cv2"),
    (os.path.join(site_packages, "qrcode"), "lib/qrcode"),
    (os.path.join(site_packages, "pyzbar"), "lib/pyzbar"),
    (os.path.join(site_packages, "PIL"), "lib/PIL"),
    (os.path.join(site_packages, "flask"), "lib/flask"),
    (os.path.join(site_packages, "apscheduler"), "lib/apscheduler"),
    (os.path.join(site_packages, "numpy"), "lib/numpy"),
    (os.path.join(site_packages, "werkzeug"), "lib/werkzeug"),
    (os.path.join(site_packages, "jinja2"), "lib/jinja2"),
    (os.path.join(site_packages, "markupsafe"), "lib/markupsafe"),
    (os.path.join(site_packages, "itsdangerous"), "lib/itsdangerous"),
    (os.path.join(site_packages, "click"), "lib/click"),
    (os.path.join(site_packages, "blinker"), "lib/blinker"),
]

# 过滤存在的文件
filtered_include_files = []
for item in include_files:
    if isinstance(item, tuple):
        src, dst = item
        if os.path.exists(src):
            filtered_include_files.append((src, dst))
            print(f"包含模块: {src} -> {dst}")
        else:
            print(f"警告: 模块不存在: {src}")
    else:
        if os.path.exists(item):
            filtered_include_files.append(item)
            print(f"包含文件: {item}")
        else:
            print(f"警告: 文件不存在: {item}")

# 构建选项
build_exe_options = {
    "packages": [
        "mysql.connector",
        "mysql",
        "cv2",
        "qrcode",
        "pyzbar",
        "PIL",
        "apscheduler",
        "flask",
        "numpy",
        "werkzeug",
        "jinja2",
        "markupsafe",
        "itsdangerous",
        "click",
        "blinker",
        "logging",
        "threading",
        "time",
        "datetime",
        "json",
        "os",
        "sys",
        "pathlib",
        "sqlite3",
        "urllib",
        "email",
        "encodings",
    ],
    "excludes": [
        "distutils",
        "setuptools",
        "matplotlib",
        "scipy",
        "pandas",
        "jupyter",
        "test",
        "tests",
        "unittest",
        "tkinter",
        "PyQt5",
        "PyQt6",
        "PySide2",
        "PySide6",
    ],
    "include_files": filtered_include_files,
    "optimize": 2,
    "zip_include_packages": "*",
    "zip_exclude_packages": [],
}

# 基础设置
base = None
if sys.platform == "win32":
    base = "Console"  # 使用控制台模式

setup(
    name="DataTransmission",
    version="1.0",
    description="数据传输工具",
    options={"build_exe": build_exe_options},
    executables=[Executable("main.py", base=base, target_name="DataTransmission.exe")]
)
