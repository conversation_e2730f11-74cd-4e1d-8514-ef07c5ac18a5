#!/bin/bash
# DataTransmission CentOS 7 离线安装脚本

set -e

echo "=== DataTransmission CentOS 7 离线安装 ==="

# 检查系统
if ! grep -q "CentOS Linux release 7" /etc/redhat-release 2>/dev/null; then
    echo "警告: 此脚本专为CentOS 7设计"
    read -p "是否继续? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        exit 1
    fi
fi

# 检查桌面环境
if [ -z "$DISPLAY" ]; then
    echo "警告: 未检测到图形桌面环境"
    echo "请确保在CentOS 7桌面版本中运行"
fi

# 设置变量
CONDA_DIR="/opt/miniconda3"
INSTALL_DIR="/opt/DataTransmission"
ENV_NAME="datatransmission"
CURRENT_DIR="$(pwd)"

# 检查权限
if [ "$EUID" -ne 0 ]; then
    echo "此脚本需要root权限，请使用sudo运行"
    exit 1
fi

# 安装系统依赖
echo "安装系统依赖..."
yum update -y
yum install -y epel-release
yum install -y mariadb-server mariadb mariadb-devel
yum install -y zbar zbar-devel
yum install -y mesa-libGL mesa-libGL-devel
yum install -y python3-tkinter
yum install -y libX11-devel libXext-devel
yum install -y wget curl

# 启动数据库
systemctl start mariadb
systemctl enable mariadb

# 安装Miniconda
if [ ! -d "$CONDA_DIR" ]; then
    echo "安装Miniconda..."
    cd /tmp
    
    if [ ! -f "Miniconda3-latest-Linux-x86_64.sh" ]; then
        wget -O Miniconda3-latest-Linux-x86_64.sh \
             "https://repo.anaconda.com/miniconda/Miniconda3-latest-Linux-x86_64.sh" || {
            echo "下载失败，请手动下载Miniconda到/tmp目录"
            exit 1
        }
    fi
    
    bash Miniconda3-latest-Linux-x86_64.sh -b -p $CONDA_DIR
    chown -R $(logname):$(logname) $CONDA_DIR 2>/dev/null || chown -R $SUDO_USER:$SUDO_USER $CONDA_DIR
    
    echo 'export PATH="'$CONDA_DIR'/bin:$PATH"' >> /etc/profile
    export PATH="$CONDA_DIR/bin:$PATH"
    
    cd $CURRENT_DIR
fi

# 设置conda环境
echo "设置conda环境..."
source $CONDA_DIR/etc/profile.d/conda.sh

# 安装环境
if [ -f "conda_env/datatransmission_env.tar.gz" ]; then
    echo "使用打包的conda环境..."
    mkdir -p $CONDA_DIR/envs/$ENV_NAME
    tar -xzf conda_env/datatransmission_env.tar.gz -C $CONDA_DIR/envs/$ENV_NAME
    conda-unpack -n $ENV_NAME 2>/dev/null || echo "环境已准备就绪"
else
    echo "从配置文件创建环境..."
    conda env create -f conda_env/environment.yml -n $ENV_NAME
fi

# 安装程序
echo "安装程序..."
mkdir -p $INSTALL_DIR
cp -r src/* $INSTALL_DIR/
chown -R $(logname):$(logname) $INSTALL_DIR 2>/dev/null || chown -R $SUDO_USER:$SUDO_USER $INSTALL_DIR

# 创建启动脚本
cat > $INSTALL_DIR/start_datatransmission.sh << 'STARTEOF'
#!/bin/bash
# DataTransmission CentOS 7 启动脚本

echo "=== DataTransmission 启动中 ==="

# 设置变量
CONDA_DIR="/opt/miniconda3"
ENV_NAME="datatransmission"
INSTALL_DIR="/opt/DataTransmission"

# 检查桌面环境
if [ -z "$DISPLAY" ]; then
    echo "错误: 未检测到图形桌面环境"
    exit 1
fi

# 激活conda环境
export PATH="$CONDA_DIR/bin:$PATH"
source $CONDA_DIR/etc/profile.d/conda.sh
conda activate $ENV_NAME || {
    echo "错误: 无法激活环境 $ENV_NAME"
    exit 1
}

# 设置环境变量
export DISPLAY=${DISPLAY:-:0.0}
export LD_LIBRARY_PATH=/usr/lib64:/usr/local/lib:$LD_LIBRARY_PATH

# 切换到项目目录
cd $INSTALL_DIR || {
    echo "错误: 无法进入项目目录"
    exit 1
}

echo "✓ 环境已激活: $(conda info --envs | grep '*')"
echo "✓ 启动DataTransmission..."

# 启动程序
python main.py
STARTEOF

chmod +x $INSTALL_DIR/start_datatransmission.sh

# 创建桌面快捷方式
REAL_USER=$(logname 2>/dev/null || echo $SUDO_USER)
if [ -n "$REAL_USER" ] && [ -d "/home/<USER>/Desktop" ]; then
    cat > /home/<USER>/Desktop/DataTransmission.desktop << 'DESKTOPEOF'
[Desktop Entry]
Version=1.0
Type=Application
Name=DataTransmission
Comment=数据传输客户端
Exec=/opt/DataTransmission/start_datatransmission.sh
Terminal=true
StartupNotify=true
Categories=Application;Network;
DESKTOPEOF
    
    chmod +x /home/<USER>/Desktop/DataTransmission.desktop
    chown $REAL_USER:$REAL_USER /home/<USER>/Desktop/DataTransmission.desktop
    echo "✓ 桌面快捷方式已创建"
fi

echo ""
echo "=== 安装完成 ==="
echo "下一步操作："
echo "1. 配置数据库: mysql_secure_installation"
echo "2. 创建数据库和用户"
echo "3. 配置应用: vi $INSTALL_DIR/config.py"
echo "4. 启动程序: $INSTALL_DIR/start_datatransmission.sh"
echo "5. 或双击桌面快捷方式启动"
