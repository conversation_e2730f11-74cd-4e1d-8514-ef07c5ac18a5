# DataTransmission Windows部署说明

## 文件说明
- DataTransmission.exe: 主程序
- config.py: 配置文件
- start.bat: 启动脚本
- README.md: 项目说明

## 部署步骤

### 1. 安装MySQL数据库
下载并安装MySQL 8.0或MariaDB 10.x

### 2. 创建数据库
```sql
CREATE DATABASE DataTransmission CHARACTER SET utf8 COLLATE utf8_general_ci;
CREATE USER 'datatrans'@'localhost' IDENTIFIED BY 'your_password';
GRANT ALL PRIVILEGES ON DataTransmission.* TO 'datatrans'@'localhost';
FLUSH PRIVILEGES;
```

### 3. 配置应用程序
编辑 config.py 文件，修改数据库连接信息：
```python
DATABASE_CONFIG = {
    'host': 'localhost',
    'port': 3306,
    'database': 'DataTransmission',
    'user': 'datatrans',
    'password': 'your_password',
    'charset': 'utf8'
}
```

### 4. 运行程序
双击 start.bat 或直接运行 DataTransmission.exe

### 5. 测试功能
打开浏览器访问: http://localhost:5000/health

## 注意事项
1. 确保Windows防火墙允许程序访问网络
2. 如果使用摄像头功能，确保摄像头驱动正常
3. 程序需要管理员权限来访问摄像头和网络端口
4. 建议在Windows Server 2016+或Windows 10+上运行

## 故障排除
- 如果程序无法启动，检查config.py中的数据库配置
- 如果摄像头无法使用，检查设备管理器中的摄像头状态
- 如果二维码无法显示，确保系统支持图形界面
- 查看程序目录下的data_transmission.log文件获取详细错误信息
