from flask import Flask, request, jsonify
import logging
from database import DatabaseManager
from config import FLASK_CONFIG
import time

class WebServer:
    def __init__(self, db_manager):
        self.app = Flask(__name__)
        self.db_manager = db_manager
        self.setup_routes()
    
    def setup_routes(self):
        """设置路由"""
        
        @self.app.route('/receiveData', methods=['POST'])
        def receive_data():
            """接收数据的HTTP接口"""
            try:
                # 获取请求参数
                data = request.get_json()
                
                if not data:
                    return jsonify({
                        'success': False,
                        'message': '请求数据为空'
                    }), 400
                
                # 验证必需参数
                required_fields = ['id', 'type', 'data']
                for field in required_fields:
                    if field not in data:
                        return jsonify({
                            'success': False,
                            'message': f'缺少必需参数: {field}'
                        }), 400
                
                id_val = str(data['id'])
                type_val = int(data['type'])
                data_val = str(data['data'])
                
                # 存储数据到数据库
                success = self.db_manager.insert_transmission_data(id_val, type_val, data_val)
                
                if success:
                    return jsonify({
                        'success': True,
                        'message': '数据接收成功'
                    }), 200
                else:
                    return jsonify({
                        'success': False,
                        'message': '数据已存在或存储失败'
                    }), 409
                    
            except ValueError as e:
                return jsonify({
                    'success': False,
                    'message': f'参数类型错误: {str(e)}'
                }), 400
            except Exception as e:
                logging.error(f"接收数据时出错: {e}")
                return jsonify({
                    'success': False,
                    'message': '服务器内部错误'
                }), 500
        
        @self.app.route('/health', methods=['GET'])
        def health_check():
            """健康检查接口"""
            return jsonify({
                'status': 'healthy',
                'timestamp': time.time()
            }), 200
        
        @self.app.route('/', methods=['GET'])
        def index():
            """根路径"""
            return jsonify({
                'service': 'DataTransmission Client',
                'version': '1.0.0',
                'endpoints': [
                    'POST /receiveData - 接收传输数据',
                    'GET /health - 健康检查'
                ]
            }), 200
    
    def run(self):
        """启动Web服务器"""
        logging.info(f"启动Web服务器，监听 {FLASK_CONFIG['host']}:{FLASK_CONFIG['port']}")
        self.app.run(
            host=FLASK_CONFIG['host'],
            port=FLASK_CONFIG['port'],
            debug=FLASK_CONFIG['debug'],
            threaded=True
        )
