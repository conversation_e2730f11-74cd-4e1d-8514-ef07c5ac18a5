#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
使用cx_Freeze打包Windows EXE
避免PyInstaller的distutils问题
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path

def check_cx_freeze():
    """检查并安装cx_Freeze"""
    print("检查cx_Freeze...")
    
    try:
        import cx_Freeze
        print(f"✓ cx_Freeze版本: {cx_Freeze.__version__}")
        return True
    except ImportError:
        print("✗ cx_Freeze未安装，正在安装...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", "cx_Freeze"])
            print("✓ cx_Freeze安装完成")
            return True
        except subprocess.CalledProcessError as e:
            print(f"✗ cx_Freeze安装失败: {e}")
            return False

def create_setup_py():
    """创建setup.py文件"""
    print("创建setup.py文件...")

    setup_content = '''import sys
import os
from cx_Freeze import setup, Executable

# 获取当前Python环境的site-packages路径
import site
site_packages = site.getsitepackages()[0]

# 包含的文件和目录
include_files = [
    "config.py",
    # 手动包含关键模块的完整路径
    (os.path.join(site_packages, "mysql"), "lib/mysql"),
    (os.path.join(site_packages, "cv2"), "lib/cv2"),
    (os.path.join(site_packages, "qrcode"), "lib/qrcode"),
    (os.path.join(site_packages, "pyzbar"), "lib/pyzbar"),
    (os.path.join(site_packages, "PIL"), "lib/PIL"),
    (os.path.join(site_packages, "flask"), "lib/flask"),
    (os.path.join(site_packages, "apscheduler"), "lib/apscheduler"),
    (os.path.join(site_packages, "numpy"), "lib/numpy"),
    (os.path.join(site_packages, "werkzeug"), "lib/werkzeug"),
    (os.path.join(site_packages, "jinja2"), "lib/jinja2"),
    (os.path.join(site_packages, "markupsafe"), "lib/markupsafe"),
    (os.path.join(site_packages, "itsdangerous"), "lib/itsdangerous"),
    (os.path.join(site_packages, "click"), "lib/click"),
    (os.path.join(site_packages, "blinker"), "lib/blinker"),
]

# 过滤存在的文件
filtered_include_files = []
for item in include_files:
    if isinstance(item, tuple):
        src, dst = item
        if os.path.exists(src):
            filtered_include_files.append((src, dst))
            print(f"包含模块: {src} -> {dst}")
        else:
            print(f"警告: 模块不存在: {src}")
    else:
        if os.path.exists(item):
            filtered_include_files.append(item)
            print(f"包含文件: {item}")
        else:
            print(f"警告: 文件不存在: {item}")

# 构建选项
build_exe_options = {
    "packages": [
        "mysql.connector",
        "mysql",
        "cv2",
        "qrcode",
        "pyzbar",
        "PIL",
        "apscheduler",
        "flask",
        "numpy",
        "werkzeug",
        "jinja2",
        "markupsafe",
        "itsdangerous",
        "click",
        "blinker",
        "logging",
        "threading",
        "time",
        "datetime",
        "json",
        "os",
        "sys",
        "pathlib",
        "sqlite3",
        "urllib",
        "email",
        "encodings",
    ],
    "excludes": [
        "distutils",
        "setuptools",
        "matplotlib",
        "scipy",
        "pandas",
        "jupyter",
        "test",
        "tests",
        "unittest",
        "tkinter",
        "PyQt5",
        "PyQt6",
        "PySide2",
        "PySide6",
    ],
    "include_files": filtered_include_files,
    "optimize": 2,
    "zip_include_packages": "*",
    "zip_exclude_packages": [],
}

# 基础设置
base = None
if sys.platform == "win32":
    base = "Console"  # 使用控制台模式

setup(
    name="DataTransmission",
    version="1.0",
    description="数据传输工具",
    options={"build_exe": build_exe_options},
    executables=[Executable("main.py", base=base, target_name="DataTransmission.exe")]
)
'''
    
    with open('setup.py', 'w', encoding='utf-8') as f:
        f.write(setup_content)
    
    print("✓ 创建setup.py文件")

def build_with_cx_freeze():
    """使用cx_Freeze构建EXE"""
    print("开始使用cx_Freeze构建EXE...")
    
    # 清理之前的构建
    if os.path.exists('build'):
        shutil.rmtree('build')
    if os.path.exists('dist'):
        shutil.rmtree('dist')
    
    # 运行cx_Freeze
    cmd = [sys.executable, 'setup.py', 'build']
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("✓ cx_Freeze构建成功")
        print("构建输出:", result.stdout[-500:] if result.stdout else "无输出")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ cx_Freeze构建失败: {e}")
        print(f"错误输出: {e.stderr}")
        if e.stdout:
            print(f"标准输出: {e.stdout}")
        return False

def create_windows_config():
    """创建Windows配置文件"""
    config_content = '''# Windows配置文件
# 数据库配置
DATABASE_CONFIG = {
    'host': 'localhost',
    'port': 3306,
    'database': 'DataTransmission',
    'user': 'root',
    'password': '',
    'charset': 'utf8'
}

# Flask配置
FLASK_CONFIG = {
    'host': '0.0.0.0',
    'port': 5000,
    'debug': False
}

# 二维码显示配置
QR_DISPLAY_TIME = 2
QR_DISPLAY_SIZE = 900

# 摄像头配置
CAMERA_INDEX = 0
CAPTURE_INTERVAL = 1
'''
    
    with open('config_windows.py', 'w', encoding='utf-8') as f:
        f.write(config_content)
    
    print("✓ 创建Windows配置文件")

def create_deployment_package():
    """创建部署包"""
    print("创建Windows部署包...")
    
    # 查找构建目录
    build_dirs = [d for d in os.listdir('build') if d.startswith('exe.')]
    if not build_dirs:
        print("✗ 未找到构建目录")
        return False
    
    build_dir = Path('build') / build_dirs[0]
    
    # 创建部署目录
    deploy_dir = Path('DataTransmission_Windows_CxFreeze')
    if deploy_dir.exists():
        shutil.rmtree(deploy_dir)
    
    # 复制整个构建目录
    shutil.copytree(build_dir, deploy_dir)
    
    # 复制配置文件
    shutil.copy2('config_windows.py', deploy_dir / 'config.py')
    
    # 创建启动脚本
    start_script = '''@echo off
echo 启动DataTransmission服务...
echo 请确保已安装MySQL数据库并配置好连接信息
echo.
DataTransmission.exe
pause
'''
    
    with open(deploy_dir / 'start.bat', 'w', encoding='gbk') as f:
        f.write(start_script)
    
    # 创建README
    readme_content = '''# DataTransmission Windows版本 (cx_Freeze打包)

## 使用说明

1. 安装MySQL数据库
2. 编辑config.py配置数据库连接
3. 双击start.bat启动程序
4. 访问 http://localhost:5000 使用Web界面

## 注意事项

- 确保MySQL服务正在运行
- 确保防火墙允许5000端口
- 如需使用摄像头功能，确保摄像头驱动正常
- 本版本使用cx_Freeze打包，避免了PyInstaller的兼容性问题

## 文件说明

- DataTransmission.exe: 主程序
- config.py: 配置文件
- start.bat: 启动脚本
- lib/: 依赖库文件夹
'''
    
    with open(deploy_dir / 'README.md', 'w', encoding='utf-8') as f:
        f.write(readme_content)
    
    print(f"✓ 部署包创建完成: {deploy_dir}")
    return True

def main():
    """主函数"""
    print("=" * 50)
    print("DataTransmission cx_Freeze Windows打包工具")
    print("=" * 50)
    
    # 检查cx_Freeze
    if not check_cx_freeze():
        return False
    
    # 创建Windows配置
    create_windows_config()
    
    # 创建setup.py
    create_setup_py()
    
    # 构建EXE
    if not build_with_cx_freeze():
        return False
    
    # 创建部署包
    if not create_deployment_package():
        return False
    
    print("\n" + "=" * 50)
    print("打包完成！")
    print("=" * 50)
    print("生成的文件:")
    print("- DataTransmission_Windows_CxFreeze/DataTransmission.exe")
    print("- DataTransmission_Windows_CxFreeze/config.py")
    print("- DataTransmission_Windows_CxFreeze/start.bat")
    print("- DataTransmission_Windows_CxFreeze/README.md")
    print("- DataTransmission_Windows_CxFreeze/lib/ (依赖库)")
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
