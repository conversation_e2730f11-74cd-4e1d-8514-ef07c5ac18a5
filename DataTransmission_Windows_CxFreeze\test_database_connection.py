#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
数据库连接测试脚本
专门用于诊断数据库连接问题
"""

import sys
import traceback
import time

def test_mysql_import():
    """测试MySQL模块导入"""
    print("=" * 50)
    print("步骤1: 测试MySQL模块导入")
    print("=" * 50)
    
    try:
        import mysql.connector
        from mysql.connector import Error
        print("✓ mysql.connector 导入成功")
        print(f"✓ MySQL Connector版本: {mysql.connector.__version__}")
        return True
    except ImportError as e:
        print(f"✗ mysql.connector 导入失败: {e}")
        return False
    except Exception as e:
        print(f"✗ 导入时出现异常: {e}")
        traceback.print_exc()
        return False

def test_config_import():
    """测试配置文件导入"""
    print("\n" + "=" * 50)
    print("步骤2: 测试配置文件导入")
    print("=" * 50)
    
    try:
        import config
        print("✓ config.py 导入成功")
        
        if hasattr(config, 'DATABASE_CONFIG'):
            db_config = config.DATABASE_CONFIG
            print("✓ DATABASE_CONFIG 存在")
            print("数据库配置信息:")
            print(f"  主机: {db_config.get('host', 'N/A')}")
            print(f"  端口: {db_config.get('port', 'N/A')}")
            print(f"  数据库: {db_config.get('database', 'N/A')}")
            print(f"  用户: {db_config.get('user', 'N/A')}")
            print(f"  密码: {'***已设置***' if db_config.get('password') else '未设置'}")
            print(f"  字符集: {db_config.get('charset', 'N/A')}")
            return db_config
        else:
            print("✗ DATABASE_CONFIG 不存在")
            return None
            
    except ImportError as e:
        print(f"✗ config.py 导入失败: {e}")
        return None
    except Exception as e:
        print(f"✗ 配置导入时出现异常: {e}")
        traceback.print_exc()
        return None

def test_basic_connection(db_config):
    """测试基本数据库连接"""
    print("\n" + "=" * 50)
    print("步骤3: 测试基本数据库连接")
    print("=" * 50)
    
    try:
        import mysql.connector
        from mysql.connector import Error
        
        print(f"尝试连接到 {db_config['host']}:{db_config['port']}...")
        
        # 首先测试不指定数据库的连接
        basic_config = {
            'host': db_config['host'],
            'port': db_config['port'],
            'user': db_config['user'],
            'password': db_config['password'],
            'charset': db_config['charset']
        }
        
        connection = mysql.connector.connect(**basic_config)
        
        if connection.is_connected():
            print("✓ MySQL服务器连接成功")
            
            # 获取服务器信息
            cursor = connection.cursor()
            cursor.execute("SELECT VERSION()")
            version = cursor.fetchone()
            print(f"✓ MySQL版本: {version[0]}")
            
            cursor.execute("SELECT USER()")
            user = cursor.fetchone()
            print(f"✓ 当前用户: {user[0]}")
            
            cursor.close()
            connection.close()
            return True
            
    except Error as e:
        print(f"✗ MySQL连接错误: {e}")
        print(f"错误代码: {e.errno}")
        print(f"错误信息: {e.msg}")
        
        # 提供具体的错误解决建议
        if e.errno == 2003:
            print("\n💡 解决建议:")
            print("- 检查MySQL服务是否启动: net start mysql")
            print("- 检查防火墙是否阻止3306端口")
            print("- 确认MySQL服务器地址是否正确")
        elif e.errno == 1045:
            print("\n💡 解决建议:")
            print("- 检查用户名和密码是否正确")
            print("- 确认用户是否有连接权限")
        elif e.errno == 1049:
            print("\n💡 解决建议:")
            print("- 数据库不存在，需要先创建数据库")
        
        return False
    except Exception as e:
        print(f"✗ 连接时出现异常: {e}")
        traceback.print_exc()
        return False

def test_database_exists(db_config):
    """测试指定数据库是否存在"""
    print("\n" + "=" * 50)
    print("步骤4: 测试指定数据库是否存在")
    print("=" * 50)
    
    try:
        import mysql.connector
        from mysql.connector import Error
        
        # 连接到MySQL服务器（不指定数据库）
        basic_config = {
            'host': db_config['host'],
            'port': db_config['port'],
            'user': db_config['user'],
            'password': db_config['password'],
            'charset': db_config['charset']
        }
        
        connection = mysql.connector.connect(**basic_config)
        cursor = connection.cursor()
        
        # 检查数据库是否存在
        cursor.execute(f"SHOW DATABASES LIKE '{db_config['database']}'")
        result = cursor.fetchone()
        
        if result:
            print(f"✓ 数据库 '{db_config['database']}' 存在")
            
            # 尝试连接到指定数据库
            cursor.execute(f"USE {db_config['database']}")
            print(f"✓ 成功切换到数据库 '{db_config['database']}'")
            
            # 显示数据库中的表
            cursor.execute("SHOW TABLES")
            tables = cursor.fetchall()
            if tables:
                print(f"✓ 数据库中有 {len(tables)} 个表:")
                for table in tables:
                    print(f"  - {table[0]}")
            else:
                print("ℹ 数据库为空（没有表）")
            
            cursor.close()
            connection.close()
            return True
        else:
            print(f"✗ 数据库 '{db_config['database']}' 不存在")
            print("\n💡 解决方案:")
            print(f"请在MySQL中创建数据库:")
            print(f"CREATE DATABASE {db_config['database']} CHARACTER SET utf8 COLLATE utf8_general_ci;")
            
            cursor.close()
            connection.close()
            return False
            
    except Error as e:
        print(f"✗ 检查数据库时出错: {e}")
        return False
    except Exception as e:
        print(f"✗ 检查数据库时出现异常: {e}")
        traceback.print_exc()
        return False

def test_full_connection(db_config):
    """测试完整的数据库连接（包括指定数据库）"""
    print("\n" + "=" * 50)
    print("步骤5: 测试完整数据库连接")
    print("=" * 50)
    
    try:
        import mysql.connector
        from mysql.connector import Error
        
        print("尝试连接到完整的数据库配置...")
        connection = mysql.connector.connect(**db_config)
        
        if connection.is_connected():
            print("✓ 完整数据库连接成功")
            
            cursor = connection.cursor()
            cursor.execute("SELECT DATABASE()")
            current_db = cursor.fetchone()
            print(f"✓ 当前数据库: {current_db[0]}")
            
            cursor.close()
            connection.close()
            return True
            
    except Error as e:
        print(f"✗ 完整连接失败: {e}")
        return False
    except Exception as e:
        print(f"✗ 完整连接时出现异常: {e}")
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("DataTransmission 数据库连接测试工具")
    print("=" * 60)
    
    # 步骤1: 测试MySQL模块导入
    if not test_mysql_import():
        print("\n❌ MySQL模块导入失败，请先安装mysql-connector-python")
        input("按回车键退出...")
        return
    
    # 步骤2: 测试配置文件导入
    db_config = test_config_import()
    if not db_config:
        print("\n❌ 配置文件导入失败，请检查config.py文件")
        input("按回车键退出...")
        return
    
    # 步骤3: 测试基本连接
    if not test_basic_connection(db_config):
        print("\n❌ 基本数据库连接失败，请检查MySQL服务和配置")
        input("按回车键退出...")
        return
    
    # 步骤4: 测试数据库是否存在
    if not test_database_exists(db_config):
        print("\n⚠️ 指定的数据库不存在，但MySQL服务器连接正常")
        print("请创建数据库后重新测试")
        input("按回车键退出...")
        return
    
    # 步骤5: 测试完整连接
    if test_full_connection(db_config):
        print("\n" + "=" * 60)
        print("🎉 所有数据库连接测试通过！")
        print("=" * 60)
        print("DataTransmission程序应该能够正常连接数据库")
    else:
        print("\n❌ 完整数据库连接测试失败")
    
    input("按回车键退出...")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n用户中断测试")
    except Exception as e:
        print(f"\n测试过程中出现未预期的错误: {e}")
        traceback.print_exc()
        input("按回车键退出...")
