# DataTransmission 离线部署检查清单

## 准备阶段 ✓

### 开发环境准备
- [ ] 确认项目在开发环境中正常运行
- [ ] 测试所有功能模块（HTTP接口、二维码生成、摄像头识别）
- [ ] 确认所有依赖包版本兼容

### 创建部署包
- [ ] 运行构建脚本：`./build_offline_package.sh` 或 `build_offline_package.bat`
- [ ] 验证部署包内容完整
- [ ] 测试离线依赖包是否完整

## 传输阶段 ✓

### 文件传输
- [ ] 将部署包传输到目标Ubuntu机器
- [ ] 验证文件传输完整性
- [ ] 解压部署包：`tar -xzf DataTransmission_offline_*.tar.gz`

## 目标机器准备 ✓

### 系统要求检查
- [ ] Ubuntu 18.04+ 系统
- [ ] 至少2GB可用磁盘空间
- [ ] 具有sudo权限的用户账户
- [ ] 摄像头设备可用（如需要）

### 基础软件安装
- [ ] 更新系统：`sudo apt update && sudo apt upgrade`
- [ ] 安装基础工具：`sudo apt install curl wget git`

## 部署阶段 ✓

### 选择部署方法

#### 方法一：自动安装脚本（推荐）
- [ ] 运行：`sudo ./DataTransmission_offline/scripts/install_offline.sh`
- [ ] 检查安装过程是否有错误
- [ ] 验证服务文件创建成功

#### 方法二：Conda环境部署
- [ ] 确认conda已安装
- [ ] 运行：`./conda_deploy.sh`
- [ ] 激活conda环境测试

#### 方法三：手动部署
- [ ] 安装Python 3.10
- [ ] 创建虚拟环境
- [ ] 安装离线依赖包
- [ ] 配置系统服务

## 数据库配置 ✓

### MySQL安装和配置
- [ ] 安装MySQL：`sudo apt install mysql-server`
- [ ] 启动MySQL服务：`sudo systemctl start mysql`
- [ ] 设置root密码：`sudo mysql_secure_installation`

### 创建应用数据库
- [ ] 登录MySQL：`sudo mysql -u root -p`
- [ ] 创建数据库：`CREATE DATABASE DataTransmission CHARACTER SET utf8;`
- [ ] 创建用户：`CREATE USER 'datatrans'@'localhost' IDENTIFIED BY 'password';`
- [ ] 授权：`GRANT ALL PRIVILEGES ON DataTransmission.* TO 'datatrans'@'localhost';`
- [ ] 刷新权限：`FLUSH PRIVILEGES;`

### 应用配置
- [ ] 编辑配置文件：`sudo nano /opt/DataTransmission/config.py`
- [ ] 修改数据库连接信息
- [ ] 设置正确的摄像头索引
- [ ] 配置Flask服务端口

## 服务启动 ✓

### 启动服务
- [ ] 启动服务：`sudo systemctl start datatransmission`
- [ ] 检查服务状态：`sudo systemctl status datatransmission`
- [ ] 设置开机自启：`sudo systemctl enable datatransmission`

### 验证服务运行
- [ ] 检查端口监听：`netstat -tlnp | grep 5000`
- [ ] 测试健康检查：`curl http://localhost:5000/health`
- [ ] 查看服务日志：`sudo journalctl -u datatransmission -f`

## 功能测试 ✓

### HTTP接口测试
- [ ] 测试receiveData接口
- [ ] 验证数据存储到数据库
- [ ] 检查API响应格式

### 二维码功能测试
- [ ] 插入测试数据到transmission_data表
- [ ] 验证二维码生成和显示
- [ ] 确认二维码居中显示，尺寸900x900
- [ ] 检查数据状态更新

### 摄像头功能测试
- [ ] 检查摄像头设备：`ls /dev/video*`
- [ ] 测试摄像头初始化
- [ ] 验证二维码识别功能
- [ ] 检查识别数据存储

## 安全配置 ✓

### 系统安全
- [ ] 配置防火墙：`sudo ufw enable`
- [ ] 开放必要端口：`sudo ufw allow 5000`
- [ ] 设置文件权限：`sudo chmod 600 /opt/DataTransmission/config.py`

### 应用安全
- [ ] 修改默认数据库密码
- [ ] 限制API访问（如需要）
- [ ] 配置日志轮转

## 监控和维护 ✓

### 日志配置
- [ ] 检查应用日志：`tail -f /opt/DataTransmission/data_transmission.log`
- [ ] 配置日志轮转：`sudo nano /etc/logrotate.d/datatransmission`
- [ ] 设置日志级别

### 监控设置
- [ ] 设置服务监控脚本
- [ ] 配置磁盘空间监控
- [ ] 设置数据库备份计划

## 故障排除 ✓

### 常见问题检查
- [ ] Python版本兼容性
- [ ] 依赖包安装完整性
- [ ] 数据库连接配置
- [ ] 摄像头权限问题
- [ ] 端口占用冲突

### 调试工具
- [ ] 查看系统日志：`sudo journalctl -xe`
- [ ] 检查进程状态：`ps aux | grep python`
- [ ] 网络连接测试：`netstat -tlnp`
- [ ] 磁盘空间检查：`df -h`

## 部署完成确认 ✓

### 最终验证
- [ ] 所有服务正常运行
- [ ] HTTP接口响应正常
- [ ] 二维码生成显示正常
- [ ] 摄像头识别功能正常
- [ ] 数据库读写正常
- [ ] 日志记录正常

### 文档交付
- [ ] 提供管理员操作手册
- [ ] 记录配置参数
- [ ] 提供故障排除指南
- [ ] 备份重要配置文件

## 部署后维护 ✓

### 定期检查项目
- [ ] 每日检查服务状态
- [ ] 每周检查日志文件
- [ ] 每月检查磁盘空间
- [ ] 定期备份数据库

### 更新和升级
- [ ] 制定更新计划
- [ ] 测试环境验证
- [ ] 生产环境升级
- [ ] 回滚方案准备

---

## 快速命令参考

### 服务管理
```bash
# 启动服务
sudo systemctl start datatransmission

# 停止服务
sudo systemctl stop datatransmission

# 重启服务
sudo systemctl restart datatransmission

# 查看状态
sudo systemctl status datatransmission

# 查看日志
sudo journalctl -u datatransmission -f
```

### 故障诊断
```bash
# 检查端口
netstat -tlnp | grep 5000

# 检查进程
ps aux | grep python

# 测试API
curl http://localhost:5000/health

# 查看配置
cat /opt/DataTransmission/config.py
```

### 数据库操作
```bash
# 连接数据库
mysql -u datatrans -p DataTransmission

# 查看表结构
DESCRIBE transmission_data;
DESCRIBE receive_data;

# 查看数据
SELECT * FROM transmission_data LIMIT 10;
```
