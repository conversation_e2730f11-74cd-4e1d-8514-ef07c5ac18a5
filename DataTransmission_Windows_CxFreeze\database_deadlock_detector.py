#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
数据库死锁检测和修复工具
"""

import mysql.connector
from mysql.connector import Error
import time
import logging
import threading
from config import DATABASE_CONFIG

class DeadlockDetector:
    def __init__(self):
        self.connection = None
        self.monitoring = False
        self.monitor_thread = None
        
    def connect(self):
        """连接到数据库"""
        try:
            config = DATABASE_CONFIG.copy()
            config['autocommit'] = True
            self.connection = mysql.connector.connect(**config)
            print("✓ 数据库连接成功")
            return True
        except Error as e:
            print(f"✗ 数据库连接失败: {e}")
            return False
    
    def check_current_locks(self):
        """检查当前锁状态"""
        if not self.connection or not self.connection.is_connected():
            return None
        
        try:
            cursor = self.connection.cursor(dictionary=True)
            
            # 检查当前锁等待
            lock_query = """
            SELECT 
                r.trx_id waiting_trx_id,
                r.trx_mysql_thread_id waiting_thread,
                r.trx_query waiting_query,
                b.trx_id blocking_trx_id,
                b.trx_mysql_thread_id blocking_thread,
                b.trx_query blocking_query,
                l.lock_table,
                l.lock_mode,
                l.lock_type
            FROM information_schema.innodb_lock_waits w
            INNER JOIN information_schema.innodb_trx r ON r.trx_id = w.requesting_trx_id
            INNER JOIN information_schema.innodb_trx b ON b.trx_id = w.blocking_trx_id
            INNER JOIN information_schema.innodb_locks l ON l.lock_trx_id = w.blocking_trx_id
            """
            
            cursor.execute(lock_query)
            locks = cursor.fetchall()
            
            cursor.close()
            return locks
            
        except Error as e:
            print(f"检查锁状态时出错: {e}")
            return None
    
    def check_long_running_transactions(self, max_seconds=30):
        """检查长时间运行的事务"""
        if not self.connection or not self.connection.is_connected():
            return None
        
        try:
            cursor = self.connection.cursor(dictionary=True)
            
            query = """
            SELECT 
                trx_id,
                trx_state,
                trx_started,
                trx_mysql_thread_id,
                trx_query,
                TIMESTAMPDIFF(SECOND, trx_started, NOW()) as duration_seconds
            FROM information_schema.innodb_trx 
            WHERE TIMESTAMPDIFF(SECOND, trx_started, NOW()) > %s
            ORDER BY trx_started
            """
            
            cursor.execute(query, (max_seconds,))
            transactions = cursor.fetchall()
            
            cursor.close()
            return transactions
            
        except Error as e:
            print(f"检查长事务时出错: {e}")
            return None
    
    def get_process_list(self):
        """获取当前进程列表"""
        if not self.connection or not self.connection.is_connected():
            return None
        
        try:
            cursor = self.connection.cursor(dictionary=True)
            
            cursor.execute("SHOW FULL PROCESSLIST")
            processes = cursor.fetchall()
            
            cursor.close()
            return processes
            
        except Error as e:
            print(f"获取进程列表时出错: {e}")
            return None
    
    def kill_process(self, process_id):
        """终止指定进程"""
        if not self.connection or not self.connection.is_connected():
            return False
        
        try:
            cursor = self.connection.cursor()
            cursor.execute(f"KILL {process_id}")
            cursor.close()
            print(f"✓ 进程 {process_id} 已终止")
            return True
            
        except Error as e:
            print(f"✗ 终止进程 {process_id} 失败: {e}")
            return False
    
    def analyze_deadlocks(self):
        """分析死锁情况"""
        print("=" * 60)
        print("数据库死锁分析")
        print("=" * 60)
        
        # 1. 检查当前锁等待
        print("\n1. 当前锁等待情况:")
        print("-" * 40)
        locks = self.check_current_locks()
        
        if locks:
            print(f"发现 {len(locks)} 个锁等待:")
            for i, lock in enumerate(locks, 1):
                print(f"\n锁等待 #{i}:")
                print(f"  等待事务ID: {lock['waiting_trx_id']}")
                print(f"  等待线程ID: {lock['waiting_thread']}")
                print(f"  等待查询: {lock['waiting_query'][:100]}...")
                print(f"  阻塞事务ID: {lock['blocking_trx_id']}")
                print(f"  阻塞线程ID: {lock['blocking_thread']}")
                print(f"  阻塞查询: {lock['blocking_query'][:100]}...")
                print(f"  锁表: {lock['lock_table']}")
                print(f"  锁模式: {lock['lock_mode']}")
        else:
            print("✓ 当前没有锁等待")
        
        # 2. 检查长时间运行的事务
        print("\n2. 长时间运行的事务:")
        print("-" * 40)
        long_transactions = self.check_long_running_transactions()
        
        if long_transactions:
            print(f"发现 {len(long_transactions)} 个长事务:")
            for i, trx in enumerate(long_transactions, 1):
                print(f"\n长事务 #{i}:")
                print(f"  事务ID: {trx['trx_id']}")
                print(f"  状态: {trx['trx_state']}")
                print(f"  开始时间: {trx['trx_started']}")
                print(f"  线程ID: {trx['trx_mysql_thread_id']}")
                print(f"  运行时长: {trx['duration_seconds']} 秒")
                print(f"  当前查询: {trx['trx_query'][:100]}...")
        else:
            print("✓ 没有发现长时间运行的事务")
        
        # 3. 检查进程列表
        print("\n3. 当前数据库连接:")
        print("-" * 40)
        processes = self.get_process_list()
        
        if processes:
            active_processes = [p for p in processes if p['Command'] != 'Sleep']
            print(f"活跃连接数: {len(active_processes)} / 总连接数: {len(processes)}")
            
            if active_processes:
                print("\n活跃连接详情:")
                for i, proc in enumerate(active_processes[:5], 1):  # 只显示前5个
                    print(f"  连接 #{i}: ID={proc['Id']}, 用户={proc['User']}, "
                          f"命令={proc['Command']}, 时间={proc['Time']}s")
                    if proc['Info']:
                        print(f"    查询: {proc['Info'][:80]}...")
        
        return locks, long_transactions, processes
    
    def auto_fix_deadlocks(self):
        """自动修复死锁"""
        print("\n" + "=" * 60)
        print("自动死锁修复")
        print("=" * 60)
        
        locks, long_transactions, processes = self.analyze_deadlocks()
        
        fixed_count = 0
        
        # 1. 终止长时间运行的事务
        if long_transactions:
            print(f"\n正在处理 {len(long_transactions)} 个长事务...")
            for trx in long_transactions:
                thread_id = trx['trx_mysql_thread_id']
                duration = trx['duration_seconds']
                
                if duration > 60:  # 超过1分钟的事务
                    print(f"终止长事务: 线程ID={thread_id}, 运行时长={duration}秒")
                    if self.kill_process(thread_id):
                        fixed_count += 1
        
        # 2. 处理锁等待
        if locks:
            print(f"\n正在处理 {len(locks)} 个锁等待...")
            processed_threads = set()
            
            for lock in locks:
                blocking_thread = lock['blocking_thread']
                
                if blocking_thread not in processed_threads:
                    print(f"终止阻塞线程: ID={blocking_thread}")
                    if self.kill_process(blocking_thread):
                        fixed_count += 1
                        processed_threads.add(blocking_thread)
        
        if fixed_count > 0:
            print(f"\n✓ 成功修复 {fixed_count} 个问题")
            time.sleep(2)  # 等待系统稳定
            
            # 重新检查
            print("\n重新检查修复结果...")
            self.analyze_deadlocks()
        else:
            print("\n✓ 没有需要修复的问题")
    
    def start_monitoring(self, interval=10):
        """开始监控死锁"""
        if self.monitoring:
            print("监控已在运行中")
            return
        
        self.monitoring = True
        
        def monitor_loop():
            print(f"开始监控数据库死锁 (间隔: {interval}秒)")
            
            while self.monitoring:
                try:
                    locks = self.check_current_locks()
                    long_trx = self.check_long_running_transactions()
                    
                    if locks or long_trx:
                        print(f"\n[{time.strftime('%Y-%m-%d %H:%M:%S')}] 检测到问题:")
                        if locks:
                            print(f"  - 锁等待: {len(locks)} 个")
                        if long_trx:
                            print(f"  - 长事务: {len(long_trx)} 个")
                        
                        # 自动修复
                        self.auto_fix_deadlocks()
                    
                    time.sleep(interval)
                    
                except Exception as e:
                    print(f"监控过程中出错: {e}")
                    time.sleep(interval)
        
        self.monitor_thread = threading.Thread(target=monitor_loop, daemon=True)
        self.monitor_thread.start()
    
    def stop_monitoring(self):
        """停止监控"""
        self.monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=5)
        print("监控已停止")
    
    def close(self):
        """关闭连接"""
        self.stop_monitoring()
        if self.connection and self.connection.is_connected():
            self.connection.close()
            print("数据库连接已关闭")

def main():
    """主函数"""
    print("DataTransmission 数据库死锁检测工具")
    print("=" * 60)
    
    detector = DeadlockDetector()
    
    if not detector.connect():
        input("按回车键退出...")
        return
    
    try:
        while True:
            print("\n选择操作:")
            print("1. 分析当前死锁情况")
            print("2. 自动修复死锁")
            print("3. 开始监控模式")
            print("4. 停止监控")
            print("5. 退出")
            
            choice = input("\n请输入选项 (1-5): ").strip()
            
            if choice == '1':
                detector.analyze_deadlocks()
            elif choice == '2':
                detector.auto_fix_deadlocks()
            elif choice == '3':
                interval = input("监控间隔(秒，默认10): ").strip()
                interval = int(interval) if interval.isdigit() else 10
                detector.start_monitoring(interval)
            elif choice == '4':
                detector.stop_monitoring()
            elif choice == '5':
                break
            else:
                print("无效选项，请重新选择")
    
    except KeyboardInterrupt:
        print("\n\n用户中断")
    finally:
        detector.close()

if __name__ == "__main__":
    main()
