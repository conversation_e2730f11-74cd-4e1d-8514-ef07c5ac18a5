# DataTransmission CentOS 7 离线程序构建指南

## 🎯 概述
基于conda环境创建可在CentOS 7桌面版本直接运行的离线程序包，无需网络连接即可完整部署。

## 📋 系统要求

### 开发环境（Windows）
- Windows 10/11
- Anaconda或Miniconda
- 已配置的DataTransmission项目环境
- Python 3.8-3.10

### 目标环境（CentOS 7）
- CentOS 7 桌面版本（GNOME/KDE）
- 图形界面环境
- 至少2GB内存
- 至少3GB磁盘空间
- root或sudo权限

## 🚀 构建离线程序包

### 方法一：使用批处理脚本（推荐）

```cmd
# 在Windows开发环境中运行
build_centos7_offline.bat
```

### 方法二：使用Python脚本

```cmd
# 1. 激活项目环境
conda activate your_project_env

# 2. 运行构建脚本
python build_centos7_offline.py
```

### 方法三：手动构建

```cmd
# 1. 激活环境
conda activate your_project_env

# 2. 安装conda-pack
conda install conda-pack -y

# 3. 运行传统部署脚本
create_centos7_deployment.bat
```

## 📦 生成的程序包结构

```
DataTransmission_CentOS7_Offline_YYYYMMDD_HHMMSS/
├── src/                           # 项目源代码
│   ├── main.py
│   ├── database.py
│   ├── web_server.py
│   ├── qr_generator.py
│   ├── camera_monitor.py
│   └── config.py
├── conda_env/                     # conda环境文件
│   ├── datatransmission_env.tar.gz  # 打包的conda环境
│   ├── environment.yml            # 环境配置文件
│   ├── conda_packages.txt         # conda包列表
│   └── pip_packages.txt           # pip包列表
├── scripts/                       # 安装脚本
│   ├── install_centos7.sh         # 主安装脚本
│   └── init_database.sql          # 数据库初始化脚本
└── README.md                      # 使用说明
```

## 🔧 在CentOS 7上部署

### 第一步：传输程序包

#### 方法A：网络传输
```bash
# 使用scp传输
scp -r DataTransmission_CentOS7_Offline_* user@centos7-ip:/tmp/
```

#### 方法B：USB传输（离线）
1. 将程序包复制到USB设备
2. 在CentOS 7上挂载USB并复制到本地

### 第二步：运行安装脚本

```bash
# 1. 进入程序包目录
cd /tmp/DataTransmission_CentOS7_Offline_*

# 2. 给安装脚本执行权限
chmod +x scripts/install_centos7.sh

# 3. 以root权限运行安装脚本
sudo ./scripts/install_centos7.sh
```

安装脚本会自动：
- 检查系统环境
- 安装系统依赖包
- 安装Miniconda
- 解压conda环境
- 安装程序文件
- 创建启动脚本
- 创建桌面快捷方式

### 第三步：配置数据库

```bash
# 1. 配置MariaDB安全设置
sudo mysql_secure_installation

# 2. 初始化数据库
mysql -u root -p < scripts/init_database.sql
```

### 第四步：配置应用程序

```bash
# 编辑配置文件
sudo vi /opt/DataTransmission/config.py
```

确认以下配置：
```python
DATABASE_CONFIG = {
    'host': 'localhost',
    'port': 3306,
    'database': 'DataTransmission',
    'user': 'datatrans',
    'password': 'JKga#123',
    'charset': 'utf8'
}

# 二维码显示配置（900x900像素，居中显示）
QR_DISPLAY_SIZE = 900
QR_DISPLAY_TIME = 2

# 摄像头预览配置（320x240像素，左上角）
CAMERA_PREVIEW_ENABLED = True
CAMERA_PREVIEW_SIZE = (320, 240)
CAMERA_PREVIEW_POSITION = (10, 10)
```

### 第五步：启动程序

#### 方法A：命令行启动
```bash
/opt/DataTransmission/start_datatransmission.sh
```

#### 方法B：桌面快捷方式
双击桌面上的"DataTransmission"图标

## ✅ 功能验证

### 1. 测试HTTP接口
```bash
curl http://localhost:5000/health
```

### 2. 测试数据接收
```bash
curl -X POST http://localhost:5000/receiveData \
  -H "Content-Type: application/json" \
  -d '{"id":"'$(date +%s)'","type":1,"data":"测试数据"}'
```

### 3. 验证图形功能
- 观察是否显示摄像头预览窗口（左上角320x240）
- 插入数据后观察二维码是否在屏幕中央显示（900x900）

## 🔍 故障排除

### 安装阶段问题

1. **"未检测到图形桌面环境"**：
   ```bash
   # 检查DISPLAY变量
   echo $DISPLAY
   
   # 启动图形界面
   startx
   ```

2. **"conda环境解压失败"**：
   ```bash
   # 手动创建环境
   cd /opt/DataTransmission
   conda env create -f conda_env/environment.yml -n datatransmission
   ```

3. **"权限不足"**：
   ```bash
   # 确保使用sudo运行安装脚本
   sudo ./scripts/install_centos7.sh
   ```

### 运行阶段问题

1. **"无法激活conda环境"**：
   ```bash
   # 手动激活环境
   source /opt/miniconda3/etc/profile.d/conda.sh
   conda activate datatransmission
   ```

2. **"摄像头无法访问"**：
   ```bash
   # 检查摄像头设备
   ls /dev/video*
   
   # 添加用户到video组
   sudo usermod -a -G video $(whoami)
   ```

3. **"二维码无法显示"**：
   ```bash
   # 检查图形环境
   echo $DISPLAY
   
   # 测试图形功能
   xclock &
   ```

## 📊 离线程序包特性

### ✅ 优势
- **完全离线**：无需网络连接即可部署
- **环境一致**：使用打包的conda环境，确保依赖一致
- **自动化安装**：一键安装脚本，减少人工操作
- **桌面集成**：自动创建桌面快捷方式
- **完整功能**：保留所有原有功能

### 📈 性能特点
- **安装时间**：约5-10分钟（取决于硬件）
- **程序包大小**：约500MB-1GB（包含完整conda环境）
- **内存占用**：约200-500MB
- **启动时间**：约10-30秒

### 🔒 安全特性
- **环境隔离**：使用独立的conda环境
- **权限控制**：程序以普通用户权限运行
- **数据库安全**：使用专用数据库用户

## 📈 最佳实践

1. **环境准备**：
   ```bash
   # 在Windows上确保环境干净
   conda clean --all
   conda update conda
   ```

2. **传输优化**：
   ```bash
   # 压缩程序包以减少传输时间
   tar -czf datatransmission_centos7.tar.gz DataTransmission_CentOS7_Offline_*
   ```

3. **部署验证**：
   ```bash
   # 部署后运行完整测试
   /opt/DataTransmission/start_datatransmission.sh
   ```

4. **维护更新**：
   ```bash
   # 更新时只需替换src目录下的文件
   sudo cp new_files/* /opt/DataTransmission/
   ```

这个离线程序包方案确保了DataTransmission能够在CentOS 7桌面环境中完全离线部署和运行，保持所有功能的完整性，包括图形界面的二维码显示和摄像头预览功能。
