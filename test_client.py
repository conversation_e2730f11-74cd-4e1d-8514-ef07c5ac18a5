#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json
import time
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)

def test_receive_data_api():
    """测试receiveData API"""
    url = "http://localhost:5000/receiveData"
    
    # 测试数据
    test_cases = [
        {
            "id": str(int(time.time() * 1000)),
            "type": 1,
            "data": "测试数据1"
        },
        {
            "id": str(int(time.time() * 1000) + 1),
            "type": 2,
            "data": "测试数据2"
        },
        {
            "id": str(int(time.time() * 1000) + 2),
            "type": 1,
            "data": "测试数据3"
        }
    ]
    
    print("开始测试receiveData API...")
    
    for i, test_data in enumerate(test_cases, 1):
        try:
            print(f"\n测试用例 {i}: {test_data}")
            
            response = requests.post(url, json=test_data, timeout=10)
            
            print(f"状态码: {response.status_code}")
            print(f"响应: {response.json()}")
            
            if response.status_code == 200:
                print("✓ 测试通过")
            else:
                print("✗ 测试失败")
                
        except requests.exceptions.RequestException as e:
            print(f"✗ 请求失败: {e}")
        except Exception as e:
            print(f"✗ 测试出错: {e}")
        
        time.sleep(1)

def test_health_check():
    """测试健康检查API"""
    url = "http://localhost:5000/health"
    
    print("\n开始测试健康检查API...")
    
    try:
        response = requests.get(url, timeout=5)
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.json()}")
        
        if response.status_code == 200:
            print("✓ 健康检查通过")
        else:
            print("✗ 健康检查失败")
            
    except requests.exceptions.RequestException as e:
        print(f"✗ 请求失败: {e}")
    except Exception as e:
        print(f"✗ 测试出错: {e}")

def test_index_page():
    """测试首页"""
    url = "http://localhost:5000/"
    
    print("\n开始测试首页...")
    
    try:
        response = requests.get(url, timeout=5)
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.json()}")
        
        if response.status_code == 200:
            print("✓ 首页访问成功")
        else:
            print("✗ 首页访问失败")
            
    except requests.exceptions.RequestException as e:
        print(f"✗ 请求失败: {e}")
    except Exception as e:
        print(f"✗ 测试出错: {e}")

def main():
    """主测试函数"""
    print("=" * 50)
    print("数据传输客户端API测试")
    print("=" * 50)
    
    # 等待服务启动
    print("等待服务启动...")
    time.sleep(3)
    
    # 执行测试
    test_health_check()
    test_index_page()
    test_receive_data_api()
    
    print("\n" + "=" * 50)
    print("测试完成")
    print("=" * 50)

if __name__ == "__main__":
    main()
