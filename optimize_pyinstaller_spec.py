#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
PyInstaller规格文件优化脚本
解决打包时的模块导入和依赖问题
"""

import os
import sys

def create_optimized_spec():
    """创建优化的PyInstaller规格文件"""
    
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

# 数据文件
datas = [
    ('config.py', '.'),
    ('README.md', '.'),
]

# 二进制文件（如果需要）
binaries = []

# 隐藏导入（解决动态导入问题）
hiddenimports = [
    # MySQL Connector - 完整模块列表
    'mysql.connector',
    'mysql.connector.locales',
    'mysql.connector.locales.eng',
    'mysql.connector.locales.eng.client_error',
    'mysql.connector.connection',
    'mysql.connector.cursor',
    'mysql.connector.pooling',
    'mysql.connector.errors',
    'mysql.connector.constants',
    'mysql.connector.conversion',
    'mysql.connector.protocol',
    'mysql.connector.utils',
    'mysql.connector.abstracts',
    'mysql.connector.authentication',
    'mysql.connector.charsets',
    'mysql.connector.custom_types',
    'mysql.connector.dbapi',
    'mysql.connector.errorcode',
    'mysql.connector.network',
    'mysql.connector.optionfiles',
    'mysql.connector.plugins',
    
    # PyZBar - 二维码识别
    'pyzbar',
    'pyzbar.pyzbar',
    'pyzbar.wrapper',
    'pyzbar.locations',
    
    # OpenCV - 计算机视觉
    'cv2',
    'cv2.data',
    
    # PIL/Pillow - 图像处理
    'PIL',
    'PIL.Image',
    'PIL.ImageDraw',
    'PIL.ImageFont',
    'PIL.ImageTk',
    'PIL._tkinter_finder',
    'PIL.ImageFile',
    'PIL.JpegImagePlugin',
    'PIL.PngImagePlugin',
    'PIL.BmpImagePlugin',
    'PIL.GifImagePlugin',
    
    # Tkinter - GUI
    'tkinter',
    'tkinter.ttk',
    'tkinter.messagebox',
    'tkinter.filedialog',
    'tkinter.simpledialog',
    'tkinter.colorchooser',
    'tkinter.commondialog',
    'tkinter.constants',
    'tkinter.dnd',
    'tkinter.font',
    'tkinter.scrolledtext',
    
    # APScheduler - 任务调度
    'apscheduler',
    'apscheduler.schedulers',
    'apscheduler.schedulers.background',
    'apscheduler.schedulers.base',
    'apscheduler.triggers',
    'apscheduler.triggers.interval',
    'apscheduler.triggers.date',
    'apscheduler.triggers.cron',
    'apscheduler.executors',
    'apscheduler.executors.pool',
    'apscheduler.jobstores',
    'apscheduler.jobstores.memory',
    'apscheduler.events',
    'apscheduler.util',
    
    # Flask - Web框架
    'flask',
    'flask.json',
    'flask.json.tag',
    'flask.helpers',
    'flask.wrappers',
    'flask.ctx',
    'flask.globals',
    'flask.sessions',
    'flask.templating',
    'flask.signals',
    'flask.blueprints',
    'flask.config',
    'flask.logging',
    
    # Werkzeug - Flask依赖
    'werkzeug',
    'werkzeug.serving',
    'werkzeug.utils',
    'werkzeug.exceptions',
    'werkzeug.routing',
    'werkzeug.wrappers',
    'werkzeug.datastructures',
    'werkzeug.http',
    'werkzeug.urls',
    'werkzeug.formparser',
    'werkzeug.wsgi',
    
    # Jinja2 - 模板引擎
    'jinja2',
    'jinja2.runtime',
    'jinja2.loaders',
    'jinja2.environment',
    'jinja2.lexer',
    'jinja2.parser',
    'jinja2.compiler',
    'jinja2.filters',
    'jinja2.tests',
    'jinja2.utils',
    
    # NumPy - 数值计算
    'numpy',
    'numpy.core',
    'numpy.core._multiarray_umath',
    'numpy.core.multiarray',
    'numpy.core.umath',
    'numpy.linalg',
    'numpy.fft',
    'numpy.random',
    'numpy.lib',
    'numpy.lib.format',
    
    # QRCode - 二维码生成
    'qrcode',
    'qrcode.image',
    'qrcode.image.pil',
    'qrcode.image.svg',
    'qrcode.image.styledpil',
    'qrcode.constants',
    'qrcode.util',
    'qrcode.main',
    
    # Requests - HTTP库
    'requests',
    'requests.adapters',
    'requests.auth',
    'requests.cookies',
    'requests.exceptions',
    'requests.hooks',
    'requests.models',
    'requests.packages',
    'requests.sessions',
    'requests.structures',
    'requests.utils',
    
    # 标准库模块 - 修复PyInstaller运行时错误
    'json',
    'logging',
    'logging.handlers',
    'threading',
    'time',
    'datetime',
    'os',
    'sys',
    'pathlib',
    'sqlite3',
    'urllib',
    'urllib.parse',
    'urllib.request',
    'http',
    'http.server',
    'socketserver',
    'socket',
    'ssl',
    'hashlib',
    'base64',
    'uuid',
    'collections',
    'collections.abc',
    'itertools',
    'functools',
    'operator',
    'copy',
    'copyreg',
    'pickle',
    'tempfile',
    'shutil',
    'glob',
    'fnmatch',
    're',
    'string',
    'textwrap',
    'unicodedata',
    'codecs',
    'locale',
    'calendar',
    'math',
    'random',
    'statistics',
    'decimal',
    'fractions',
    'numbers',
    'cmath',
    'struct',
    'array',
    'weakref',
    'types',
    'inspect',
    'importlib',
    'importlib.util',
    'importlib.machinery',
    'importlib.abc',
    'pkgutil',
    'modulefinder',
    'runpy',
    'argparse',
    'getopt',
    'configparser',
    'fileinput',
    'linecache',
    'traceback',
    'pdb',
    'profile',
    'timeit',
    'trace',
    'gc',
    'builtins',
    'warnings',
    'contextlib',
    'abc',
    'atexit',
    'tracemalloc',
    'faulthandler',

    # 修复PyInstaller运行时错误的关键模块
    'reprlib',
    'keyword',
    'token',
    'tokenize',
    'io',
    'encodings',
    'encodings.utf_8',
    'encodings.latin_1',
    'encodings.cp1252',
    'encodings.ascii',
    'encodings.idna',
    'encodings.mbcs',
]

# 排除的模块（减少文件大小，但保留必要模块）
excludes = [
    'matplotlib',
    'scipy',
    'pandas',
    'jupyter',
    'notebook',
    'IPython',
    'pytest',
    'setuptools',
    'distutils',
    'test',
    'tests',
    'unittest',
    'doctest',
    'pydoc',
    'xmlrpc',
    'email',
    'html',
    'xml',
    'multiprocessing',
    'concurrent',
    'asyncio',
    'queue',
    'dbm',
    'shelve',
    'marshal',
    'xdrlib',
    # 注意：不排除 reprlib, collections.abc 等关键模块
    'heapq',
    'bisect',
    'pprint',
    'difflib',
    'stringprep',
    'readline',
    'rlcompleter',
    'cmd',
    'shlex',
    'tkinter.tix',
    'tkinter.turtle',
    'turtle',
    'turtledemo',
]

a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=binaries,
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=excludes,
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='DataTransmission',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='app_icon.ico' if os.path.exists('app_icon.ico') else None,
)
'''
    
    with open('DataTransmission_optimized.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("✓ 优化的PyInstaller规格文件已创建: DataTransmission_optimized.spec")
    return True

def main():
    """主函数"""
    print("PyInstaller规格文件优化工具")
    print("创建包含完整依赖的优化规格文件")
    
    if create_optimized_spec():
        print("\n使用方法:")
        print("pyinstaller --clean --noconfirm DataTransmission_optimized.spec")
        print("\n或者在build_windows_exe.py中使用这个规格文件")
    else:
        print("创建规格文件失败")

if __name__ == "__main__":
    main()
