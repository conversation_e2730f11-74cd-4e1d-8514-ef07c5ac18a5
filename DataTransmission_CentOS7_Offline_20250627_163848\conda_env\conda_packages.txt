# This file may be used to create an environment using:
# $ conda create --name <env> --file <this file>
# platform: win-64
# created-by: conda 24.11.3
altgraph=0.17.4=pypi_0
apscheduler=3.10.4=pypi_0
blinker=1.9.0=pypi_0
bzip2=1.0.8=h2bbff1b_6
ca-certificates=2025.2.25=haa95532_0
certifi=2025.6.15=pypi_0
charset-normalizer=3.4.2=pypi_0
click=8.2.1=pypi_0
colorama=0.4.6=pypi_0
conda-pack=0.7.1=py310haa95532_0
expat=2.7.1=h8ddb27b_0
flask=2.3.3=pypi_0
idna=3.10=pypi_0
itsdangerous=2.2.0=pypi_0
jinja2=3.1.6=pypi_0
libffi=3.4.4=hd77b12b_1
markupsafe=3.0.2=pypi_0
mysql-connector-python=8.1.0=pypi_0
numpy=1.24.3=pypi_0
opencv-python=********=pypi_0
openssl=3.0.16=h3f729d1_0
packaging=25.0=pypi_0
pefile=2023.2.7=pypi_0
pillow=10.0.1=pypi_0
pip=25.1=pyhc872135_2
protobuf=4.21.12=pypi_0
pyinstaller=6.14.1=pypi_0
pyinstaller-hooks-contrib=2025.5=pypi_0
pypng=0.20220715.0=pypi_0
python=3.10.18=h981015d_0
pytz=2025.2=pypi_0
pywin32-ctypes=0.2.3=pypi_0
pyzbar=0.1.9=pypi_0
qrcode=7.4.2=pypi_0
requests=2.31.0=pypi_0
setuptools=78.1.1=py310haa95532_0
six=1.17.0=pypi_0
sqlite=3.45.3=h2bbff1b_0
tk=8.6.14=h5e9d12e_1
typing-extensions=4.14.0=pypi_0
tzdata=2025.2=pypi_0
tzlocal=5.3.1=pypi_0
urllib3=2.5.0=pypi_0
vc=14.42=haa95532_5
vs2015_runtime=14.42.34433=hbfb602d_5
werkzeug=3.1.3=pypi_0
wheel=0.45.1=py310haa95532_0
xz=5.6.4=h4754444_1
zlib=1.2.13=h8cc25b3_1
