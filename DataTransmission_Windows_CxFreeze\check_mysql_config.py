#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
MySQL配置检查工具
检查MySQL配置文件和连接设置
"""

import os
import sys
import configparser
import subprocess

def find_mysql_config_files():
    """查找MySQL配置文件"""
    print("=" * 50)
    print("查找MySQL配置文件")
    print("=" * 50)
    
    possible_locations = [
        "C:\\ProgramData\\MySQL\\MySQL Server 8.0\\my.ini",
        "C:\\Program Files\\MySQL\\MySQL Server 8.0\\my.ini",
        "C:\\mysql\\my.ini",
        "C:\\my.ini",
        "%WINDIR%\\my.ini",
        "%MYSQL_HOME%\\my.ini"
    ]
    
    found_configs = []
    
    for location in possible_locations:
        expanded_location = os.path.expandvars(location)
        if os.path.exists(expanded_location):
            print(f"✓ 找到配置文件: {expanded_location}")
            found_configs.append(expanded_location)
        else:
            print(f"✗ 配置文件不存在: {expanded_location}")
    
    if not found_configs:
        print("\n⚠️ 未找到MySQL配置文件")
        print("MySQL可能使用默认配置")
    
    return found_configs

def check_mysql_config(config_file):
    """检查MySQL配置文件内容"""
    print(f"\n" + "=" * 50)
    print(f"检查配置文件: {config_file}")
    print("=" * 50)
    
    try:
        # 读取配置文件
        config = configparser.ConfigParser()
        config.read(config_file, encoding='utf-8')
        
        # 检查关键配置项
        sections_to_check = ['mysqld', 'mysql']
        
        for section_name in sections_to_check:
            if section_name in config:
                print(f"\n[{section_name}] 配置:")
                section = config[section_name]
                
                # 检查端口配置
                port = section.get('port', '3306')
                print(f"  port = {port}")
                
                # 检查绑定地址
                bind_address = section.get('bind-address', '未设置')
                print(f"  bind-address = {bind_address}")
                
                if bind_address == '127.0.0.1':
                    print("  ⚠️ MySQL只绑定到本地地址")
                elif bind_address == '0.0.0.0':
                    print("  ✓ MySQL绑定到所有地址")
                
                # 检查数据目录
                datadir = section.get('datadir', '未设置')
                print(f"  datadir = {datadir}")
                
                # 检查日志文件
                log_error = section.get('log-error', '未设置')
                print(f"  log-error = {log_error}")
                
                # 检查其他重要配置
                other_configs = ['socket', 'pid-file', 'skip-networking']
                for config_key in other_configs:
                    if config_key in section:
                        value = section[config_key]
                        print(f"  {config_key} = {value}")
                        
                        if config_key == 'skip-networking' and value.lower() in ['1', 'true', 'yes']:
                            print("  ⚠️ 网络连接被禁用 (skip-networking)")
        
        return True
        
    except Exception as e:
        print(f"✗ 读取配置文件失败: {e}")
        return False

def check_mysql_variables():
    """检查MySQL运行时变量"""
    print(f"\n" + "=" * 50)
    print("检查MySQL运行时变量")
    print("=" * 50)
    
    try:
        # 尝试连接MySQL并查询变量
        import mysql.connector
        import config
        
        if not hasattr(config, 'DATABASE_CONFIG'):
            print("✗ 无法加载数据库配置")
            return False
        
        db_config = config.DATABASE_CONFIG
        
        # 创建不指定数据库的连接
        connect_config = {
            'host': db_config['host'],
            'port': db_config['port'],
            'user': db_config['user'],
            'password': db_config['password'],
            'connection_timeout': 5
        }
        
        connection = mysql.connector.connect(**connect_config)
        cursor = connection.cursor()
        
        # 查询关键变量
        variables_to_check = [
            'port',
            'bind_address',
            'datadir',
            'log_error',
            'skip_networking',
            'version'
        ]
        
        for var_name in variables_to_check:
            try:
                cursor.execute(f"SHOW VARIABLES LIKE '{var_name}'")
                result = cursor.fetchone()
                if result:
                    print(f"  {result[0]} = {result[1]}")
                else:
                    print(f"  {var_name} = 未找到")
            except Exception as e:
                print(f"  {var_name} = 查询失败: {e}")
        
        cursor.close()
        connection.close()
        return True
        
    except ImportError:
        print("✗ mysql.connector 模块未安装")
        return False
    except Exception as e:
        print(f"✗ 连接MySQL失败: {e}")
        return False

def check_mysql_processes():
    """检查MySQL相关进程"""
    print(f"\n" + "=" * 50)
    print("检查MySQL进程")
    print("=" * 50)
    
    try:
        result = subprocess.run(['tasklist', '/FI', 'IMAGENAME eq mysqld.exe', '/FO', 'CSV'], 
                              capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            lines = result.stdout.strip().split('\n')
            if len(lines) > 1:  # 有标题行
                print("✓ 找到MySQL进程:")
                for line in lines[1:]:  # 跳过标题行
                    parts = line.split(',')
                    if len(parts) >= 2:
                        process_name = parts[0].strip('"')
                        pid = parts[1].strip('"')
                        print(f"  进程: {process_name}, PID: {pid}")
            else:
                print("✗ 未找到MySQL进程")
        else:
            print("✗ 无法查询进程信息")
            
    except Exception as e:
        print(f"✗ 检查进程失败: {e}")

def check_firewall_settings():
    """检查防火墙设置"""
    print(f"\n" + "=" * 50)
    print("检查防火墙设置")
    print("=" * 50)
    
    try:
        # 检查防火墙状态
        result = subprocess.run(['netsh', 'advfirewall', 'show', 'allprofiles', 'state'], 
                              capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            print("防火墙状态:")
            lines = result.stdout.split('\n')
            for line in lines:
                if 'State' in line:
                    print(f"  {line.strip()}")
        
        # 检查MySQL端口规则
        result = subprocess.run(['netsh', 'advfirewall', 'firewall', 'show', 'rule', 'name=all'], 
                              capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            mysql_rules = []
            lines = result.stdout.split('\n')
            for line in lines:
                if '3306' in line or 'mysql' in line.lower():
                    mysql_rules.append(line.strip())
            
            if mysql_rules:
                print("\nMySQL相关防火墙规则:")
                for rule in mysql_rules[:5]:  # 只显示前5个
                    print(f"  {rule}")
            else:
                print("\n⚠️ 未找到MySQL相关防火墙规则")
                print("可能需要添加防火墙规则允许3306端口")
        
    except Exception as e:
        print(f"✗ 检查防火墙失败: {e}")

def main():
    """主函数"""
    print("=" * 60)
    print("MySQL配置检查工具")
    print("=" * 60)
    
    # 1. 查找配置文件
    config_files = find_mysql_config_files()
    
    # 2. 检查配置文件内容
    for config_file in config_files:
        check_mysql_config(config_file)
    
    # 3. 检查MySQL进程
    check_mysql_processes()
    
    # 4. 检查运行时变量
    check_mysql_variables()
    
    # 5. 检查防火墙设置
    check_firewall_settings()
    
    # 总结和建议
    print(f"\n" + "=" * 60)
    print("检查完成 - 常见问题解决方案")
    print("=" * 60)
    
    print("\n如果MySQL连接卡住，请检查:")
    print("1. MySQL服务是否正在运行: net start mysql")
    print("2. 端口3306是否在监听: netstat -ano | findstr :3306")
    print("3. 防火墙是否阻止连接")
    print("4. MySQL配置中的bind-address设置")
    print("5. 是否启用了skip-networking")
    
    print("\n常见修复方法:")
    print("1. 重启MySQL服务: net stop mysql && net start mysql")
    print("2. 检查MySQL错误日志")
    print("3. 重新安装MySQL")
    print("4. 添加防火墙规则: netsh advfirewall firewall add rule name=\"MySQL\" dir=in action=allow protocol=TCP localport=3306")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n用户中断检查")
    except Exception as e:
        print(f"\n检查过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        input("按回车键退出...")
