@echo off
chcp 65001 >nul
title DataTransmission 调试启动

echo ========================================
echo    DataTransmission 调试启动工具
echo ========================================
echo.

echo 当前目录: %CD%
echo.

echo 检查文件是否存在...
if exist "DataTransmission.exe" (
    echo ✓ DataTransmission.exe 存在
) else (
    echo ✗ DataTransmission.exe 不存在
    pause
    exit /b 1
)

if exist "config.py" (
    echo ✓ config.py 存在
) else (
    echo ✗ config.py 不存在
    pause
    exit /b 1
)

echo.
echo 运行诊断脚本...
echo ========================================

:: 首先运行诊断脚本
python diagnose_windows.py

echo.
echo ========================================
echo 启动主程序（调试模式）...
echo ========================================

:: 启动主程序并捕获输出
DataTransmission.exe 2>&1

echo.
echo 程序已退出，错误代码: %ERRORLEVEL%
echo.

:: 检查是否有日志文件
if exist "data_transmission.log" (
    echo 发现日志文件，显示最后20行:
    echo ----------------------------------------
    powershell "Get-Content 'data_transmission.log' -Tail 20"
    echo ----------------------------------------
) else (
    echo 未找到日志文件 data_transmission.log
)

if exist "diagnose.log" (
    echo.
    echo 诊断日志文件已生成: diagnose.log
)

echo.
echo 调试信息收集完成
pause
