@echo off
chcp 65001 >nul
title 安装DataTransmission依赖包

echo ========================================
echo    DataTransmission 依赖包安装工具
echo ========================================
echo.

echo 检查Python环境...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ✗ Python未安装或不在PATH中
    echo   请先安装Python 3.8或更高版本
    pause
    exit /b 1
)

python --version
echo ✓ Python环境正常

echo.
echo 检查pip...
python -m pip --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ✗ pip不可用
    echo   请检查Python安装
    pause
    exit /b 1
)

echo ✓ pip可用

echo.
echo 升级pip...
python -m pip install --upgrade pip

echo.
echo 开始安装依赖包...
echo ========================================

echo 1/8 安装MySQL连接器...
python -m pip install mysql-connector-python
if %errorlevel% neq 0 (
    echo ✗ mysql-connector-python 安装失败
) else (
    echo ✓ mysql-connector-python 安装成功
)

echo.
echo 2/8 安装OpenCV...
python -m pip install opencv-python
if %errorlevel% neq 0 (
    echo ✗ opencv-python 安装失败
) else (
    echo ✓ opencv-python 安装成功
)

echo.
echo 3/8 安装二维码生成库...
python -m pip install qrcode[pil]
if %errorlevel% neq 0 (
    echo ✗ qrcode 安装失败
) else (
    echo ✓ qrcode 安装成功
)

echo.
echo 4/8 安装二维码识别库...
python -m pip install pyzbar
if %errorlevel% neq 0 (
    echo ✗ pyzbar 安装失败
) else (
    echo ✓ pyzbar 安装成功
)

echo.
echo 5/8 安装图像处理库...
python -m pip install Pillow
if %errorlevel% neq 0 (
    echo ✗ Pillow 安装失败
) else (
    echo ✓ Pillow 安装成功
)

echo.
echo 6/8 安装Flask Web框架...
python -m pip install Flask
if %errorlevel% neq 0 (
    echo ✗ Flask 安装失败
) else (
    echo ✓ Flask 安装成功
)

echo.
echo 7/8 安装任务调度器...
python -m pip install APScheduler
if %errorlevel% neq 0 (
    echo ✗ APScheduler 安装失败
) else (
    echo ✓ APScheduler 安装成功
)

echo.
echo 8/8 安装NumPy...
python -m pip install numpy
if %errorlevel% neq 0 (
    echo ✗ numpy 安装失败
) else (
    echo ✓ numpy 安装成功
)

echo.
echo ========================================
echo 安装完成！
echo ========================================

echo.
echo 验证安装...
python -c "import mysql.connector; print('✓ mysql.connector')" 2>nul || echo "✗ mysql.connector"
python -c "import cv2; print('✓ cv2')" 2>nul || echo "✗ cv2"
python -c "import qrcode; print('✓ qrcode')" 2>nul || echo "✗ qrcode"
python -c "import pyzbar; print('✓ pyzbar')" 2>nul || echo "✗ pyzbar"
python -c "import PIL; print('✓ PIL')" 2>nul || echo "✗ PIL"
python -c "import flask; print('✓ flask')" 2>nul || echo "✗ flask"
python -c "import apscheduler; print('✓ apscheduler')" 2>nul || echo "✗ apscheduler"
python -c "import numpy; print('✓ numpy')" 2>nul || echo "✗ numpy"

echo.
echo 如果所有模块都显示✓，则可以运行DataTransmission程序
echo 如果有模块显示✗，请手动安装对应的包
echo.
pause
