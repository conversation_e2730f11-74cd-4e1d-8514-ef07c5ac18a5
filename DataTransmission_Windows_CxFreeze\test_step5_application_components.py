#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
步骤5: 应用程序组件测试
测试DataTransmission各个组件的初始化
"""

import sys
import traceback
import os

def test_database_manager():
    """测试数据库管理器"""
    print("=" * 50)
    print("数据库管理器测试")
    print("=" * 50)
    
    try:
        # 检查database.py文件是否存在
        if not os.path.exists('database.py'):
            print("✗ database.py 文件不存在")
            return False
        
        from database import DatabaseManager
        print("✓ DatabaseManager 类导入成功")
        
        # 尝试创建实例
        db_manager = DatabaseManager()
        print("✓ DatabaseManager 实例创建成功")
        
        # 测试基本方法
        if hasattr(db_manager, 'connect'):
            print("✓ connect 方法存在")
        if hasattr(db_manager, 'close'):
            print("✓ close 方法存在")
        if hasattr(db_manager, 'execute_query'):
            print("✓ execute_query 方法存在")
        
        return True
        
    except ImportError as e:
        print(f"✗ DatabaseManager 导入失败: {e}")
        return False
    except Exception as e:
        print(f"✗ DatabaseManager 测试异常: {e}")
        traceback.print_exc()
        return False

def test_web_server():
    """测试Web服务器"""
    print("\n" + "=" * 50)
    print("Web服务器测试")
    print("=" * 50)
    
    try:
        # 检查web_server.py文件是否存在
        if not os.path.exists('web_server.py'):
            print("✗ web_server.py 文件不存在")
            return False
        
        from web_server import WebServer
        print("✓ WebServer 类导入成功")
        
        # 创建模拟的数据库管理器
        class MockDatabaseManager:
            pass
        
        mock_db = MockDatabaseManager()
        
        # 尝试创建实例
        web_server = WebServer(mock_db)
        print("✓ WebServer 实例创建成功")
        
        # 测试基本方法
        if hasattr(web_server, 'run'):
            print("✓ run 方法存在")
        if hasattr(web_server, 'app'):
            print("✓ Flask app 属性存在")
        
        return True
        
    except ImportError as e:
        print(f"✗ WebServer 导入失败: {e}")
        return False
    except Exception as e:
        print(f"✗ WebServer 测试异常: {e}")
        traceback.print_exc()
        return False

def test_qr_generator():
    """测试二维码生成器"""
    print("\n" + "=" * 50)
    print("二维码生成器测试")
    print("=" * 50)
    
    try:
        # 检查qr_generator.py文件是否存在
        if not os.path.exists('qr_generator.py'):
            print("✗ qr_generator.py 文件不存在")
            return False
        
        from qr_generator import QRGenerator
        print("✓ QRGenerator 类导入成功")
        
        # 创建模拟的数据库管理器
        class MockDatabaseManager:
            pass
        
        mock_db = MockDatabaseManager()
        
        # 尝试创建实例
        qr_generator = QRGenerator(mock_db)
        print("✓ QRGenerator 实例创建成功")
        
        # 测试基本方法
        if hasattr(qr_generator, 'start'):
            print("✓ start 方法存在")
        if hasattr(qr_generator, 'stop'):
            print("✓ stop 方法存在")
        if hasattr(qr_generator, 'generate_qr'):
            print("✓ generate_qr 方法存在")
        
        return True
        
    except ImportError as e:
        print(f"✗ QRGenerator 导入失败: {e}")
        return False
    except Exception as e:
        print(f"✗ QRGenerator 测试异常: {e}")
        traceback.print_exc()
        return False

def test_camera_monitor():
    """测试摄像头监控"""
    print("\n" + "=" * 50)
    print("摄像头监控测试")
    print("=" * 50)
    
    try:
        # 检查camera_monitor.py文件是否存在
        if not os.path.exists('camera_monitor.py'):
            print("✗ camera_monitor.py 文件不存在")
            return False
        
        from camera_monitor import CameraMonitor
        print("✓ CameraMonitor 类导入成功")
        
        # 创建模拟的数据库管理器
        class MockDatabaseManager:
            pass
        
        mock_db = MockDatabaseManager()
        
        # 尝试创建实例
        camera_monitor = CameraMonitor(mock_db)
        print("✓ CameraMonitor 实例创建成功")
        
        # 测试基本方法
        if hasattr(camera_monitor, 'start'):
            print("✓ start 方法存在")
        if hasattr(camera_monitor, 'stop'):
            print("✓ stop 方法存在")
        
        return True
        
    except ImportError as e:
        print(f"✗ CameraMonitor 导入失败: {e}")
        return False
    except Exception as e:
        print(f"✗ CameraMonitor 测试异常: {e}")
        traceback.print_exc()
        return False

def test_main_application():
    """测试主应用程序"""
    print("\n" + "=" * 50)
    print("主应用程序测试")
    print("=" * 50)
    
    try:
        # 检查main.py文件是否存在
        if not os.path.exists('main.py'):
            print("✗ main.py 文件不存在")
            return False
        
        # 尝试导入main模块（不执行）
        import importlib.util
        spec = importlib.util.spec_from_file_location("main", "main.py")
        main_module = importlib.util.module_from_spec(spec)
        
        print("✓ main.py 文件语法检查通过")
        
        # 检查是否有main函数
        spec.loader.exec_module(main_module)
        if hasattr(main_module, 'main'):
            print("✓ main 函数存在")
        
        # 检查是否有DataTransmissionClient类
        if hasattr(main_module, 'DataTransmissionClient'):
            print("✓ DataTransmissionClient 类存在")
        
        return True
        
    except SyntaxError as e:
        print(f"✗ main.py 语法错误: {e}")
        return False
    except Exception as e:
        print(f"✗ main.py 测试异常: {e}")
        traceback.print_exc()
        return False

def test_component_integration():
    """测试组件集成"""
    print("\n" + "=" * 50)
    print("组件集成测试")
    print("=" * 50)
    
    try:
        # 尝试创建所有组件的实例
        from database import DatabaseManager
        from web_server import WebServer
        from qr_generator import QRGenerator
        from camera_monitor import CameraMonitor
        
        print("✓ 所有组件类导入成功")
        
        # 创建数据库管理器
        db_manager = DatabaseManager()
        print("✓ 数据库管理器创建成功")
        
        # 创建其他组件
        web_server = WebServer(db_manager)
        qr_generator = QRGenerator(db_manager)
        camera_monitor = CameraMonitor(db_manager)
        
        print("✓ 所有组件实例创建成功")
        print("✓ 组件间依赖关系正常")
        
        return True
        
    except Exception as e:
        print(f"✗ 组件集成测试失败: {e}")
        traceback.print_exc()
        return False

def test_file_structure():
    """测试文件结构"""
    print("\n" + "=" * 50)
    print("文件结构测试")
    print("=" * 50)
    
    required_files = [
        ('main.py', '主程序文件'),
        ('config.py', '配置文件'),
        ('database.py', '数据库管理模块'),
        ('web_server.py', 'Web服务器模块'),
        ('qr_generator.py', '二维码生成模块'),
        ('camera_monitor.py', '摄像头监控模块'),
    ]
    
    optional_files = [
        ('templates/', '模板目录'),
        ('static/', '静态文件目录'),
    ]
    
    missing_files = []
    
    print("检查必需文件:")
    for filename, description in required_files:
        if os.path.exists(filename):
            print(f"✓ {filename} ({description})")
        else:
            print(f"✗ {filename} ({description}) - 缺失")
            missing_files.append(filename)
    
    print("\n检查可选文件:")
    for filename, description in optional_files:
        if os.path.exists(filename):
            print(f"✓ {filename} ({description})")
        else:
            print(f"⚠️ {filename} ({description}) - 可选")
    
    if missing_files:
        print(f"\n✗ 缺少必需文件: {missing_files}")
        return False
    else:
        print("\n✓ 所有必需文件都存在")
        return True

def main():
    """主函数"""
    print("=" * 60)
    print("步骤5: 应用程序组件测试")
    print("=" * 60)
    
    # 测试文件结构
    file_structure_ok = test_file_structure()
    
    # 如果文件结构不完整，跳过其他测试
    if not file_structure_ok:
        print("\n❌ 文件结构不完整，无法进行组件测试")
        print("请确保所有必需的Python文件都存在")
        input("按回车键退出...")
        return
    
    # 测试各个组件
    db_manager_ok = test_database_manager()
    web_server_ok = test_web_server()
    qr_generator_ok = test_qr_generator()
    camera_monitor_ok = test_camera_monitor()
    main_app_ok = test_main_application()
    
    # 测试组件集成
    integration_ok = False
    if all([db_manager_ok, web_server_ok, qr_generator_ok, camera_monitor_ok]):
        integration_ok = test_component_integration()
    
    # 总结
    print("\n" + "=" * 60)
    print("步骤5测试结果总结")
    print("=" * 60)
    
    results = [
        ("文件结构", file_structure_ok),
        ("数据库管理器", db_manager_ok),
        ("Web服务器", web_server_ok),
        ("二维码生成器", qr_generator_ok),
        ("摄像头监控", camera_monitor_ok),
        ("主应用程序", main_app_ok),
        ("组件集成", integration_ok),
    ]
    
    all_success = True
    for test_name, result in results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name}: {status}")
        if not result:
            all_success = False
    
    print()
    if all_success:
        print("🎉 应用程序组件测试全部通过！")
        print("所有组件都可以正常初始化")
        print("现在可以尝试启动完整的应用程序")
    else:
        print("❌ 应用程序组件测试失败")
        print("请检查失败的组件")
        
        # 提供针对性建议
        if not file_structure_ok:
            print("\n💡 文件结构问题:")
            print("- 确保所有Python文件都在正确位置")
            print("- 检查是否有文件被意外删除或移动")
        
        if not integration_ok and all([db_manager_ok, web_server_ok, qr_generator_ok, camera_monitor_ok]):
            print("\n💡 组件集成问题:")
            print("- 各组件单独测试通过，但集成时失败")
            print("- 可能是组件间接口不匹配")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n用户中断测试")
    except Exception as e:
        print(f"\n测试过程中出现未预期的错误: {e}")
        traceback.print_exc()
        input("按回车键退出...")
