# DataTransmission CentOS 7 简化离线程序包

## 系统要求
- CentOS 7 桌面版本
- 图形界面环境 (GNOME/KDE)
- 至少2GB内存
- 至少1GB磁盘空间

## 安装步骤

### 1. 传输文件
将整个程序包传输到CentOS 7机器

### 2. 运行安装脚本
```bash
cd DataTransmission_CentOS7_Simple_20250627_162245
sudo ./scripts/install_centos7.sh
```

### 3. 配置数据库
```bash
# 配置MariaDB
sudo mysql_secure_installation

# 初始化数据库
mysql -u root -p < scripts/init_database.sql
```

### 4. 配置应用程序
```bash
sudo vi /opt/DataTransmission/config.py
```

### 5. 启动程序
```bash
# 方法1: 命令行启动
/opt/DataTransmission/start_datatransmission.sh

# 方法2: 双击桌面快捷方式
```

## 功能特性
- HTTP API接口 (端口5000)
- 二维码生成和显示 (900x900像素，居中)
- 摄像头监控和二维码识别
- 实时摄像头预览窗口 (320x240像素，左上角)
- 数据库存储和管理

## 注意事项
- 此版本使用系统Python3和pip安装依赖
- 不依赖conda环境
- 适合简单部署场景

## 故障排除
1. 确保在图形桌面环境中运行
2. 检查摄像头权限和驱动
3. 确认数据库配置正确
4. 查看程序日志获取详细错误信息
