# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

# 数据文件
datas = [
    ('config.py', '.'),
    ('README.md', '.'),
]

# 隐藏导入（解决动态导入问题）
hiddenimports = [
    # MySQL Connector
    'mysql.connector',
    'mysql.connector.locales.eng',
    'mysql.connector.locales.eng.client_error',
    'mysql.connector.connection',
    'mysql.connector.cursor',
    'mysql.connector.pooling',
    'mysql.connector.errors',

    # PyZBar
    'pyzbar',
    'pyzbar.pyzbar',
    'pyzbar.wrapper',

    # OpenCV
    'cv2',
    'cv2.data',

    # PIL/Pillow
    'PIL',
    'PIL.Image',
    'PIL.ImageTk',
    'PIL._tkinter_finder',

    # Tkinter
    'tkinter',
    'tkinter.ttk',
    'tkinter.messagebox',
    'tkinter.filedialog',

    # APScheduler
    'apscheduler',
    'apscheduler.schedulers.background',
    'apscheduler.triggers.interval',
    'apscheduler.executors.pool',
    'apscheduler.jobstores.memory',

    # Flask
    'flask',
    'flask.json.tag',
    'flask.helpers',
    'flask.wrappers',

    # NumPy
    'numpy',
    'numpy.core',
    'numpy.core._multiarray_umath',

    # QRCode
    'qrcode',
    'qrcode.image.pil',
    'qrcode.image.svg',

    # 其他可能需要的模块
    'json',
    'logging',
    'threading',
    'time',
    'datetime',
]

# 排除的模块（减少文件大小）
excludes = [
    'matplotlib',
    'scipy',
    'pandas',
    'jupyter',
    'notebook',
    'IPython',
    'pytest',
    'setuptools',
]

a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=[],
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=excludes,
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='DataTransmission',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='app_icon.ico' if os.path.exists('app_icon.ico') else None,
)
