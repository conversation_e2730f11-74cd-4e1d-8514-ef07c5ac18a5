Documentation.addTranslations({
    "locale": "el",
    "messages": {
        "%(filename)s &#8212; %(docstitle)s": "%(filename)s &#8212; %(docstitle)s",
        "&#169; %(copyright_prefix)s %(copyright)s.": "",
        ", in ": ", \u03c3\u03c4\u03bf ",
        "About these documents": "\u03a3\u03c7\u03b5\u03c4\u03b9\u03ba\u03ac \u03bc\u03b5 \u03b1\u03c5\u03c4\u03ac \u03c4\u03b1 \u03ba\u03b5\u03af\u03bc\u03b5\u03bd\u03b1",
        "Automatically generated list of changes in version %(version)s": "\u0391\u03c5\u03c4\u03cc\u03bc\u03b1\u03c4\u03b1 \u03c0\u03b1\u03c1\u03b1\u03b3\u03cc\u03bc\u03b5\u03bd\u03b7 \u03bb\u03af\u03c3\u03c4\u03b1 \u03b1\u03bb\u03bb\u03b1\u03b3\u03ce\u03bd \u03c3\u03c4\u03b7\u03bd \u03ad\u03ba\u03b4\u03bf\u03c3\u03b7 %(version)s",
        "C API changes": "\u0391\u03bb\u03bb\u03b1\u03b3\u03ad\u03c2 \u03c3\u03c4\u03bf API \u03c4\u03b7\u03c2 C",
        "Changes in Version %(version)s &#8212; %(docstitle)s": "\u0391\u03bb\u03bb\u03b1\u03b3\u03ad\u03c2 \u03c3\u03c4\u03b7\u03bd \u0388\u03ba\u03b4\u03bf\u03c3\u03b7 %(version)s &#8212'\n%(docstitle)s",
        "Collapse sidebar": "\u039a\u03bb\u03b5\u03af\u03c3\u03b9\u03bc\u03bf \u03c0\u03bb\u03b1\u03ca\u03bd\u03ae\u03c2 \u03bc\u03c0\u03ac\u03c1\u03b1\u03c2",
        "Complete Table of Contents": "\u03a0\u03bb\u03ae\u03c1\u03b7\u03c2 \u03a0\u03af\u03bd\u03b1\u03ba\u03b1\u03c2 \u03a0\u03b5\u03c1\u03b9\u03b5\u03c7\u03bf\u03bc\u03ad\u03bd\u03c9\u03bd",
        "Contents": "\u03a0\u03b5\u03c1\u03b9\u03b5\u03c7\u03cc\u03bc\u03b5\u03bd\u03b1",
        "Copyright": "Copyright",
        "Created using <a href=\"https://www.sphinx-doc.org/\">Sphinx</a> %(sphinx_version)s.": "",
        "Expand sidebar": "\u0386\u03bd\u03bf\u03b9\u03b3\u03bc\u03b1 \u03c0\u03bb\u03b1\u03ca\u03bd\u03ae\u03c2 \u03bc\u03c0\u03ac\u03c1\u03b1\u03c2",
        "Full index on one page": "\u03a0\u03bb\u03ae\u03c1\u03b5\u03c2 \u03b5\u03c5\u03c1\u03b5\u03c4\u03ae\u03c1\u03b9\u03bf \u03c3\u03b5 \u03bc\u03af\u03b1 \u03c3\u03b5\u03bb\u03af\u03b4\u03b1",
        "General Index": "\u039a\u03b5\u03bd\u03c4\u03c1\u03b9\u03ba\u03cc \u0395\u03c5\u03c1\u03b5\u03c4\u03ae\u03c1\u03b9\u03bf\u03bf",
        "Global Module Index": "\u039a\u03b1\u03b8\u03bf\u03bb\u03b9\u03ba\u03cc \u0395\u03c5\u03c1\u03b5\u03c4\u03ae\u03c1\u03b9\u03bf \u039c\u03bf\u03bd\u03ac\u03b4\u03c9\u03bd",
        "Go": "\u03a0\u03ac\u03bc\u03b5",
        "Hide Search Matches": "\u0391\u03c0\u03cc\u03ba\u03c1\u03c5\u03c8\u03b7 \u0395\u03c5\u03c1\u03b5\u03b8\u03ad\u03bd\u03c4\u03c9\u03bd \u0391\u03bd\u03b1\u03b6\u03b7\u03c4\u03ae\u03c3\u03b5\u03c9\u03bd",
        "Index": "\u0395\u03c5\u03c1\u03b5\u03c4\u03ae\u03c1\u03b9\u03bf",
        "Index &ndash; %(key)s": "\u0395\u03c5\u03c1\u03b5\u03c4\u03ae\u03c1\u03b9\u03bf &ndash; %(key)s",
        "Index pages by letter": "\u03a3\u03b5\u03bb\u03af\u03b4\u03b5\u03c2 \u03b5\u03c5\u03c1\u03b5\u03c4\u03b7\u03c1\u03af\u03bf\u03c5 \u03b1\u03bd\u03ac \u03b3\u03c1\u03ac\u03bc\u03bc\u03b1",
        "Indices and tables:": "\u0395\u03c5\u03c1\u03b5\u03c4\u03ae\u03c1\u03b9\u03b1 \u03ba\u03b1\u03b9 \u03c0\u03af\u03bd\u03b1\u03ba\u03b5\u03c2:",
        "Last updated on %(last_updated)s.": "\u03a4\u03b5\u03bb\u03b5\u03c5\u03c4\u03b1\u03af\u03b1 \u03b5\u03bd\u03b7\u03bc\u03ad\u03c1\u03c9\u03c3\u03b7 \u03c3\u03c4\u03b9\u03c2 %(last_updated)s.",
        "Library changes": "\u0391\u03bb\u03bb\u03b1\u03b3\u03ad\u03c2 \u03b2\u03b9\u03b2\u03bb\u03b9\u03bf\u03b8\u03ae\u03ba\u03b7\u03c2",
        "Navigation": "\u03a0\u03bb\u03bf\u03ae\u03b3\u03b7\u03c3\u03b7",
        "Next topic": "\u0395\u03c0\u03cc\u03bc\u03b5\u03bd\u03bf \u03b8\u03ad\u03bc\u03b1",
        "Other changes": "\u0386\u03bb\u03bb\u03b5\u03c2 \u03b1\u03bb\u03bb\u03b1\u03b3\u03ad\u03c2",
        "Overview": "\u0395\u03c0\u03b9\u03c3\u03ba\u03cc\u03c0\u03b7\u03c3\u03b7",
        "Please activate JavaScript to enable the search\n    functionality.": "\u03a0\u03b1\u03c1\u03b1\u03ba\u03b1\u03bb\u03ce, \u03b5\u03bd\u03b5\u03c1\u03b3\u03bf\u03c0\u03bf\u03b9\u03ae\u03c3\u03c4\u03b5 \u03c4\u03b7 JavaScript \u03b3\u03b9\u03b1 \u03bd\u03b1 \u03b5\u03af\u03bd\u03b1\u03b9 \u03b4\u03c5\u03bd\u03b1\u03c4\u03ae \u03b7 \u03bb\u03b5\u03b9\u03c4\u03bf\u03c5\u03c1\u03b3\u03af\u03b1\n    \u03b1\u03bd\u03b1\u03b6\u03ae\u03c4\u03b7\u03c3\u03b7\u03c2.",
        "Preparing search...": "\u03a0\u03c1\u03bf\u03b5\u03c4\u03bf\u03b9\u03bc\u03b1\u03c3\u03af\u03b1 \u03b1\u03bd\u03b1\u03b6\u03ae\u03c4\u03b7\u03c3\u03b7\u03c2...",
        "Previous topic": "\u03a0\u03c1\u03bf\u03b7\u03b3\u03bf\u03cd\u03bc\u03b5\u03bd\u03bf \u03b8\u03ad\u03bc\u03b1",
        "Quick search": "\u03a3\u03cd\u03bd\u03c4\u03bf\u03bc\u03b7 \u03b1\u03bd\u03b1\u03b6\u03ae\u03c4\u03b7\u03c3\u03b7",
        "Search": "\u0391\u03bd\u03b1\u03b6\u03ae\u03c4\u03b7\u03c3\u03b7",
        "Search Page": "\u03a3\u03b5\u03bb\u03af\u03b4\u03b1 \u03b1\u03bd\u03b1\u03b6\u03ae\u03c4\u03b7\u03c3\u03b7\u03c2",
        "Search Results": "\u0391\u03c0\u03bf\u03c4\u03b5\u03bb\u03ad\u03c3\u03bc\u03b1\u03c4\u03b1 \u0391\u03bd\u03b1\u03b6\u03ae\u03c4\u03b7\u03c3\u03b7\u03c2",
        "Search finished, found ${resultCount} page(s) matching the search query.": "",
        "Search within %(docstitle)s": "\u0391\u03bd\u03b1\u03b6\u03ae\u03c4\u03b7\u03c3\u03b7 \u03c3\u03c4\u03bf %(docstitle)s",
        "Searching": "\u0395\u03ba\u03c4\u03b5\u03bb\u03b5\u03af\u03c4\u03b1\u03b9 \u03b7 \u03b1\u03bd\u03b1\u03b6\u03ae\u03c4\u03b7\u03c3\u03b7",
        "Searching for multiple words only shows matches that contain\n    all words.": "",
        "Show Source": "\u03a0\u03c1\u03bf\u03b2\u03bf\u03bb\u03ae \u03ba\u03ce\u03b4\u03b9\u03ba\u03b1",
        "Table of Contents": "\u03a0\u03af\u03bd\u03b1\u03ba\u03b1\u03c2 \u03c0\u03b5\u03c1\u03b9\u03b5\u03c7\u03bf\u03bc\u03ad\u03bd\u03c9\u03bd",
        "This Page": "\u0391\u03c5\u03c4\u03ae \u03b7 \u03c3\u03b5\u03bb\u03af\u03b4\u03b1",
        "Welcome! This is": "\u039a\u03b1\u03bb\u03c9\u03c3\u03ae\u03c1\u03b8\u03b1\u03c4\u03b5! \u0391\u03c5\u03c4\u03ae \u03b5\u03af\u03bd\u03b1\u03b9",
        "Your search did not match any documents. Please make sure that all words are spelled correctly and that you've selected enough categories.": "\u0397 \u03b1\u03bd\u03b1\u03b6\u03ae\u03c4\u03b7\u03c3\u03ae \u03c3\u03b1\u03c2 \u03b4\u03b5\u03bd \u03c4\u03b1\u03c5\u03c4\u03bf\u03c0\u03bf\u03b9\u03ae\u03b8\u03b7\u03ba\u03b5 \u03bc\u03b5 \u03ba\u03b1\u03bd\u03ad\u03bd\u03b1 \u03ba\u03b5\u03af\u03bc\u03b5\u03bd\u03bf. \u03a0\u03b1\u03c1\u03b1\u03ba\u03b1\u03bb\u03ce, \u03b5\u03c0\u03b9\u03b2\u03b5\u03b2\u03b1\u03b9\u03ce\u03c3\u03c4\u03b5 \u03cc\u03c4\u03b9 \u03cc\u03bb\u03b5\u03c2 \u03bf\u03b9 \u03bb\u03ad\u03be\u03b5\u03b9\u03c2 \u03ad\u03c7\u03bf\u03c5\u03bd \u03c4\u03b7 \u03c3\u03c9\u03c3\u03c4\u03ae \u03bf\u03c1\u03b8\u03bf\u03b3\u03c1\u03b1\u03c6\u03af\u03b1 \u03ba\u03b1\u03b9 \u03cc\u03c4\u03b9 \u03ad\u03c7\u03b5\u03c4\u03b5 \u03b5\u03c0\u03b9\u03bb\u03ad\u03be\u03b5\u03b9\u03c2 \u03b1\u03c1\u03ba\u03b5\u03c4\u03ad\u03c2 \u03ba\u03b1\u03c4\u03b7\u03b3\u03bf\u03c1\u03af\u03b5\u03c2.",
        "all functions, classes, terms": "\u03cc\u03bb\u03b5\u03c2 \u03bf\u03b9 \u03c3\u03c5\u03bd\u03b1\u03c1\u03c4\u03ae\u03c3\u03b5\u03b9\u03c2, \u03ba\u03bb\u03ac\u03c3\u03b5\u03b9\u03c2, \u03cc\u03c1\u03bf\u03b9",
        "can be huge": "\u03bc\u03c0\u03bf\u03c1\u03b5\u03af \u03bd\u03b1 \u03b5\u03af\u03bd\u03b1\u03b9 \u03c4\u03b5\u03c1\u03ac\u03c3\u03c4\u03b9\u03bf",
        "last updated": "\u03c4\u03b5\u03bb\u03b5\u03c5\u03c4\u03b1\u03af\u03b1 \u03b5\u03bd\u03b7\u03bc\u03ad\u03c1\u03c9\u03c3\u03b7",
        "lists all sections and subsections": "\u03b1\u03c0\u03b1\u03c1\u03b9\u03b8\u03bc\u03b5\u03af \u03cc\u03bb\u03b1 \u03c4\u03b1 \u03ba\u03b5\u03c6\u03ac\u03bb\u03b1\u03b9\u03b1 \u03ba\u03b1\u03b9 \u03c5\u03c0\u03bf\u03ba\u03b5\u03c6\u03ac\u03bb\u03b1\u03b9\u03b1",
        "next chapter": "\u03b5\u03c0\u03cc\u03bc\u03b5\u03bd\u03bf \u03ba\u03b5\u03c6\u03ac\u03bb\u03b1\u03b9\u03bf",
        "previous chapter": "\u03c0\u03c1\u03bf\u03b7\u03b3\u03bf\u03cd\u03bc\u03b5\u03bd\u03bf \u03ba\u03b5\u03c6\u03ac\u03bb\u03b1\u03b9\u03bf",
        "quick access to all modules": "\u03b3\u03c1\u03ae\u03b3\u03bf\u03c1\u03b7 \u03c0\u03c1\u03cc\u03c3\u03b2\u03b1\u03c3\u03b7 \u03c3\u03b5 \u03cc\u03bb\u03b5\u03c2 \u03c4\u03b9\u03c2 \u03bc\u03bf\u03bd\u03ac\u03b4\u03b5\u03c2",
        "search": "\u03b1\u03bd\u03b1\u03b6\u03ae\u03c4\u03b7\u03c3\u03b7",
        "search this documentation": "\u03b1\u03bd\u03b1\u03b6\u03ae\u03c4\u03b7\u03c3\u03b7 \u03b1\u03c5\u03c4\u03ae\u03c2 \u03c4\u03b7\u03c2 \u03c4\u03b5\u03ba\u03bc\u03b7\u03c1\u03af\u03c9\u03c3\u03b7\u03c2",
        "the documentation for": "\u03b7 \u03c4\u03b5\u03ba\u03bc\u03b7\u03c1\u03af\u03c9\u03c3\u03b7 \u03c4\u03bf\u03c5"
    },
    "plural_expr": "(n != 1)"
});