DanishStemmer=function(){var r=new BaseStemmer;var e=[["hed",-1,1],["ethed",0,1],["ered",-1,1],["e",-1,1],["erede",3,1],["ende",3,1],["erende",5,1],["ene",3,1],["erne",3,1],["ere",3,1],["en",-1,1],["heden",10,1],["eren",10,1],["er",-1,1],["heder",13,1],["erer",13,1],["s",-1,2],["heds",16,1],["es",16,1],["endes",18,1],["erendes",19,1],["enes",18,1],["ernes",18,1],["eres",18,1],["ens",16,1],["hedens",24,1],["erens",24,1],["ers",16,1],["ets",16,1],["erets",28,1],["et",-1,1],["eret",30,1]];var i=[["gd",-1,-1],["dt",-1,-1],["gt",-1,-1],["kt",-1,-1]];var s=[["ig",-1,1],["lig",0,1],["elig",1,1],["els",-1,1],["løst",-1,2]];var t=[119,223,119,1];var a=[17,65,16,1,0,0,0,0,0,0,0,0,0,0,0,0,48,0,128];var u=[239,254,42,3,0,0,0,0,0,0,0,0,0,0,0,0,16];var c=0;var l=0;var n="";function o(){l=r.limit;var e=r.cursor;{var i=r.cursor+3;if(i>r.limit){return false}r.cursor=i}c=r.cursor;r.cursor=e;r:while(true){var s=r.cursor;e:{if(!r.in_grouping(a,97,248)){break e}r.cursor=s;break r}r.cursor=s;if(r.cursor>=r.limit){return false}r.cursor++}r:while(true){e:{if(!r.out_grouping(a,97,248)){break e}break r}if(r.cursor>=r.limit){return false}r.cursor++}l=r.cursor;r:{if(!(l<c)){break r}l=c}return true}function f(){var i;if(r.cursor<l){return false}var s=r.limit_backward;r.limit_backward=l;r.ket=r.cursor;i=r.find_among_b(e);if(i==0){r.limit_backward=s;return false}r.bra=r.cursor;r.limit_backward=s;switch(i){case 1:if(!r.slice_del()){return false}break;case 2:if(!r.in_grouping_b(u,97,229)){return false}if(!r.slice_del()){return false}break}return true}function m(){var e=r.limit-r.cursor;if(r.cursor<l){return false}var s=r.limit_backward;r.limit_backward=l;r.ket=r.cursor;if(r.find_among_b(i)==0){r.limit_backward=s;return false}r.bra=r.cursor;r.limit_backward=s;r.cursor=r.limit-e;if(r.cursor<=r.limit_backward){return false}r.cursor--;r.bra=r.cursor;if(!r.slice_del()){return false}return true}function _(){var e;var i=r.limit-r.cursor;r:{r.ket=r.cursor;if(!r.eq_s_b("st")){break r}r.bra=r.cursor;if(!r.eq_s_b("ig")){break r}if(!r.slice_del()){return false}}r.cursor=r.limit-i;if(r.cursor<l){return false}var t=r.limit_backward;r.limit_backward=l;r.ket=r.cursor;e=r.find_among_b(s);if(e==0){r.limit_backward=t;return false}r.bra=r.cursor;r.limit_backward=t;switch(e){case 1:if(!r.slice_del()){return false}var a=r.limit-r.cursor;m();r.cursor=r.limit-a;break;case 2:if(!r.slice_from("løs")){return false}break}return true}function b(){if(r.cursor<l){return false}var e=r.limit_backward;r.limit_backward=l;r.ket=r.cursor;if(!r.in_grouping_b(t,98,122)){r.limit_backward=e;return false}r.bra=r.cursor;n=r.slice_to();if(n==""){return false}r.limit_backward=e;if(!r.eq_s_b(n)){return false}if(!r.slice_del()){return false}return true}this.stem=function(){var e=r.cursor;o();r.cursor=e;r.limit_backward=r.cursor;r.cursor=r.limit;var i=r.limit-r.cursor;f();r.cursor=r.limit-i;var s=r.limit-r.cursor;m();r.cursor=r.limit-s;var t=r.limit-r.cursor;_();r.cursor=r.limit-t;var a=r.limit-r.cursor;b();r.cursor=r.limit-a;r.cursor=r.limit_backward;return true};this["stemWord"]=function(e){r.setCurrent(e);this.stem();return r.getCurrent()}};