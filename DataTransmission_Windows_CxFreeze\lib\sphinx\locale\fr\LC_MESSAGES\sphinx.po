# Translations template for Sphinx.
# Copyright (C) 2024 ORGANIZATION
# This file is distributed under the same license as the Sphinx project.
# 
# Translators:
# <AUTHOR> <EMAIL>, 2020
# <PERSON> <<EMAIL>>, 2020
# <PERSON>UVET <<EMAIL>>, 2017,2023-2024
# <PERSON>UVET <<EMAIL>>, 2013,2015
# <AUTHOR> <EMAIL>, 2019
# <PERSON>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2008
# <PERSON> <<EMAIL>>, 2020-2023
# <AUTHOR> <EMAIL>, 2010
# <AUTHOR> <EMAIL>, 2010
# <PERSON> <<EMAIL>>, 2016-2017,2020
# <PERSON> <<EMAIL>>, 2014
# <PERSON> <<PERSON>.<PERSON>@inria.fr>, 2021
# <PERSON><PERSON><PERSON> <jean<PERSON><PERSON>.<EMAIL>>, 2010
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2017-2019,2022-2023
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2022-2023
# <PERSON> <<EMAIL>>, 2017
# <PERSON> <PERSON>ard <<EMAIL>>, 2019
# <AUTHOR> <EMAIL>, 2019
# <AUTHOR> <EMAIL>, 2008
# <AUTHOR> <EMAIL>, 2018-2019
# 751bad527461b9b1a5628371fac587ce_51f5b30 <748bb51e7ee5d7c2fa68b9a5e88dc8fb_87395>, 2013-2014
# <AUTHOR> <EMAIL>, 2021
# <AUTHOR> <EMAIL>, 2014-2015
# <AUTHOR> <EMAIL>, 2019
# <AUTHOR> <EMAIL>, 2016
# <AUTHOR> <EMAIL>, 2008
# <AUTHOR> <EMAIL>, 2016,2020
msgid ""
msgstr ""
"Project-Id-Version: Sphinx\n"
"Report-Msgid-Bugs-To: EMAIL@ADDRESS\n"
"POT-Creation-Date: 2024-04-14 23:27+0000\n"
"PO-Revision-Date: 2013-04-02 08:44+0000\n"
"Last-Translator: Christophe CHAUVET <<EMAIL>>, 2017,2023-2024\n"
"Language-Team: French (http://app.transifex.com/sphinx-doc/sphinx-1/language/fr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.14.0\n"
"Language: fr\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#: sphinx/application.py:157
#, python-format
msgid "Cannot find source directory (%s)"
msgstr "Impossible de trouver le répertoire source (%s)"

#: sphinx/application.py:161
#, python-format
msgid "Output directory (%s) is not a directory"
msgstr "Le répertoire de sortie (%s) n'est pas un répertoire"

#: sphinx/application.py:165
msgid "Source directory and destination directory cannot be identical"
msgstr "Les dossiers source et destination ne doivent pas être identiques"

#: sphinx/application.py:197
#, python-format
msgid "Running Sphinx v%s"
msgstr "Sphinx v%s en cours d'exécution"

#: sphinx/application.py:219
#, python-format
msgid ""
"This project needs at least Sphinx v%s and therefore cannot be built with "
"this version."
msgstr "Ce projet nécessite au minimum Sphinx v%s et ne peut donc être construit avec cette version."

#: sphinx/application.py:235
msgid "making output directory"
msgstr "création du répertoire de sortie"

#: sphinx/application.py:240 sphinx/registry.py:450
#, python-format
msgid "while setting up extension %s:"
msgstr "lors de l'initialisation de l'extension %s :"

#: sphinx/application.py:246
msgid ""
"'setup' as currently defined in conf.py isn't a Python callable. Please "
"modify its definition to make it a callable function. This is needed for "
"conf.py to behave as a Sphinx extension."
msgstr "'setup' tel que défini dans conf.py n'est pas un objet Python appelable. Veuillez modifier sa définition pour en faire une fonction appelable. Ceci est nécessaire pour que conf.py se comporte comme une extension Sphinx."

#: sphinx/application.py:277
#, python-format
msgid "loading translations [%s]... "
msgstr "chargement des traductions [%s]... "

#: sphinx/application.py:294 sphinx/util/display.py:85
msgid "done"
msgstr "fait"

#: sphinx/application.py:296
msgid "not available for built-in messages"
msgstr "traductions indisponibles"

#: sphinx/application.py:310
msgid "loading pickled environment"
msgstr "Chargement de l'environnement pickled"

#: sphinx/application.py:318
#, python-format
msgid "failed: %s"
msgstr "échec : %s"

#: sphinx/application.py:332
msgid "No builder selected, using default: html"
msgstr "Aucun constructeur sélectionné, utilisation du défaut : html"

#: sphinx/application.py:365
msgid "succeeded"
msgstr "a réussi"

#: sphinx/application.py:366
msgid "finished with problems"
msgstr "s'est terminée avec des problèmes"

#: sphinx/application.py:370
#, python-format
msgid "build %s, %s warning (with warnings treated as errors)."
msgstr "La compilation %s, %s avertissement (avec les avertissements considérés comme des erreurs)."

#: sphinx/application.py:372
#, python-format
msgid "build %s, %s warnings (with warnings treated as errors)."
msgstr "La compilation %s, %s avertissements (avec les avertissements considérés comme des erreurs)."

#: sphinx/application.py:375
#, python-format
msgid "build %s, %s warning."
msgstr "La compilation %s, %s avertissement."

#: sphinx/application.py:377
#, python-format
msgid "build %s, %s warnings."
msgstr "La compilation %s, %s avertissements."

#: sphinx/application.py:381
#, python-format
msgid "build %s."
msgstr "La compilation %s."

#: sphinx/application.py:610
#, python-format
msgid "node class %r is already registered, its visitors will be overridden"
msgstr "la classe de nœud %r est déjà enregistrée, ses visiteurs seront écrasés"

#: sphinx/application.py:689
#, python-format
msgid "directive %r is already registered, it will be overridden"
msgstr "la directive %r est déjà enregistrée, elle sera écrasée"

#: sphinx/application.py:711 sphinx/application.py:733
#, python-format
msgid "role %r is already registered, it will be overridden"
msgstr "le rôle %r est déjà enregistré, il sera écrasé"

#: sphinx/application.py:1282
#, python-format
msgid ""
"the %s extension does not declare if it is safe for parallel reading, "
"assuming it isn't - please ask the extension author to check and make it "
"explicit"
msgstr "l’extension %s ne se déclare pas compatible à la lecture en parallèle, on supposera qu’elle ne l'est pas - merci de demander à l'auteur de l’extension de vérifier ce qu’il en est et de le préciser explicitement"

#: sphinx/application.py:1286
#, python-format
msgid "the %s extension is not safe for parallel reading"
msgstr "l'extension %s n'est pas compatible avec les lectures parallèles"

#: sphinx/application.py:1289
#, python-format
msgid ""
"the %s extension does not declare if it is safe for parallel writing, "
"assuming it isn't - please ask the extension author to check and make it "
"explicit"
msgstr "l’extension %s ne se déclare pas compatible à l’écriture en parallèle, on supposera qu’elle ne l’est pas - merci de demander à l'auteur de l’extension de vérifier ce qu’il en est et de le préciser explicitement"

#: sphinx/application.py:1293
#, python-format
msgid "the %s extension is not safe for parallel writing"
msgstr "l'extension %s n'est pas compatible avec les écritures parallèles"

#: sphinx/application.py:1301 sphinx/application.py:1305
#, python-format
msgid "doing serial %s"
msgstr "sérialisation en cours %s"

#: sphinx/config.py:309
#, python-format
msgid "config directory doesn't contain a conf.py file (%s)"
msgstr "Le dossier de configuration ne contient pas de fichier conf.py (%s)"

#: sphinx/config.py:318
msgid ""
"Invalid configuration value found: 'language = None'. Update your "
"configuration to a valid language code. Falling back to 'en' (English)."
msgstr "Valeur de configuration non valide trouvée: 'language = None'. Mettez à jour la configuration avec un code de langage valide. Utilisation de 'en' (English) comme substitut."

#: sphinx/config.py:341
#, python-format
msgid ""
"cannot override dictionary config setting %r, ignoring (use %r to set "
"individual elements)"
msgstr "impossible d'écraser le dictionnaire de configuration %r ; ignoré (utilisez %r pour modifier les éléments individuellement)"

#: sphinx/config.py:350
#, python-format
msgid "invalid number %r for config value %r, ignoring"
msgstr "nombre non valide %r pour l'option de configuration %r ; ignoré"

#: sphinx/config.py:355
#, python-format
msgid "cannot override config setting %r with unsupported type, ignoring"
msgstr "impossible de remplacer le paramètre de configuration %r par un type non-supporté ; ignoré"

#: sphinx/config.py:378
#, python-format
msgid "unknown config value %r in override, ignoring"
msgstr "paramètre de configuration %r inconnu dans override ; ignoré"

#: sphinx/config.py:418
#, python-format
msgid "No such config value: %r"
msgstr ""

#: sphinx/config.py:440
#, python-format
msgid "Config value %r already present"
msgstr "L'option de configuration %r est déjà présente"

#: sphinx/config.py:473
#, python-format
msgid "cannot cache unpickable configuration value: %r"
msgstr ""

#: sphinx/config.py:509
#, python-format
msgid "There is a syntax error in your configuration file: %s\n"
msgstr "Votre fichier de configuration comporte une erreur de syntaxe : %s\n"

#: sphinx/config.py:512
msgid ""
"The configuration file (or one of the modules it imports) called sys.exit()"
msgstr "Le fichier de configuration (ou un des modules qu'il utilise) génère un sys.exit()"

#: sphinx/config.py:519
#, python-format
msgid ""
"There is a programmable error in your configuration file:\n"
"\n"
"%s"
msgstr "Votre fichier de configuration comporte une erreur de programmation : \n\n%s"

#: sphinx/config.py:540
#, python-format
msgid "Failed to convert %r to a set or tuple"
msgstr ""

#: sphinx/config.py:565
#, python-format
msgid ""
"The config value `source_suffix' expects a string, list of strings, or "
"dictionary. But `%r' is given."
msgstr "Le paramètre `source_suffix` s'attend à recevoir une chaîne de caractères, une liste de chaînes de caractères ou un dictionnaire. Mais vous avez fourni un `%r'."

#: sphinx/config.py:585
#, python-format
msgid "Section %s"
msgstr "Section %s"

#: sphinx/config.py:586
#, python-format
msgid "Fig. %s"
msgstr "Fig. %s"

#: sphinx/config.py:587
#, python-format
msgid "Table %s"
msgstr "Tableau %s"

#: sphinx/config.py:588
#, python-format
msgid "Listing %s"
msgstr "Code source %s"

#: sphinx/config.py:663
msgid ""
"The config value `{name}` has to be a one of {candidates}, but `{current}` "
"is given."
msgstr "La valeur « {current} » du paramètre « {name} » ne figure pas dans la liste des possibilités valables « {candidates} »."

#: sphinx/config.py:687
msgid ""
"The config value `{name}' has type `{current.__name__}'; expected "
"{permitted}."
msgstr "Le type du paramètre de configuration « {name} » doit être {permitted} et non « {current.__name__} »."

#: sphinx/config.py:700
msgid ""
"The config value `{name}' has type `{current.__name__}', defaults to "
"`{default.__name__}'."
msgstr "Le paramètre de configuration « {name} » a pour type « {current.__name__} », tandis que le type par défaut est « {default.__name__} »."

#: sphinx/config.py:711
#, python-format
msgid "primary_domain %r not found, ignored."
msgstr "primary_domain %r non trouvé; ignoré."

#: sphinx/config.py:723
msgid ""
"Since v2.0, Sphinx uses \"index\" as root_doc by default. Please add "
"\"root_doc = 'contents'\" to your conf.py."
msgstr "Depuis sa version 2.0, Sphinx utilise \"index\" comme root_doc par défaut. Veuillez ajouter \"root_doc = 'contents'\" à votre conf.py."

#: sphinx/events.py:64
#, python-format
msgid "Event %r already present"
msgstr "Évènement %r déjà présent"

#: sphinx/events.py:70
#, python-format
msgid "Unknown event name: %s"
msgstr "Nom d'évènement inconnu : %s"

#: sphinx/events.py:109
#, python-format
msgid "Handler %r for event %r threw an exception"
msgstr "Le gestionnaire %r de l'évènement %r a créé une exception."

#: sphinx/extension.py:55
#, python-format
msgid ""
"The %s extension is required by needs_extensions settings, but it is not "
"loaded."
msgstr "L'extension %s  est exigée par le paramètre needs_extensions, mais n'est pas chargée."

#: sphinx/extension.py:76
#, python-format
msgid ""
"This project needs the extension %s at least in version %s and therefore "
"cannot be built with the loaded version (%s)."
msgstr "Ce projet nécessite que l'extension %s soit au minimum en version %s et par conséquent il ne peut pas être construit avec la version chargée (%s)."

#: sphinx/highlighting.py:155
#, python-format
msgid "Pygments lexer name %r is not known"
msgstr "Le nom du l'analyseur Pygments %r est inconnu"

#: sphinx/highlighting.py:189
#, python-format
msgid ""
"Lexing literal_block %r as \"%s\" resulted in an error at token: %r. "
"Retrying in relaxed mode."
msgstr "Le lexème du bloc_littéral %r en tant que \"%s\" a entraîné une erreur au niveau du jeton : %r. Réessayer en mode relaxé."

#: sphinx/project.py:66
#, python-format
msgid ""
"multiple files found for the document \"%s\": %r\n"
"Use %r for the build."
msgstr "plusieurs fichiers trouvés pour le document \"%s\" : %r\nUtiliser %r pour la compilation."

#: sphinx/project.py:81
#, python-format
msgid "Ignored unreadable document %r."
msgstr "Document illisible %r ignoré."

#: sphinx/registry.py:142
#, python-format
msgid "Builder class %s has no \"name\" attribute"
msgstr "La classe Builder %s n'a pas d'attribut « name »"

#: sphinx/registry.py:144
#, python-format
msgid "Builder %r already exists (in module %s)"
msgstr "Le constructeur %r existe déjà (dans le module %s)"

#: sphinx/registry.py:157
#, python-format
msgid "Builder name %s not registered or available through entry point"
msgstr "Le nom de Constructeur %s n'est ni enregistré ni accessible par point d'entrée"

#: sphinx/registry.py:164
#, python-format
msgid "Builder name %s not registered"
msgstr "Constructeur %s non enregistré"

#: sphinx/registry.py:171
#, python-format
msgid "domain %s already registered"
msgstr "le domaine %s a déjà été enregistré"

#: sphinx/registry.py:194 sphinx/registry.py:207 sphinx/registry.py:218
#, python-format
msgid "domain %s not yet registered"
msgstr "le domaine %s n'a pas encore été enregistré"

#: sphinx/registry.py:198
#, python-format
msgid "The %r directive is already registered to domain %s"
msgstr "La directive %r est déjà enregistrée sur le domaine %s"

#: sphinx/registry.py:210
#, python-format
msgid "The %r role is already registered to domain %s"
msgstr "Le rôle %r est déjà enregistré sur le domaine %s"

#: sphinx/registry.py:221
#, python-format
msgid "The %r index is already registered to domain %s"
msgstr "L'index %r est déjà enregistré sur le domaine %s"

#: sphinx/registry.py:252
#, python-format
msgid "The %r object_type is already registered"
msgstr "Le type de l'objet %r est déjà enregistré"

#: sphinx/registry.py:278
#, python-format
msgid "The %r crossref_type is already registered"
msgstr "Le type %r crossref_type est déjà enregistré"

#: sphinx/registry.py:285
#, python-format
msgid "source_suffix %r is already registered"
msgstr "L'extension source %r est déjà enregistrée"

#: sphinx/registry.py:294
#, python-format
msgid "source_parser for %r is already registered"
msgstr "source_parser pour %r est déjà enregistré"

#: sphinx/registry.py:302
#, python-format
msgid "Source parser for %s not registered"
msgstr "source_parser pour %s non enregistré"

#: sphinx/registry.py:318
#, python-format
msgid "Translator for %r already exists"
msgstr "Il existe déjà un traducteur pour %r"

#: sphinx/registry.py:334
#, python-format
msgid "kwargs for add_node() must be a (visit, depart) function tuple: %r=%r"
msgstr "Les kwargs pour add_node() doivent être un tuple de fonction (visite, départ) : %r=%r"

#: sphinx/registry.py:417
#, python-format
msgid "enumerable_node %r already registered"
msgstr "enumerable_node %r est déjà enregistré"

#: sphinx/registry.py:429
#, python-format
msgid "math renderer %s is already registered"
msgstr "le moteur de rendu mathématique %s est déjà enregistré"

#: sphinx/registry.py:444
#, python-format
msgid ""
"the extension %r was already merged with Sphinx since version %s; this "
"extension is ignored."
msgstr "l'extension %r a été intégrée à Sphinx depuis la version %s ; cette extension est ignorée."

#: sphinx/registry.py:455
msgid "Original exception:\n"
msgstr "Exception initiale :\n"

#: sphinx/registry.py:456
#, python-format
msgid "Could not import extension %s"
msgstr "L'extension %s ne peut pas être importée"

#: sphinx/registry.py:461
#, python-format
msgid ""
"extension %r has no setup() function; is it really a Sphinx extension "
"module?"
msgstr "l'extension %r n'a pas de fonction setup(); est-elle réellement un module d'extension de Sphinx ?"

#: sphinx/registry.py:470
#, python-format
msgid ""
"The %s extension used by this project needs at least Sphinx v%s; it "
"therefore cannot be built with this version."
msgstr "L'extension %s utilisée par ce projet nécessite au moins Sphinx v%s ; il ne peut donc pas être construit avec la version courante."

#: sphinx/registry.py:478
#, python-format
msgid ""
"extension %r returned an unsupported object from its setup() function; it "
"should return None or a metadata dictionary"
msgstr "l'extension %r a renvoyé par sa fonction setup() un type d'objet non supporté ; elle devrait renvoyer None ou un dictionnaire de méta-données"

#: sphinx/roles.py:201
#, python-format
msgid "Python Enhancement Proposals; PEP %s"
msgstr "Python Enhancement Proposals; PEP %s"

#: sphinx/roles.py:222
#, python-format
msgid "invalid PEP number %s"
msgstr "numéro PEP %s non valide"

#: sphinx/roles.py:257
#, python-format
msgid "invalid RFC number %s"
msgstr "numéro RFC %snon valide"

#: sphinx/theming.py:125
#, python-format
msgid "setting %s.%s occurs in none of the searched theme configs"
msgstr "le paramètre %s.%s n'apparaît dans aucune des configurations de thème recherchées"

#: sphinx/theming.py:140
#, python-format
msgid "unsupported theme option %r given"
msgstr "l'option %r n'est pas supportée pour ce thème"

#: sphinx/theming.py:206
#, python-format
msgid "file %r on theme path is not a valid zipfile or contains no theme"
msgstr "le fichier %r dans le dossier des thèmes n'est pas une archive zip valide ou ne contient aucun thème"

#: sphinx/theming.py:226
#, python-format
msgid "no theme named %r found (missing theme.toml?)"
msgstr ""

#: sphinx/theming.py:259
#, python-format
msgid "The %r theme has circular inheritance"
msgstr ""

#: sphinx/theming.py:262
#, python-format
msgid ""
"The %r theme inherits from %r, which is not a loaded theme. Loaded themes "
"are: %s"
msgstr ""

#: sphinx/theming.py:269
#, python-format
msgid "The %r theme has too many ancestors"
msgstr "Le thème %r a trop d'ancêtres"

#: sphinx/theming.py:295
#, python-format
msgid "no theme configuration file found in %r"
msgstr ""

#: sphinx/theming.py:323 sphinx/theming.py:374
#, python-format
msgid "theme %r doesn't have the \"theme\" table"
msgstr ""

#: sphinx/theming.py:327
#, python-format
msgid "The %r theme \"[theme]\" table is not a table"
msgstr ""

#: sphinx/theming.py:331 sphinx/theming.py:377
#, python-format
msgid "The %r theme must define the \"theme.inherit\" setting"
msgstr "Le thème %r doit définir la clé \"theme.inherit\" dans les paramètres"

#: sphinx/theming.py:335
#, python-format
msgid "The %r theme \"[options]\" table is not a table"
msgstr ""

#: sphinx/theming.py:353
#, python-format
msgid "The \"theme.pygments_style\" setting must be a table. Hint: \"%s\""
msgstr ""

#: sphinx/builders/__init__.py:183
#, python-format
msgid "a suitable image for %s builder not found: %s (%s)"
msgstr "l'image appropriée pour le constructeur %s n'a pas été trouvée : %s (%s)"

#: sphinx/builders/__init__.py:187
#, python-format
msgid "a suitable image for %s builder not found: %s"
msgstr "l'image appropriée pour le constructeur %s n'a pas été trouvée : %s"

#: sphinx/builders/__init__.py:207
msgid "building [mo]: "
msgstr "construction en cours [mo] : "

#: sphinx/builders/__init__.py:208 sphinx/builders/__init__.py:574
#: sphinx/builders/__init__.py:601
msgid "writing output... "
msgstr "Écriture... "

#: sphinx/builders/__init__.py:217
#, python-format
msgid "all of %d po files"
msgstr "tous les %d fichiers po"

#: sphinx/builders/__init__.py:235
#, python-format
msgid "targets for %d po files that are specified"
msgstr "cibles spécifiées pour les fichiers po %d"

#: sphinx/builders/__init__.py:243
#, python-format
msgid "targets for %d po files that are out of date"
msgstr "cibles périmées pour les fichiers po %d"

#: sphinx/builders/__init__.py:252
msgid "all source files"
msgstr "tous les fichiers source"

#: sphinx/builders/__init__.py:262
#, python-format
msgid "file %r given on command line does not exist, "
msgstr "le fichier %r passé dans la ligne de commande n'existe pas, "

#: sphinx/builders/__init__.py:267
#, python-format
msgid ""
"file %r given on command line is not under the source directory, ignoring"
msgstr "le fichier %r saisi en ligne de commande n'est pas présent dans le dossier source, il sera ignoré"

#: sphinx/builders/__init__.py:273
#, python-format
msgid "file %r given on command line is not a valid document, ignoring"
msgstr "le fichier %r passé dans la ligne de commande n'est pas un document valide, ignoré"

#: sphinx/builders/__init__.py:282
#, python-format
msgid "%d source files given on command line"
msgstr "%d fichiers source saisis en ligne de commande"

#: sphinx/builders/__init__.py:294
#, python-format
msgid "targets for %d source files that are out of date"
msgstr "cibles périmées pour les fichiers sources %d"

#: sphinx/builders/__init__.py:309 sphinx/builders/gettext.py:243
#, python-format
msgid "building [%s]: "
msgstr "construction [%s] : "

#: sphinx/builders/__init__.py:316
msgid "looking for now-outdated files... "
msgstr "Recherche des fichiers périmés... "

#: sphinx/builders/__init__.py:320
#, python-format
msgid "%d found"
msgstr "%d trouvé"

#: sphinx/builders/__init__.py:322
msgid "none found"
msgstr "aucun résultat trouvé"

#: sphinx/builders/__init__.py:327
msgid "pickling environment"
msgstr "Environnement de sérialisation"

#: sphinx/builders/__init__.py:333
msgid "checking consistency"
msgstr "vérification de la cohérence"

#: sphinx/builders/__init__.py:337
msgid "no targets are out of date."
msgstr "aucune cible n'est périmée."

#: sphinx/builders/__init__.py:376
msgid "updating environment: "
msgstr "mise à jour de l'environnement : "

#: sphinx/builders/__init__.py:397
#, python-format
msgid "%s added, %s changed, %s removed"
msgstr "%s ajouté(s), %s modifié(s), %s supprimé(s)"

#: sphinx/builders/__init__.py:435 sphinx/builders/__init__.py:447
msgid "reading sources... "
msgstr "lecture des sources... "

#: sphinx/builders/__init__.py:549
#, python-format
msgid "docnames to write: %s"
msgstr "documents à écrire : %s"

#: sphinx/builders/__init__.py:558 sphinx/builders/singlehtml.py:157
msgid "preparing documents"
msgstr "documents en préparation"

#: sphinx/builders/__init__.py:561
msgid "copying assets"
msgstr "copie des ressources"

#: sphinx/builders/_epub_base.py:215
#, python-format
msgid "duplicated ToC entry found: %s"
msgstr "entrées dupliquées de la table des matières trouvées : %s"

#: sphinx/builders/_epub_base.py:404 sphinx/builders/html/__init__.py:758
#: sphinx/builders/latex/__init__.py:432 sphinx/builders/texinfo.py:187
msgid "copying images... "
msgstr "Copie des images... "

#: sphinx/builders/_epub_base.py:411
#, python-format
msgid "cannot read image file %r: copying it instead"
msgstr "impossible de lire le fichier image %r: il sera copié à la place"

#: sphinx/builders/_epub_base.py:417 sphinx/builders/html/__init__.py:766
#: sphinx/builders/latex/__init__.py:440 sphinx/builders/texinfo.py:197
#, python-format
msgid "cannot copy image file %r: %s"
msgstr "impossible de copier le fichier image %r: %s"

#: sphinx/builders/_epub_base.py:434
#, python-format
msgid "cannot write image file %r: %s"
msgstr "impossible d'écrire le fichier image %r: %s"

#: sphinx/builders/_epub_base.py:444
msgid "Pillow not found - copying image files"
msgstr "Pillow n'a pas été trouvé - copie des fichiers image"

#: sphinx/builders/_epub_base.py:470
msgid "writing mimetype file..."
msgstr "écriture du type MIME du fichier ..."

#: sphinx/builders/_epub_base.py:475
msgid "writing META-INF/container.xml file..."
msgstr "écriture du fichier META-INF/container.xml..."

#: sphinx/builders/_epub_base.py:508
msgid "writing content.opf file..."
msgstr "Enregistrement du fichier content.opf..."

#: sphinx/builders/_epub_base.py:539
#, python-format
msgid "unknown mimetype for %s, ignoring"
msgstr "type MIME inconnu pour %s, il sera ignoré"

#: sphinx/builders/_epub_base.py:686
msgid "writing toc.ncx file..."
msgstr "Enregistrement du fichier toc.ncx..."

#: sphinx/builders/_epub_base.py:711
#, python-format
msgid "writing %s file..."
msgstr "fichier %s en cours d'écriture..."

#: sphinx/builders/changes.py:32
#, python-format
msgid "The overview file is in %(outdir)s."
msgstr "Le fichier d'aperçu se trouve dans %(outdir)s."

#: sphinx/builders/changes.py:59
#, python-format
msgid "no changes in version %s."
msgstr "aucun changement dans la version %s"

#: sphinx/builders/changes.py:61
msgid "writing summary file..."
msgstr "écriture du fichier de résumé..."

#: sphinx/builders/changes.py:76
msgid "Builtins"
msgstr "Fonctions de base"

#: sphinx/builders/changes.py:78
msgid "Module level"
msgstr "Module"

#: sphinx/builders/changes.py:123
msgid "copying source files..."
msgstr "copie des fichiers sources..."

#: sphinx/builders/changes.py:130
#, python-format
msgid "could not read %r for changelog creation"
msgstr "impossible de lire %r pour la création du changelog"

#: sphinx/builders/dummy.py:19
msgid "The dummy builder generates no files."
msgstr "Le constructeur factice ne génère aucun fichier."

#: sphinx/builders/epub3.py:81
#, python-format
msgid "The ePub file is in %(outdir)s."
msgstr "Le fichier ePub se trouve dans %(outdir)s ."

#: sphinx/builders/epub3.py:185
msgid "writing nav.xhtml file..."
msgstr "Enregistrement du fichier nav.xhtml..."

#: sphinx/builders/epub3.py:211
msgid "conf value \"epub_language\" (or \"language\") should not be empty for EPUB3"
msgstr "la variable de configuration \"epub_language\" (ou \"language\") ne peut pas être vide pour EPUB3"

#: sphinx/builders/epub3.py:215
msgid "conf value \"epub_uid\" should be XML NAME for EPUB3"
msgstr "le paramètre de configuration \"epub_uid\" ne peut pas être vide pour EPUB3"

#: sphinx/builders/epub3.py:218
msgid "conf value \"epub_title\" (or \"html_title\") should not be empty for EPUB3"
msgstr "le paramètre de configuration \"epub_title\" (ou \"html_title\") ne peut pas être vide pour EPUB3"

#: sphinx/builders/epub3.py:222
msgid "conf value \"epub_author\" should not be empty for EPUB3"
msgstr "le paramètre de configuration \"epub_author\" ne peut pas être vide pour EPUB3"

#: sphinx/builders/epub3.py:225
msgid "conf value \"epub_contributor\" should not be empty for EPUB3"
msgstr "le paramètre de configuration \"epub_contributor\" ne peut pas être vide pour EPUB3"

#: sphinx/builders/epub3.py:228
msgid "conf value \"epub_description\" should not be empty for EPUB3"
msgstr "le paramètre de configuration \"epub_description\" ne peut pas être vide pour EPUB3"

#: sphinx/builders/epub3.py:231
msgid "conf value \"epub_publisher\" should not be empty for EPUB3"
msgstr "le paramètre de configuration \"epub_publisher\" ne peut pas être vide pour EPUB3"

#: sphinx/builders/epub3.py:234
msgid "conf value \"epub_copyright\" (or \"copyright\")should not be empty for EPUB3"
msgstr "le paramètre de configuration \"epub_copyright\" (ou \"copyright\") ne peut pas être vide pour EPUB3"

#: sphinx/builders/epub3.py:238
msgid "conf value \"epub_identifier\" should not be empty for EPUB3"
msgstr "le paramètre de configuration \"epub_identifier\" ne peut pas être vide pour EPUB3"

#: sphinx/builders/epub3.py:241
msgid "conf value \"version\" should not be empty for EPUB3"
msgstr "le paramètre de configuration \"version\" ne peut pas être vide pour EPUB3"

#: sphinx/builders/epub3.py:255 sphinx/builders/html/__init__.py:1187
#, python-format
msgid "invalid css_file: %r, ignored"
msgstr "Fichier CSS non valide : %r, il sera ignoré"

#: sphinx/builders/gettext.py:222
#, python-format
msgid "The message catalogs are in %(outdir)s."
msgstr "La liste des messages se trouve dans %(outdir)s."

#: sphinx/builders/gettext.py:244
#, python-format
msgid "targets for %d template files"
msgstr "cibles pour les modèles de fichiers %d"

#: sphinx/builders/gettext.py:248
msgid "reading templates... "
msgstr "lecture des gabarits... "

#: sphinx/builders/gettext.py:282
msgid "writing message catalogs... "
msgstr "écriture des catalogues de messages... "

#: sphinx/builders/linkcheck.py:59
#, python-format
msgid "Look for any errors in the above output or in %(outdir)s/output.txt"
msgstr "Recherchez les éventuelles erreurs dans la sortie ci-dessus ou dans %(outdir)s/output.txt"

#: sphinx/builders/linkcheck.py:137
#, python-format
msgid "broken link: %s (%s)"
msgstr "lien mort: %s (%s)"

#: sphinx/builders/linkcheck.py:660
#, python-format
msgid "Failed to compile regex in linkcheck_allowed_redirects: %r %s"
msgstr "Échec de la compilation de la regex dans linkcheck_allowed_redirects : %r%s"

#: sphinx/builders/manpage.py:37
#, python-format
msgid "The manual pages are in %(outdir)s."
msgstr "Le manuel se trouve dans %(outdir)s."

#: sphinx/builders/manpage.py:44
msgid "no \"man_pages\" config value found; no manual pages will be written"
msgstr "aucun valeur de configuration \"man_pages\" trouvée; aucun page du manuel ne sera enregistrée"

#: sphinx/builders/latex/__init__.py:314 sphinx/builders/manpage.py:53
#: sphinx/builders/singlehtml.py:165 sphinx/builders/texinfo.py:112
msgid "writing"
msgstr "enregistrement"

#: sphinx/builders/manpage.py:68
#, python-format
msgid "\"man_pages\" config value references unknown document %s"
msgstr "le paramètre de configuration \"man_pages\" référence un document inconnu %s"

#: sphinx/builders/singlehtml.py:34
#, python-format
msgid "The HTML page is in %(outdir)s."
msgstr "Les pages HTML sont dans %(outdir)s."

#: sphinx/builders/singlehtml.py:160
msgid "assembling single document"
msgstr "création du document unique"

#: sphinx/builders/singlehtml.py:178
msgid "writing additional files"
msgstr "Enregistrement des fichiers supplémentaires"

#: sphinx/builders/texinfo.py:48
#, python-format
msgid "The Texinfo files are in %(outdir)s."
msgstr "Les fichiers Texinfo se trouvent dans %(outdir)s."

#: sphinx/builders/texinfo.py:50
msgid ""
"\n"
"Run 'make' in that directory to run these through makeinfo\n"
"(use 'make info' here to do that automatically)."
msgstr "\nExécuter 'make' dans ce répertoire pour les soumettre à makeinfo\n(ou 'make info' directement ici pour l'automatiser)."

#: sphinx/builders/texinfo.py:77
msgid "no \"texinfo_documents\" config value found; no documents will be written"
msgstr "aucun paramètre de configuration \"texinfo_documents\" trouvé: aucun document ne sera écrit"

#: sphinx/builders/texinfo.py:85
#, python-format
msgid "\"texinfo_documents\" config value references unknown document %s"
msgstr "La valeur du paramètre \"texinfo_documents\" référence un document inconnu %s"

#: sphinx/builders/latex/__init__.py:296 sphinx/builders/texinfo.py:108
#, python-format
msgid "processing %s"
msgstr "Traitement de %s en cours"

#: sphinx/builders/latex/__init__.py:369 sphinx/builders/texinfo.py:161
msgid "resolving references..."
msgstr "résolution des références..."

#: sphinx/builders/latex/__init__.py:380 sphinx/builders/texinfo.py:171
msgid " (in "
msgstr "(dans"

#: sphinx/builders/texinfo.py:202
msgid "copying Texinfo support files"
msgstr "copie des fichiers de support Texinfo"

#: sphinx/builders/texinfo.py:206
#, python-format
msgid "error writing file Makefile: %s"
msgstr "erreur lors l'écriture du fichier Makefile : %s"

#: sphinx/builders/text.py:30
#, python-format
msgid "The text files are in %(outdir)s."
msgstr "Les fichiers texte se trouvent dans %(outdir)s."

#: sphinx/builders/html/__init__.py:1138 sphinx/builders/text.py:77
#: sphinx/builders/xml.py:96
#, python-format
msgid "error writing file %s: %s"
msgstr "erreur lors l'écriture du fichier %s : %s"

#: sphinx/builders/xml.py:36
#, python-format
msgid "The XML files are in %(outdir)s."
msgstr "Les fichiers XML se trouvent dans %(outdir)s."

#: sphinx/builders/xml.py:109
#, python-format
msgid "The pseudo-XML files are in %(outdir)s."
msgstr "Le fichier pseudo-XML se trouve dans %(outdir)s."

#: sphinx/builders/html/__init__.py:130
#, python-format
msgid "build info file is broken: %r"
msgstr "Le fichier de configuration de construction est corrompu : %r"

#: sphinx/builders/html/__init__.py:168
#, python-format
msgid "The HTML pages are in %(outdir)s."
msgstr "Les pages HTML sont dans %(outdir)s."

#: sphinx/builders/html/__init__.py:394
#, python-format
msgid "Failed to read build info file: %r"
msgstr "Échec de lecture du fichier de configuration de construction : %r"

#: sphinx/builders/html/__init__.py:487 sphinx/builders/latex/__init__.py:189
#: sphinx/transforms/__init__.py:119 sphinx/writers/manpage.py:101
#: sphinx/writers/texinfo.py:227
#, python-format
msgid "%b %d, %Y"
msgstr "%b %d, %Y"

#: sphinx/builders/html/__init__.py:506 sphinx/themes/basic/defindex.html:30
msgid "General Index"
msgstr "Index général"

#: sphinx/builders/html/__init__.py:506
msgid "index"
msgstr "index"

#: sphinx/builders/html/__init__.py:579
msgid "next"
msgstr "suivant"

#: sphinx/builders/html/__init__.py:588
msgid "previous"
msgstr "précédent"

#: sphinx/builders/html/__init__.py:684
msgid "generating indices"
msgstr "génération des index"

#: sphinx/builders/html/__init__.py:699
msgid "writing additional pages"
msgstr "Écriture des pages additionnelles"

#: sphinx/builders/html/__init__.py:776
msgid "copying downloadable files... "
msgstr "Copie des fichiers téléchargeables... "

#: sphinx/builders/html/__init__.py:784
#, python-format
msgid "cannot copy downloadable file %r: %s"
msgstr "impossible de copier le fichier téléchargeable %r: %s"

#: sphinx/builders/html/__init__.py:817 sphinx/builders/html/__init__.py:829
#, python-format
msgid "Failed to copy a file in html_static_file: %s: %r"
msgstr "Échec de la copie du fichier dans html_static_file : %s : %r"

#: sphinx/builders/html/__init__.py:850
msgid "copying static files"
msgstr "Copie des fichiers statiques"

#: sphinx/builders/html/__init__.py:866
#, python-format
msgid "cannot copy static file %r"
msgstr "impossible de copier le fichier static %r"

#: sphinx/builders/html/__init__.py:871
msgid "copying extra files"
msgstr "copie des fichiers complémentaires"

#: sphinx/builders/html/__init__.py:877
#, python-format
msgid "cannot copy extra file %r"
msgstr "Copie des fichiers supplémentaires impossible %r"

#: sphinx/builders/html/__init__.py:884
#, python-format
msgid "Failed to write build info file: %r"
msgstr "Échec d'écriture du fichier de configuration de construction : %r"

#: sphinx/builders/html/__init__.py:933
msgid ""
"search index couldn't be loaded, but not all documents will be built: the "
"index will be incomplete."
msgstr "L'index de recherche n'a pas pu être chargé, mais tous les documents ne seront pas construits: l'index sera incomplet."

#: sphinx/builders/html/__init__.py:978
#, python-format
msgid "page %s matches two patterns in html_sidebars: %r and %r"
msgstr "La page %s correspond à deux motifs dans html_sidebars: %r et %r"

#: sphinx/builders/html/__init__.py:1121
#, python-format
msgid ""
"a Unicode error occurred when rendering the page %s. Please make sure all "
"config values that contain non-ASCII content are Unicode strings."
msgstr "une erreur Unicode est survenue lors du rendu de la page %s. Veuillez vous assurer que toutes les valeurs de configuration comportant des caractères non-ASCII sont des chaînes Unicode."

#: sphinx/builders/html/__init__.py:1126
#, python-format
msgid ""
"An error happened in rendering the page %s.\n"
"Reason: %r"
msgstr "Un erreur est survenue lors de la génération de la page: %s.\nLa raison est: %r"

#: sphinx/builders/html/__init__.py:1154
msgid "dumping object inventory"
msgstr "Export de l'inventaire des objets"

#: sphinx/builders/html/__init__.py:1162
#, python-format
msgid "dumping search index in %s"
msgstr "Export de l'index de recherche en %s"

#: sphinx/builders/html/__init__.py:1210
#, python-format
msgid "invalid js_file: %r, ignored"
msgstr "Fichier js_file : %r invalide, sera ignoré"

#: sphinx/builders/html/__init__.py:1238
msgid "Many math_renderers are registered. But no math_renderer is selected."
msgstr "Plusieurs math_renderers sont enregistrés. Mais aucun n'est sélectionné."

#: sphinx/builders/html/__init__.py:1241
#, python-format
msgid "Unknown math_renderer %r is given."
msgstr "math_renderer inconnu %r saisi."

#: sphinx/builders/html/__init__.py:1249
#, python-format
msgid "html_extra_path entry %r does not exist"
msgstr "L’entrée %r de html_extra_path n’existe pas"

#: sphinx/builders/html/__init__.py:1253
#, python-format
msgid "html_extra_path entry %r is placed inside outdir"
msgstr "L’entrée %r de html_extra_path se trouve à l’intérieur de outdir"

#: sphinx/builders/html/__init__.py:1262
#, python-format
msgid "html_static_path entry %r does not exist"
msgstr "L’entrée %r de html_static_path n’existe pas"

#: sphinx/builders/html/__init__.py:1266
#, python-format
msgid "html_static_path entry %r is placed inside outdir"
msgstr "L’entrée %r de html_static_path se trouve à l’intérieur de outdir"

#: sphinx/builders/html/__init__.py:1275 sphinx/builders/latex/__init__.py:444
#, python-format
msgid "logo file %r does not exist"
msgstr "Le fichier de logo %r n’existe pas"

#: sphinx/builders/html/__init__.py:1284
#, python-format
msgid "favicon file %r does not exist"
msgstr "Le fichier de favicon %r n’existe pas "

#: sphinx/builders/html/__init__.py:1291
msgid ""
"HTML 4 is no longer supported by Sphinx. (\"html4_writer=True\" detected in "
"configuration options)"
msgstr "HTML 4 n'est plus pris en charge par Sphinx. (\"html4_writer=True\" détecté dans les options de configuration)"

#: sphinx/builders/html/__init__.py:1306
#, python-format
msgid "%s %s documentation"
msgstr "Documentation %s %s"

#: sphinx/builders/latex/__init__.py:115
#, python-format
msgid "The LaTeX files are in %(outdir)s."
msgstr "Les fichiers LaTex se trouvent dans %(outdir)s."

#: sphinx/builders/latex/__init__.py:117
msgid ""
"\n"
"Run 'make' in that directory to run these through (pdf)latex\n"
"(use `make latexpdf' here to do that automatically)."
msgstr "\nExécuter 'make' dans ce répertoire pour les soumettre à (pdf)latex\n(ou 'make latexpdf' directement ici pour l’automatiser)."

#: sphinx/builders/latex/__init__.py:152
msgid "no \"latex_documents\" config value found; no documents will be written"
msgstr "aucune valeur de configuration \"latex_documents\" trouvée; aucun document de sera généré"

#: sphinx/builders/latex/__init__.py:160
#, python-format
msgid "\"latex_documents\" config value references unknown document %s"
msgstr "La valeur du paramètre \"latex_documents\" référence un document inconnu %s"

#: sphinx/builders/latex/__init__.py:196 sphinx/domains/std/__init__.py:559
#: sphinx/domains/std/__init__.py:571 sphinx/templates/latex/latex.tex_t:106
#: sphinx/themes/basic/genindex-single.html:30
#: sphinx/themes/basic/genindex-single.html:55
#: sphinx/themes/basic/genindex-split.html:11
#: sphinx/themes/basic/genindex-split.html:14
#: sphinx/themes/basic/genindex.html:11 sphinx/themes/basic/genindex.html:34
#: sphinx/themes/basic/genindex.html:67 sphinx/themes/basic/layout.html:138
#: sphinx/writers/texinfo.py:497
msgid "Index"
msgstr "Index"

#: sphinx/builders/latex/__init__.py:199 sphinx/templates/latex/latex.tex_t:91
msgid "Release"
msgstr "Version"

#: sphinx/builders/latex/__init__.py:213 sphinx/writers/latex.py:370
#, python-format
msgid "no Babel option known for language %r"
msgstr "Aucune option Babel disponible pour la langue %r"

#: sphinx/builders/latex/__init__.py:394
msgid "copying TeX support files"
msgstr "copie des fichiers de support TeX"

#: sphinx/builders/latex/__init__.py:410
msgid "copying TeX support files..."
msgstr "Copie des fichiers de support TeX..."

#: sphinx/builders/latex/__init__.py:423
msgid "copying additional files"
msgstr "Copie de fichiers supplémentaires"

#: sphinx/builders/latex/__init__.py:466
#, python-format
msgid "Unknown configure key: latex_elements[%r], ignored."
msgstr "Clé de configuration inconnue : latex_elements[%r]; ignorée."

#: sphinx/builders/latex/__init__.py:474
#, python-format
msgid "Unknown theme option: latex_theme_options[%r], ignored."
msgstr "Option de thème inconnue : latex_theme_options[%r], ignoré."

#: sphinx/builders/latex/theming.py:87
#, python-format
msgid "%r doesn't have \"theme\" setting"
msgstr "%r n'a pas d'option « theme »"

#: sphinx/builders/latex/theming.py:90
#, python-format
msgid "%r doesn't have \"%s\" setting"
msgstr "%r n'a pas d'option « %s »"

#: sphinx/builders/latex/transforms.py:120
msgid "Failed to get a docname!"
msgstr "Échec de l'obtention d'un nom de document !"

#: sphinx/builders/latex/transforms.py:121
msgid "Failed to get a docname for source {source!r}!"
msgstr "Échec de l'obtention d'un nom de document pour la source {source!r} !"

#: sphinx/builders/latex/transforms.py:482
#, python-format
msgid "No footnote was found for given reference node %r"
msgstr "Aucune note de bas de page n'a été trouvée pour la référence de nœud %r donnée"

#: sphinx/cmd/build.py:46
msgid "Exception occurred while building, starting debugger:"
msgstr "Une exception a été levée lors de la génération, démarrage du débogueur :"

#: sphinx/cmd/build.py:61
msgid "Interrupted!"
msgstr "Interrompu !"

#: sphinx/cmd/build.py:63
msgid "reST markup error:"
msgstr "Erreur de balise reST  :"

#: sphinx/cmd/build.py:69
msgid "Encoding error:"
msgstr "Erreur d'encodage :"

#: sphinx/cmd/build.py:72 sphinx/cmd/build.py:87
#, python-format
msgid ""
"The full traceback has been saved in %s, if you want to report the issue to "
"the developers."
msgstr "La trace d’appels complète a été sauvegardée dans %s, au cas où vous souhaiteriez signaler le problème aux développeurs."

#: sphinx/cmd/build.py:76
msgid "Recursion error:"
msgstr "Erreur de récursion :"

#: sphinx/cmd/build.py:79
msgid ""
"This can happen with very large or deeply nested source files. You can "
"carefully increase the default Python recursion limit of 1000 in conf.py "
"with e.g.:"
msgstr "Cela peut se produire avec des fichiers sources très volumineux ou profondément imbriqués. Vous pouvez  augmenter avec attention la limite de récursivité par défaut de Python de 1000 dans conf.py avec p. ex. :"

#: sphinx/cmd/build.py:84
msgid "Exception occurred:"
msgstr "Une exception a été levée :"

#: sphinx/cmd/build.py:90
msgid ""
"Please also report this if it was a user error, so that a better error "
"message can be provided next time."
msgstr "Merci de rapporter ceci s'il s'agit d'une erreur utilisateur, afin d'améliorer le message d'erreur à l'avenir."

#: sphinx/cmd/build.py:93
msgid ""
"A bug report can be filed in the tracker at <https://github.com/sphinx-"
"doc/sphinx/issues>. Thanks!"
msgstr "Un rapport d'erreur peut être déposé dans le système de tickets à <https://github.com/sphinx-doc/sphinx/issues>. Merci !"

#: sphinx/cmd/build.py:109
msgid "job number should be a positive number"
msgstr "Le numéro du job doit être strictement positif"

#: sphinx/cmd/build.py:117 sphinx/cmd/quickstart.py:474
#: sphinx/ext/apidoc.py:317 sphinx/ext/autosummary/generate.py:689
msgid "For more information, visit <https://www.sphinx-doc.org/>."
msgstr "Pour plus d'informations, visitez le site <https://www.sphinx-doc.org/>."

#: sphinx/cmd/build.py:118
msgid ""
"\n"
"Generate documentation from source files.\n"
"\n"
"sphinx-build generates documentation from the files in SOURCEDIR and places it\n"
"in OUTPUTDIR. It looks for 'conf.py' in SOURCEDIR for the configuration\n"
"settings. The 'sphinx-quickstart' tool may be used to generate template files,\n"
"including 'conf.py'\n"
"\n"
"sphinx-build can create documentation in different formats. A format is\n"
"selected by specifying the builder name on the command line; it defaults to\n"
"HTML. Builders can also perform other tasks related to documentation\n"
"processing.\n"
"\n"
"By default, everything that is outdated is built. Output only for selected\n"
"files can be built by specifying individual filenames.\n"
msgstr "\nGénération de la documentation à partir des fichiers sources.\n\nsphinx-build génère de la documentation à partir des fichiers de SOURCEDIR et la place\ndans OUTPUTDIR. Il recherche 'conf.py' dans SOURCEDIR pour les paramètres de configuration.\nL'outil 'sphinx-quickstart' peut être utilisé pour générer des fichiers modèles,\ny compris 'conf.py'.\n\nsphinx-build peut créer de la documentation dans différents formats. Un format est\nsélectionné en spécifiant le nom du constructeur sur la ligne de commande ; le format par défaut est\nHTML. Les constructeurs peuvent également effectuer d'autres tâches liées au traitement de la documentation.\n\nPar défaut, tout ce qui est obsolète est construit. La sortie pour les fichiers sélectionnés seulement\npeut être construite en spécifiant des noms de fichiers individuels.\n"

#: sphinx/cmd/build.py:139
msgid "path to documentation source files"
msgstr "chemin des fichiers sources de la documentation"

#: sphinx/cmd/build.py:141
msgid "path to output directory"
msgstr "chemin du répertoire de sortie"

#: sphinx/cmd/build.py:143
msgid ""
"(optional) a list of specific files to rebuild. Ignored if --write-all is "
"specified"
msgstr ""

#: sphinx/cmd/build.py:146
msgid "general options"
msgstr "options générales"

#: sphinx/cmd/build.py:149
msgid "builder to use (default: 'html')"
msgstr "constructeur à utiliser (par défaut: 'html')"

#: sphinx/cmd/build.py:152
msgid ""
"run in parallel with N processes, when possible. 'auto' uses the number of "
"CPU cores"
msgstr ""

#: sphinx/cmd/build.py:155
msgid "write all files (default: only write new and changed files)"
msgstr "enregistrer tous les fichiers (par défaut : enregistrer seulement les fichiers nouveaux ou modifiés)"

#: sphinx/cmd/build.py:158
msgid "don't use a saved environment, always read all files"
msgstr "ne pas utiliser un environnement sauvegardé, relire toujours tous les fichiers"

#: sphinx/cmd/build.py:161
msgid "path options"
msgstr ""

#: sphinx/cmd/build.py:163
msgid ""
"directory for doctree and environment files (default: OUTPUT_DIR/.doctrees)"
msgstr ""

#: sphinx/cmd/build.py:166
msgid "directory for the configuration file (conf.py) (default: SOURCE_DIR)"
msgstr ""

#: sphinx/cmd/build.py:171
msgid "use no configuration file, only use settings from -D options"
msgstr ""

#: sphinx/cmd/build.py:174
msgid "override a setting in configuration file"
msgstr "outre passer un paramètre du fichier de configuration"

#: sphinx/cmd/build.py:177
msgid "pass a value into HTML templates"
msgstr "passer une valeur aux templates HTML"

#: sphinx/cmd/build.py:180
msgid "define tag: include \"only\" blocks with TAG"
msgstr "définit une balise : seules les blocs \"only\" avec TAG seront inclus"

#: sphinx/cmd/build.py:182
msgid "nit-picky mode: warn about all missing references"
msgstr ""

#: sphinx/cmd/build.py:184
msgid "console output options"
msgstr "options de la console de sortie"

#: sphinx/cmd/build.py:187
msgid "increase verbosity (can be repeated)"
msgstr "augmenter la verbosité (peut être répété)"

#: sphinx/cmd/build.py:189 sphinx/ext/apidoc.py:340
msgid "no output on stdout, just warnings on stderr"
msgstr "aucune sortie vers stdout, seulement les avertissements vers stderr"

#: sphinx/cmd/build.py:191
msgid "no output at all, not even warnings"
msgstr "aucune sortie du tout, même pas les avertissements"

#: sphinx/cmd/build.py:194
msgid "do emit colored output (default: auto-detect)"
msgstr "émettre une sortie de couleur (par défaut : auto-détection)"

#: sphinx/cmd/build.py:197
msgid "do not emit colored output (default: auto-detect)"
msgstr "ne pas émettre une sortie de couleur (par défaut : auto-détection)"

#: sphinx/cmd/build.py:199
msgid "warning control options"
msgstr ""

#: sphinx/cmd/build.py:201
msgid "write warnings (and errors) to given file"
msgstr "écrire les avertissements (et les erreurs) vers le fichier spécifié"

#: sphinx/cmd/build.py:203
msgid "turn warnings into errors"
msgstr "modifier les avertissements en erreurs"

#: sphinx/cmd/build.py:205
msgid "with --fail-on-warning, keep going when getting warnings"
msgstr ""

#: sphinx/cmd/build.py:207
msgid "show full traceback on exception"
msgstr "montrer la trace d’appels complète si une exception est levée"

#: sphinx/cmd/build.py:209
msgid "run Pdb on exception"
msgstr "exécuter Pdb si une exception se produit."

#: sphinx/cmd/build.py:244
msgid "cannot combine -a option and filenames"
msgstr "impossible de combiner l'option -a avec le nom du fichier"

#: sphinx/cmd/build.py:276
#, python-format
msgid "cannot open warning file %r: %s"
msgstr "impossible d'ouvrir le fichier des avertissements %r : %s"

#: sphinx/cmd/build.py:296
msgid "-D option argument must be in the form name=value"
msgstr "l'option -D doit être sous la forme nom=valeur"

#: sphinx/cmd/build.py:303
msgid "-A option argument must be in the form name=value"
msgstr "l'option -A doit être sous la forme nom=valeur"

#: sphinx/cmd/quickstart.py:42
msgid "automatically insert docstrings from modules"
msgstr "insère automatiquement les docstrings des modules"

#: sphinx/cmd/quickstart.py:43
msgid "automatically test code snippets in doctest blocks"
msgstr "tester automatiquement des extraits de code dans des blocs doctest"

#: sphinx/cmd/quickstart.py:44
msgid "link between Sphinx documentation of different projects"
msgstr "lien entre la documentation Sphinx de différents projets"

#: sphinx/cmd/quickstart.py:45
msgid "write \"todo\" entries that can be shown or hidden on build"
msgstr "entrées \"todo\" pouvant être montrées ou cachées à la compilation"

#: sphinx/cmd/quickstart.py:46
msgid "checks for documentation coverage"
msgstr "vérification de la couverture de la documentation"

#: sphinx/cmd/quickstart.py:47
msgid "include math, rendered as PNG or SVG images"
msgstr "expressions mathématiques, traduites en images PNG ou SVG"

#: sphinx/cmd/quickstart.py:48
msgid "include math, rendered in the browser by MathJax"
msgstr "expressions mathématiques, transmises dans le navigateur à MathJax"

#: sphinx/cmd/quickstart.py:49
msgid "conditional inclusion of content based on config values"
msgstr "inclusion conditionnelle du contenu basé sur la valeur de configuration"

#: sphinx/cmd/quickstart.py:50
msgid "include links to the source code of documented Python objects"
msgstr "inclure des liens vers le code source documenté des objets Python"

#: sphinx/cmd/quickstart.py:51
msgid "create .nojekyll file to publish the document on GitHub pages"
msgstr "crée un fichier .nojekyll pour publier le document sur GitHub pages"

#: sphinx/cmd/quickstart.py:93
msgid "Please enter a valid path name."
msgstr "Merci de saisir un chemin valide."

#: sphinx/cmd/quickstart.py:109
msgid "Please enter some text."
msgstr "Merci de saisir du texte."

#: sphinx/cmd/quickstart.py:116
#, python-format
msgid "Please enter one of %s."
msgstr "Merci de saisir un des %s."

#: sphinx/cmd/quickstart.py:123
msgid "Please enter either 'y' or 'n'."
msgstr "Merci de saisir 'y' ou 'n'."

#: sphinx/cmd/quickstart.py:129
msgid "Please enter a file suffix, e.g. '.rst' or '.txt'."
msgstr "Merci de saisir l'extension du fichier, par exemple '.rst' ou '.txt'."

#: sphinx/cmd/quickstart.py:208
#, python-format
msgid "Welcome to the Sphinx %s quickstart utility."
msgstr "Bienvenue dans le kit de démarrage rapide de Sphinx %s."

#: sphinx/cmd/quickstart.py:210
msgid ""
"Please enter values for the following settings (just press Enter to\n"
"accept a default value, if one is given in brackets)."
msgstr "Veuillez saisir des valeurs pour les paramètres suivants (tapez Entrée pour accepter la valeur par défaut, lorsque celle-ci est indiquée entre crochets)."

#: sphinx/cmd/quickstart.py:215
#, python-format
msgid "Selected root path: %s"
msgstr "Chemin racine sélectionné : %s"

#: sphinx/cmd/quickstart.py:218
msgid "Enter the root path for documentation."
msgstr "Saisissez le répertoire racine de la documentation."

#: sphinx/cmd/quickstart.py:219
msgid "Root path for the documentation"
msgstr "racine de la documentation."

#: sphinx/cmd/quickstart.py:224
msgid "Error: an existing conf.py has been found in the selected root path."
msgstr "Erreur : un fichier conf.py a été trouvé dans le répertoire racine."

#: sphinx/cmd/quickstart.py:226
msgid "sphinx-quickstart will not overwrite existing Sphinx projects."
msgstr "sphinx-quickstart n'écrasera pas un projet Sphinx existant."

#: sphinx/cmd/quickstart.py:228
msgid "Please enter a new root path (or just Enter to exit)"
msgstr "Merci de saisir un nouveau répertoire racine (ou tapez juste Entrée)"

#: sphinx/cmd/quickstart.py:235
msgid ""
"You have two options for placing the build directory for Sphinx output.\n"
"Either, you use a directory \"_build\" within the root path, or you separate\n"
"\"source\" and \"build\" directories within the root path."
msgstr "Vous avez deux options pour l'emplacement du répertoire de construction de la sortie de Sphinx.\nSoit vous utilisez un répertoire \"_build\" dans le chemin racine, soit vous séparez les répertoires \"source\" et \"build\" dans le chemin racine."

#: sphinx/cmd/quickstart.py:238
msgid "Separate source and build directories (y/n)"
msgstr "Séparer les répertoires source et de sortie (y/n)"

#: sphinx/cmd/quickstart.py:242
msgid ""
"Inside the root directory, two more directories will be created; \"_templates\"\n"
"for custom HTML templates and \"_static\" for custom stylesheets and other static\n"
"files. You can enter another prefix (such as \".\") to replace the underscore."
msgstr "Dans le répertoire racine, deux autres répertoires seront créés : \"_templates\" pour les modèles HTML personnalisés et \"_static\" pour les feuilles de style personnalisées et autres fichiers statiques. Vous pouvez entrer un autre préfixe (p. ex. \".\") pour remplacer le tiret bas (\"_\")."

#: sphinx/cmd/quickstart.py:245
msgid "Name prefix for templates and static dir"
msgstr "Préfixe de nom pour les répertoires static et de gabarits (templates)"

#: sphinx/cmd/quickstart.py:249
msgid ""
"The project name will occur in several places in the built documentation."
msgstr "Le nom du projet apparaîtra à plusieurs endroits dans la documentation construite."

#: sphinx/cmd/quickstart.py:250
msgid "Project name"
msgstr "Nom du projet"

#: sphinx/cmd/quickstart.py:252
msgid "Author name(s)"
msgstr "Nom(s) de(s) l'auteur(s)"

#: sphinx/cmd/quickstart.py:256
msgid ""
"Sphinx has the notion of a \"version\" and a \"release\" for the\n"
"software. Each version can have multiple releases. For example, for\n"
"Python the version is something like 2.5 or 3.0, while the release is\n"
"something like 2.5.1 or 3.0a1. If you don't need this dual structure,\n"
"just set both to the same value."
msgstr "Sphinx a la notion de « version » et de « release » pour le\nlogiciel. Chaque version peut avoir plusieurs « releases ». Par exemple, pour\nPython, la version est quelque chose comme 2.5 ou 3.0, tandis que la « release » est\nquelque chose comme 2.5.1 ou 3.0a1. Si vous n'avez pas besoin de cette double structure,\nmettez simplement la même valeur aux deux."

#: sphinx/cmd/quickstart.py:261
msgid "Project version"
msgstr "Version du projet"

#: sphinx/cmd/quickstart.py:263
msgid "Project release"
msgstr "Version du projet"

#: sphinx/cmd/quickstart.py:267
msgid ""
"If the documents are to be written in a language other than English,\n"
"you can select a language here by its language code. Sphinx will then\n"
"translate text that it generates into that language.\n"
"\n"
"For a list of supported codes, see\n"
"https://www.sphinx-doc.org/en/master/usage/configuration.html#confval-language."
msgstr "Si les documents doivent être rédigés dans une langue autre que l’anglais, vous pouvez sélectionner une langue ici grâce à son identifiant. Sphinx utilisera ensuite cette langue pour traduire les textes que lui-même génère.\n\nPour une liste des identifiants supportés, voir\nhttps://www.sphinx-doc.org/en/master/usage/configuration.html#confval-language."

#: sphinx/cmd/quickstart.py:275
msgid "Project language"
msgstr "Langue du projet"

#: sphinx/cmd/quickstart.py:281
msgid ""
"The file name suffix for source files. Commonly, this is either \".txt\"\n"
"or \".rst\". Only files with this suffix are considered documents."
msgstr "L'extension de fichier pour les fichiers sources. En général : \".txt\" ou \".rst\". Seuls les fichiers avec cette extension sont considérés."

#: sphinx/cmd/quickstart.py:283
msgid "Source file suffix"
msgstr "Extension des fichiers sources"

#: sphinx/cmd/quickstart.py:287
msgid ""
"One document is special in that it is considered the top node of the\n"
"\"contents tree\", that is, it is the root of the hierarchical structure\n"
"of the documents. Normally, this is \"index\", but if your \"index\"\n"
"document is a custom template, you can also set this to another filename."
msgstr "Un document est particulier en ce sens qu'il est considéré comme le nœud supérieur de \"l'arbre des contenus\", c'est-à-dire la racine de la structure hiérarchique des documents. Normalement, il s'agit d'un \"index\", mais si votre \"index\" est un modèle personnalisé, vous pouvez également le définir sous un autre nom de fichier."

#: sphinx/cmd/quickstart.py:291
msgid "Name of your master document (without suffix)"
msgstr "Non du fichier principal (sans extension)"

#: sphinx/cmd/quickstart.py:296
#, python-format
msgid ""
"Error: the master file %s has already been found in the selected root path."
msgstr "Erreur : le fichier principal %s est déjà présent dans le répertoire racine du projet."

#: sphinx/cmd/quickstart.py:298
msgid "sphinx-quickstart will not overwrite the existing file."
msgstr "sphinx-quickstart n'écrasera pas les fichiers existants."

#: sphinx/cmd/quickstart.py:300
msgid ""
"Please enter a new file name, or rename the existing file and press Enter"
msgstr "Merci de saisir un nouveau nom de fichier, ou de renommer le fichier existant et valider avec Entrée"

#: sphinx/cmd/quickstart.py:304
msgid "Indicate which of the following Sphinx extensions should be enabled:"
msgstr "Indiquer lesquelles de ces extensions Sphinx doivent être activées :"

#: sphinx/cmd/quickstart.py:312
msgid ""
"Note: imgmath and mathjax cannot be enabled at the same time. imgmath has "
"been deselected."
msgstr "Note : imgmath et mathjax ne peuvent pas être activés en même temps. imgmath a été désactivé."

#: sphinx/cmd/quickstart.py:318
msgid ""
"A Makefile and a Windows command file can be generated for you so that you\n"
"only have to run e.g. `make html' instead of invoking sphinx-build\n"
"directly."
msgstr "Un fichier Makefile et un fichier de commandes Windows peuvent être générés pour vous, afin que vous puissiez exécuter par exemple `make html' au lieu d'appeler directement sphinx-build."

#: sphinx/cmd/quickstart.py:321
msgid "Create Makefile? (y/n)"
msgstr "Création du Makefile ? (y/n)"

#: sphinx/cmd/quickstart.py:324
msgid "Create Windows command file? (y/n)"
msgstr "Création du fichier de commandes Windows ? (y/n)"

#: sphinx/cmd/quickstart.py:368 sphinx/ext/apidoc.py:93
#, python-format
msgid "Creating file %s."
msgstr "Fichier en cours de création %s."

#: sphinx/cmd/quickstart.py:373 sphinx/ext/apidoc.py:90
#, python-format
msgid "File %s already exists, skipping."
msgstr "Le fichier %s existe déjà, il ne sera pas remplacé"

#: sphinx/cmd/quickstart.py:418
msgid "Finished: An initial directory structure has been created."
msgstr "Terminé : la structure initiale a été créée."

#: sphinx/cmd/quickstart.py:420
#, python-format
msgid ""
"You should now populate your master file %s and create other documentation\n"
"source files. "
msgstr "Vous devez maintenant compléter votre fichier principal %s et créer d'autres fichiers sources de documentation. "

#: sphinx/cmd/quickstart.py:423
msgid ""
"Use the Makefile to build the docs, like so:\n"
"   make builder"
msgstr "Utilisez le Makefile pour construire la documentation comme ceci :\n   make builder"

#: sphinx/cmd/quickstart.py:426
#, python-format
msgid ""
"Use the sphinx-build command to build the docs, like so:\n"
"   sphinx-build -b builder %s %s"
msgstr "Utilisez sphinx-build pour construire la documentation comme ceci : \n   sphinx-build -b builder %s %s"

#: sphinx/cmd/quickstart.py:428
msgid ""
"where \"builder\" is one of the supported builders, e.g. html, latex or "
"linkcheck."
msgstr "où « builder » est l'un des constructeurs disponibles, tel que html, latex, ou linkcheck."

#: sphinx/cmd/quickstart.py:464
msgid ""
"\n"
"Generate required files for a Sphinx project.\n"
"\n"
"sphinx-quickstart is an interactive tool that asks some questions about your\n"
"project and then generates a complete documentation directory and sample\n"
"Makefile to be used with sphinx-build.\n"
msgstr "\nEngendre les fichiers requis pour un projet Sphinx.\n\nsphinx-quickstart est un outil interactif qui pose des questions à propos de votre projet et génère un répertoire avec la structure complète nécessaire ainsi qu'un Makefile qui peut être utilisé comme alternative à sphinx-build.\n"

#: sphinx/cmd/quickstart.py:479
msgid "quiet mode"
msgstr "mode silencieux"

#: sphinx/cmd/quickstart.py:484
msgid "project root"
msgstr "racine du projet"

#: sphinx/cmd/quickstart.py:486
msgid "Structure options"
msgstr "Options de structure"

#: sphinx/cmd/quickstart.py:488
msgid "if specified, separate source and build dirs"
msgstr "si spécifié, les répertoires source et build seront séparés"

#: sphinx/cmd/quickstart.py:490
msgid "if specified, create build dir under source dir"
msgstr "si spécifié, créé le dossier build dans le dossier source"

#: sphinx/cmd/quickstart.py:492
msgid "replacement for dot in _templates etc."
msgstr "remplace le point dans _templates etc."

#: sphinx/cmd/quickstart.py:494
msgid "Project basic options"
msgstr "Options basiques du projet."

#: sphinx/cmd/quickstart.py:496
msgid "project name"
msgstr "nom du projet"

#: sphinx/cmd/quickstart.py:498
msgid "author names"
msgstr "nom de l'auteur"

#: sphinx/cmd/quickstart.py:500
msgid "version of project"
msgstr "version du projet"

#: sphinx/cmd/quickstart.py:502
msgid "release of project"
msgstr "version du projet"

#: sphinx/cmd/quickstart.py:504
msgid "document language"
msgstr "langue du document"

#: sphinx/cmd/quickstart.py:506
msgid "source file suffix"
msgstr "préfixe des fichiers source"

#: sphinx/cmd/quickstart.py:508
msgid "master document name"
msgstr "nom du document principal"

#: sphinx/cmd/quickstart.py:510
msgid "use epub"
msgstr "utilisé epub"

#: sphinx/cmd/quickstart.py:512
msgid "Extension options"
msgstr "Options d'extension"

#: sphinx/cmd/quickstart.py:516 sphinx/ext/apidoc.py:400
#, python-format
msgid "enable %s extension"
msgstr "autoriser l'extension %s"

#: sphinx/cmd/quickstart.py:518 sphinx/ext/apidoc.py:396
msgid "enable arbitrary extensions"
msgstr "active l'emploi d'extensions quelconques"

#: sphinx/cmd/quickstart.py:520
msgid "Makefile and Batchfile creation"
msgstr "Création des fichiers Batchfile et Makefile"

#: sphinx/cmd/quickstart.py:522
msgid "create makefile"
msgstr "créer un fichier makefile"

#: sphinx/cmd/quickstart.py:524
msgid "do not create makefile"
msgstr "ne pas créer un fichier makefile"

#: sphinx/cmd/quickstart.py:526
msgid "create batchfile"
msgstr "créer un fichier batch"

#: sphinx/cmd/quickstart.py:529
msgid "do not create batchfile"
msgstr "ne pas créer un fichier batch"

#: sphinx/cmd/quickstart.py:532
msgid "use make-mode for Makefile/make.bat"
msgstr "utiliser make-mode pour Makefile/make.bat"

#: sphinx/cmd/quickstart.py:535
msgid "do not use make-mode for Makefile/make.bat"
msgstr "ne pas utiliser make-mode pour Makefile/make.bat"

#: sphinx/cmd/quickstart.py:537 sphinx/ext/apidoc.py:402
msgid "Project templating"
msgstr "Gabarits de projet"

#: sphinx/cmd/quickstart.py:540 sphinx/ext/apidoc.py:405
msgid "template directory for template files"
msgstr "répertoire des templates"

#: sphinx/cmd/quickstart.py:543
msgid "define a template variable"
msgstr "définissez une variable de template"

#: sphinx/cmd/quickstart.py:576
msgid "\"quiet\" is specified, but any of \"project\" or \"author\" is not specified."
msgstr "vous avez spécifiez \"quit\" , mais \"project\" ou \"author\" ne sont pas spécifiés."

#: sphinx/cmd/quickstart.py:590
msgid ""
"Error: specified path is not a directory, or sphinx files already exist."
msgstr "Erreur : le chemin spécifié n'est pas un répertoire, ou les fichiers Sphinx existent déjà."

#: sphinx/cmd/quickstart.py:592
msgid ""
"sphinx-quickstart only generate into a empty directory. Please specify a new"
" root path."
msgstr "sphinx-quickstart peut générer ces fichiers seulement dans un répertoire vide. Merci de spécifier un nouveau répertoire racine."

#: sphinx/cmd/quickstart.py:607
#, python-format
msgid "Invalid template variable: %s"
msgstr "Variable de template invalide : %s"

#: sphinx/directives/code.py:61
msgid "non-whitespace stripped by dedent"
msgstr "les espaces non blancs sont supprimés par dedent"

#: sphinx/directives/code.py:82
#, python-format
msgid "Invalid caption: %s"
msgstr "Légende invalide: %s"

#: sphinx/directives/code.py:127 sphinx/directives/code.py:277
#: sphinx/directives/code.py:453
#, python-format
msgid "line number spec is out of range(1-%d): %r"
msgstr "le numéro de ligne spécifiée est en dehors des limites (1-%d):%r"

#: sphinx/directives/code.py:206
#, python-format
msgid "Cannot use both \"%s\" and \"%s\" options"
msgstr "Impossible d'utiliser les options \"%s\" et \"%s\" en même temps."

#: sphinx/directives/code.py:220
#, python-format
msgid "Include file %r not found or reading it failed"
msgstr "Le fichier d'include %r est introuvable ou sa lecture a échouée."

#: sphinx/directives/code.py:223
#, python-format
msgid ""
"Encoding %r used for reading included file %r seems to be wrong, try giving "
"an :encoding: option"
msgstr "L’encodage %r utilisé pour lire le fichier inclus %r semble erroné, veuillez ajouter une option :encoding:"

#: sphinx/directives/code.py:260
#, python-format
msgid "Object named %r not found in include file %r"
msgstr "L'objet nommé %r est introuvable dans le fichier d'include %r"

#: sphinx/directives/code.py:286
msgid "Cannot use \"lineno-match\" with a disjoint set of \"lines\""
msgstr "On ne peut pas utiliser \"lineno-match\" avec un \"lines\" non contigu "

#: sphinx/directives/code.py:291
#, python-format
msgid "Line spec %r: no lines pulled from include file %r"
msgstr "Spécification de lignes %r : aucune ligne extraite du fichier inclus %r"

#: sphinx/directives/other.py:120
#, python-format
msgid "toctree glob pattern %r didn't match any documents"
msgstr "le motif global toctree %r ne correspond à aucun document"

#: sphinx/directives/other.py:146 sphinx/environment/adapters/toctree.py:324
#, python-format
msgid "toctree contains reference to excluded document %r"
msgstr "le toctree contient une référence à des documents exclus %r"

#: sphinx/directives/other.py:149 sphinx/environment/adapters/toctree.py:328
#, python-format
msgid "toctree contains reference to nonexisting document %r"
msgstr "la table des matières contient des références à des documents inexistants %r"

#: sphinx/directives/other.py:160
#, python-format
msgid "duplicated entry found in toctree: %s"
msgstr "entrée dupliquée trouvée dans toctree: %s"

#: sphinx/directives/other.py:193
msgid "Section author: "
msgstr "Auteur de la section : "

#: sphinx/directives/other.py:195
msgid "Module author: "
msgstr "Auteur du module : "

#: sphinx/directives/other.py:197
msgid "Code author: "
msgstr "Auteur du code : "

#: sphinx/directives/other.py:199
msgid "Author: "
msgstr "Auteur : "

#: sphinx/directives/other.py:275
msgid ".. acks content is not a list"
msgstr "... le contenu de acks n'est pas une liste"

#: sphinx/directives/other.py:301
msgid ".. hlist content is not a list"
msgstr "... le contenu de hlist n'est pas une liste"

#: sphinx/directives/patches.py:66
msgid ""
"\":file:\" option for csv-table directive now recognizes an absolute path as"
" a relative path from source directory. Please update your document."
msgstr "L'option \":file :\" de la directive csv-table reconnaît désormais un chemin absolu comme un chemin relatif du répertoire source. Veuillez mettre à jour votre document."

#: sphinx/domains/__init__.py:397
#, python-format
msgid "%s %s"
msgstr "%s %s"

#: sphinx/domains/changeset.py:23
#, python-format
msgid "Added in version %s"
msgstr ""

#: sphinx/domains/changeset.py:24
#, python-format
msgid "Changed in version %s"
msgstr "Modifié dans la version %s"

#: sphinx/domains/changeset.py:25
#, python-format
msgid "Deprecated since version %s"
msgstr "Obsolète depuis la version %s"

#: sphinx/domains/changeset.py:26
#, python-format
msgid "Removed in version %s"
msgstr "Supprimé dans la version %s"

#: sphinx/domains/citation.py:71
#, python-format
msgid "duplicate citation %s, other instance in %s"
msgstr "citation dupliquée %s, une autre instance dans %s"

#: sphinx/domains/citation.py:82
#, python-format
msgid "Citation [%s] is not referenced."
msgstr "La citation [%s] n'est pas référencée"

#: sphinx/domains/javascript.py:165
#, python-format
msgid "%s() (built-in function)"
msgstr "%s() (fonction de base)"

#: sphinx/domains/javascript.py:166 sphinx/domains/python/__init__.py:240
#, python-format
msgid "%s() (%s method)"
msgstr "%s() (méthode %s)"

#: sphinx/domains/javascript.py:168
#, python-format
msgid "%s() (class)"
msgstr "%s() (classe)"

#: sphinx/domains/javascript.py:170
#, python-format
msgid "%s (global variable or constant)"
msgstr "%s (variable globale ou constante)"

#: sphinx/domains/javascript.py:172 sphinx/domains/python/__init__.py:325
#, python-format
msgid "%s (%s attribute)"
msgstr "%s (attribut %s)"

#: sphinx/domains/javascript.py:255
msgid "Arguments"
msgstr "Arguments"

#: sphinx/domains/cpp/__init__.py:350 sphinx/domains/javascript.py:258
msgid "Throws"
msgstr "Déclenche"

#: sphinx/domains/c/__init__.py:251 sphinx/domains/cpp/__init__.py:361
#: sphinx/domains/javascript.py:261 sphinx/domains/python/_object.py:175
msgid "Returns"
msgstr "Renvoie"

#: sphinx/domains/c/__init__.py:253 sphinx/domains/javascript.py:263
#: sphinx/domains/python/_object.py:177
msgid "Return type"
msgstr "Type renvoyé"

#: sphinx/domains/javascript.py:331
#, python-format
msgid "%s (module)"
msgstr "%s (module)"

#: sphinx/domains/c/__init__.py:622 sphinx/domains/cpp/__init__.py:764
#: sphinx/domains/javascript.py:368 sphinx/domains/python/__init__.py:574
msgid "function"
msgstr "fonction"

#: sphinx/domains/javascript.py:369 sphinx/domains/python/__init__.py:578
msgid "method"
msgstr "méthode"

#: sphinx/domains/cpp/__init__.py:762 sphinx/domains/javascript.py:370
#: sphinx/domains/python/__init__.py:576
msgid "class"
msgstr "classe"

#: sphinx/domains/javascript.py:371 sphinx/domains/python/__init__.py:575
msgid "data"
msgstr "données"

#: sphinx/domains/javascript.py:372 sphinx/domains/python/__init__.py:581
msgid "attribute"
msgstr "attribut"

#: sphinx/domains/javascript.py:373 sphinx/domains/python/__init__.py:583
msgid "module"
msgstr "module"

#: sphinx/domains/javascript.py:404
#, python-format
msgid "duplicate %s description of %s, other %s in %s"
msgstr "description de %s dupliquée pour%s; l'autre %s se trouve dans %s"

#: sphinx/domains/math.py:63
#, python-format
msgid "duplicate label of equation %s, other instance in %s"
msgstr "Libellé dupliqué pour l'équation %s, autre instance dans %s"

#: sphinx/domains/math.py:118 sphinx/writers/latex.py:2252
#, python-format
msgid "Invalid math_eqref_format: %r"
msgstr "math_eqref_format invalide : %r"

#: sphinx/domains/rst.py:127 sphinx/domains/rst.py:184
#, python-format
msgid "%s (directive)"
msgstr "%s (directive)"

#: sphinx/domains/rst.py:185 sphinx/domains/rst.py:189
#, python-format
msgid ":%s: (directive option)"
msgstr ":%s: (option de directive)"

#: sphinx/domains/rst.py:213
#, python-format
msgid "%s (role)"
msgstr "%s (role)"

#: sphinx/domains/rst.py:223
msgid "directive"
msgstr "directive"

#: sphinx/domains/rst.py:224
msgid "directive-option"
msgstr "option de directive"

#: sphinx/domains/rst.py:225
msgid "role"
msgstr "role"

#: sphinx/domains/rst.py:247
#, python-format
msgid "duplicate description of %s %s, other instance in %s"
msgstr "description dupliquée pour %s %s; l'autre instance se trouve dans %s"

#: sphinx/domains/c/__init__.py:146
#, python-format
msgid "%s (C %s)"
msgstr "%s (C %s)"

#: sphinx/domains/c/__init__.py:207 sphinx/domains/c/_symbol.py:552
#, python-format
msgid ""
"Duplicate C declaration, also defined at %s:%s.\n"
"Declaration is '.. c:%s:: %s'."
msgstr "Déclaration C dupliquée, également définie à %s:%s.\nLa déclaration est '.. c:%s:: %s'."

#: sphinx/domains/c/__init__.py:245 sphinx/domains/cpp/__init__.py:344
#: sphinx/domains/python/_object.py:163 sphinx/ext/napoleon/docstring.py:762
msgid "Parameters"
msgstr "Paramètres"

#: sphinx/domains/c/__init__.py:248 sphinx/domains/cpp/__init__.py:357
msgid "Return values"
msgstr "Valeurs retournées"

#: sphinx/domains/c/__init__.py:620 sphinx/domains/cpp/__init__.py:765
msgid "member"
msgstr "membre"

#: sphinx/domains/c/__init__.py:621
msgid "variable"
msgstr "variable"

#: sphinx/domains/c/__init__.py:623
msgid "macro"
msgstr "macro"

#: sphinx/domains/c/__init__.py:624
msgid "struct"
msgstr "structure"

#: sphinx/domains/c/__init__.py:625 sphinx/domains/cpp/__init__.py:763
msgid "union"
msgstr "union"

#: sphinx/domains/c/__init__.py:626 sphinx/domains/cpp/__init__.py:768
msgid "enum"
msgstr "énumération"

#: sphinx/domains/c/__init__.py:627 sphinx/domains/cpp/__init__.py:769
msgid "enumerator"
msgstr "énumérateur"

#: sphinx/domains/c/__init__.py:628 sphinx/domains/cpp/__init__.py:766
msgid "type"
msgstr "type"

#: sphinx/domains/c/__init__.py:630 sphinx/domains/cpp/__init__.py:771
msgid "function parameter"
msgstr "paramètre de fonction"

#: sphinx/domains/cpp/__init__.py:63
msgid "Template Parameters"
msgstr "Paramètres du modèle"

#: sphinx/domains/cpp/__init__.py:185
#, python-format
msgid "%s (C++ %s)"
msgstr "%s (C++ %s)"

#: sphinx/domains/cpp/__init__.py:268 sphinx/domains/cpp/_symbol.py:790
#, python-format
msgid ""
"Duplicate C++ declaration, also defined at %s:%s.\n"
"Declaration is '.. cpp:%s:: %s'."
msgstr "Déclaration C++ dupliquée, également définie à %s:%s.\nLa déclaration est '.. cpp:%s:: %s'."

#: sphinx/domains/cpp/__init__.py:767
msgid "concept"
msgstr "concept"

#: sphinx/domains/cpp/__init__.py:772
msgid "template parameter"
msgstr "paramètre du modèle"

#: sphinx/domains/python/__init__.py:94 sphinx/domains/python/__init__.py:231
#, python-format
msgid "%s() (in module %s)"
msgstr "%s() (dans le module %s)"

#: sphinx/domains/python/__init__.py:154 sphinx/domains/python/__init__.py:321
#: sphinx/domains/python/__init__.py:372
#, python-format
msgid "%s (in module %s)"
msgstr "%s (dans le module %s)"

#: sphinx/domains/python/__init__.py:156
#, python-format
msgid "%s (built-in variable)"
msgstr "%s (variable de base)"

#: sphinx/domains/python/__init__.py:181
#, python-format
msgid "%s (built-in class)"
msgstr "%s (classe de base)"

#: sphinx/domains/python/__init__.py:182
#, python-format
msgid "%s (class in %s)"
msgstr "%s (classe dans %s)"

#: sphinx/domains/python/__init__.py:236
#, python-format
msgid "%s() (%s class method)"
msgstr "%s() (méthode de la classe %s)"

#: sphinx/domains/python/__init__.py:238
#, python-format
msgid "%s() (%s static method)"
msgstr "%s() (méthode statique %s)"

#: sphinx/domains/python/__init__.py:376
#, python-format
msgid "%s (%s property)"
msgstr "%s (propriété %s)"

#: sphinx/domains/python/__init__.py:502
msgid "Python Module Index"
msgstr "Index des modules Python"

#: sphinx/domains/python/__init__.py:503
msgid "modules"
msgstr "modules"

#: sphinx/domains/python/__init__.py:552
msgid "Deprecated"
msgstr "Obsolète"

#: sphinx/domains/python/__init__.py:577
msgid "exception"
msgstr "exception"

#: sphinx/domains/python/__init__.py:579
msgid "class method"
msgstr "méthode de classe"

#: sphinx/domains/python/__init__.py:580
msgid "static method"
msgstr "méthode statique"

#: sphinx/domains/python/__init__.py:582
msgid "property"
msgstr "propriété"

#: sphinx/domains/python/__init__.py:640
#, python-format
msgid ""
"duplicate object description of %s, other instance in %s, use :no-index: for"
" one of them"
msgstr "description dupliquée de l'objet %s, autre instance dans %s, utiliser :no-index: pour l'un d'eux"

#: sphinx/domains/python/__init__.py:760
#, python-format
msgid "more than one target found for cross-reference %r: %s"
msgstr "plusieurs cibles trouvées pour le renvoi %r : %s"

#: sphinx/domains/python/__init__.py:821
msgid " (deprecated)"
msgstr " (obsolète)"

#: sphinx/domains/python/_object.py:168
msgid "Variables"
msgstr "Variables"

#: sphinx/domains/python/_object.py:172
msgid "Raises"
msgstr "Lève"

#: sphinx/domains/std/__init__.py:80 sphinx/domains/std/__init__.py:97
#, python-format
msgid "environment variable; %s"
msgstr "variable d'environnement; %s"

#: sphinx/domains/std/__init__.py:157
#, python-format
msgid ""
"Malformed option description %r, should look like \"opt\", \"-opt args\", \""
"--opt args\", \"/opt args\" or \"+opt args\""
msgstr "description de l'option malformée, elle doit ressembler à \nMalformed option description %r, should look like \"opt\", \"-opt args\", \"--opt args\", \"/opt args\" or \"+opt args\""

#: sphinx/domains/std/__init__.py:228
#, python-format
msgid "%s command line option"
msgstr "option de ligne de commande %s"

#: sphinx/domains/std/__init__.py:230
msgid "command line option"
msgstr "option de ligne de commande"

#: sphinx/domains/std/__init__.py:348
msgid "glossary term must be preceded by empty line"
msgstr "le terme du glossaire doit être précédé d'une ligne vide"

#: sphinx/domains/std/__init__.py:356
msgid "glossary terms must not be separated by empty lines"
msgstr "les termes du glossaire ne doivent pas être séparés par des lignes vides"

#: sphinx/domains/std/__init__.py:362 sphinx/domains/std/__init__.py:375
msgid "glossary seems to be misformatted, check indentation"
msgstr "le glossaire semble être mal formaté; vérifiez l'indentation"

#: sphinx/domains/std/__init__.py:518
msgid "glossary term"
msgstr "terme du glossaire"

#: sphinx/domains/std/__init__.py:519
msgid "grammar token"
msgstr "élément de grammaire"

#: sphinx/domains/std/__init__.py:520
msgid "reference label"
msgstr "étiquette de référence"

#: sphinx/domains/std/__init__.py:522
msgid "environment variable"
msgstr "variable d'environnement"

#: sphinx/domains/std/__init__.py:523
msgid "program option"
msgstr "option du programme"

#: sphinx/domains/std/__init__.py:524
msgid "document"
msgstr "document"

#: sphinx/domains/std/__init__.py:560 sphinx/domains/std/__init__.py:572
msgid "Module Index"
msgstr "Index du module"

#: sphinx/domains/std/__init__.py:561 sphinx/domains/std/__init__.py:573
#: sphinx/themes/basic/defindex.html:25
msgid "Search Page"
msgstr "Page de recherche"

#: sphinx/domains/std/__init__.py:616 sphinx/domains/std/__init__.py:722
#: sphinx/ext/autosectionlabel.py:53
#, python-format
msgid "duplicate label %s, other instance in %s"
msgstr "libellé dupliqué %s, l'autre instance se trouve dans %s"

#: sphinx/domains/std/__init__.py:635
#, python-format
msgid "duplicate %s description of %s, other instance in %s"
msgstr "description %s dupliquée pour %s; l'autre instance se trouve dans %s"

#: sphinx/domains/std/__init__.py:841
msgid "numfig is disabled. :numref: is ignored."
msgstr "le paramètre numfig est désactivé : le paramètre :numref: est ignoré"

#: sphinx/domains/std/__init__.py:849
#, python-format
msgid "Failed to create a cross reference. Any number is not assigned: %s"
msgstr "Impossible de créer une référence croisée. Aucun nombre n'est attribué: %s"

#: sphinx/domains/std/__init__.py:861
#, python-format
msgid "the link has no caption: %s"
msgstr "le lien n'a pas de légende : %s"

#: sphinx/domains/std/__init__.py:875
#, python-format
msgid "invalid numfig_format: %s (%r)"
msgstr "numfig_format invalide : %s (%r)"

#: sphinx/domains/std/__init__.py:878
#, python-format
msgid "invalid numfig_format: %s"
msgstr "numfig_format invalide : %s"

#: sphinx/domains/std/__init__.py:1109
#, python-format
msgid "undefined label: %r"
msgstr "label non défini: %r"

#: sphinx/domains/std/__init__.py:1111
#, python-format
msgid "Failed to create a cross reference. A title or caption not found: %r"
msgstr "Échec de création d'une référence. Ni titre ni légende trouvé : %r"

#: sphinx/environment/__init__.py:71
msgid "new config"
msgstr "nouvelle configuration"

#: sphinx/environment/__init__.py:72
msgid "config changed"
msgstr "la configuration a changé"

#: sphinx/environment/__init__.py:73
msgid "extensions changed"
msgstr "les extensions ont changé"

#: sphinx/environment/__init__.py:279
msgid "build environment version not current"
msgstr "version non à jour de l’environnement de construction"

#: sphinx/environment/__init__.py:281
msgid "source directory has changed"
msgstr "le répertoire racine a changé"

#: sphinx/environment/__init__.py:360
msgid ""
"This environment is incompatible with the selected builder, please choose "
"another doctree directory."
msgstr "Cet environnement est incompatible avec le constructeur sélectionné, veuillez choisir un autre répertoire doctree."

#: sphinx/environment/__init__.py:459
#, python-format
msgid "Failed to scan documents in %s: %r"
msgstr "Échec du scan des documents dans %s : %r"

#: sphinx/environment/__init__.py:596
#, python-format
msgid "Domain %r is not registered"
msgstr "le domaine %r n'est pas enregistré."

#: sphinx/environment/__init__.py:730
msgid "document isn't included in any toctree"
msgstr "Le document n'est inclus dans aucune toctree."

#: sphinx/environment/__init__.py:766
msgid "self referenced toctree found. Ignored."
msgstr "une table des matières auto-référencée a été trouvée. Elle sera ignorée."

#: sphinx/environment/adapters/indexentries.py:69
#, python-format
msgid "see %s"
msgstr "voir %s"

#: sphinx/environment/adapters/indexentries.py:73
#, python-format
msgid "see also %s"
msgstr "voir aussi %s"

#: sphinx/environment/adapters/indexentries.py:76
#, python-format
msgid "unknown index entry type %r"
msgstr "type d'index saisie inconnu %r"

#: sphinx/environment/adapters/indexentries.py:187
#: sphinx/templates/latex/sphinxmessages.sty_t:11
msgid "Symbols"
msgstr "Symboles"

#: sphinx/environment/adapters/toctree.py:297
#, python-format
msgid "circular toctree references detected, ignoring: %s <- %s"
msgstr "table des matières avec une référence circulaire détectée, elle sera ignorée : %s <- %s"

#: sphinx/environment/adapters/toctree.py:317
#, python-format
msgid ""
"toctree contains reference to document %r that doesn't have a title: no link"
" will be generated"
msgstr "la table des matières contient une référence à un document %r qui n'a pas de titre : aucun lien ne sera généré"

#: sphinx/environment/adapters/toctree.py:326
#, python-format
msgid "toctree contains reference to non-included document %r"
msgstr "toctree contient une référence au document non inclu %r"

#: sphinx/environment/collectors/asset.py:89
#, python-format
msgid "image file not readable: %s"
msgstr "fichier image %s illisible "

#: sphinx/environment/collectors/asset.py:108
#, python-format
msgid "image file %s not readable: %s"
msgstr "fichier image %s illisible : %s"

#: sphinx/environment/collectors/asset.py:134
#, python-format
msgid "download file not readable: %s"
msgstr "le fichier téléchargé n’est pas lisible: %s"

#: sphinx/environment/collectors/toctree.py:225
#, python-format
msgid "%s is already assigned section numbers (nested numbered toctree?)"
msgstr "%s a déjà des numéros de section attribués (toctree numérotés emboîtés ?)"

#: sphinx/ext/apidoc.py:86
#, python-format
msgid "Would create file %s."
msgstr "Créerait le fichier %s."

#: sphinx/ext/apidoc.py:318
msgid ""
"\n"
"Look recursively in <MODULE_PATH> for Python modules and packages and create\n"
"one reST file with automodule directives per package in the <OUTPUT_PATH>.\n"
"\n"
"The <EXCLUDE_PATTERN>s can be file and/or directory patterns that will be\n"
"excluded from generation.\n"
"\n"
"Note: By default this script will not overwrite already created files."
msgstr "\nCherche récursivement dans <MODULE_PATH> des modules et packages Python et crée\ndans <OUTPUT_PATH> un fichier reST par package avec des directives automodule.\n\nLes <EXCLUDE_PATTERN>s peuvent être tout pattern de fichiers et/ou de répertoires à exclure.\n\nNote : par défaut ce script n'écrasera pas des fichiers déjà créés."

#: sphinx/ext/apidoc.py:331
msgid "path to module to document"
msgstr "chemin vers le module à documenter"

#: sphinx/ext/apidoc.py:333
msgid ""
"fnmatch-style file and/or directory patterns to exclude from generation"
msgstr "patterns de fichier fnmatch-style et/ou répertoire à exclure"

#: sphinx/ext/apidoc.py:338
msgid "directory to place all output"
msgstr "répertoire où placer toutes les sorties"

#: sphinx/ext/apidoc.py:343
msgid "maximum depth of submodules to show in the TOC (default: 4)"
msgstr "Nombre maximum de sous-modules visibles dans la table des matières (par défaut : 4)"

#: sphinx/ext/apidoc.py:346
msgid "overwrite existing files"
msgstr "remplacer les fichiers existants"

#: sphinx/ext/apidoc.py:349
msgid ""
"follow symbolic links. Powerful when combined with "
"collective.recipe.omelette."
msgstr "suivre les liens symboliques. Très utile en combinaison avec collective.recipe.omelette."

#: sphinx/ext/apidoc.py:352
msgid "run the script without creating files"
msgstr "exécuter le script sans créer les fichiers"

#: sphinx/ext/apidoc.py:355
msgid "put documentation for each module on its own page"
msgstr "afficher la documentation de chaque module sur sa propre page"

#: sphinx/ext/apidoc.py:358
msgid "include \"_private\" modules"
msgstr "inclure le module \"_private\""

#: sphinx/ext/apidoc.py:360
msgid "filename of table of contents (default: modules)"
msgstr "nom du fichier de table des matières (défaut : modules)"

#: sphinx/ext/apidoc.py:362
msgid "don't create a table of contents file"
msgstr "ne pas créer de fichier de table des matières"

#: sphinx/ext/apidoc.py:365
msgid ""
"don't create headings for the module/package packages (e.g. when the "
"docstrings already contain them)"
msgstr "ne pas créer de titres pour le module ou package (e.g. lorsque les doctrings en fournissent déjà)"

#: sphinx/ext/apidoc.py:370
msgid "put module documentation before submodule documentation"
msgstr "mettre la documentation du module avant celle du sous-module"

#: sphinx/ext/apidoc.py:374
msgid ""
"interpret module paths according to PEP-0420 implicit namespaces "
"specification"
msgstr "interprète les chemins de module selon la spécification PEP-0420 des espaces implicites de noms"

#: sphinx/ext/apidoc.py:378
msgid "file suffix (default: rst)"
msgstr "extension du fichier (par défaut : rst)"

#: sphinx/ext/apidoc.py:380
msgid "generate a full project with sphinx-quickstart"
msgstr "générer un projet complet avec sphinx-quickstart"

#: sphinx/ext/apidoc.py:383
msgid "append module_path to sys.path, used when --full is given"
msgstr "ajoute module_path à la fin de sys.path, utilisé lorsque --full est présent"

#: sphinx/ext/apidoc.py:385
msgid "project name (default: root module name)"
msgstr "nom du projet (par défaut : nom du module principal)"

#: sphinx/ext/apidoc.py:387
msgid "project author(s), used when --full is given"
msgstr "auteur(s) du projet, utilisé quand l'option -full est précisée"

#: sphinx/ext/apidoc.py:389
msgid "project version, used when --full is given"
msgstr "version du projet, utilisé quand l'option -full est précisée"

#: sphinx/ext/apidoc.py:391
msgid "project release, used when --full is given, defaults to --doc-version"
msgstr "révision du projet, utilisé lorsque --full est présent, par défaut reprend --doc-version"

#: sphinx/ext/apidoc.py:394
msgid "extension options"
msgstr "options relatives aux extensions"

#: sphinx/ext/apidoc.py:427
#, python-format
msgid "%s is not a directory."
msgstr "%s n'est pas un répertoire"

#: sphinx/ext/autosectionlabel.py:49
#, python-format
msgid "section \"%s\" gets labeled as \"%s\""
msgstr "la section \"%s\" est étiquettée \"%s\""

#: sphinx/ext/coverage.py:46
#, python-format
msgid "invalid regex %r in %s"
msgstr "regex invalide %r dans %s"

#: sphinx/ext/coverage.py:75
#, python-format
msgid ""
"Testing of coverage in the sources finished, look at the results in "
"%(outdir)spython.txt."
msgstr "Vérification du taux de couverture documentaire dans les sources achevée, voir les résultats dans %(outdir)spython.txt."

#: sphinx/ext/coverage.py:89
#, python-format
msgid "invalid regex %r in coverage_c_regexes"
msgstr "regex invalide %r dans coverage_c_regexes"

#: sphinx/ext/coverage.py:157
#, python-format
msgid "undocumented c api: %s [%s] in file %s"
msgstr "API C non documentée : %s [%s] dans le fichier %s"

#: sphinx/ext/coverage.py:189
#, python-format
msgid "module %s could not be imported: %s"
msgstr "le module %s ne pas être importé : %s"

#: sphinx/ext/coverage.py:340
#, python-format
msgid "undocumented python function: %s :: %s"
msgstr "fonction python non documentée: %s :: %s"

#: sphinx/ext/coverage.py:356
#, python-format
msgid "undocumented python class: %s :: %s"
msgstr "classe python non documentée: %s :: %s"

#: sphinx/ext/coverage.py:369
#, python-format
msgid "undocumented python method: %s :: %s :: %s"
msgstr "méthode python non documentée: %s :: %s :: %s"

#: sphinx/ext/doctest.py:115
#, python-format
msgid "missing '+' or '-' in '%s' option."
msgstr "option '+' ou '-' manquante dans %s."

#: sphinx/ext/doctest.py:120
#, python-format
msgid "'%s' is not a valid option."
msgstr "'%s' n'est pas une option valide."

#: sphinx/ext/doctest.py:134
#, python-format
msgid "'%s' is not a valid pyversion option"
msgstr "%s n'est pas une option pyversion valide"

#: sphinx/ext/doctest.py:220
msgid "invalid TestCode type"
msgstr "type invalide de TestCode"

#: sphinx/ext/doctest.py:281
#, python-format
msgid ""
"Testing of doctests in the sources finished, look at the results in "
"%(outdir)s/output.txt."
msgstr "Exécution des doctests des sources achevée, voir les résultats dans %(outdir)s/output.txt."

#: sphinx/ext/doctest.py:438
#, python-format
msgid "no code/output in %s block at %s:%s"
msgstr "pas de code ou sortie dans le bloc %s en %s : %s"

#: sphinx/ext/doctest.py:526
#, python-format
msgid "ignoring invalid doctest code: %r"
msgstr "code doctest invalide sera ignoré : %r"

#: sphinx/ext/duration.py:77
msgid ""
"====================== slowest reading durations ======================="
msgstr "====================== durées de lecture les plus lentes ======================="

#: sphinx/ext/extlinks.py:82
#, python-format
msgid ""
"hardcoded link %r could be replaced by an extlink (try using %r instead)"
msgstr "le lien %r codé en dur pourrait être remplacé par un extlink (essayez d'utiliser %r à la place)"

#: sphinx/ext/graphviz.py:135
msgid "Graphviz directive cannot have both content and a filename argument"
msgstr "La directive Graphviz ne peut pas avoir simultanément du contenu et un argument de nom de fichier"

#: sphinx/ext/graphviz.py:145
#, python-format
msgid "External Graphviz file %r not found or reading it failed"
msgstr "Fichier externe Graphviz %r non trouvé ou échec de sa lecture"

#: sphinx/ext/graphviz.py:152
msgid "Ignoring \"graphviz\" directive without content."
msgstr "Directive « graphviz » sans contenu ignorée."

#: sphinx/ext/graphviz.py:268
#, python-format
msgid "graphviz_dot executable path must be set! %r"
msgstr "Le chemin de l'exécutable de graphviz_dot doit être défini ! %r"

#: sphinx/ext/graphviz.py:303
#, python-format
msgid ""
"dot command %r cannot be run (needed for graphviz output), check the "
"graphviz_dot setting"
msgstr "la commande dot %r ne peut pas être exécutée (nécessaire pour le rendu graphviz). Vérifiez le paramètre graphviz_dot"

#: sphinx/ext/graphviz.py:310
#, python-format
msgid ""
"dot exited with error:\n"
"[stderr]\n"
"%r\n"
"[stdout]\n"
"%r"
msgstr "dot a terminé avec une erreur :\n[stderr]\n%r\n[stdout]\n%r"

#: sphinx/ext/graphviz.py:313
#, python-format
msgid ""
"dot did not produce an output file:\n"
"[stderr]\n"
"%r\n"
"[stdout]\n"
"%r"
msgstr "dot n'a pas produit de fichier de sortie : \n[stderr]\n%r\n[stdout]\n%r"

#: sphinx/ext/graphviz.py:329
#, python-format
msgid "graphviz_output_format must be one of 'png', 'svg', but is %r"
msgstr "graphviz_output_format doit être « png » ou « svg »,  mais est %r"

#: sphinx/ext/graphviz.py:333 sphinx/ext/graphviz.py:386
#: sphinx/ext/graphviz.py:423
#, python-format
msgid "dot code %r: %s"
msgstr "dot code %r: %s"

#: sphinx/ext/graphviz.py:436 sphinx/ext/graphviz.py:444
#, python-format
msgid "[graph: %s]"
msgstr "[graphe: %s]"

#: sphinx/ext/graphviz.py:438 sphinx/ext/graphviz.py:446
msgid "[graph]"
msgstr "[graphe]"

#: sphinx/ext/imgconverter.py:39
#, python-format
msgid ""
"Unable to run the image conversion command %r. 'sphinx.ext.imgconverter' requires ImageMagick by default. Ensure it is installed, or set the 'image_converter' option to a custom conversion command.\n"
"\n"
"Traceback: %s"
msgstr "Impossible d’exécuter la commande de conversion d'image %r. 'sphinx.ext.imgconverter' nécessite par défaut ImageMagick. Assurez-vous que ce dernier est installé, ou configurez l’option 'image_converter' pour faire référence à une commande de conversion ad hoc.\n\nTrace d’appels : %s"

#: sphinx/ext/imgconverter.py:48 sphinx/ext/imgconverter.py:72
#, python-format
msgid ""
"convert exited with error:\n"
"[stderr]\n"
"%r\n"
"[stdout]\n"
"%r"
msgstr "convert a terminé avec une erreur :\n[stderr]\n%r\n[stdout]\n%r"

#: sphinx/ext/imgconverter.py:67
#, python-format
msgid "convert command %r cannot be run, check the image_converter setting"
msgstr "la commande convert %r ne peut pas être exécutée; vérifiez le paramètre image_converter"

#: sphinx/ext/imgmath.py:158
#, python-format
msgid ""
"LaTeX command %r cannot be run (needed for math display), check the "
"imgmath_latex setting"
msgstr "La commande LaTeX %r (nécessaire pour le rendu des équations mathématiques), ne peut pas être exécutée, vérifier le paramètre imgmath_latex"

#: sphinx/ext/imgmath.py:173
#, python-format
msgid ""
"%s command %r cannot be run (needed for math display), check the imgmath_%s "
"setting"
msgstr "La commande de %s, %r, ne pas être exécuté (nécessaire pour display mathématique), vérifier la configuration imgmath_%s"

#: sphinx/ext/imgmath.py:327
#, python-format
msgid "display latex %r: %s"
msgstr "latex de type display %r : %s"

#: sphinx/ext/imgmath.py:361
#, python-format
msgid "inline latex %r: %s"
msgstr "latex en ligne %r : %s"

#: sphinx/ext/imgmath.py:368 sphinx/ext/mathjax.py:53
msgid "Link to this equation"
msgstr "Lien vers cette équation"

#: sphinx/ext/intersphinx.py:195
#, python-format
msgid "intersphinx inventory has moved: %s -> %s"
msgstr "l’inventaire intersphinx a bougé : %s -> %s"

#: sphinx/ext/intersphinx.py:230
#, python-format
msgid "loading intersphinx inventory from %s..."
msgstr "chargement de l'inventaire intersphinx de %s..."

#: sphinx/ext/intersphinx.py:244
msgid ""
"encountered some issues with some of the inventories, but they had working "
"alternatives:"
msgstr "quelques problèmes ont été rencontrés avec quelques uns des inventaires, mais ils disposaient d'alternatives fonctionnelles :"

#: sphinx/ext/intersphinx.py:250
msgid "failed to reach any of the inventories with the following issues:"
msgstr "échec d'accès à un quelconque inventaire, messages de contexte suivants :"

#: sphinx/ext/intersphinx.py:303
#, python-format
msgid "(in %s v%s)"
msgstr "(disponible dans %s v%s)"

#: sphinx/ext/intersphinx.py:305
#, python-format
msgid "(in %s)"
msgstr "(dans %s)"

#: sphinx/ext/intersphinx.py:538
#, python-format
msgid "inventory for external cross-reference not found: %r"
msgstr ""

#: sphinx/ext/intersphinx.py:546
#, python-format
msgid "invalid external cross-reference suffix: %r"
msgstr ""

#: sphinx/ext/intersphinx.py:557
#, python-format
msgid "domain for external cross-reference not found: %r"
msgstr ""

#: sphinx/ext/intersphinx.py:750
#, python-format
msgid "external %s:%s reference target not found: %s"
msgstr "%sexterne :%s cible de référence non trouvée : %s"

#: sphinx/ext/intersphinx.py:775
#, python-format
msgid "intersphinx identifier %r is not string. Ignored"
msgstr "l’identifiant intersphinx %r n'est pas une chaîne. Il sera ignoré"

#: sphinx/ext/intersphinx.py:797
#, python-format
msgid "Failed to read intersphinx_mapping[%s], ignored: %r"
msgstr "Échec de la lecture de intersphinx_mapping[%s]; ignoré : %r"

#: sphinx/ext/linkcode.py:69 sphinx/ext/viewcode.py:199
msgid "[source]"
msgstr "[source]"

#: sphinx/ext/todo.py:69
msgid "Todo"
msgstr "À faire"

#: sphinx/ext/todo.py:102
#, python-format
msgid "TODO entry found: %s"
msgstr "Entrée TODO trouvée : %s"

#: sphinx/ext/todo.py:161
msgid "<<original entry>>"
msgstr "<<entrée originale>>"

#: sphinx/ext/todo.py:163
#, python-format
msgid "(The <<original entry>> is located in %s, line %d.)"
msgstr "(l'<<entrée originale>> se trouve dans %s, à la ligne %d)"

#: sphinx/ext/todo.py:173
msgid "original entry"
msgstr "entrée originale"

#: sphinx/ext/viewcode.py:256
msgid "highlighting module code... "
msgstr "Coloration syntaxique du code du module..."

#: sphinx/ext/viewcode.py:284
msgid "[docs]"
msgstr "[docs]"

#: sphinx/ext/viewcode.py:304
msgid "Module code"
msgstr "Code du module"

#: sphinx/ext/viewcode.py:310
#, python-format
msgid "<h1>Source code for %s</h1>"
msgstr "<h1>Code source de %s</h1>"

#: sphinx/ext/viewcode.py:336
msgid "Overview: module code"
msgstr "Vue d'ensemble : code du module"

#: sphinx/ext/viewcode.py:337
msgid "<h1>All modules for which code is available</h1>"
msgstr "<h1>Modules pour lesquels le code est disponible</h1>"

#: sphinx/ext/autodoc/__init__.py:135
#, python-format
msgid "invalid value for member-order option: %s"
msgstr "valeur invalide pour l'option member-order : %s"

#: sphinx/ext/autodoc/__init__.py:143
#, python-format
msgid "invalid value for class-doc-from option: %s"
msgstr "valeur invalide pour l'option class-doc-from : %s"

#: sphinx/ext/autodoc/__init__.py:399
#, python-format
msgid "invalid signature for auto%s (%r)"
msgstr "signature invalide pour auto%s (%r)"

#: sphinx/ext/autodoc/__init__.py:515
#, python-format
msgid "error while formatting arguments for %s: %s"
msgstr "erreur pendant la mise en forme de l'argument %s:%s"

#: sphinx/ext/autodoc/__init__.py:798
#, python-format
msgid ""
"autodoc: failed to determine %s.%s (%r) to be documented, the following exception was raised:\n"
"%s"
msgstr "autodoc : n'a pas réussi à déterminer %s.%s (%r) devait être documenté, l'exception suivante a été levée :\n%s"

#: sphinx/ext/autodoc/__init__.py:893
#, python-format
msgid ""
"don't know which module to import for autodocumenting %r (try placing a "
"\"module\" or \"currentmodule\" directive in the document, or giving an "
"explicit module name)"
msgstr "module à importer pour auto-documenter %r est inconnu (essayer de placer une directive \"module\" ou \"currentmodule\" dans le document, ou de donner un nom de module explicite)"

#: sphinx/ext/autodoc/__init__.py:937
#, python-format
msgid "A mocked object is detected: %r"
msgstr "Un faux objet a été détecté : %r"

#: sphinx/ext/autodoc/__init__.py:956
#, python-format
msgid "error while formatting signature for %s: %s"
msgstr "erreur lors du formatage de la signature pour %s : %s"

#: sphinx/ext/autodoc/__init__.py:1019
msgid "\"::\" in automodule name doesn't make sense"
msgstr "\"::\" dans le nom d'automodule n'a pas de sens"

#: sphinx/ext/autodoc/__init__.py:1026
#, python-format
msgid "signature arguments or return annotation given for automodule %s"
msgstr "arguments de signature ou annotation de return donnés pour l’automodule %s"

#: sphinx/ext/autodoc/__init__.py:1039
#, python-format
msgid ""
"__all__ should be a list of strings, not %r (in module %s) -- ignoring "
"__all__"
msgstr "__all__ devrait être une liste de chaînes, pas %r (dans module %s) -- __all__ sera ignoré"

#: sphinx/ext/autodoc/__init__.py:1105
#, python-format
msgid ""
"missing attribute mentioned in :members: option: module %s, attribute %s"
msgstr "attribut manquant mentionné dans l'option :members: : module %s, attribut %s"

#: sphinx/ext/autodoc/__init__.py:1327 sphinx/ext/autodoc/__init__.py:1404
#: sphinx/ext/autodoc/__init__.py:2824
#, python-format
msgid "Failed to get a function signature for %s: %s"
msgstr "Échec pour obtenir la signature de la fonction pour %s : %s"

#: sphinx/ext/autodoc/__init__.py:1618
#, python-format
msgid "Failed to get a constructor signature for %s: %s"
msgstr "Échec pour obtenir la signature du constructeur pour %s : %s"

#: sphinx/ext/autodoc/__init__.py:1745
#, python-format
msgid "Bases: %s"
msgstr "Bases : %s"

#: sphinx/ext/autodoc/__init__.py:1759
#, python-format
msgid "missing attribute %s in object %s"
msgstr "attribut manquant %s dans l'objet %s"

#: sphinx/ext/autodoc/__init__.py:1858 sphinx/ext/autodoc/__init__.py:1895
#: sphinx/ext/autodoc/__init__.py:1990
#, python-format
msgid "alias of %s"
msgstr "alias de %s"

#: sphinx/ext/autodoc/__init__.py:1878
#, python-format
msgid "alias of TypeVar(%s)"
msgstr "alias de TypeVar(%s)"

#: sphinx/ext/autodoc/__init__.py:2216 sphinx/ext/autodoc/__init__.py:2316
#, python-format
msgid "Failed to get a method signature for %s: %s"
msgstr "Échec pour obtenir la signature de la méthode pour %s : %s"

#: sphinx/ext/autodoc/__init__.py:2447
#, python-format
msgid "Invalid __slots__ found on %s. Ignored."
msgstr "Invalide __slots__ trouvé sur %s. Ignoré."

#: sphinx/ext/autodoc/preserve_defaults.py:190
#, python-format
msgid "Failed to parse a default argument value for %r: %s"
msgstr "Impossible d'analyser une valeur d'argument par défaut pour %r : %s"

#: sphinx/ext/autodoc/type_comment.py:132
#, python-format
msgid "Failed to update signature for %r: parameter not found: %s"
msgstr "Échec de la mise à jour de la signature pour %r : paramètre non trouvé : %s"

#: sphinx/ext/autodoc/type_comment.py:135
#, python-format
msgid "Failed to parse type_comment for %r: %s"
msgstr "Échec de l'analyse de type_comment pour %r : %s"

#: sphinx/ext/autosummary/__init__.py:251
#, python-format
msgid "autosummary references excluded document %r. Ignored."
msgstr "autosummary fait référence au document exclu %r. Ignoré"

#: sphinx/ext/autosummary/__init__.py:253
#, python-format
msgid ""
"autosummary: stub file not found %r. Check your autosummary_generate "
"setting."
msgstr "autosummary : fichier stub non trouvé %r. Vérifiez votre paramètre autosummary_generate."

#: sphinx/ext/autosummary/__init__.py:272
msgid "A captioned autosummary requires :toctree: option. ignored."
msgstr "Un résumé automatique sous-titré nécessite l'option :toctree:. Ignoré."

#: sphinx/ext/autosummary/__init__.py:325
#, python-format
msgid ""
"autosummary: failed to import %s.\n"
"Possible hints:\n"
"%s"
msgstr "autosummary : échec de l'importation de %s.\nIndications possibles :\n%s"

#: sphinx/ext/autosummary/__init__.py:339
#, python-format
msgid "failed to parse name %s"
msgstr "échec de l’analyse du nom %s"

#: sphinx/ext/autosummary/__init__.py:344
#, python-format
msgid "failed to import object %s"
msgstr "échec d’importation de l'object %s"

#: sphinx/ext/autosummary/__init__.py:802
#, python-format
msgid "autosummary_generate: file not found: %s"
msgstr "autosummary_generate : fichier nontrouvé : %s"

#: sphinx/ext/autosummary/__init__.py:810
msgid ""
"autosummary generates .rst files internally. But your source_suffix does not"
" contain .rst. Skipped."
msgstr ""

#: sphinx/ext/autosummary/generate.py:200
#: sphinx/ext/autosummary/generate.py:358
#, python-format
msgid ""
"autosummary: failed to determine %r to be documented, the following exception was raised:\n"
"%s"
msgstr "autosummary : impossible de déterminer si %r est documenté; l'exception suivante a été levée :\n%s"

#: sphinx/ext/autosummary/generate.py:470
#, python-format
msgid "[autosummary] generating autosummary for: %s"
msgstr "[autosummary] engendrement d’un auto-sommaire pour : %s"

#: sphinx/ext/autosummary/generate.py:474
#, python-format
msgid "[autosummary] writing to %s"
msgstr "[autosummary] écriture dans %s"

#: sphinx/ext/autosummary/generate.py:517
#, python-format
msgid ""
"[autosummary] failed to import %s.\n"
"Possible hints:\n"
"%s"
msgstr "[autosummary] échec de l'importation de %s.\nIndications possibles :\n%s"

#: sphinx/ext/autosummary/generate.py:690
msgid ""
"\n"
"Generate ReStructuredText using autosummary directives.\n"
"\n"
"sphinx-autogen is a frontend to sphinx.ext.autosummary.generate. It generates\n"
"the reStructuredText files from the autosummary directives contained in the\n"
"given input files.\n"
"\n"
"The format of the autosummary directive is documented in the\n"
"``sphinx.ext.autosummary`` Python module and can be read using::\n"
"\n"
"  pydoc sphinx.ext.autosummary\n"
msgstr "\nEngendre du ReStructuredText par les directives autosummary.\n\nsphinx-autogen est une interface à sphinx.ext.autosummary.generate. Il\nengendre les fichiers reStructuredText à partir des directives autosummary\ncontenues dans les fichiers donnés en entrée.\n\nLe format de la directive autosummary est documentée dans le module\nPython \"sphinx.ext.autosummary\" et peut être lu via : ::\n\npydoc sphinx.ext.autosummary\n"

#: sphinx/ext/autosummary/generate.py:707
msgid "source files to generate rST files for"
msgstr "fichiers sources pour lesquels il faut produire des fichiers rST"

#: sphinx/ext/autosummary/generate.py:711
msgid "directory to place all output in"
msgstr "répertoire où placer toutes les sorties"

#: sphinx/ext/autosummary/generate.py:714
#, python-format
msgid "default suffix for files (default: %(default)s)"
msgstr "extension par défaut pour les fichiers (par défaut : %(default)s)"

#: sphinx/ext/autosummary/generate.py:718
#, python-format
msgid "custom template directory (default: %(default)s)"
msgstr "répertoire des templates spécifiques (par défaut : %(default)s)"

#: sphinx/ext/autosummary/generate.py:722
#, python-format
msgid "document imported members (default: %(default)s)"
msgstr "membres importés du document (défaut : %(default)s)"

#: sphinx/ext/autosummary/generate.py:726
#, python-format
msgid ""
"document exactly the members in module __all__ attribute. (default: "
"%(default)s)"
msgstr "documenter exactement les membres dans l'attribut __all__ du module. (par défaut : %(default)s)"

#: sphinx/ext/napoleon/__init__.py:341 sphinx/ext/napoleon/docstring.py:728
msgid "Keyword Arguments"
msgstr "Arguments de mots-clés"

#: sphinx/ext/napoleon/docstring.py:682
msgid "Example"
msgstr "Exemple"

#: sphinx/ext/napoleon/docstring.py:683
msgid "Examples"
msgstr "Exemples"

#: sphinx/ext/napoleon/docstring.py:744
msgid "Notes"
msgstr "Notes"

#: sphinx/ext/napoleon/docstring.py:753
msgid "Other Parameters"
msgstr "Autres paramètres"

#: sphinx/ext/napoleon/docstring.py:789
msgid "Receives"
msgstr "Reçoit"

#: sphinx/ext/napoleon/docstring.py:793
msgid "References"
msgstr "Références"

#: sphinx/ext/napoleon/docstring.py:825
msgid "Warns"
msgstr "Avertissements"

#: sphinx/ext/napoleon/docstring.py:829
msgid "Yields"
msgstr "Yields"

#: sphinx/ext/napoleon/docstring.py:987
#, python-format
msgid "invalid value set (missing closing brace): %s"
msgstr "ensemble invalide de valeurs (accolade fermante manquante) : %s"

#: sphinx/ext/napoleon/docstring.py:994
#, python-format
msgid "invalid value set (missing opening brace): %s"
msgstr "ensemble invalide de valeurs  (accolade ouvrante manquante) :%s"

#: sphinx/ext/napoleon/docstring.py:1001
#, python-format
msgid "malformed string literal (missing closing quote): %s"
msgstr "chaîne littérale malformée (guillemet fermant manquant) : %s"

#: sphinx/ext/napoleon/docstring.py:1008
#, python-format
msgid "malformed string literal (missing opening quote): %s"
msgstr "chaîne littérale malformée (guillemet ouvrant manquant) : %s"

#: sphinx/locale/__init__.py:228
msgid "Attention"
msgstr "Attention"

#: sphinx/locale/__init__.py:229
msgid "Caution"
msgstr "Prudence"

#: sphinx/locale/__init__.py:230
msgid "Danger"
msgstr "Danger"

#: sphinx/locale/__init__.py:231
msgid "Error"
msgstr "Erreur"

#: sphinx/locale/__init__.py:232
msgid "Hint"
msgstr "Indication"

#: sphinx/locale/__init__.py:233
msgid "Important"
msgstr "Important"

#: sphinx/locale/__init__.py:234
msgid "Note"
msgstr "Note"

#: sphinx/locale/__init__.py:235
msgid "See also"
msgstr "Voir aussi"

#: sphinx/locale/__init__.py:236
msgid "Tip"
msgstr "Astuce"

#: sphinx/locale/__init__.py:237
msgid "Warning"
msgstr "Avertissement"

#: sphinx/templates/latex/longtable.tex_t:52
#: sphinx/templates/latex/sphinxmessages.sty_t:8
msgid "continued from previous page"
msgstr "suite de la page précédente"

#: sphinx/templates/latex/longtable.tex_t:63
#: sphinx/templates/latex/sphinxmessages.sty_t:9
msgid "continues on next page"
msgstr "suite sur la page suivante"

#: sphinx/templates/latex/sphinxmessages.sty_t:10
msgid "Non-alphabetical"
msgstr "Non alphabétique"

#: sphinx/templates/latex/sphinxmessages.sty_t:12
msgid "Numbers"
msgstr "Chiffres"

#: sphinx/templates/latex/sphinxmessages.sty_t:13
msgid "page"
msgstr "page"

#: sphinx/themes/agogo/layout.html:38 sphinx/themes/basic/globaltoc.html:10
#: sphinx/themes/basic/localtoc.html:12 sphinx/themes/scrolls/layout.html:41
msgid "Table of Contents"
msgstr "Table des matières"

#: sphinx/themes/agogo/layout.html:43 sphinx/themes/basic/layout.html:141
#: sphinx/themes/basic/search.html:11 sphinx/themes/basic/search.html:23
msgid "Search"
msgstr "Recherche"

#: sphinx/themes/agogo/layout.html:46 sphinx/themes/basic/searchbox.html:16
#: sphinx/themes/basic/searchfield.html:18
msgid "Go"
msgstr "Go"

#: sphinx/themes/agogo/layout.html:90 sphinx/themes/basic/sourcelink.html:15
msgid "Show Source"
msgstr "Montrer le code source"

#: sphinx/themes/basic/defindex.html:11
msgid "Overview"
msgstr "Résumé"

#: sphinx/themes/basic/defindex.html:15
msgid "Welcome! This is"
msgstr "Bienvenue ! Ceci est"

#: sphinx/themes/basic/defindex.html:16
msgid "the documentation for"
msgstr "la documentation pour"

#: sphinx/themes/basic/defindex.html:17
msgid "last updated"
msgstr "dernière modification"

#: sphinx/themes/basic/defindex.html:20
msgid "Indices and tables:"
msgstr "Index et tables :"

#: sphinx/themes/basic/defindex.html:23
msgid "Complete Table of Contents"
msgstr "Table des matières complète"

#: sphinx/themes/basic/defindex.html:24
msgid "lists all sections and subsections"
msgstr "lister l'ensemble des sections et sous-sections"

#: sphinx/themes/basic/defindex.html:26
msgid "search this documentation"
msgstr "rechercher dans cette documentation"

#: sphinx/themes/basic/defindex.html:28
msgid "Global Module Index"
msgstr "Index général des modules"

#: sphinx/themes/basic/defindex.html:29
msgid "quick access to all modules"
msgstr "accès rapide à l'ensemble des modules"

#: sphinx/themes/basic/defindex.html:31
msgid "all functions, classes, terms"
msgstr "toutes les fonctions, classes, termes"

#: sphinx/themes/basic/genindex-single.html:33
#, python-format
msgid "Index &ndash; %(key)s"
msgstr "Index &ndash; %(key)s"

#: sphinx/themes/basic/genindex-single.html:61
#: sphinx/themes/basic/genindex-split.html:24
#: sphinx/themes/basic/genindex-split.html:38
#: sphinx/themes/basic/genindex.html:73
msgid "Full index on one page"
msgstr "Index complet sur une seule page"

#: sphinx/themes/basic/genindex-split.html:16
msgid "Index pages by letter"
msgstr "Indexer les pages par lettre"

#: sphinx/themes/basic/genindex-split.html:25
msgid "can be huge"
msgstr "peut être énorme"

#: sphinx/themes/basic/layout.html:26
msgid "Navigation"
msgstr "Navigation"

#: sphinx/themes/basic/layout.html:126
#, python-format
msgid "Search within %(docstitle)s"
msgstr "Recherchez dans %(docstitle)s"

#: sphinx/themes/basic/layout.html:135
msgid "About these documents"
msgstr "À propos de ces documents"

#: sphinx/themes/basic/layout.html:144 sphinx/themes/basic/layout.html:188
#: sphinx/themes/basic/layout.html:190
msgid "Copyright"
msgstr "Copyright"

#: sphinx/themes/basic/layout.html:194 sphinx/themes/basic/layout.html:200
#, python-format
msgid "&#169; %(copyright_prefix)s %(copyright)s."
msgstr "&#169; %(copyright_prefix)s %(copyright)s."

#: sphinx/themes/basic/layout.html:212
#, python-format
msgid "Last updated on %(last_updated)s."
msgstr "Mis à jour le %(last_updated)s."

#: sphinx/themes/basic/layout.html:215
#, python-format
msgid ""
"Created using <a href=\"https://www.sphinx-doc.org/\">Sphinx</a> "
"%(sphinx_version)s."
msgstr "Créé en utilisant <a href=\"https://www.sphinx-doc.org/\">Sphinx</a> %(sphinx_version)s."

#: sphinx/themes/basic/opensearch.xml:4
#, python-format
msgid "Search %(docstitle)s"
msgstr "Rechercher %(docstitle)s"

#: sphinx/themes/basic/relations.html:12
msgid "Previous topic"
msgstr "Sujet précédent"

#: sphinx/themes/basic/relations.html:14
msgid "previous chapter"
msgstr "Chapitre précédent"

#: sphinx/themes/basic/relations.html:19
msgid "Next topic"
msgstr "Sujet suivant"

#: sphinx/themes/basic/relations.html:21
msgid "next chapter"
msgstr "Chapitre suivant"

#: sphinx/themes/basic/search.html:28
msgid ""
"Please activate JavaScript to enable the search\n"
"    functionality."
msgstr "Veuillez activer le JavaScript pour que la recherche fonctionne."

#: sphinx/themes/basic/search.html:36
msgid ""
"Searching for multiple words only shows matches that contain\n"
"    all words."
msgstr "Une recherche sur plusieurs mots ne retourne que les résultats contenant tous les mots."

#: sphinx/themes/basic/search.html:43
msgid "search"
msgstr "rechercher"

#: sphinx/themes/basic/searchbox.html:12
msgid "Quick search"
msgstr "Recherche rapide"

#: sphinx/themes/basic/sourcelink.html:12
msgid "This Page"
msgstr "Cette page"

#: sphinx/themes/basic/changes/frameset.html:5
#: sphinx/themes/basic/changes/versionchanges.html:12
#, python-format
msgid "Changes in Version %(version)s &#8212; %(docstitle)s"
msgstr "Changements dans la version %(version)s &#8212; %(docstitle)s"

#: sphinx/themes/basic/changes/rstsource.html:5
#, python-format
msgid "%(filename)s &#8212; %(docstitle)s"
msgstr "%(filename)s &#8212; %(docstitle)s"

#: sphinx/themes/basic/changes/versionchanges.html:17
#, python-format
msgid "Automatically generated list of changes in version %(version)s"
msgstr "Liste auto-générée des modifications dans la version %(version)s"

#: sphinx/themes/basic/changes/versionchanges.html:18
msgid "Library changes"
msgstr "Modifications de la bibliothèque"

#: sphinx/themes/basic/changes/versionchanges.html:23
msgid "C API changes"
msgstr "Modifications de l'API C"

#: sphinx/themes/basic/changes/versionchanges.html:25
msgid "Other changes"
msgstr "Autres modifications"

#: sphinx/themes/basic/static/searchtools.js:112
msgid "Search Results"
msgstr "Résultats de la recherche"

#: sphinx/themes/basic/static/searchtools.js:114
msgid ""
"Your search did not match any documents. Please make sure that all words are"
" spelled correctly and that you've selected enough categories."
msgstr "Votre recherche ne correspond à aucun document. Veuillez vérifier que les mots sont correctement orthographiés et que vous avez sélectionné assez de catégories."

#: sphinx/themes/basic/static/searchtools.js:118
msgid ""
"Search finished, found ${resultCount} page(s) matching the search query."
msgstr "Recherche terminée, ${resultCount} page(s) correspondant à la requête de recherche ont été trouvées."

#: sphinx/themes/basic/static/searchtools.js:246
msgid "Searching"
msgstr "Recherche en cours"

#: sphinx/themes/basic/static/searchtools.js:262
msgid "Preparing search..."
msgstr "Préparation de la recherche..."

#: sphinx/themes/basic/static/searchtools.js:463
msgid ", in "
msgstr ", dans "

#: sphinx/themes/basic/static/sphinx_highlight.js:112
msgid "Hide Search Matches"
msgstr "Cacher les résultats de la recherche"

#: sphinx/themes/classic/layout.html:20
#: sphinx/themes/classic/static/sidebar.js_t:57
msgid "Collapse sidebar"
msgstr "Réduire la barre latérale"

#: sphinx/themes/classic/static/sidebar.js_t:48
msgid "Expand sidebar"
msgstr "Agrandir la barre latérale"

#: sphinx/themes/haiku/layout.html:24
msgid "Contents"
msgstr "Contenu"

#: sphinx/transforms/__init__.py:128
msgid "could not calculate translation progress!"
msgstr "impossible de calculer l'avancement de la traduction !"

#: sphinx/transforms/__init__.py:133
msgid "no translated elements!"
msgstr "pas d'éléments traduits !"

#: sphinx/transforms/__init__.py:250
#, python-format
msgid ""
"4 column based index found. It might be a bug of extensions you use: %r"
msgstr "index trouvé avec style ancien à 4 colonnes. Possiblement un bogue d’extensions que vous utilisez : %r"

#: sphinx/transforms/__init__.py:291
#, python-format
msgid "Footnote [%s] is not referenced."
msgstr "La note de bas de page [%s] n'est pas référencée."

#: sphinx/transforms/__init__.py:297
msgid "Footnote [#] is not referenced."
msgstr "La note de bas de page [#] n'est pas référencée."

#: sphinx/transforms/i18n.py:205 sphinx/transforms/i18n.py:270
msgid ""
"inconsistent footnote references in translated message. original: {0}, "
"translated: {1}"
msgstr "incohérences de références de notes de bas de page dans le message traduit. Original : {0}, traduit : {1} "

#: sphinx/transforms/i18n.py:245
msgid ""
"inconsistent references in translated message. original: {0}, translated: "
"{1}"
msgstr "incohérences de références dans le message traduit. Original : {0}, traduit : {1}"

#: sphinx/transforms/i18n.py:285
msgid ""
"inconsistent citation references in translated message. original: {0}, "
"translated: {1}"
msgstr "incohérences de références de citation dans le message traduit. Original : {0}, traduit : {1}"

#: sphinx/transforms/i18n.py:302
msgid ""
"inconsistent term references in translated message. original: {0}, "
"translated: {1}"
msgstr "incohérences de références de terme dans le message traduit. Original : {0}, traduit : {1}"

#: sphinx/transforms/post_transforms/__init__.py:116
msgid ""
"Could not determine the fallback text for the cross-reference. Might be a "
"bug."
msgstr "Impossible de déterminer le texte de remplacement pour le renvoi. Il peut s'agir d'un bogue."

#: sphinx/transforms/post_transforms/__init__.py:158
#, python-format
msgid "more than one target found for 'any' cross-reference %r: could be %s"
msgstr "plus d'une cible trouvée pour la référence %r de type 'any' : pourrait être %s"

#: sphinx/transforms/post_transforms/__init__.py:209
#, python-format
msgid "%s:%s reference target not found: %s"
msgstr "%s:%s cible de référence non trouvée : %s"

#: sphinx/transforms/post_transforms/__init__.py:212
#, python-format
msgid "%r reference target not found: %s"
msgstr "%r cible de référence non trouvée : %s"

#: sphinx/transforms/post_transforms/images.py:89
#, python-format
msgid "Could not fetch remote image: %s [%d]"
msgstr "impossible d'atteindre l'image distante %s[%d]"

#: sphinx/transforms/post_transforms/images.py:117
#, python-format
msgid "Could not fetch remote image: %s [%s]"
msgstr "impossible d'atteindre l'image distante %s[%s]"

#: sphinx/transforms/post_transforms/images.py:135
#, python-format
msgid "Unknown image format: %s..."
msgstr "Format d'image inconnu : %s..."

#: sphinx/util/__init__.py:168
#, python-format
msgid "undecodable source characters, replacing with \"?\": %r"
msgstr "le caractère source est indécodable, il sera remplacé par \"?\" : %r"

#: sphinx/util/display.py:78
msgid "skipped"
msgstr "ignoré"

#: sphinx/util/display.py:83
msgid "failed"
msgstr "échoué"

#: sphinx/util/docfields.py:87
#, python-format
msgid ""
"Problem in %s domain: field is supposed to use role '%s', but that role is "
"not in the domain."
msgstr "Problème dans le domaine %s : le champ est censé utiliser le rôle '%s', mais ce rôle ne figure pas dans le domaine."

#: sphinx/util/docutils.py:295
#, python-format
msgid "unknown directive or role name: %s:%s"
msgstr "nom de rôle ou de directive inconnu: %s:%s"

#: sphinx/util/docutils.py:591
#, python-format
msgid "unknown node type: %r"
msgstr "type de node inconnu : %r"

#: sphinx/util/i18n.py:94
#, python-format
msgid "reading error: %s, %s"
msgstr "erreur de lecture : %s,%s"

#: sphinx/util/i18n.py:101
#, python-format
msgid "writing error: %s, %s"
msgstr "erreur d'écriture : %s,%s"

#: sphinx/util/i18n.py:125
#, python-format
msgid "locale_dir %s does not exist"
msgstr "locale_dir %s n'existe pas"

#: sphinx/util/i18n.py:215
#, python-format
msgid ""
"Invalid date format. Quote the string by single quote if you want to output "
"it directly: %s"
msgstr "Format de date invalide. Insérez la chaîne de caractères entre des guillemets simples si vous voulez l'afficher telle quelle : %s"

#: sphinx/util/nodes.py:386
#, python-format
msgid ""
"%r is deprecated for index entries (from entry %r). Use 'pair: %s' instead."
msgstr "%r est obsolète pour les entrées d'index (à partir de l'entrée %r). Utilisez plutôt 'pair:%s'."

#: sphinx/util/nodes.py:439
#, python-format
msgid "toctree contains ref to nonexisting file %r"
msgstr "la table des matières contient des références à des fichiers inexistants %r"

#: sphinx/util/nodes.py:637
#, python-format
msgid "exception while evaluating only directive expression: %s"
msgstr "exception pendant l’évaluation de l'expression de la directive only : %s"

#: sphinx/util/rst.py:71
#, python-format
msgid "default role %s not found"
msgstr "rôle par défaut %s introuvable"

#: sphinx/writers/html5.py:100 sphinx/writers/html5.py:109
msgid "Link to this definition"
msgstr "Lien vers cette définition"

#: sphinx/writers/html5.py:397
#, python-format
msgid "numfig_format is not defined for %s"
msgstr "numfig_format n'est pas défini %s"

#: sphinx/writers/html5.py:407
#, python-format
msgid "Any IDs not assigned for %s node"
msgstr "Aucun ID assigné au node %s"

#: sphinx/writers/html5.py:462
msgid "Link to this term"
msgstr "Lien vers ce terme"

#: sphinx/writers/html5.py:496 sphinx/writers/html5.py:501
msgid "Link to this heading"
msgstr "Lien vers cette rubrique"

#: sphinx/writers/html5.py:505
msgid "Link to this table"
msgstr "Lien vers ce tableau"

#: sphinx/writers/html5.py:548
msgid "Link to this code"
msgstr "Lien vers ce code"

#: sphinx/writers/html5.py:550
msgid "Link to this image"
msgstr "Lien vers cette image"

#: sphinx/writers/html5.py:552
msgid "Link to this toctree"
msgstr "Lien vers cette table des matières"

#: sphinx/writers/html5.py:688
msgid "Could not obtain image size. :scale: option is ignored."
msgstr "impossible d'obtenir la taille de l'image. L'option :scale: est ignorée."

#: sphinx/writers/latex.py:335
#, python-format
msgid "unknown %r toplevel_sectioning for class %r"
msgstr "toplevel_sectioning %r inconnu pour la classe %r"

#: sphinx/writers/latex.py:386
msgid "too large :maxdepth:, ignored."
msgstr ":maxdepth: trop grand, ignoré."

#: sphinx/writers/latex.py:625
msgid "document title is not a single Text node"
msgstr "le titre du document n'est pas un unique node de type Text"

#: sphinx/writers/latex.py:656 sphinx/writers/texinfo.py:626
msgid ""
"encountered title node not in section, topic, table, admonition or sidebar"
msgstr "le titre de node rencontré n'est apparenté à aucun parmi section, topic, table, admonition ou sidebar"

#: sphinx/writers/latex.py:959 sphinx/writers/manpage.py:259
#: sphinx/writers/texinfo.py:641
msgid "Footnotes"
msgstr "Notes de bas de page"

#: sphinx/writers/latex.py:1028
msgid ""
"both tabularcolumns and :widths: option are given. :widths: is ignored."
msgstr "options tabularcolumns et :widths: simultanément présentes. :widths: sera ignoré."

#: sphinx/writers/latex.py:1388
#, python-format
msgid "dimension unit %s is invalid. Ignored."
msgstr "%s est invalide comme unité de dimension. Ignoré."

#: sphinx/writers/latex.py:1722
#, python-format
msgid "unknown index entry type %s found"
msgstr "le type inconnu d’entrée d’index %s a été trouvé"

#: sphinx/writers/manpage.py:305 sphinx/writers/text.py:907
#, python-format
msgid "[image: %s]"
msgstr "[image: %s]"

#: sphinx/writers/manpage.py:306 sphinx/writers/text.py:908
msgid "[image]"
msgstr "[image]"

#: sphinx/writers/texinfo.py:1197
msgid "caption not inside a figure."
msgstr "la légende n'est pas à l'intérieur de la figure."

#: sphinx/writers/texinfo.py:1284
#, python-format
msgid "unimplemented node type: %r"
msgstr "type de node non-implémenté : %r"
