#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import logging
import signal
import sys
import threading
import time
import traceback

# 配置日志 - 增加更详细的日志
logging.basicConfig(
    level=logging.DEBUG,  # 改为DEBUG级别
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('data_transmission_debug.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)

class DataTransmissionClient:
    def __init__(self):
        self.db_manager = None
        self.web_server = None
        self.qr_generator = None
        self.camera_monitor = None
        self.web_thread = None
        self.is_running = False
    
    def initialize(self):
        """初始化所有组件"""
        try:
            logging.info("正在初始化数据传输客户端...")
            
            # 步骤1: 检查配置文件
            logging.info("步骤1: 检查配置文件...")
            try:
                import config
                logging.info("配置文件导入成功")
                if hasattr(config, 'DATABASE_CONFIG'):
                    logging.info(f"数据库配置: {config.DATABASE_CONFIG}")
                else:
                    logging.error("配置文件中缺少DATABASE_CONFIG")
                    return False
            except Exception as e:
                logging.error(f"配置文件导入失败: {e}")
                traceback.print_exc()
                return False
            
            # 步骤2: 初始化数据库管理器
            logging.info("步骤2: 初始化数据库管理器...")
            try:
                from database import DatabaseManager
                self.db_manager = DatabaseManager()
                logging.info("数据库管理器初始化完成")
            except Exception as e:
                logging.error(f"数据库管理器初始化失败: {e}")
                traceback.print_exc()
                return False
            
            # 步骤3: 初始化Web服务器
            logging.info("步骤3: 初始化Web服务器...")
            try:
                from web_server import WebServer
                self.web_server = WebServer(self.db_manager)
                logging.info("Web服务器初始化完成")
            except Exception as e:
                logging.error(f"Web服务器初始化失败: {e}")
                traceback.print_exc()
                return False
            
            # 步骤4: 初始化二维码生成器
            logging.info("步骤4: 初始化二维码生成器...")
            try:
                from qr_generator import QRGenerator
                self.qr_generator = QRGenerator(self.db_manager)
                logging.info("二维码生成器初始化完成")
            except Exception as e:
                logging.error(f"二维码生成器初始化失败: {e}")
                traceback.print_exc()
                return False
            
            # 步骤5: 初始化摄像头监控
            logging.info("步骤5: 初始化摄像头监控...")
            try:
                from camera_monitor import CameraMonitor
                self.camera_monitor = CameraMonitor(self.db_manager)
                logging.info("摄像头监控初始化完成")
            except Exception as e:
                logging.error(f"摄像头监控初始化失败: {e}")
                traceback.print_exc()
                # 摄像头失败不应该导致程序退出
                logging.warning("摄像头监控初始化失败，但程序将继续运行")
            
            logging.info("所有组件初始化完成")
            return True
            
        except Exception as e:
            logging.error(f"初始化时出现未预期的错误: {e}")
            traceback.print_exc()
            return False
    
    def start_services(self):
        """启动所有服务"""
        try:
            logging.info("正在启动所有服务...")
            
            # 启动Web服务器
            if self.web_server:
                logging.info("启动Web服务器，监听 0.0.0.0:5000")
                self.web_thread = threading.Thread(target=self.web_server.run, daemon=True)
                self.web_thread.start()
                logging.info("Web服务器已启动")
            
            # 等待Web服务器启动
            time.sleep(2)
            
            # 启动二维码生成服务
            if self.qr_generator:
                self.qr_generator.start()
                logging.info("二维码生成服务已启动")
            
            # 启动摄像头监控服务
            if self.camera_monitor:
                self.camera_monitor.start()
                logging.info("摄像头监控服务已启动")
            
            logging.info("所有服务启动完成")
            return True
            
        except Exception as e:
            logging.error(f"启动服务时出错: {e}")
            traceback.print_exc()
            return False
    
    def run(self):
        """运行主程序"""
        try:
            # 初始化组件
            if not self.initialize():
                logging.error("初始化失败，程序退出")
                return False
            
            # 启动服务
            if not self.start_services():
                logging.error("启动服务失败，程序退出")
                return False
            
            self.is_running = True
            logging.info("数据传输客户端正在运行...")
            logging.info("按 Ctrl+C 停止程序")
            
            # 主循环
            while self.is_running:
                time.sleep(1)
            
            return True
            
        except KeyboardInterrupt:
            logging.info("接收到键盘中断信号，正在停止程序...")
            self.stop()
            return True
        except Exception as e:
            logging.error(f"程序运行时出错: {e}")
            traceback.print_exc()
            return False
    
    def stop(self):
        """停止程序"""
        logging.info("正在停止所有服务...")
        self.is_running = False
        
        if self.camera_monitor:
            self.camera_monitor.stop()
        
        if self.qr_generator:
            self.qr_generator.stop()
        
        logging.info("程序已停止")

def signal_handler(signum, frame):
    """信号处理器"""
    logging.info(f"接收到信号 {signum}，正在停止程序...")
    global client
    if client:
        client.stop()
    sys.exit(0)

def main():
    """主函数"""
    global client
    
    try:
        logging.info("DataTransmission 调试版本启动")
        logging.info(f"Python版本: {sys.version}")
        logging.info(f"当前工作目录: {sys.path}")
        
        # 注册信号处理器
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
        
        # 创建客户端实例
        client = DataTransmissionClient()
        
        # 运行程序
        success = client.run()
        
        if success:
            logging.info("程序正常退出")
        else:
            logging.error("程序异常退出")
            
    except Exception as e:
        logging.error(f"主函数运行时出错: {e}")
        traceback.print_exc()
    
    finally:
        logging.info("程序结束")
        # 在Windows环境下保持窗口打开
        if sys.platform.startswith('win'):
            input("按回车键退出...")

if __name__ == "__main__":
    client = None
    main()
