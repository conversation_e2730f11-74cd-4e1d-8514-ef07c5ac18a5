#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
增强版数据库连接测试 - 带超时控制
专门解决连接卡住的问题
"""

import sys
import socket
import threading
import time
import traceback
from concurrent.futures import ThreadPoolExecutor, TimeoutError as FutureTimeoutError

def test_tcp_connection_with_timeout(host, port, timeout=5):
    """测试TCP连接（带超时）"""
    print(f"测试TCP连接到 {host}:{port} (超时: {timeout}秒)")
    
    def connect_test():
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(timeout)
            result = sock.connect_ex((host, port))
            sock.close()
            return result
        except Exception as e:
            return str(e)
    
    try:
        with ThreadPoolExecutor(max_workers=1) as executor:
            future = executor.submit(connect_test)
            result = future.result(timeout=timeout + 1)
            
            if result == 0:
                print(f"✓ TCP连接成功: {host}:{port}")
                return True
            else:
                print(f"✗ TCP连接失败: {host}:{port} (错误代码: {result})")
                return False
                
    except FutureTimeoutError:
        print(f"✗ TCP连接超时: {host}:{port} (超过{timeout}秒)")
        return False
    except Exception as e:
        print(f"✗ TCP连接异常: {e}")
        return False

def test_mysql_service_status():
    """检查MySQL服务状态"""
    print("=" * 50)
    print("MySQL服务状态检查")
    print("=" * 50)
    
    import subprocess
    
    try:
        # 检查MySQL服务
        result = subprocess.run(['sc', 'query', 'mysql'], 
                              capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            print("✓ MySQL服务已安装")
            
            # 检查运行状态
            if 'RUNNING' in result.stdout:
                print("✓ MySQL服务正在运行")
                return True
            else:
                print("✗ MySQL服务未运行")
                print("尝试启动MySQL服务...")
                
                start_result = subprocess.run(['net', 'start', 'mysql'], 
                                            capture_output=True, text=True, timeout=30)
                if start_result.returncode == 0:
                    print("✓ MySQL服务启动成功")
                    return True
                else:
                    print(f"✗ MySQL服务启动失败: {start_result.stderr}")
                    return False
        else:
            print("✗ MySQL服务未安装")
            return False
            
    except subprocess.TimeoutExpired:
        print("✗ 检查MySQL服务超时")
        return False
    except Exception as e:
        print(f"✗ 检查MySQL服务异常: {e}")
        return False

def test_port_availability(port=3306):
    """检查端口可用性"""
    print(f"\n检查端口 {port} 状态...")
    
    import subprocess
    
    try:
        # 使用netstat检查端口
        result = subprocess.run(['netstat', '-ano'], 
                              capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            lines = result.stdout.split('\n')
            mysql_ports = [line for line in lines if f':{port}' in line and 'LISTENING' in line]
            
            if mysql_ports:
                print(f"✓ 端口 {port} 正在监听:")
                for line in mysql_ports[:3]:  # 只显示前3个
                    print(f"  {line.strip()}")
                return True
            else:
                print(f"✗ 端口 {port} 未在监听")
                return False
        else:
            print("⚠️ 无法检查端口状态")
            return None
            
    except subprocess.TimeoutExpired:
        print("✗ 检查端口状态超时")
        return False
    except Exception as e:
        print(f"✗ 检查端口状态异常: {e}")
        return False

def test_mysql_connection_with_timeout(db_config, timeout=10):
    """测试MySQL连接（带超时）"""
    print(f"\n测试MySQL连接 (超时: {timeout}秒)")
    print("-" * 30)
    
    def mysql_connect_test():
        try:
            import mysql.connector
            from mysql.connector import Error
            
            # 添加连接超时设置
            config_with_timeout = db_config.copy()
            config_with_timeout['connection_timeout'] = timeout
            config_with_timeout['autocommit'] = True
            
            print(f"尝试连接: {db_config['user']}@{db_config['host']}:{db_config['port']}")
            
            connection = mysql.connector.connect(**config_with_timeout)
            
            if connection.is_connected():
                print("✓ MySQL连接成功")
                
                cursor = connection.cursor()
                cursor.execute("SELECT VERSION()")
                version = cursor.fetchone()
                print(f"✓ MySQL版本: {version[0]}")
                
                cursor.close()
                connection.close()
                return True
            else:
                print("✗ MySQL连接失败")
                return False
                
        except Exception as e:
            return str(e)
    
    try:
        with ThreadPoolExecutor(max_workers=1) as executor:
            future = executor.submit(mysql_connect_test)
            result = future.result(timeout=timeout + 2)
            
            if result is True:
                return True
            elif isinstance(result, str):
                print(f"✗ MySQL连接错误: {result}")
                return False
            else:
                print("✗ MySQL连接失败")
                return False
                
    except FutureTimeoutError:
        print(f"✗ MySQL连接超时 (超过{timeout}秒)")
        print("这通常表示:")
        print("  - MySQL服务未启动")
        print("  - 防火墙阻止连接")
        print("  - MySQL配置不正确")
        return False
    except Exception as e:
        print(f"✗ MySQL连接测试异常: {e}")
        return False

def diagnose_connection_issue(host, port):
    """诊断连接问题"""
    print("\n" + "=" * 50)
    print("连接问题诊断")
    print("=" * 50)
    
    # 1. 检查主机名解析
    try:
        import socket
        ip = socket.gethostbyname(host)
        print(f"✓ 主机名解析: {host} -> {ip}")
    except Exception as e:
        print(f"✗ 主机名解析失败: {e}")
        return
    
    # 2. 检查网络连通性（ping）
    import subprocess
    try:
        result = subprocess.run(['ping', '-n', '1', host], 
                              capture_output=True, text=True, timeout=5)
        if result.returncode == 0:
            print(f"✓ 网络连通性正常 (ping {host})")
        else:
            print(f"✗ 网络连通性异常 (ping {host})")
    except:
        print("⚠️ 无法测试网络连通性")
    
    # 3. 检查端口连接
    tcp_ok = test_tcp_connection_with_timeout(host, port, timeout=3)
    
    if not tcp_ok:
        print("\n💡 连接失败的可能原因:")
        print("1. MySQL服务未启动 - 运行: net start mysql")
        print("2. MySQL未监听3306端口 - 检查MySQL配置")
        print("3. 防火墙阻止连接 - 检查Windows防火墙设置")
        print("4. MySQL绑定到127.0.0.1 - 检查bind-address配置")

def main():
    """主函数"""
    print("=" * 60)
    print("增强版数据库连接测试 (带超时控制)")
    print("=" * 60)
    
    # 1. 检查MySQL服务
    service_ok = test_mysql_service_status()
    
    # 2. 检查端口状态
    port_ok = test_port_availability()
    
    # 3. 加载配置
    try:
        import config
        if hasattr(config, 'DATABASE_CONFIG'):
            db_config = config.DATABASE_CONFIG
            print(f"\n✓ 数据库配置加载成功")
            
            # 显示配置（隐藏密码）
            config_display = db_config.copy()
            config_display['password'] = '***' if db_config.get('password') else '未设置'
            print(f"配置: {config_display}")
        else:
            print("\n✗ 配置文件中缺少DATABASE_CONFIG")
            input("按回车键退出...")
            return
    except Exception as e:
        print(f"\n✗ 配置加载失败: {e}")
        input("按回车键退出...")
        return
    
    # 4. 诊断连接问题
    if not service_ok or not port_ok:
        diagnose_connection_issue(db_config['host'], db_config['port'])
    
    # 5. 测试TCP连接
    print(f"\n" + "=" * 50)
    print("TCP连接测试")
    print("=" * 50)
    tcp_ok = test_tcp_connection_with_timeout(db_config['host'], db_config['port'], timeout=5)
    
    # 6. 测试MySQL连接
    if tcp_ok:
        print(f"\n" + "=" * 50)
        print("MySQL连接测试")
        print("=" * 50)
        mysql_ok = test_mysql_connection_with_timeout(db_config, timeout=10)
        
        if mysql_ok:
            print("\n🎉 数据库连接测试成功！")
        else:
            print("\n❌ MySQL连接失败")
    else:
        print("\n❌ TCP连接失败，跳过MySQL连接测试")
        print("\n💡 解决建议:")
        print("1. 确保MySQL服务正在运行: net start mysql")
        print("2. 检查MySQL配置文件 my.ini")
        print("3. 检查防火墙设置")
        print("4. 重启MySQL服务: net stop mysql && net start mysql")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n用户中断测试")
    except Exception as e:
        print(f"\n测试过程中出现未预期的错误: {e}")
        traceback.print_exc()
        input("按回车键退出...")
