#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
包依赖问题修复脚本
专门解决PyInstaller打包时的包检查和依赖问题
"""

import sys
import subprocess
import importlib
import pkg_resources

def check_package_installation():
    """检查包安装情况"""
    print("=" * 60)
    print("包安装情况检查")
    print("=" * 60)
    
    # 包名映射：pip包名 -> 导入名
    package_mapping = {
        'mysql-connector-python': 'mysql.connector',
        'pillow': 'PIL',
        'opencv-python': 'cv2',
        'qrcode': 'qrcode',
        'pyzbar': 'pyzbar',
        'flask': 'flask',
        'apscheduler': 'apscheduler',
        'numpy': 'numpy',
        'requests': 'requests'
    }
    
    installed_packages = {}
    missing_packages = []
    
    # 检查已安装的包
    try:
        installed = [pkg.project_name.lower() for pkg in pkg_resources.working_set]
        print("已安装的相关包:")
        for pip_name, import_name in package_mapping.items():
            if pip_name.lower() in installed:
                try:
                    # 获取包信息
                    pkg = pkg_resources.get_distribution(pip_name)
                    installed_packages[pip_name] = pkg.version
                    print(f"✓ {pip_name} (版本: {pkg.version})")
                except:
                    print(f"⚠️  {pip_name} (已安装但无法获取版本)")
            else:
                missing_packages.append(pip_name)
                print(f"✗ {pip_name} (未安装)")
    
    except Exception as e:
        print(f"检查已安装包时出错: {e}")
    
    return installed_packages, missing_packages

def check_import_capability():
    """检查导入能力"""
    print("\n" + "=" * 60)
    print("包导入能力检查")
    print("=" * 60)
    
    import_tests = {
        'mysql.connector': 'MySQL Connector',
        'PIL': 'Pillow',
        'PIL.Image': 'Pillow Image',
        'cv2': 'OpenCV',
        'qrcode': 'QRCode',
        'pyzbar': 'PyZBar',
        'flask': 'Flask',
        'apscheduler': 'APScheduler',
        'numpy': 'NumPy',
        'requests': 'Requests'
    }
    
    import_results = {}
    
    for import_name, display_name in import_tests.items():
        try:
            module = importlib.import_module(import_name)
            version = getattr(module, '__version__', 'unknown')
            import_results[import_name] = True
            print(f"✓ {display_name} ({import_name}) - 版本: {version}")
        except ImportError as e:
            import_results[import_name] = False
            print(f"✗ {display_name} ({import_name}) - 错误: {e}")
    
    return import_results

def fix_mysql_connector():
    """修复MySQL Connector问题"""
    print("\n修复MySQL Connector...")
    try:
        # 卸载可能冲突的包
        subprocess.run([sys.executable, "-m", "pip", "uninstall", "mysql-connector", "-y"], 
                      capture_output=True)
        subprocess.run([sys.executable, "-m", "pip", "uninstall", "mysql-connector-python", "-y"], 
                      capture_output=True)
        
        # 重新安装
        result = subprocess.run([sys.executable, "-m", "pip", "install", "mysql-connector-python"], 
                               capture_output=True, text=True)
        if result.returncode == 0:
            print("✓ MySQL Connector 重新安装成功")
            return True
        else:
            print(f"✗ MySQL Connector 安装失败: {result.stderr}")
            return False
    except Exception as e:
        print(f"✗ 修复MySQL Connector时出错: {e}")
        return False

def fix_pillow():
    """修复Pillow问题"""
    print("\n修复Pillow...")
    try:
        # 卸载并重新安装
        subprocess.run([sys.executable, "-m", "pip", "uninstall", "pillow", "-y"], 
                      capture_output=True)
        
        result = subprocess.run([sys.executable, "-m", "pip", "install", "pillow"], 
                               capture_output=True, text=True)
        if result.returncode == 0:
            print("✓ Pillow 重新安装成功")
            return True
        else:
            print(f"✗ Pillow 安装失败: {result.stderr}")
            return False
    except Exception as e:
        print(f"✗ 修复Pillow时出错: {e}")
        return False

def install_missing_packages(missing_packages):
    """安装缺失的包"""
    if not missing_packages:
        return True
    
    print(f"\n安装缺失的包: {missing_packages}")
    try:
        for package in missing_packages:
            print(f"安装 {package}...")
            result = subprocess.run([sys.executable, "-m", "pip", "install", package], 
                                   capture_output=True, text=True)
            if result.returncode == 0:
                print(f"✓ {package} 安装成功")
            else:
                print(f"✗ {package} 安装失败: {result.stderr}")
                return False
        return True
    except Exception as e:
        print(f"安装包时出错: {e}")
        return False

def create_fixed_requirements():
    """创建修复后的requirements.txt"""
    print("\n创建修复后的requirements.txt...")
    
    fixed_requirements = [
        "Flask==2.3.3",
        "mysql-connector-python==8.1.0",
        "opencv-python==********",
        "qrcode[pil]==7.4.2",
        "pyzbar==0.1.9",
        "Pillow==10.0.1",
        "APScheduler==3.10.4",
        "numpy==1.24.3",
        "requests==2.31.0",
        "pyinstaller>=5.0.0"
    ]
    
    try:
        with open('requirements_fixed.txt', 'w', encoding='utf-8') as f:
            for req in fixed_requirements:
                f.write(req + '\n')
        
        print("✓ requirements_fixed.txt 已创建")
        print("可以使用: pip install -r requirements_fixed.txt")
        return True
    except Exception as e:
        print(f"创建requirements文件时出错: {e}")
        return False

def main():
    """主修复函数"""
    print("DataTransmission 包依赖问题修复工具")
    print("解决PyInstaller打包时的包检查和依赖问题")
    
    # 检查当前安装情况
    installed, missing = check_package_installation()
    import_results = check_import_capability()
    
    # 分析问题
    problems = []
    if not import_results.get('mysql.connector', False):
        problems.append('mysql-connector')
    if not import_results.get('PIL', False):
        problems.append('pillow')
    if missing:
        problems.extend(missing)
    
    if not problems:
        print("\n" + "=" * 60)
        print("✓ 所有包都正常，无需修复")
        print("=" * 60)
        return
    
    print(f"\n发现问题: {problems}")
    
    # 询问是否修复
    try:
        response = input("\n是否尝试自动修复这些问题? (y/N): ")
        if response.lower() != 'y':
            print("修复已取消")
            return
    except KeyboardInterrupt:
        print("\n修复已取消")
        return
    
    print("\n" + "=" * 60)
    print("开始自动修复")
    print("=" * 60)
    
    # 修复MySQL Connector
    if 'mysql-connector' in problems:
        fix_mysql_connector()
    
    # 修复Pillow
    if 'pillow' in problems:
        fix_pillow()
    
    # 安装缺失的包
    if missing:
        install_missing_packages(missing)
    
    # 创建修复后的requirements文件
    create_fixed_requirements()
    
    # 重新检查
    print("\n" + "=" * 60)
    print("修复后验证")
    print("=" * 60)
    
    check_import_capability()
    
    print("\n" + "=" * 60)
    print("修复完成")
    print("=" * 60)
    print("下一步:")
    print("1. 重新运行 build_windows_exe.py")
    print("2. 或使用: pip install -r requirements_fixed.txt")
    print("3. 如果仍有问题，请检查具体的错误信息")

if __name__ == "__main__":
    main()
