#!/bin/bash

# 数据传输客户端安装脚本
# 适用于Ubuntu 18.04+ 服务器部署

set -e  # 遇到错误立即退出

echo "=========================================="
echo "数据传输客户端服务器部署脚本"
echo "=========================================="

# 检查是否为root用户或有sudo权限
if [[ $EUID -eq 0 ]]; then
   echo "检测到root用户，继续安装..."
elif sudo -n true 2>/dev/null; then
   echo "检测到sudo权限，继续安装..."
else
   echo "错误: 需要root权限或sudo权限来安装系统依赖"
   exit 1
fi

# 检查Ubuntu版本
echo "检查系统版本..."
if ! lsb_release -d | grep -q "Ubuntu"; then
    echo "警告: 此脚本专为Ubuntu设计，其他系统可能需要调整"
fi

# 更新系统包
echo "更新系统包..."
sudo apt update && sudo apt upgrade -y

# 安装基础工具
echo "安装基础工具..."
sudo apt install -y curl wget git unzip software-properties-common

# 安装Python 3.10
echo "安装Python 3.10..."
sudo apt install -y python3.10 python3.10-pip python3.10-venv python3.10-dev

# 安装系统依赖
echo "安装系统依赖..."
sudo apt install -y build-essential
sudo apt install -y libzbar0 libzbar-dev  # pyzbar依赖
sudo apt install -y libgl1-mesa-glx libglib2.0-0 libsm6 libxext6 libxrender-dev libgomp1  # OpenCV依赖
sudo apt install -y libgtk-3-dev  # tkinter GUI依赖
sudo apt install -y mysql-server mysql-client libmysqlclient-dev  # MySQL

# 创建项目目录和用户
echo "创建项目用户和目录..."
PROJECT_USER="datatrans"
PROJECT_HOME="/opt/datatransmission"

# 创建系统用户
if ! id "$PROJECT_USER" &>/dev/null; then
    sudo useradd -r -s /bin/bash -d "$PROJECT_HOME" "$PROJECT_USER"
    echo "创建用户: $PROJECT_USER"
fi

# 创建项目目录
sudo mkdir -p "$PROJECT_HOME"
sudo chown "$PROJECT_USER:$PROJECT_USER" "$PROJECT_HOME"

# 复制项目文件到目标目录
echo "复制项目文件..."
sudo cp -r . "$PROJECT_HOME/"
sudo chown -R "$PROJECT_USER:$PROJECT_USER" "$PROJECT_HOME"

# 切换到项目目录
cd "$PROJECT_HOME"

# 创建虚拟环境
echo "创建Python虚拟环境..."
sudo -u "$PROJECT_USER" python3.10 -m venv venv

# 激活虚拟环境并安装依赖
echo "安装Python依赖包..."
sudo -u "$PROJECT_USER" bash -c "source venv/bin/activate && pip install --upgrade pip && pip install -r requirements.txt"

# 配置MySQL数据库
echo "配置MySQL数据库..."
sudo systemctl start mysql
sudo systemctl enable mysql

# 生成随机密码
DB_PASSWORD=$(openssl rand -base64 12)
echo "生成的数据库密码: $DB_PASSWORD"

# 创建数据库和用户
sudo mysql -e "CREATE DATABASE IF NOT EXISTS DataTransmission CHARACTER SET utf8 COLLATE utf8_general_ci;"
sudo mysql -e "CREATE USER IF NOT EXISTS 'datatrans'@'localhost' IDENTIFIED BY '$DB_PASSWORD';"
sudo mysql -e "GRANT ALL PRIVILEGES ON DataTransmission.* TO 'datatrans'@'localhost';"
sudo mysql -e "FLUSH PRIVILEGES;"

# 更新配置文件
echo "更新配置文件..."
sudo -u "$PROJECT_USER" sed -i "s/'password': ''/'password': '$DB_PASSWORD'/" "$PROJECT_HOME/config.py"
sudo -u "$PROJECT_USER" sed -i "s/'user': 'root'/'user': 'datatrans'/" "$PROJECT_HOME/config.py"

# 创建日志目录
echo "创建日志目录..."
sudo mkdir -p /var/log/datatransmission
sudo chown "$PROJECT_USER:$PROJECT_USER" /var/log/datatransmission

# 创建启动脚本
echo "创建启动脚本..."
sudo -u "$PROJECT_USER" cat > "$PROJECT_HOME/start.sh" << 'EOF'
#!/bin/bash
cd "$(dirname "$0")"
source venv/bin/activate
export DISPLAY=:0  # 设置显示环境变量
python main.py >> /var/log/datatransmission/app.log 2>&1
EOF

sudo chmod +x "$PROJECT_HOME/start.sh"

# 创建停止脚本
echo "创建停止脚本..."
sudo -u "$PROJECT_USER" cat > "$PROJECT_HOME/stop.sh" << 'EOF'
#!/bin/bash
pkill -f "python main.py"
echo "数据传输客户端已停止"
EOF

sudo chmod +x "$PROJECT_HOME/stop.sh"

# 创建systemd服务文件
echo "创建systemd服务..."
sudo tee /etc/systemd/system/datatransmission.service > /dev/null << EOF
[Unit]
Description=Data Transmission Client
After=network.target mysql.service
Wants=mysql.service

[Service]
Type=simple
User=$PROJECT_USER
Group=$PROJECT_USER
WorkingDirectory=$PROJECT_HOME
Environment=DISPLAY=:0
ExecStart=$PROJECT_HOME/venv/bin/python $PROJECT_HOME/main.py
ExecStop=/bin/kill -TERM \$MAINPID
Restart=always
RestartSec=10
StandardOutput=append:/var/log/datatransmission/app.log
StandardError=append:/var/log/datatransmission/error.log

[Install]
WantedBy=multi-user.target
EOF

# 重新加载systemd并启用服务
sudo systemctl daemon-reload
sudo systemctl enable datatransmission

# 配置防火墙
echo "配置防火墙..."
if command -v ufw &> /dev/null; then
    sudo ufw allow 5000/tcp
    echo "已开放端口5000"
fi

echo ""
echo "=========================================="
echo "安装完成！"
echo "=========================================="
echo ""
echo "数据库信息："
echo "- 数据库名: DataTransmission"
echo "- 用户名: datatrans"
echo "- 密码: $DB_PASSWORD"
echo ""
echo "项目信息："
echo "- 项目目录: $PROJECT_HOME"
echo "- 项目用户: $PROJECT_USER"
echo "- 日志目录: /var/log/datatransmission"
echo ""
echo "服务管理："
echo "- 启动服务: sudo systemctl start datatransmission"
echo "- 停止服务: sudo systemctl stop datatransmission"
echo "- 查看状态: sudo systemctl status datatransmission"
echo "- 查看日志: sudo journalctl -u datatransmission -f"
echo ""
echo "手动管理："
echo "- 切换用户: sudo su - $PROJECT_USER"
echo "- 手动启动: cd $PROJECT_HOME && ./start.sh"
echo "- 手动停止: cd $PROJECT_HOME && ./stop.sh"
echo ""
echo "服务端口："
echo "- HTTP API: http://$(hostname -I | awk '{print $1}'):5000"
echo "- 主要接口: POST /receiveData"
echo "- 健康检查: GET /health"
echo ""
echo "重要提示："
echo "1. 请保存数据库密码: $DB_PASSWORD"
echo "2. 如需修改配置，请编辑: $PROJECT_HOME/config.py"
echo "3. 建议重启系统后测试服务自启动功能"
echo "=========================================="
