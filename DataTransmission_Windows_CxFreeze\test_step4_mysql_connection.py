#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
步骤4: MySQL连接测试
详细测试MySQL数据库连接的各个环节
"""

import sys
import traceback
import socket

def test_mysql_module():
    """测试MySQL模块"""
    print("=" * 50)
    print("MySQL模块测试")
    print("=" * 50)
    
    try:
        import mysql.connector
        from mysql.connector import Error
        print(f"✓ mysql.connector 导入成功")
        print(f"✓ 版本: {mysql.connector.__version__}")
        return mysql.connector, Error
    except ImportError as e:
        print(f"✗ mysql.connector 导入失败: {e}")
        return None, None
    except Exception as e:
        print(f"✗ MySQL模块异常: {e}")
        return None, None

def test_config_loading():
    """测试配置加载"""
    print("\n" + "=" * 50)
    print("配置加载测试")
    print("=" * 50)
    
    try:
        import config
        if hasattr(config, 'DATABASE_CONFIG'):
            db_config = config.DATABASE_CONFIG
            print("✓ 数据库配置加载成功")
            
            # 显示配置信息（隐藏密码）
            config_display = db_config.copy()
            if 'password' in config_display:
                config_display['password'] = '***' if config_display['password'] else '未设置'
            
            for key, value in config_display.items():
                print(f"  {key}: {value}")
            
            return db_config
        else:
            print("✗ 配置文件中缺少 DATABASE_CONFIG")
            return None
    except Exception as e:
        print(f"✗ 配置加载失败: {e}")
        return None

def test_network_connectivity(host, port):
    """测试网络连接"""
    print(f"\n" + "=" * 50)
    print(f"网络连接测试 ({host}:{port})")
    print("=" * 50)
    
    try:
        # 测试TCP连接
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(5)  # 5秒超时
        result = sock.connect_ex((host, port))
        sock.close()
        
        if result == 0:
            print(f"✓ 网络连接成功: {host}:{port}")
            return True
        else:
            print(f"✗ 网络连接失败: {host}:{port}")
            print(f"错误代码: {result}")
            
            # 提供具体的错误解释
            if result == 10061:  # Windows: Connection refused
                print("原因: 连接被拒绝 - MySQL服务可能未启动")
            elif result == 10060:  # Windows: Connection timeout
                print("原因: 连接超时 - 主机不可达或防火墙阻止")
            
            return False
            
    except socket.gaierror as e:
        print(f"✗ 主机名解析失败: {e}")
        return False
    except Exception as e:
        print(f"✗ 网络测试异常: {e}")
        return False

def test_mysql_server_connection(mysql_connector, Error, db_config):
    """测试MySQL服务器连接（不指定数据库）"""
    print(f"\n" + "=" * 50)
    print("MySQL服务器连接测试")
    print("=" * 50)
    
    try:
        # 创建不包含数据库的连接配置
        server_config = {
            'host': db_config['host'],
            'port': db_config['port'],
            'user': db_config['user'],
            'password': db_config['password'],
            'charset': db_config.get('charset', 'utf8')
        }
        
        print(f"尝试连接到MySQL服务器...")
        connection = mysql_connector.connect(**server_config)
        
        if connection.is_connected():
            print("✓ MySQL服务器连接成功")
            
            # 获取服务器信息
            cursor = connection.cursor()
            
            # MySQL版本
            cursor.execute("SELECT VERSION()")
            version = cursor.fetchone()
            print(f"✓ MySQL版本: {version[0]}")
            
            # 当前用户
            cursor.execute("SELECT USER()")
            user = cursor.fetchone()
            print(f"✓ 当前用户: {user[0]}")
            
            # 连接ID
            cursor.execute("SELECT CONNECTION_ID()")
            conn_id = cursor.fetchone()
            print(f"✓ 连接ID: {conn_id[0]}")
            
            # 服务器状态
            cursor.execute("SHOW STATUS LIKE 'Uptime'")
            uptime = cursor.fetchone()
            if uptime:
                print(f"✓ 服务器运行时间: {uptime[1]} 秒")
            
            cursor.close()
            connection.close()
            return True
            
    except Error as e:
        print(f"✗ MySQL服务器连接失败")
        print(f"错误代码: {e.errno}")
        print(f"错误信息: {e.msg}")
        
        # 提供具体的解决建议
        if e.errno == 2003:
            print("\n💡 解决建议:")
            print("- 检查MySQL服务是否启动: net start mysql")
            print("- 检查防火墙是否阻止3306端口")
            print("- 确认MySQL服务器地址是否正确")
        elif e.errno == 1045:
            print("\n💡 解决建议:")
            print("- 检查用户名和密码是否正确")
            print("- 确认用户是否有连接权限")
            print("- 尝试重置MySQL密码")
        elif e.errno == 1130:
            print("\n💡 解决建议:")
            print("- 主机不允许连接到MySQL服务器")
            print("- 检查MySQL用户权限设置")
        
        return False
    except Exception as e:
        print(f"✗ 连接时出现异常: {e}")
        traceback.print_exc()
        return False

def test_database_existence(mysql_connector, Error, db_config):
    """测试数据库是否存在"""
    print(f"\n" + "=" * 50)
    print("数据库存在性测试")
    print("=" * 50)
    
    try:
        # 连接到MySQL服务器（不指定数据库）
        server_config = {
            'host': db_config['host'],
            'port': db_config['port'],
            'user': db_config['user'],
            'password': db_config['password'],
            'charset': db_config.get('charset', 'utf8')
        }
        
        connection = mysql_connector.connect(**server_config)
        cursor = connection.cursor()
        
        # 检查数据库是否存在
        database_name = db_config['database']
        cursor.execute(f"SHOW DATABASES LIKE '{database_name}'")
        result = cursor.fetchone()
        
        if result:
            print(f"✓ 数据库 '{database_name}' 存在")
            
            # 切换到数据库
            cursor.execute(f"USE {database_name}")
            print(f"✓ 成功切换到数据库 '{database_name}'")
            
            # 显示数据库信息
            cursor.execute("SELECT DATABASE()")
            current_db = cursor.fetchone()
            print(f"✓ 当前数据库: {current_db[0]}")
            
            # 显示表列表
            cursor.execute("SHOW TABLES")
            tables = cursor.fetchall()
            if tables:
                print(f"✓ 数据库中有 {len(tables)} 个表:")
                for table in tables[:5]:  # 只显示前5个表
                    print(f"  - {table[0]}")
                if len(tables) > 5:
                    print(f"  ... 还有 {len(tables) - 5} 个表")
            else:
                print("ℹ️ 数据库为空（没有表）")
            
            cursor.close()
            connection.close()
            return True
        else:
            print(f"✗ 数据库 '{database_name}' 不存在")
            print(f"\n💡 解决方案:")
            print(f"在MySQL中执行以下命令创建数据库:")
            print(f"CREATE DATABASE {database_name} CHARACTER SET utf8 COLLATE utf8_general_ci;")
            
            cursor.close()
            connection.close()
            return False
            
    except Error as e:
        print(f"✗ 检查数据库时出错: {e}")
        return False
    except Exception as e:
        print(f"✗ 检查数据库时出现异常: {e}")
        traceback.print_exc()
        return False

def test_full_database_connection(mysql_connector, Error, db_config):
    """测试完整的数据库连接"""
    print(f"\n" + "=" * 50)
    print("完整数据库连接测试")
    print("=" * 50)
    
    try:
        print("尝试连接到完整的数据库配置...")
        connection = mysql_connector.connect(**db_config)
        
        if connection.is_connected():
            print("✓ 完整数据库连接成功")
            
            cursor = connection.cursor()
            
            # 验证连接信息
            cursor.execute("SELECT DATABASE(), USER(), CONNECTION_ID()")
            info = cursor.fetchone()
            print(f"✓ 数据库: {info[0]}")
            print(f"✓ 用户: {info[1]}")
            print(f"✓ 连接ID: {info[2]}")
            
            # 测试简单查询
            cursor.execute("SELECT 1 as test")
            test_result = cursor.fetchone()
            if test_result[0] == 1:
                print("✓ 数据库查询测试通过")
            
            cursor.close()
            connection.close()
            return True
            
    except Error as e:
        print(f"✗ 完整连接失败")
        print(f"错误代码: {e.errno}")
        print(f"错误信息: {e.msg}")
        return False
    except Exception as e:
        print(f"✗ 完整连接时出现异常: {e}")
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("步骤4: MySQL连接详细测试")
    print("=" * 60)
    
    # 步骤1: 测试MySQL模块
    mysql_connector, Error = test_mysql_module()
    if not mysql_connector:
        print("\n❌ MySQL模块测试失败")
        print("请先运行: offline_packages/install_offline.bat")
        input("按回车键退出...")
        return
    
    # 步骤2: 测试配置加载
    db_config = test_config_loading()
    if not db_config:
        print("\n❌ 配置加载失败")
        print("请先运行: test_step3_config_file.py")
        input("按回车键退出...")
        return
    
    # 步骤3: 测试网络连接
    network_ok = test_network_connectivity(db_config['host'], db_config['port'])
    
    # 步骤4: 测试MySQL服务器连接
    server_ok = False
    if network_ok:
        server_ok = test_mysql_server_connection(mysql_connector, Error, db_config)
    
    # 步骤5: 测试数据库存在性
    database_ok = False
    if server_ok:
        database_ok = test_database_existence(mysql_connector, Error, db_config)
    
    # 步骤6: 测试完整连接
    full_connection_ok = False
    if database_ok:
        full_connection_ok = test_full_database_connection(mysql_connector, Error, db_config)
    
    # 总结
    print("\n" + "=" * 60)
    print("步骤4测试结果总结")
    print("=" * 60)
    
    results = [
        ("MySQL模块", mysql_connector is not None),
        ("配置加载", db_config is not None),
        ("网络连接", network_ok),
        ("MySQL服务器连接", server_ok),
        ("数据库存在", database_ok),
        ("完整数据库连接", full_connection_ok),
    ]
    
    all_success = True
    for test_name, result in results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name}: {status}")
        if not result:
            all_success = False
    
    print()
    if all_success:
        print("🎉 MySQL连接测试全部通过！")
        print("可以继续下一步: test_step5_application_components.py")
    else:
        print("❌ MySQL连接测试失败")
        
        # 提供针对性建议
        if not network_ok:
            print("\n💡 网络连接失败，请检查:")
            print("- MySQL服务是否启动: net start mysql")
            print("- 防火墙设置")
        elif not server_ok:
            print("\n💡 服务器连接失败，请检查:")
            print("- 用户名和密码是否正确")
            print("- MySQL用户权限")
        elif not database_ok:
            print("\n💡 数据库不存在，请运行:")
            print("- setup_database.bat")
            print("- 或手动创建数据库")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n用户中断测试")
    except Exception as e:
        print(f"\n测试过程中出现未预期的错误: {e}")
        traceback.print_exc()
        input("按回车键退出...")
