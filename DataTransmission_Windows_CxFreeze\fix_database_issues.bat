@echo off
chcp 65001 >nul
title 数据库问题修复工具

echo ========================================
echo    DataTransmission 数据库问题修复
echo ========================================
echo.

echo 正在检查和修复数据库问题...
echo.

echo 步骤1: 检查MySQL服务状态
echo ----------------------------------------
sc query mysql >nul 2>&1
if %errorlevel% equ 0 (
    echo ✓ MySQL服务已安装
    sc query mysql | find "RUNNING" >nul
    if %errorlevel% equ 0 (
        echo ✓ MySQL服务正在运行
    ) else (
        echo ⚠ MySQL服务未运行，正在尝试启动...
        net start mysql >nul 2>&1
        if %errorlevel% equ 0 (
            echo ✓ MySQL服务启动成功
        ) else (
            echo ✗ MySQL服务启动失败
            echo   请以管理员身份运行此脚本，或手动启动MySQL服务
            echo   命令: net start mysql
            pause
            exit /b 1
        )
    )
) else (
    echo ✗ 未检测到MySQL服务
    echo.
    echo 💡 解决方案:
    echo 1. 下载并安装MySQL 8.0
    echo 2. 确保在安装时选择了"Install as Windows Service"
    echo 3. 记住设置的root密码
    echo.
    pause
    exit /b 1
)

echo.
echo 步骤2: 测试数据库连接
echo ----------------------------------------
echo 运行数据库连接测试...
python test_database_connection.py

echo.
echo 步骤3: 常见问题解决方案
echo ----------------------------------------
echo.
echo 如果上述测试失败，请根据以下情况处理:
echo.
echo 问题1: "Can't connect to MySQL server"
echo 解决: 
echo   - 确保MySQL服务正在运行: net start mysql
echo   - 检查防火墙设置
echo   - 确认端口3306未被占用
echo.
echo 问题2: "Access denied for user 'root'"
echo 解决:
echo   - 检查config.py中的密码是否正确
echo   - 重置MySQL root密码
echo.
echo 问题3: "Unknown database 'DataTransmission'"
echo 解决:
echo   - 创建数据库: CREATE DATABASE DataTransmission;
echo.
echo 问题4: 程序启动后立即退出
echo 解决:
echo   - 运行: python test_database_connection.py
echo   - 查看详细错误信息
echo.

echo ========================================
echo 详细测试和修复选项
echo ========================================
echo.
echo 选择要执行的操作:
echo.
echo === 分步测试 ===
echo 1. 测试MySQL服务状态
echo 2. 测试Python模块
echo 3. 测试配置文件
echo 4. 测试MySQL连接
echo 5. 测试应用组件
echo.
echo === 修复操作 ===
echo 6. 重启MySQL服务
echo 7. 创建DataTransmission数据库
echo 8. 一键数据库设置
echo 9. 查看MySQL错误日志
echo.
echo === 综合测试 ===
echo A. 运行完整数据库连接测试
echo B. 运行所有分步测试
echo.
echo 0. 退出
echo.

set /p choice="请输入选项: "

if "%choice%"=="1" goto test_mysql_service
if "%choice%"=="2" goto test_python_modules
if "%choice%"=="3" goto test_config_file
if "%choice%"=="4" goto test_mysql_connection
if "%choice%"=="5" goto test_app_components
if "%choice%"=="6" goto restart_mysql
if "%choice%"=="7" goto create_database
if "%choice%"=="8" goto setup_database
if "%choice%"=="9" goto view_logs
if /i "%choice%"=="A" goto test_connection
if /i "%choice%"=="B" goto run_all_tests
if "%choice%"=="0" goto end
goto invalid_choice

:test_mysql_service
echo.
echo 运行MySQL服务测试...
test_step1_mysql_service.bat
goto menu_end

:test_python_modules
echo.
echo 运行Python模块测试...
python test_step2_python_modules.py
goto menu_end

:test_config_file
echo.
echo 运行配置文件测试...
python test_step3_config_file.py
goto menu_end

:test_mysql_connection
echo.
echo 运行MySQL连接测试...
python test_step4_mysql_connection.py
goto menu_end

:test_app_components
echo.
echo 运行应用组件测试...
python test_step5_application_components.py
goto menu_end

:run_all_tests
echo.
echo 运行所有分步测试...
echo.
echo === 步骤1: MySQL服务测试 ===
call test_step1_mysql_service.bat
echo.
echo === 步骤2: Python模块测试 ===
python test_step2_python_modules.py
echo.
echo === 步骤3: 配置文件测试 ===
python test_step3_config_file.py
echo.
echo === 步骤4: MySQL连接测试 ===
python test_step4_mysql_connection.py
echo.
echo === 步骤5: 应用组件测试 ===
python test_step5_application_components.py
echo.
echo 所有分步测试完成！
goto menu_end

:setup_database
echo.
echo 运行一键数据库设置...
setup_database.bat
goto menu_end

:restart_mysql
echo.
echo 重启MySQL服务...
net stop mysql >nul 2>&1
timeout /t 2 >nul
net start mysql >nul 2>&1
if %errorlevel% equ 0 (
    echo ✓ MySQL服务重启成功
) else (
    echo ✗ MySQL服务重启失败
)
goto menu_end

:create_database
echo.
echo 创建DataTransmission数据库...
echo 请输入MySQL root密码:
mysql -u root -p -e "CREATE DATABASE IF NOT EXISTS DataTransmission CHARACTER SET utf8 COLLATE utf8_general_ci; SHOW DATABASES LIKE 'DataTransmission';"
if %errorlevel% equ 0 (
    echo ✓ 数据库创建成功
) else (
    echo ✗ 数据库创建失败，请检查密码是否正确
)
goto menu_end

:test_connection
echo.
echo 运行数据库连接测试...
python test_database_connection.py
goto menu_end

:view_logs
echo.
echo 查看MySQL错误日志...
echo 常见日志位置:
echo - C:\ProgramData\MySQL\MySQL Server 8.0\Data\*.err
echo - C:\Program Files\MySQL\MySQL Server 8.0\data\*.err
echo.
echo 请手动检查这些位置的错误日志文件
goto menu_end

:invalid_choice
echo.
echo 无效选项，请重新选择
goto menu_end

:menu_end
echo.
pause
goto end

:end
echo.
echo 修复工具结束
echo 如果问题仍然存在，请运行: python test_database_connection.py
echo 获取详细的错误信息
pause
