@echo off
chcp 65001 >nul
title 数据库问题修复工具

echo ========================================
echo    DataTransmission 数据库问题修复
echo ========================================
echo.

echo 正在检查和修复数据库问题...
echo.

echo 步骤1: 检查MySQL服务状态
echo ----------------------------------------
sc query mysql >nul 2>&1
if %errorlevel% equ 0 (
    echo ✓ MySQL服务已安装
    sc query mysql | find "RUNNING" >nul
    if %errorlevel% equ 0 (
        echo ✓ MySQL服务正在运行
    ) else (
        echo ⚠ MySQL服务未运行，正在尝试启动...
        net start mysql >nul 2>&1
        if %errorlevel% equ 0 (
            echo ✓ MySQL服务启动成功
        ) else (
            echo ✗ MySQL服务启动失败
            echo   请以管理员身份运行此脚本，或手动启动MySQL服务
            echo   命令: net start mysql
            pause
            exit /b 1
        )
    )
) else (
    echo ✗ 未检测到MySQL服务
    echo.
    echo 💡 解决方案:
    echo 1. 下载并安装MySQL 8.0
    echo 2. 确保在安装时选择了"Install as Windows Service"
    echo 3. 记住设置的root密码
    echo.
    pause
    exit /b 1
)

echo.
echo 步骤2: 测试数据库连接
echo ----------------------------------------
echo 运行数据库连接测试...
python test_database_connection.py

echo.
echo 步骤3: 常见问题解决方案
echo ----------------------------------------
echo.
echo 如果上述测试失败，请根据以下情况处理:
echo.
echo 问题1: "Can't connect to MySQL server"
echo 解决: 
echo   - 确保MySQL服务正在运行: net start mysql
echo   - 检查防火墙设置
echo   - 确认端口3306未被占用
echo.
echo 问题2: "Access denied for user 'root'"
echo 解决:
echo   - 检查config.py中的密码是否正确
echo   - 重置MySQL root密码
echo.
echo 问题3: "Unknown database 'DataTransmission'"
echo 解决:
echo   - 创建数据库: CREATE DATABASE DataTransmission;
echo.
echo 问题4: 程序启动后立即退出
echo 解决:
echo   - 运行: python test_database_connection.py
echo   - 查看详细错误信息
echo.

echo ========================================
echo 自动修复选项
echo ========================================
echo.
echo 选择要执行的修复操作:
echo 1. 重启MySQL服务
echo 2. 创建DataTransmission数据库
echo 3. 测试数据库连接
echo 4. 查看MySQL错误日志
echo 5. 退出
echo.

set /p choice="请输入选项 (1-5): "

if "%choice%"=="1" goto restart_mysql
if "%choice%"=="2" goto create_database
if "%choice%"=="3" goto test_connection
if "%choice%"=="4" goto view_logs
if "%choice%"=="5" goto end
goto invalid_choice

:restart_mysql
echo.
echo 重启MySQL服务...
net stop mysql >nul 2>&1
timeout /t 2 >nul
net start mysql >nul 2>&1
if %errorlevel% equ 0 (
    echo ✓ MySQL服务重启成功
) else (
    echo ✗ MySQL服务重启失败
)
goto menu_end

:create_database
echo.
echo 创建DataTransmission数据库...
echo 请输入MySQL root密码:
mysql -u root -p -e "CREATE DATABASE IF NOT EXISTS DataTransmission CHARACTER SET utf8 COLLATE utf8_general_ci; SHOW DATABASES LIKE 'DataTransmission';"
if %errorlevel% equ 0 (
    echo ✓ 数据库创建成功
) else (
    echo ✗ 数据库创建失败，请检查密码是否正确
)
goto menu_end

:test_connection
echo.
echo 运行数据库连接测试...
python test_database_connection.py
goto menu_end

:view_logs
echo.
echo 查看MySQL错误日志...
echo 常见日志位置:
echo - C:\ProgramData\MySQL\MySQL Server 8.0\Data\*.err
echo - C:\Program Files\MySQL\MySQL Server 8.0\data\*.err
echo.
echo 请手动检查这些位置的错误日志文件
goto menu_end

:invalid_choice
echo.
echo 无效选项，请重新选择
goto menu_end

:menu_end
echo.
pause
goto end

:end
echo.
echo 修复工具结束
echo 如果问题仍然存在，请运行: python test_database_connection.py
echo 获取详细的错误信息
pause
