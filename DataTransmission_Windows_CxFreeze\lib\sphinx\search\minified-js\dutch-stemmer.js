DutchStemmer=function(){var r=new BaseStemmer;var e=[["",-1,6],["á",0,1],["ä",0,1],["é",0,2],["ë",0,2],["í",0,3],["ï",0,3],["ó",0,4],["ö",0,4],["ú",0,5],["ü",0,5]];var i=[["",-1,3],["I",0,2],["Y",0,1]];var u=[["dd",-1,-1],["kk",-1,-1],["tt",-1,-1]];var s=[["ene",-1,2],["se",-1,3],["en",-1,2],["heden",2,1],["s",-1,3]];var a=[["end",-1,1],["ig",-1,2],["ing",-1,1],["lijk",-1,3],["baar",-1,4],["bar",-1,5]];var t=[["aa",-1,-1],["ee",-1,-1],["oo",-1,-1],["uu",-1,-1]];var c=[17,65,16,1,0,0,0,0,0,0,0,0,0,0,0,0,128];var o=[1,0,0,17,65,16,1,0,0,0,0,0,0,0,0,0,0,0,0,128];var f=[17,67,16,1,0,0,0,0,0,0,0,0,0,0,0,0,128];var l=0;var n=0;var b=false;function k(){var i;var u=r.cursor;while(true){var s=r.cursor;r:{r.bra=r.cursor;i=r.find_among(e);if(i==0){break r}r.ket=r.cursor;switch(i){case 1:if(!r.slice_from("a")){return false}break;case 2:if(!r.slice_from("e")){return false}break;case 3:if(!r.slice_from("i")){return false}break;case 4:if(!r.slice_from("o")){return false}break;case 5:if(!r.slice_from("u")){return false}break;case 6:if(r.cursor>=r.limit){break r}r.cursor++;break}continue}r.cursor=s;break}r.cursor=u;var a=r.cursor;r:{r.bra=r.cursor;if(!r.eq_s("y")){r.cursor=a;break r}r.ket=r.cursor;if(!r.slice_from("Y")){return false}}while(true){var t=r.cursor;r:{e:while(true){var o=r.cursor;i:{if(!r.in_grouping(c,97,232)){break i}r.bra=r.cursor;u:{var f=r.cursor;s:{if(!r.eq_s("i")){break s}r.ket=r.cursor;if(!r.in_grouping(c,97,232)){break s}if(!r.slice_from("I")){return false}break u}r.cursor=f;if(!r.eq_s("y")){break i}r.ket=r.cursor;if(!r.slice_from("Y")){return false}}r.cursor=o;break e}r.cursor=o;if(r.cursor>=r.limit){break r}r.cursor++}continue}r.cursor=t;break}return true}function _(){n=r.limit;l=r.limit;r:while(true){e:{if(!r.in_grouping(c,97,232)){break e}break r}if(r.cursor>=r.limit){return false}r.cursor++}r:while(true){e:{if(!r.out_grouping(c,97,232)){break e}break r}if(r.cursor>=r.limit){return false}r.cursor++}n=r.cursor;r:{if(!(n<3)){break r}n=3}r:while(true){e:{if(!r.in_grouping(c,97,232)){break e}break r}if(r.cursor>=r.limit){return false}r.cursor++}r:while(true){e:{if(!r.out_grouping(c,97,232)){break e}break r}if(r.cursor>=r.limit){return false}r.cursor++}l=r.cursor;return true}function m(){var e;while(true){var u=r.cursor;r:{r.bra=r.cursor;e=r.find_among(i);if(e==0){break r}r.ket=r.cursor;switch(e){case 1:if(!r.slice_from("y")){return false}break;case 2:if(!r.slice_from("i")){return false}break;case 3:if(r.cursor>=r.limit){break r}r.cursor++;break}continue}r.cursor=u;break}return true}function v(){if(!(n<=r.cursor)){return false}return true}function g(){if(!(l<=r.cursor)){return false}return true}function d(){var e=r.limit-r.cursor;if(r.find_among_b(u)==0){return false}r.cursor=r.limit-e;r.ket=r.cursor;if(r.cursor<=r.limit_backward){return false}r.cursor--;r.bra=r.cursor;if(!r.slice_del()){return false}return true}function h(){b=false;r.ket=r.cursor;if(!r.eq_s_b("e")){return false}r.bra=r.cursor;if(!v()){return false}var e=r.limit-r.cursor;if(!r.out_grouping_b(c,97,232)){return false}r.cursor=r.limit-e;if(!r.slice_del()){return false}b=true;if(!d()){return false}return true}function w(){if(!v()){return false}var e=r.limit-r.cursor;if(!r.out_grouping_b(c,97,232)){return false}r.cursor=r.limit-e;{var i=r.limit-r.cursor;r:{if(!r.eq_s_b("gem")){break r}return false}r.cursor=r.limit-i}if(!r.slice_del()){return false}if(!d()){return false}return true}function p(){var e;var i=r.limit-r.cursor;r:{r.ket=r.cursor;e=r.find_among_b(s);if(e==0){break r}r.bra=r.cursor;switch(e){case 1:if(!v()){break r}if(!r.slice_from("heid")){return false}break;case 2:if(!w()){break r}break;case 3:if(!v()){break r}if(!r.out_grouping_b(f,97,232)){break r}if(!r.slice_del()){return false}break}}r.cursor=r.limit-i;var u=r.limit-r.cursor;h();r.cursor=r.limit-u;var l=r.limit-r.cursor;r:{r.ket=r.cursor;if(!r.eq_s_b("heid")){break r}r.bra=r.cursor;if(!g()){break r}{var n=r.limit-r.cursor;e:{if(!r.eq_s_b("c")){break e}break r}r.cursor=r.limit-n}if(!r.slice_del()){return false}r.ket=r.cursor;if(!r.eq_s_b("en")){break r}r.bra=r.cursor;if(!w()){break r}}r.cursor=r.limit-l;var k=r.limit-r.cursor;r:{r.ket=r.cursor;e=r.find_among_b(a);if(e==0){break r}r.bra=r.cursor;switch(e){case 1:if(!g()){break r}if(!r.slice_del()){return false}e:{var _=r.limit-r.cursor;i:{r.ket=r.cursor;if(!r.eq_s_b("ig")){break i}r.bra=r.cursor;if(!g()){break i}{var m=r.limit-r.cursor;u:{if(!r.eq_s_b("e")){break u}break i}r.cursor=r.limit-m}if(!r.slice_del()){return false}break e}r.cursor=r.limit-_;if(!d()){break r}}break;case 2:if(!g()){break r}{var p=r.limit-r.cursor;e:{if(!r.eq_s_b("e")){break e}break r}r.cursor=r.limit-p}if(!r.slice_del()){return false}break;case 3:if(!g()){break r}if(!r.slice_del()){return false}if(!h()){break r}break;case 4:if(!g()){break r}if(!r.slice_del()){return false}break;case 5:if(!g()){break r}if(!b){break r}if(!r.slice_del()){return false}break}}r.cursor=r.limit-k;var q=r.limit-r.cursor;r:{if(!r.out_grouping_b(o,73,232)){break r}var y=r.limit-r.cursor;if(r.find_among_b(t)==0){break r}if(!r.out_grouping_b(c,97,232)){break r}r.cursor=r.limit-y;r.ket=r.cursor;if(r.cursor<=r.limit_backward){break r}r.cursor--;r.bra=r.cursor;if(!r.slice_del()){return false}}r.cursor=r.limit-q;return true}this.stem=function(){var e=r.cursor;k();r.cursor=e;var i=r.cursor;_();r.cursor=i;r.limit_backward=r.cursor;r.cursor=r.limit;p();r.cursor=r.limit_backward;var u=r.cursor;m();r.cursor=u;return true};this["stemWord"]=function(e){r.setCurrent(e);this.stem();return r.getCurrent()}};