@echo off
REM 离线部署包构建脚本 (Windows版本)
REM 用于在Windows环境下创建包含所有依赖的离线部署包

echo 开始构建离线部署包...

REM 设置变量
set PROJECT_NAME=DataTransmission
set BUILD_DIR=build_offline
set PACKAGE_DIR=%BUILD_DIR%\%PROJECT_NAME%_offline
set PYTHON_VERSION=3.10

REM 清理之前的构建
echo 清理之前的构建文件...
if exist %BUILD_DIR% rmdir /s /q %BUILD_DIR%

REM 创建构建目录
echo 创建构建目录...
mkdir %PACKAGE_DIR%
mkdir %PACKAGE_DIR%\src
mkdir %PACKAGE_DIR%\dependencies
mkdir %PACKAGE_DIR%\scripts

REM 复制项目源代码
echo 复制项目源代码...
copy *.py %PACKAGE_DIR%\src\
copy requirements.txt %PACKAGE_DIR%\src\
copy README.md %PACKAGE_DIR%\
copy config.py %PACKAGE_DIR%\src\
copy environment.yml %PACKAGE_DIR%\

REM 创建conda环境导出文件
echo 导出conda环境...
conda env export --name base > %PACKAGE_DIR%\environment.yml

REM 下载Python依赖包（离线包）
echo 下载Python依赖包...
cd %PACKAGE_DIR%\dependencies
mkdir python_packages

REM 使用pip下载所有依赖包到本地
pip download -r ..\src\requirements.txt --dest .\python_packages

REM 创建系统依赖信息文件
echo 记录系统依赖信息...
echo # Ubuntu系统依赖包 > system_dependencies_ubuntu.txt
echo python3.10 >> system_dependencies_ubuntu.txt
echo python3.10-pip >> system_dependencies_ubuntu.txt
echo python3.10-venv >> system_dependencies_ubuntu.txt
echo python3.10-dev >> system_dependencies_ubuntu.txt
echo libzbar0 >> system_dependencies_ubuntu.txt
echo libzbar-dev >> system_dependencies_ubuntu.txt
echo libgl1-mesa-glx >> system_dependencies_ubuntu.txt
echo libglib2.0-0 >> system_dependencies_ubuntu.txt
echo mysql-server >> system_dependencies_ubuntu.txt
echo mysql-client >> system_dependencies_ubuntu.txt
echo libmysqlclient-dev >> system_dependencies_ubuntu.txt
echo pkg-config >> system_dependencies_ubuntu.txt

echo # CentOS 7系统依赖包 > system_dependencies_centos7.txt
echo epel-release >> system_dependencies_centos7.txt
echo python3 >> system_dependencies_centos7.txt
echo python3-pip >> system_dependencies_centos7.txt
echo python3-devel >> system_dependencies_centos7.txt
echo python3-tkinter >> system_dependencies_centos7.txt
echo zbar >> system_dependencies_centos7.txt
echo zbar-devel >> system_dependencies_centos7.txt
echo mesa-libGL >> system_dependencies_centos7.txt
echo mesa-libGL-devel >> system_dependencies_centos7.txt
echo glib2-devel >> system_dependencies_centos7.txt
echo mariadb-server >> system_dependencies_centos7.txt
echo mariadb >> system_dependencies_centos7.txt
echo mariadb-devel >> system_dependencies_centos7.txt
echo libX11-devel >> system_dependencies_centos7.txt
echo libXext-devel >> system_dependencies_centos7.txt
echo libXrender-devel >> system_dependencies_centos7.txt
echo libICE-devel >> system_dependencies_centos7.txt
echo libSM-devel >> system_dependencies_centos7.txt
echo gcc >> system_dependencies_centos7.txt
echo gcc-c++ >> system_dependencies_centos7.txt
echo make >> system_dependencies_centos7.txt
echo cmake >> system_dependencies_centos7.txt
echo pkg-config >> system_dependencies_centos7.txt

cd ..\..\..

REM 复制安装脚本
echo 复制安装脚本...
copy build_offline_package.sh %PACKAGE_DIR%\scripts\install_offline.sh

REM 创建部署说明
echo 创建部署说明...
echo # 离线部署说明 > %PACKAGE_DIR%\DEPLOY_README.md
echo. >> %PACKAGE_DIR%\DEPLOY_README.md
echo ## 部署步骤 >> %PACKAGE_DIR%\DEPLOY_README.md
echo 1. 将整个文件夹传输到Ubuntu机器 >> %PACKAGE_DIR%\DEPLOY_README.md
echo 2. 运行: sudo ./scripts/install_offline.sh >> %PACKAGE_DIR%\DEPLOY_README.md
echo 3. 配置数据库连接 >> %PACKAGE_DIR%\DEPLOY_README.md
echo 4. 启动服务 >> %PACKAGE_DIR%\DEPLOY_README.md

REM 创建压缩包（需要7zip或其他压缩工具）
echo 创建部署包...
cd %BUILD_DIR%

REM 如果有7zip，使用7zip压缩
if exist "C:\Program Files\7-Zip\7z.exe" (
    "C:\Program Files\7-Zip\7z.exe" a -ttar %PROJECT_NAME%_offline_%date:~0,4%%date:~5,2%%date:~8,2%.tar %PROJECT_NAME%_offline\
    "C:\Program Files\7-Zip\7z.exe" a -tgzip %PROJECT_NAME%_offline_%date:~0,4%%date:~5,2%%date:~8,2%.tar.gz %PROJECT_NAME%_offline_%date:~0,4%%date:~5,2%%date:~8,2%.tar
    del %PROJECT_NAME%_offline_%date:~0,4%%date:~5,2%%date:~8,2%.tar
) else (
    echo 请手动压缩 %PACKAGE_DIR% 文件夹
)

cd ..

echo 构建完成！
echo.
echo 部署包位置: %BUILD_DIR%\
echo.
echo 部署步骤：
echo 1. 将压缩包或文件夹传输到目标Ubuntu机器
echo 2. 解压并运行安装脚本
echo 3. 配置数据库连接信息
echo 4. 启动服务
echo.
pause
