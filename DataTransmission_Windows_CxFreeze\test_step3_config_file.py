#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
步骤3: 配置文件测试
检查config.py文件是否正确配置
"""

import sys
import os
import traceback

def test_config_file_exists():
    """测试配置文件是否存在"""
    print("=" * 50)
    print("配置文件存在性测试")
    print("=" * 50)
    
    config_files = ['config.py', 'config_windows.py']
    found_config = None
    
    for config_file in config_files:
        if os.path.exists(config_file):
            print(f"✓ 找到配置文件: {config_file}")
            found_config = config_file
            break
        else:
            print(f"✗ 配置文件不存在: {config_file}")
    
    if found_config:
        print(f"✓ 使用配置文件: {found_config}")
        return found_config
    else:
        print("✗ 未找到任何配置文件")
        return None

def test_config_import():
    """测试配置文件导入"""
    print("\n" + "=" * 50)
    print("配置文件导入测试")
    print("=" * 50)
    
    try:
        import config
        print("✓ config.py 导入成功")
        return config
    except ImportError as e:
        print(f"✗ config.py 导入失败: {e}")
        return None
    except SyntaxError as e:
        print(f"✗ config.py 语法错误: {e}")
        return None
    except Exception as e:
        print(f"✗ config.py 导入异常: {e}")
        traceback.print_exc()
        return None

def test_database_config(config):
    """测试数据库配置"""
    print("\n" + "=" * 50)
    print("数据库配置测试")
    print("=" * 50)
    
    if not hasattr(config, 'DATABASE_CONFIG'):
        print("✗ 配置文件中缺少 DATABASE_CONFIG")
        return None
    
    db_config = config.DATABASE_CONFIG
    print("✓ DATABASE_CONFIG 存在")
    
    # 检查必需的配置项
    required_keys = ['host', 'port', 'database', 'user', 'password', 'charset']
    missing_keys = []
    
    for key in required_keys:
        if key in db_config:
            if key == 'password':
                value_display = '***已设置***' if db_config[key] else '未设置'
            else:
                value_display = db_config[key]
            print(f"✓ {key}: {value_display}")
        else:
            missing_keys.append(key)
            print(f"✗ {key}: 缺失")
    
    if missing_keys:
        print(f"\n✗ 缺少必需的配置项: {missing_keys}")
        return None
    
    # 验证配置值
    issues = []
    
    # 检查主机
    if not db_config['host']:
        issues.append("host 不能为空")
    
    # 检查端口
    try:
        port = int(db_config['port'])
        if port <= 0 or port > 65535:
            issues.append("port 必须是1-65535之间的数字")
    except (ValueError, TypeError):
        issues.append("port 必须是数字")
    
    # 检查数据库名
    if not db_config['database']:
        issues.append("database 不能为空")
    
    # 检查用户名
    if not db_config['user']:
        issues.append("user 不能为空")
    
    # 检查密码
    if not db_config['password']:
        issues.append("password 不能为空（这是最常见的问题）")
    
    if issues:
        print(f"\n⚠️ 配置问题:")
        for issue in issues:
            print(f"  - {issue}")
        return None
    
    print("\n✓ 数据库配置验证通过")
    return db_config

def test_flask_config(config):
    """测试Flask配置"""
    print("\n" + "=" * 50)
    print("Flask配置测试")
    print("=" * 50)
    
    if not hasattr(config, 'FLASK_CONFIG'):
        print("⚠️ 配置文件中缺少 FLASK_CONFIG（可选）")
        return True
    
    flask_config = config.FLASK_CONFIG
    print("✓ FLASK_CONFIG 存在")
    
    # 检查Flask配置项
    flask_keys = ['host', 'port', 'debug']
    for key in flask_keys:
        if key in flask_config:
            print(f"✓ {key}: {flask_config[key]}")
        else:
            print(f"⚠️ {key}: 使用默认值")
    
    # 检查端口冲突
    if 'port' in flask_config:
        try:
            port = int(flask_config['port'])
            import socket
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            result = sock.connect_ex(('localhost', port))
            sock.close()
            
            if result == 0:
                print(f"⚠️ 端口 {port} 可能被占用")
            else:
                print(f"✓ 端口 {port} 可用")
        except:
            pass
    
    return True

def test_other_configs(config):
    """测试其他配置"""
    print("\n" + "=" * 50)
    print("其他配置测试")
    print("=" * 50)
    
    optional_configs = [
        ('QR_DISPLAY_TIME', '二维码显示时间'),
        ('QR_DISPLAY_SIZE', '二维码显示尺寸'),
        ('CAMERA_INDEX', '摄像头索引'),
        ('CAPTURE_INTERVAL', '截图间隔'),
        ('CAMERA_PREVIEW_ENABLED', '摄像头预览启用'),
        ('CAMERA_PREVIEW_SIZE', '预览窗口尺寸'),
        ('CAMERA_PREVIEW_POSITION', '预览窗口位置'),
    ]
    
    for config_name, description in optional_configs:
        if hasattr(config, config_name):
            value = getattr(config, config_name)
            print(f"✓ {config_name} ({description}): {value}")
        else:
            print(f"⚠️ {config_name} ({description}): 使用默认值")
    
    return True

def generate_config_template():
    """生成配置文件模板"""
    print("\n" + "=" * 50)
    print("生成配置文件模板")
    print("=" * 50)
    
    template = '''# DataTransmission 配置文件

# 数据库配置
DATABASE_CONFIG = {
    'host': 'localhost',
    'port': 3306,
    'database': 'DataTransmission',
    'user': 'root',
    'password': '',  # 请设置您的MySQL密码
    'charset': 'utf8'
}

# Flask配置
FLASK_CONFIG = {
    'host': '0.0.0.0',
    'port': 5000,
    'debug': False
}

# 二维码显示配置
QR_DISPLAY_TIME = 2  # 二维码显示时间（秒）
QR_DISPLAY_SIZE = 900  # 二维码显示尺寸（像素）

# 摄像头配置
CAMERA_INDEX = 0  # 摄像头索引
CAPTURE_INTERVAL = 1  # 截图间隔（秒）

# 摄像头预览配置
CAMERA_PREVIEW_ENABLED = True  # 是否启用摄像头预览窗口
CAMERA_PREVIEW_SIZE = (320, 240)  # 预览窗口尺寸 (宽, 高)
CAMERA_PREVIEW_POSITION = (10, 10)  # 预览窗口位置 (x, y) - 左上角
'''
    
    try:
        with open('config_template.py', 'w', encoding='utf-8') as f:
            f.write(template)
        print("✓ 配置文件模板已生成: config_template.py")
        print("您可以复制此文件为 config.py 并修改相应配置")
    except Exception as e:
        print(f"✗ 生成配置文件模板失败: {e}")

def main():
    """主函数"""
    print("=" * 60)
    print("步骤3: 配置文件测试")
    print("=" * 60)
    
    # 测试配置文件存在性
    config_file = test_config_file_exists()
    if not config_file:
        print("\n❌ 配置文件不存在")
        generate_config_template()
        input("按回车键退出...")
        return
    
    # 测试配置文件导入
    config = test_config_import()
    if not config:
        print("\n❌ 配置文件导入失败")
        print("请检查 config.py 文件语法是否正确")
        input("按回车键退出...")
        return
    
    # 测试数据库配置
    db_config = test_database_config(config)
    db_config_ok = db_config is not None
    
    # 测试Flask配置
    flask_config_ok = test_flask_config(config)
    
    # 测试其他配置
    other_config_ok = test_other_configs(config)
    
    # 总结
    print("\n" + "=" * 60)
    print("步骤3测试结果总结")
    print("=" * 60)
    
    results = [
        ("配置文件存在", config_file is not None),
        ("配置文件导入", config is not None),
        ("数据库配置", db_config_ok),
        ("Flask配置", flask_config_ok),
        ("其他配置", other_config_ok),
    ]
    
    all_success = True
    for test_name, result in results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name}: {status}")
        if not result:
            all_success = False
    
    print()
    if all_success:
        print("🎉 配置文件测试通过！")
        print("可以继续下一步: test_step4_mysql_connection.py")
    else:
        print("❌ 配置文件测试失败")
        print("请修复配置文件中的问题")
        
        if not db_config_ok:
            print("\n💡 最常见的问题:")
            print("- 数据库密码未设置或错误")
            print("- 请编辑 config.py 文件，设置正确的MySQL密码")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n用户中断测试")
    except Exception as e:
        print(f"\n测试过程中出现未预期的错误: {e}")
        traceback.print_exc()
        input("按回车键退出...")
