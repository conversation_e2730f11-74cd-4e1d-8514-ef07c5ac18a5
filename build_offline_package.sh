#!/bin/bash

# 离线部署包构建脚本
# 用于创建包含所有依赖的离线部署包

echo "开始构建离线部署包..."

# 设置变量
PROJECT_NAME="DataTransmission"
BUILD_DIR="build_offline"
PACKAGE_DIR="${BUILD_DIR}/${PROJECT_NAME}_offline"
PYTHON_VERSION="3.10"

# 清理之前的构建
echo "清理之前的构建文件..."
rm -rf ${BUILD_DIR}

# 创建构建目录
echo "创建构建目录..."
mkdir -p ${PACKAGE_DIR}
mkdir -p ${PACKAGE_DIR}/src
mkdir -p ${PACKAGE_DIR}/dependencies
mkdir -p ${PACKAGE_DIR}/scripts

# 复制项目源代码
echo "复制项目源代码..."
cp *.py ${PACKAGE_DIR}/src/
cp requirements.txt ${PACKAGE_DIR}/src/
cp README.md ${PACKAGE_DIR}/
cp config.py ${PACKAGE_DIR}/src/

# 创建conda环境导出文件
echo "导出conda环境..."
conda env export --name base > ${PACKAGE_DIR}/environment.yml

# 下载Python依赖包（离线包）
echo "下载Python依赖包..."
cd ${PACKAGE_DIR}/dependencies

# 使用pip下载所有依赖包到本地
pip download -r ../src/requirements.txt --dest ./python_packages

# 下载系统依赖包信息
echo "记录系统依赖信息..."
cat > system_dependencies.txt << 'EOF'
# Ubuntu系统依赖包
python3.10
python3.10-pip
python3.10-venv
python3.10-dev
libzbar0
libzbar-dev
libgl1-mesa-glx
libglib2.0-0
mysql-server
mysql-client
libmysqlclient-dev
pkg-config
EOF

cd ../../..

# 创建离线安装脚本
echo "创建离线安装脚本..."
cat > ${PACKAGE_DIR}/scripts/install_offline.sh << 'EOF'
#!/bin/bash

# 离线安装脚本
echo "开始离线安装数据传输客户端..."

# 检查是否为root用户
if [ "$EUID" -ne 0 ]; then
    echo "请使用sudo权限运行此脚本"
    exit 1
fi

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"

echo "项目目录: $PROJECT_DIR"

# 安装系统依赖（如果有网络连接）
echo "尝试安装系统依赖..."
apt update 2>/dev/null || echo "无网络连接，跳过apt update"

# 从文件读取系统依赖并尝试安装
while IFS= read -r package; do
    if [[ ! $package =~ ^#.* ]] && [[ -n $package ]]; then
        echo "尝试安装: $package"
        apt install -y $package 2>/dev/null || echo "跳过: $package"
    fi
done < "$PROJECT_DIR/dependencies/system_dependencies.txt"

# 检查Python 3.10是否可用
if ! command -v python3.10 &> /dev/null; then
    echo "错误: Python 3.10 未安装"
    echo "请手动安装Python 3.10或使用有网络的环境"
    exit 1
fi

# 创建项目目录
PROJECT_INSTALL_DIR="/opt/DataTransmission"
echo "创建安装目录: $PROJECT_INSTALL_DIR"
mkdir -p $PROJECT_INSTALL_DIR

# 复制源代码
echo "复制源代码..."
cp -r $PROJECT_DIR/src/* $PROJECT_INSTALL_DIR/

# 创建虚拟环境
echo "创建Python虚拟环境..."
cd $PROJECT_INSTALL_DIR
python3.10 -m venv venv

# 激活虚拟环境并安装依赖
echo "安装Python依赖包..."
source venv/bin/activate

# 离线安装pip包
pip install --no-index --find-links $PROJECT_DIR/dependencies/python_packages -r requirements.txt

# 创建启动脚本
echo "创建启动脚本..."
cat > start.sh << 'STARTEOF'
#!/bin/bash
cd "$(dirname "$0")"
source venv/bin/activate
python main.py
STARTEOF

chmod +x start.sh

# 创建停止脚本
cat > stop.sh << 'STOPEOF'
#!/bin/bash
pkill -f "python main.py"
echo "数据传输客户端已停止"
STOPEOF

chmod +x stop.sh

# 创建服务文件
echo "创建systemd服务文件..."
cat > /etc/systemd/system/datatransmission.service << 'SERVICEEOF'
[Unit]
Description=Data Transmission Client
After=network.target mysql.service

[Service]
Type=simple
User=root
WorkingDirectory=/opt/DataTransmission
ExecStart=/opt/DataTransmission/start.sh
ExecStop=/opt/DataTransmission/stop.sh
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
SERVICEEOF

# 重新加载systemd
systemctl daemon-reload

echo "安装完成！"
echo ""
echo "配置步骤："
echo "1. 编辑配置文件: nano $PROJECT_INSTALL_DIR/config.py"
echo "2. 配置MySQL数据库连接信息"
echo "3. 启动服务: systemctl start datatransmission"
echo "4. 设置开机自启: systemctl enable datatransmission"
echo ""
echo "管理命令："
echo "- 启动服务: systemctl start datatransmission"
echo "- 停止服务: systemctl stop datatransmission"
echo "- 查看状态: systemctl status datatransmission"
echo "- 查看日志: journalctl -u datatransmission -f"
echo ""
echo "手动运行："
echo "- cd $PROJECT_INSTALL_DIR && ./start.sh"
EOF

chmod +x ${PACKAGE_DIR}/scripts/install_offline.sh

# 创建conda环境安装脚本（如果目标机器有conda）
cat > ${PACKAGE_DIR}/scripts/install_with_conda.sh << 'EOF'
#!/bin/bash

# 使用conda安装的脚本
echo "使用conda环境安装..."

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"

# 创建conda环境
echo "创建conda环境..."
conda env create -f $PROJECT_DIR/environment.yml -n datatransmission

# 激活环境
echo "激活conda环境..."
conda activate datatransmission

# 安装额外依赖（如果需要）
pip install --no-index --find-links $PROJECT_DIR/dependencies/python_packages -r $PROJECT_DIR/src/requirements.txt

echo "conda环境安装完成！"
echo "使用方法："
echo "1. conda activate datatransmission"
echo "2. cd $PROJECT_DIR/src"
echo "3. python main.py"
EOF

chmod +x ${PACKAGE_DIR}/scripts/install_with_conda.sh

# 创建部署说明文档
cat > ${PACKAGE_DIR}/DEPLOY_README.md << 'EOF'
# 离线部署说明

## 部署包内容
```
DataTransmission_offline/
├── src/                    # 项目源代码
├── dependencies/           # 依赖包
│   ├── python_packages/   # Python离线包
│   └── system_dependencies.txt  # 系统依赖列表
├── scripts/               # 安装脚本
│   ├── install_offline.sh    # 离线安装脚本
│   └── install_with_conda.sh # conda环境安装脚本
├── environment.yml        # conda环境文件
└── DEPLOY_README.md      # 本文档
```

## 部署方法

### 方法一：使用离线安装脚本（推荐）
```bash
sudo ./scripts/install_offline.sh
```

### 方法二：使用conda环境
```bash
./scripts/install_with_conda.sh
```

### 方法三：手动安装
1. 安装系统依赖
2. 创建Python虚拟环境
3. 安装Python依赖包
4. 配置数据库
5. 运行程序

## 配置要求
- Ubuntu 18.04+
- Python 3.10
- MySQL 5.7+
- 至少2GB可用磁盘空间

## 部署后配置
1. 编辑 `/opt/DataTransmission/config.py`
2. 配置MySQL数据库连接
3. 启动服务

## 故障排除
- 检查日志: `journalctl -u datatransmission -f`
- 手动运行: `cd /opt/DataTransmission && ./start.sh`
- 检查端口: `netstat -tlnp | grep 5000`
EOF

# 创建打包脚本
echo "创建最终部署包..."
cd ${BUILD_DIR}
tar -czf ${PROJECT_NAME}_offline_$(date +%Y%m%d_%H%M%S).tar.gz ${PROJECT_NAME}_offline/

echo "构建完成！"
echo ""
echo "部署包位置: ${BUILD_DIR}/${PROJECT_NAME}_offline_$(date +%Y%m%d_%H%M%S).tar.gz"
echo ""
echo "部署步骤："
echo "1. 将tar.gz文件传输到目标Ubuntu机器"
echo "2. 解压: tar -xzf ${PROJECT_NAME}_offline_*.tar.gz"
echo "3. 运行安装: sudo ./DataTransmission_offline/scripts/install_offline.sh"
echo "4. 配置数据库连接信息"
echo "5. 启动服务"
