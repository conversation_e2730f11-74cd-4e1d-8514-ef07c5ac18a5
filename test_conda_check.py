#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试conda环境检查功能
用于验证修复后的conda检查是否正常工作
"""

import subprocess
import sys

def test_conda_commands():
    """测试conda命令是否可用"""
    print("=" * 50)
    print("测试conda命令")
    print("=" * 50)
    
    commands = [
        ['conda', '--version'],
        ['conda', 'info', '--envs'],
        ['conda', 'list', '--export'],
        ['pip', '--version'],
        ['pip', 'freeze']
    ]
    
    for cmd in commands:
        print(f"\n测试命令: {' '.join(cmd)}")
        try:
            # 测试不使用shell
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
            if result.returncode == 0:
                print(f"✓ 成功 (无shell): {result.stdout.strip()[:100]}")
            else:
                print(f"✗ 失败 (无shell): {result.stderr.strip()[:100]}")
                
                # 尝试使用shell
                result = subprocess.run(cmd, capture_output=True, text=True, shell=True, timeout=30)
                if result.returncode == 0:
                    print(f"✓ 成功 (使用shell): {result.stdout.strip()[:100]}")
                else:
                    print(f"✗ 失败 (使用shell): {result.stderr.strip()[:100]}")
                    
        except FileNotFoundError:
            print("✗ 命令未找到，尝试使用shell...")
            try:
                result = subprocess.run(cmd, capture_output=True, text=True, shell=True, timeout=30)
                if result.returncode == 0:
                    print(f"✓ 成功 (shell): {result.stdout.strip()[:100]}")
                else:
                    print(f"✗ 失败 (shell): {result.stderr.strip()[:100]}")
            except Exception as e:
                print(f"✗ 异常: {e}")
        except Exception as e:
            print(f"✗ 异常: {e}")

def test_package_imports():
    """测试关键包导入"""
    print("\n" + "=" * 50)
    print("测试关键包导入")
    print("=" * 50)
    
    packages = {
        'flask': 'flask',
        'mysql-connector-python': 'mysql.connector',
        'opencv-python': 'cv2',
        'qrcode': 'qrcode',
        'pyzbar': 'pyzbar',
        'numpy': 'numpy',
        'requests': 'requests'
    }
    
    for package_name, import_name in packages.items():
        try:
            module = __import__(import_name)
            version = getattr(module, '__version__', 'unknown')
            print(f"✓ {package_name} ({import_name}) - 版本: {version}")
        except ImportError as e:
            print(f"✗ {package_name} ({import_name}) - 错误: {e}")

def test_environment_info():
    """测试环境信息"""
    print("\n" + "=" * 50)
    print("环境信息")
    print("=" * 50)
    
    print(f"Python版本: {sys.version}")
    print(f"Python路径: {sys.executable}")
    
    # 检查是否在虚拟环境中
    in_venv = hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix)
    print(f"虚拟环境: {in_venv}")
    
    if hasattr(sys, 'base_prefix'):
        print(f"Base prefix: {sys.base_prefix}")
    if hasattr(sys, 'prefix'):
        print(f"Prefix: {sys.prefix}")

def main():
    """主测试函数"""
    print("Conda环境检查测试工具")
    print("用于验证conda命令和包导入是否正常")
    
    test_environment_info()
    test_conda_commands()
    test_package_imports()
    
    print("\n" + "=" * 50)
    print("测试完成")
    print("=" * 50)
    print("如果conda命令测试失败:")
    print("1. 确保Anaconda/Miniconda已正确安装")
    print("2. 确保conda已添加到PATH环境变量")
    print("3. 重新打开命令行窗口")
    print("4. 尝试运行: conda init")
    print("\n如果包导入测试失败:")
    print("1. 激活正确的conda环境")
    print("2. 安装缺失的包: pip install package_name")
    print("3. 检查环境配置")

if __name__ == "__main__":
    main()
